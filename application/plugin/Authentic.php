<?php
namespace App\Plugin;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Log;
use Sofast\Core\Lang;
use Sofast\Core\Config;
use Sofast\Core\Exception as sfException;

class Authentic
{	
	public $user = NULL;
	
	function __construct($type='users')
	{
		$this->user = sf::getModel('Users');
	}
	
	function login($username,$password)
	{
		$user = $this->user;
		if($username && $password){
            if($_SESSION['robot'] > 3) return false;
			$user->selectByName($username);
            if($user->isNew()){//用户名不正确
                $_SESSION['robot'] += 1;
                return false;
            }
            if($user->getErrorNumber() > 5 && (strtotime($user->getErrorAt()) > time() - 3600 )) exit(new sfException('使用错误的密码尝试登录系统累计超过5次，为了保证您账号的安全，系统暂时冻结您的账号，请1个小时之后重新登录！'));
			if($user->check($password))
			{

				if($user->getIsLock() == 4) exit(new sfException(lang::get("You has been lock!")));//4为黑名单

				$_SESSION['id'] 		=  $user->getId();
				$_SESSION['userid'] 	=  $user->getUserId();
				$_SESSION['roleuserid'] =  $user->getRoleUserId();
				$_SESSION['username'] 	=  $user->getUserName();
				$_SESSION['nickname'] 	=  $user->getUserUsername();
				$_SESSION['userlevel'] 	=  $user->getUserGroupId();
				$_SESSION['groupname'] 	=  $user->getUserGroupName();
				$_SESSION['office_id'] 	=  $user->getOfficeId();
				$_SESSION['useremail'] 	=  $user->getUserEmail();
				$_SESSION['lastlogin'] 	=  $user->getLastloginAt();
				$_SESSION['userip']		=  $user->getUserIp();
				$_SESSION['group_id']   =  $user->getGroupId();
                $_SESSION['auth'] =  $user->getAuths();
				$_SESSION['sign']		=  md5($user->getUserName().$user->getUserGroupId().input::getIp());

				$_session_id = $user->getSessionId();
				$user->setLoginNum();
				$user->setSessionId(session_id());
				$user->setLastloginAt(date("Y-m-d H:i:s"));
				$user->setUserIp(input::getIp());
                $user->setErrorNumber(0);
				$user->save();

				Log::write("帐号“".$user->getUserUsername()."[".$user->getUserGroupName()."]”登录成功！",'Login');
				//密码太简单强制修改密码
//				if(Evaluate(trim(input::getInput("post.password"))) < 5){
//					//弱密码不能登录系统使用
//					$_SESSION['badpwd'] = true;
//					@header("Location:".site_url("login/password/type/easy"));
//					exit;
//				}
				
				//每50次登录强迫修改一次密码
//				if($user->getLoginNum()%100 == 0){
//					@header("Location:".site_url("login/password"));
//					exit;
//				}
				
				return true;
			}else{
                $user->setErrorNumber($user->getErrorNumber() + 1);
                $user->setErrorAt(date("Y-m-d H:i:s"));
                $user->save();
                //记录日志
                Log::write("帐号".$username."[".$user->getUserGroupId()."]登录失败！使用的密码是：".getMask($password,4,2)."。");
            }
		}
		return false;
	}

    function mobile($mobile,$password)
    {
        $user = $this->user;
        if($mobile && $password){
            if($_SESSION['robot'] > 3) return false;
            if(!$user->selectByMobile($mobile)){//用户名不正确
                $_SESSION['robot'] += 1;
                return false;
            }
            //重试超过5次直接拒绝登录
            if($user->getErrorNumber() > 5 && (strtotime($user->getErrorAt()) > time() - 3600 )) exit(new sfException('使用错误的密码尝试登录系统累计超过5次，为了保证您账号的安全，系统暂时冻结您的账号，请1个小时之后重新登录！'));
            if(time() - strtotime($user->getFindAt()) > 28800) exit(new sfException('该动态密码已经失效，请重新获取动态密码！'));
            if(md5($password) == $user->getHash()){//登录成功
                if($user->getIsLock() == 4) exit(new sfException(lang::get("You has been lock!")));//4为黑名单

                $_SESSION['id'] 		=  $user->getId();
                $_SESSION['userid'] 	=  $user->getUserId();
                $_SESSION['roleuserid'] =  $user->getRoleUserId();
                $_SESSION['username'] 	=  $user->getUserName();
                $_SESSION['nickname'] 	=  $user->getUserUsername();
                $_SESSION['userlevel'] 	=  $user->getUserGroupId();
                $_SESSION['unitlevel'] 	=  $user->getUnitLevel();
                $_SESSION['groupname'] 	=  $user->getUserGroupName();
                $_SESSION['office_id'] 	=  $user->getOfficeId();
                $_SESSION['useremail'] 	=  $user->getUserEmail();
                $_SESSION['lastlogin'] 	=  $user->getLastloginAt();
                $_SESSION['userip']		=  $user->getUserIp();
                $_SESSION['group_id']   =  $user->getGroupId();
                $_SESSION['sign']		=  md5($user->getUserName().$user->getUserGroupId().input::getIp());

                $_session_id = $user->getSessionId();
                $user->setLoginNum();
                $user->setSessionId(session_id());
                $user->setLastloginAt(date("Y-m-d H:i:s"));
                $user->setUserIp(input::getIp());
                $user->setErrorNumber(0);
                $user->save();
                //记录日志
                Log::write("帐号".$user->getUserUsername()."[".$user->getUserGroupName()."]手机动态密码登录成功！",'Login');
                return true;
            }else{
                $user->setErrorNumber($user->getErrorNumber() + 1);
                $user->setErrorAt(date("Y-m-d H:i:s"));
                $user->save();
                //发送短信
//                if($user->getErrorNumber() == 4) $user->sendMessage("您的账号使用错误的密码尝试登录系统累计超过5次！",date("Y-m-d H:i:s"));
                //记录日志
                Log::write("手机号".$mobile."[".$user->getUserGroupId()."]登录失败！使用的动态密码是：".$password."。");
            }
        }
        return false;
    }

    function qq($openid)
    {
        $user = $this->user;
        if(!$user->selectByOpenid($openid)){
            return false;
        }
        if($user->getIsLock() == 4) exit(new sfException(lang::get("You has been lock!")));//4为黑名单
        $_SESSION['id'] 		=  $user->getId();
        $_SESSION['userid'] 	=  $user->getUserId();
        $_SESSION['roleuserid'] =  $user->getRoleUserId();
        $_SESSION['username'] 	=  $user->getUserName();
        $_SESSION['nickname'] 	=  $user->getUserUsername();
        $_SESSION['userlevel'] 	=  $user->getUserGroupId();
        $_SESSION['unitlevel'] 	=  $user->getUnitLevel();
        $_SESSION['groupname'] 	=  $user->getUserGroupName();
        $_SESSION['office_id'] 	=  $user->getOfficeId();
        $_SESSION['useremail'] 	=  $user->getUserEmail();
        $_SESSION['lastlogin'] 	=  $user->getLastloginAt();
        $_SESSION['userip']		=  $user->getUserIp();
        $_SESSION['group_id']   =  $user->getGroupId();
        $_SESSION['sign']		=  md5($user->getUserName().$user->getUserGroupId().input::getIp());

        $user->setLoginNum();
        $user->setSessionId(session_id());
        $user->setLastloginAt(date("Y-m-d H:i:s"));
        $user->setUserIp(input::getIp());
        $user->setErrorNumber(0);
        $user->save();
        //记录日志
        Log::write("帐号".$user->getUserUsername()."[".$user->getUserGroupName()."]使用QQ登录成功！",'Login');
        return true;
    }

	function logout()
	{
		unset($_SESSION);
		session_destroy();
		return true;
	}

	function isLogin($ids)
	{
		//签名变化自动化没有权限
		//if($_SESSION['sign'] != md5($_SESSION['username'].$_SESSION['userlevel'].input::getIp())) return false;
		
		if($_SESSION['userid'] && $_SESSION['username'] && $_SESSION['userlevel']){
			if(!$ids) return true;
			$id_array = explode(",",$ids);
			if(in_array($_SESSION['userlevel'],$id_array)) return true;
			else return false;
		}

		return false;
	}

	function checkLogin($ids)
	{
		if($_SESSION['userid'] && $_SESSION['username'] && $_SESSION['userlevel']){
			$id_array = explode(",",$ids);
			if(in_array($_SESSION['userlevel'],$id_array)) return true;
			else{//如果权限不明确先注销
				unset($_SESSION);
				session_destroy();
				return true;
			}
		}else return false;
	}

	/**
	 * 取得登录模型
	 */
	function getModel($type)
	{
		switch($type)
		{
			case 'user':
				return 'Users';
			break;
			case 'unit':
				return 'Users';
			break;
			case 'expert':
				return 'Users';
			break;
			case 'gather':
				return 'Users';
			break;
			default:
				return 'Users';
			break;
		}
	}
	
}