<div class="page-header">
    <div class="row">
        <div class="col-sm-12">
            <div class="page-title-box">
                <div class="btn-group pull-right">
                    <!--
                    <?=btn('导出','export/quarter/stat_industry_top/is_hightech/'.$isHightech.'/is_service/'.$isService.'/guideid/'.input::getMix('guideid'),'mdi mdi-download')?>
                    -->
                    <?=backBtn()?>
                </div>
                <h4 class="page-title">按行业大类统计</h4>
            </div>
        </div>
    </div>
</div>
<div class="page-body">
    <div class="row">
        <div class="col-lg-12">
            <div class="card m-b-30">
                <div class="card-body">
                    <div class="table-responsive b-0">
                        <table class="table table-hover table-bordered">
                            <thead class="thead-default">
                            <tr>
                                <th rowspan="3" width="300" style="vertical-align: middle;" class="text-center">
                                    #
                                </th>
                                <th class="text-center">
                                    企业数
                                </th>
                                <th colspan="2" class="text-center">
                                    期末从业人员
                                </th>
                                <th colspan="2" class="text-center">
                                    #本科及以上从业人员
                                </th>
                                <th colspan="2" class="text-center">
                                    营业收入
                                </th>
                                <th colspan="2" class="text-center">
                                    研究开发经费支出
                                </th>
                            </tr>
                            <tr>
                                <th class="text-center">

                                </th>
                                <th colspan="2" class="text-center">
                                    D01(人)
                                </th>
                                <th colspan="2" class="text-center">
                                    D11(人)
                                </th>
                                <th colspan="2" class="text-center">
                                    E03(万元)
                                </th>
                                <th colspan="2" class="text-center">
                                    F01(万元)
                                </th>
                            </tr>
                            <tr>
                                <th class="text-center">
                                    (个)
                                </th>
                                <th class="text-center">
                                    1-本月
                                </th>
                                <th class="text-center">
                                    上年同期
                                </th>
                                <th class="text-center">
                                    1-本月
                                </th>
                                <th class="text-center">
                                    上年同期
                                </th>
                                <th class="text-center">
                                    1-本月
                                </th>
                                <th class="text-center">
                                    上年同期
                                </th>
                                <th class="text-center">
                                    1-本月
                                </th>
                                <th class="text-center">
                                    上年同期
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $stat = sf::getModel('industrys')->getStatTotal($guideid,$isHightech,$isService,input::session('roleuserid'));
                            ?>
                            <tr>
                                <th class="text-left" scope="row">合计</th>
                                <td class="text-center">
                                    <?=intval($stat['c'])?>
                                </td>
                                <td class="text-center">
                                    <?=intval($stat['D01'])?>
                                </td>
                                <td class="text-center">
                                    <?=intval($stat['D01_1'])?>
                                </td>
                                <td class="text-center">
                                    <?=intval($stat['D11'])?>
                                </td>
                                <td class="text-center">
                                    <?=intval($stat['D11_1'])?>
                                </td>
                                <td class="text-center">
                                    <?=floatval($stat['E03'])?>
                                </td>
                                <td class="text-center">
                                    <?=floatval($stat['E03_1'])?>
                                </td>
                                <td class="text-center">
                                    <?=floatval($stat['F01'])?>
                                </td>
                                <td class="text-center">
                                    <?=floatval($stat['F01_1'])?>
                                </td>
                            </tr>
                            <?php
                                while($industry = $industrys->getObject()):
                                    if($isHightech && $industry->getIsHightech()==0) continue;
                                    if($isService && $industry->getIsService()==0) continue;
                                    $stat = $industry->getStat($guideid,$isHightech,$isService,input::session('roleuserid'));
                            ?>
                            <tr>
                                <th class="text-left" scope="row"><?=$industry->getSubjects()?></th>
                                <td class="text-center">
                                    <?=intval($stat['c'])?>
                                </td>
                                <td class="text-center">
                                    <?=intval($stat['D01'])?>
                                </td>
                                <td class="text-center">
                                    <?=intval($stat['D01_1'])?>
                                </td>
                                <td class="text-center">
                                    <?=intval($stat['D11'])?>
                                </td>
                                <td class="text-center">
                                    <?=intval($stat['D11_1'])?>
                                </td>
                                <td class="text-center">
                                    <?=floatval($stat['E03'])?>
                                </td>
                                <td class="text-center">
                                    <?=floatval($stat['E03_1'])?>
                                </td>
                                <td class="text-center">
                                    <?=floatval($stat['F01'])?>
                                </td>
                                <td class="text-center">
                                    <?=floatval($stat['F01_1'])?>
                                </td>
                            </tr>
                            <?php
                                endwhile;
                            ?>
                            <?php
                            $stat = sf::getModel('industrys')->getNoCodeTotal($guideid,$isHightech,$isService,input::session('roleuserid'));
                            if($stat['c']>0):
                            ?>
                                <tr>
                                    <th class="text-left" scope="row"><i>未知行业</i></th>
                                    <td class="text-center">
                                        <?=intval($stat['c'])?>
                                    </td>
                                    <td class="text-center">
                                        <?=intval($stat['D01'])?>
                                    </td>
                                    <td class="text-center">
                                        <?=intval($stat['D01_1'])?>
                                    </td>
                                    <td class="text-center">
                                        <?=intval($stat['D11'])?>
                                    </td>
                                    <td class="text-center">
                                        <?=intval($stat['D11_1'])?>
                                    </td>
                                    <td class="text-center">
                                        <?=floatval($stat['E01'])?>
                                    </td>
                                    <td class="text-center">
                                        <?=floatval($stat['E01_1'])?>
                                    </td>
                                    <td class="text-center">
                                        <?=floatval($stat['F01'])?>
                                    </td>
                                    <td class="text-center">
                                        <?=floatval($stat['F01_1'])?>
                                    </td>
                                </tr>
                            <?php endif;?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>