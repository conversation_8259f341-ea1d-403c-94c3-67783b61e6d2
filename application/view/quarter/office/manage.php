<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            已发布的报表
        </h2>
        <div>
            <?=Button::setUrl(site_url('quarter/office/edit'))->setIcon('add')->link('新增报表')?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-content tab-content">
                    <div class="page-body">
                        <div class="table-responsive b-0">
                            <table class="table table-hover">
                                <thead class="thead-default">
                                <tr>
                                    <th>#</th>
                                    <th class="text-left">调查表名</th>
                                    <th class="text-left">开始时间</th>
                                    <th class="text-left">截止时间</th>
                                    <th class="text-center">状态</th>
                                    <th class="text-center">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php $i=0;while($pager = $pagers->getObject()):$i++;?>
                                    <tr>
                                        <th scope="row"><?=$i?></th>
                                        <td align="left"><?=$pager->getSubject()?></td>
                                        <td align="left"><?=$pager->getStartAt()?></td>
                                        <td align="left"><?=$pager->getEndAt()?></td>
                                        <td align="center"><?=$pager->getState()?></td>
                                        <td align="center">
                                            <?=btn('编辑','quarter/office/edit/id/'.$pager->getId(),'fa fa-edit','primary btn-sm')?>
                                            <a class="btn btn-danger btn-sm" onclick="return confirm('确定要删除吗？')" href="<?=site_url('quarter/office/delete/id/'.$pager->getId())?>"><i class="fa fa-trash"></i> 删除</a>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                                <?php
                                if($pagers->getTotal()==0):
                                    ?>
                                    <tr>
                                        <td colspan="6" class="text-center">
                                            暂无数据
                                        </td>
                                    </tr>
                                <?php endif;?>
                                </tbody>
                                <tfooter>
                                    <tr>
                                        <td colspan="11" align="right">&nbsp;<?=$pagers->fromTo().$pagers->navbar(10)?></td>
                                    </tr>
                                </tfooter>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>