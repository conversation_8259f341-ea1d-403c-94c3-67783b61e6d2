<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                验收填报开关
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                <?=Button::setName('生成验收报告')->setEvent('validateForm.action=\''.site_url("office/complete/generate").'\';$(\'#validateForm\').submit()')->setIcon('check')->button()?>
                <?=Button::setName('生成医院报告')->setEvent('validateForm.action=\''.site_url("office/complete/generateCompany").'\';$(\'#validateForm\').submit()')->setIcon('check')->button()?>
                <?=Button::setName('生成市州报告')->setEvent('validateForm.action=\''.site_url("office/complete/generateGather").'\';$(\'#validateForm\').submit()')->setIcon('check')->button()?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <?php include_once('project_search_part.php') ?>
                </div>
                <div class="box">
                    <div class="no-padding no-margin panel panel-default table-responsive" >
                        <form id="validateForm" name="validateForm" method="post" action="<?=site_url("office/complete/doAccept")?>">
                            <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                                <thead>
                                <tr>
                                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th width="30">详</th>
                                    <th width="15%"><?=getColumnStr('项目名称','subject')?></th>
                                    <th><?=getColumnStr('负责人','user_id')?></th>
                                    <th><?=getColumnStr('申报单位','corporation_id')?></th>
                                    <th><?=getColumnStr('主管部门','department_id')?></th>
                                    <th><?=getColumnStr('申报年度','declare_year')?></th>
                                    <th><?=getColumnStr('开启状态','complete_open')?></th>
                                    <th><?=getColumnStr('验收状态','complete_state')?></th>
                                    <th class="text-center" width="230">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($project = $pager->getObject()):?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
                                        <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                        <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?>
                                            <p class="snote">平台级别：<?=$project->getLevel()?><br>申报年度：<?=$project->getDeclareYear()."(".$project->getTypeCurrentGroup().")"?><br>平台类别：<?=$project->getCatSubject()?></p>
                                        </td>
                                        <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                                        <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
                                        <td><?=$project->getDepartmentName()?></td>
                                        <td><?=$project->getDeclareYear()?></td>
                                        <td><?=$project->getCompleteOpen()==1 ? '已开启' : '已关闭'?></td>
                                        <td><?=$project->getCompleteState()?></td>
                                        <td align="center">
                                            <?=Button::setName('开启')->setUrl(site_url("office/complete/doOpenOnlyOne/id/".$project->getProjectId()))->setClass('btn-alt-success')->setIcon('check')->link()?>
                                            <?=Button::setName('关闭')->setUrl(site_url("office/complete/doCloseOnlyOne/id/".$project->getProjectId()))->setClass('btn-alt-danger')->setIcon('close')->link()?>
                                        </td>
                                    </tr>
                                    <tr class="fold_body">
                                        <td colspan="16"><?php include('project_more_part.php') ?>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="16" align="right">&nbsp;<span class="pager_bar">
                        <?=$pager->fromto().$pager->navbar(10)?>
                      </span></td>
                                </tr>
                                </tfoot>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
