<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                验收报告综合查询
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <?php include('search_part.php');?>
                </div>
                <div class="box">
                    <div class="no-padding no-margin panel panel-default">
                        <form id="validateForm" name="validateForm" method="post" action="">
                            <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover">
                                <thead>
                                <tr>
                                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th width="50">详</th>
                                    <th><?=getColumnStr('项目名称','subject')?></th>
                                    <th width="150"><?=getColumnStr('立项年度','declare_year')?></th>
                                    <th><?=getColumnStr('承担单位','corporation_id')?></th>
                                    <th><?=getColumnStr('主管部门','department_id')?></th>
                                    <th width="170">状态</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($complete = $pager->getObject()):
                                    $project = $complete->getProject(true);
                                    ?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$complete->getProjectId()?>" /></td>
                                        <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                        <td>
                                            <?=$complete->getMark()?><?=link_to("apply/complete/show/id/".$complete->getProjectId(),$complete->getSubject(),array('target'=>"_blank"))?>
                                            <p class="snote">平台级别：<?=$project->getLevel()?><br>立项年度：<?=$project->getDeclareYear()."(".$project->getTypeCurrentGroup().")"?><br>评估年度：<?=$complete->getCompleteYear()."(".$complete->getTypeCurrentGroup().") - [".$complete->getStartYear()."-".$complete->getEndYear()."]"?><br>平台类别：<?=$project->getCatSubject()?></p>
                                        </td>
                                        <td><?=$complete->getDeclareYear()?></td>
                                        <td><?=$complete->getCorporationName()?></td>
                                        <td><?=$complete->getDepartmentName()?></td>
                                        <td><?=$complete->getState()?></td>
                                    </tr>
                                    <tr class="fold_body">
                                        <td colspan="12">
                                            <?php include('more_part.php') ?>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="12" align="right"><span class="pager_bar">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></span></td>
                                </tr>
                                </tfoot>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

