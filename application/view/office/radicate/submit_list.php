<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                已立项的项目
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                <?=btn('link','导出数据',site_url('export/export/index'),'export')?>
                <!--
                <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-danger doit" role="button">选中项不立项</a>
                -->
            </div>
        </div>
        <div class="block-content">
            <div class="main">
            <div class="search">
                <?php include_once('search_part.php') ?>
            </div>
              <div class="box">
               <div class="no-padding no-margin panel panel-default" >
                <form id="validateForm" name="validateForm" method="post" action="<?=site_url("office/radicate/doPartitionByProject")?>">
                  <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                      <thead>
                    <tr>
                      <th width="50">详</th>
                      <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
                      <th><?=getColumnStr('负责人','user_name')?></th>
                      <th><?=getColumnStr('申报单位','corporation_id')?></th>
                      <th><?=getColumnStr('主管部门','department_id')?></th>
                      <th><?=getColumnStr('立项年度','radicate_year')?></th>
                      <th class="text-center" width="130">操作</th>
                    </tr>
                      </thead>
                      <tbody>
                    <?php while($project = $pager->getObject()):?>
                      <tr>
                        <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                        <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?>
                            <p class="snote">立项编号：<?=$project->getRadicateId()?><br>平台级别：<?=$project->getLevel()?><br>申报年度：<?=$project->getDeclareYear()."(".$project->getTypeCurrentGroup().")"?><br>平台类别：<?=$project->getCatSubject()?></p>
                        </td>
                        <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                        <td><?=$project->getCorporationName()?></td>
                        <td><?=$project->getDepartmentName()?></td>
                        <td><?=$project->getRadicateYear()?></td>
                        <td align="center">
                            <?=Button::setUrl(site_url("office/radicate/backSupport/id/".$project->getProjectId()))->setEvent("return confirm('您确定将此项目返回待立项吗？');")->link('返回待立项')?>
                        </td>
                      </tr>
                        <tr class="fold_body">
                        <td colspan="16">
                          <?php include('more_part.php') ?>
                        </td>
                      </tr>
                    <?php endwhile;?>
                      </tbody>
                      <tfoot>
                    <tr>
                      <td colspan="16" align="right">
            <!--           <select name="office_id">
                        <option value="">—分管处室—</option>
                        <?=getOfficeList(input::getInput("mix.office_id"))?>
                      </select>
                      <input type="submit" name="button" id="button" value=" 更改处室 "  onclick="return confirm('您确定更改处室吗？')"/><span class="pager_bar"> -->
                    </span>&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span>
                  </td>
                </tr>
                      </tfoot>
              </table>
            </form>
            </div>
            </div>
            </div>
        </div>
    </div>
</div>