<link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
<script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>
<style>
    table.snote td{
        padding: 0.25rem;
        border:none;
        border-bottom: 1px solid #e2e8f2;
    }
</style>
<script language="javascript" type="text/javascript">
    function unit(json){
        $('#corporation_id').val(json.id);
        $('#corporation_name').val(json.subject);
        closeWindow();
    }
    function todo(json)
    {
        $.post('<?=site_url("admin/super/changeUserName")?>',{id:json.itemid,username:json.username,userid:json.userid},function(m){alert(m);location.reload(true)});
    }

</script>
<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                项目综合搜索
            </h3>
            <div class="block-options">
                <?=btn('link','导出数据',site_url('export/export/index'),'export')?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
              <div class="search">
                  <form action="" method="post" name="search" id="search">
                <table width="100%" align="center">
                  <tbody id="show_search">
                      <tr>
                        <td>关键词：
                          <input id="search" name="search" value="<?=input::getInput("post.search")?>" class="form-control w-auto custom-control-inline"  />
                          <label><input name="field" type="radio" value="subject" checked="checked" />项目名称&nbsp;</label>
                          <label><input type="radio" name="field" value="radicate_id" />项目编号&nbsp;</label>
                          <label><input type="radio" name="field" value="corporation_name" />申报单位&nbsp;</label>
                          <label><input type="radio" name="field" value="user_name" />项目负责人&nbsp;</label>
                          <label><input type="radio" name="field" value="declare_year" />申报年度&nbsp;</label>
                          <?=btn('button','搜索','submit','find')?>
                        </td>
                      </tr>
                      <tr>
                        <td>
                          <select name="level" id="level" class="form-control w-auto custom-control-inline">
                            <option value="">=项目级别=</option>
                              <?=getSelectFromArray(['国家级','省级','市州级','县级'],input::getInput("mix.level"))?>
                          </select>
                          <select name="declare_year" id="declare_year" class="form-control w-auto custom-control-inline">
                            <option value="">=申报年度=</option>
                            <?=getYearList(input::getInput("mix.declare_year"))?>
                          </select>
                            <select name="type_current_group" id="type_current_group" class="form-control w-auto custom-control-inline">
                                <option value="">=项目批次=</option>
                                <?=getSelectFromArray(array('1'=>'第一批','2'=>'第二批'),input::getInput("mix.type_current_group"),false)?>
                            </select>
                            <!--
                            <select name="district" id="district" class="form-control w-auto custom-control-inline">
                                <option value="">=所属片区=</option>
                                <?=getSelectFromArray(getDistrictList(),input::getInput("mix.district"))?>
                            </select>
                            -->
                          <select name="cat_id" id="cat_id" class="form-control w-auto custom-control-inline">
                            <option value="">=平台类别=</option>
                            <?=getSelectFromArray(get_select_data('cat'),input::getMix('cat_id'),false)?>
                          </select>
                          <select name="department_id" id="department_id" class="form-control w-auto custom-control-inline">
                            <option value="">=主管部门=</option>
                            <?=getDepartmentList(input::getMix('department_id'))?>
                          </select>
                          <select name="statement" id="statement"  class="form-control w-auto custom-control-inline">
                                <option value="">=项目状态=</option>
                                <?=projectStateSelect(input::getInput("mix.statement"))?>
                          </select>
                          <select name="is_test" id="is_test"  class="form-control w-auto custom-control-inline">
                                <option value="">=测试项目=</option>
                              <?=getSelectFromArray([1=>'不含',2=>'包含'],input::getInput("mix.is_test"),false)?>
                          </select>
                        </td>
                      </tr>
                      <tr>
                          <td>
                              <label style="float:left;margin-right: 15px;margin-top: 5px;">申报指南:</label>
                              <div class="cxselect" data-selects="guide_id1,guide_id2,guide_id3"
                                   data-url="<?= site_path('json/guides.json') ?>" data-json-value="v" style="float:left">
                                  <select class="form-control w-auto custom-control-inline guide_id1" data-value="<?=input::getMix('guide_id1')?>"
                                          name="guide_id1"></select>
                                  <select class="form-control w-auto custom-control-inline guide_id guide_id2" name="guide_id2"
                                          data-first-title="请选择" data-value="<?=input::getMix('guide_id2')?>"></select>
                                  <select class="form-control w-auto custom-control-inline guide_id guide_id3" name="guide_id3"
                                          data-first-title="请选择" data-value="<?=input::getMix('guide_id3')?>"></select>
                                  <input type="hidden" name="guide_id" id="guide_id" value="<?=input::getInput("mix.guide_id")?>">
                              </div>
                          </td>
                      </tr>
                  </tbody>
                </table>
                  </form>
              </div>
              <div class="box">
                <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                  <thead>
                    <tr>
                        <th style="width: 40px">详</th>
                        <th width="15%"><?=getColumnStr('项目名称','subject')?></th>
                        <th width="15%"><?=getColumnStr('负责人','user_name')?></th>
                        <th><?=getColumnStr('申报单位','corporation_id')?></th>
                        <th width="15%"><?=getColumnStr('主管部门','department_id')?></th>
                        <th class="text-center"><?=getColumnStr('项目状态','statement')?></th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php while($project = $pager->getObject()):?>
                      <tr>
                          <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                        <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?>
                            <p class="snote">平台级别：<?=$project->getLevel()?><br>申报年度：<?=$project->getDeclareYear()."(".$project->getTypeCurrentGroup().")"?><br>平台类别：<?=$project->getCatSubject()?></p>
                        </td>
                        <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                        <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?>
                        </td>
                        <td><?=$project->getDepartmentName()?></td>
                        <td align="center"><?=$project->getState()?></td>
                      </tr>
                      <tr class="fold_body">
                        <td colspan="14">
                         <?php include('more_part.php') ?>
                       </td>
                     </tr>
                   <?php endwhile;?>
                 </tbody>
                    <tfoot>
                 <tr>
                  <td colspan="14" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(6).$pager->pagejump()?></span></td>
                </tr>
                    </tfoot>
              </table>

            </div>
            </div>
        </div>
    </div>
</div>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
    $(function (){
        $(".guide_id").change(function () {
            $("#guide_id").val($(this).val());
        })
    });
</script>
