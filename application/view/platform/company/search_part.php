<link rel="stylesheet" href="<?=site_path('assets/bootstrap-select/bootstrap-select.min.css')?>" />
<script src="<?=site_path('assets/bootstrap-select/bootstrap-select.min.js')?>"></script>
<style>
    .bootstrap-select.dropdown{
        margin-bottom: 0.4rem;
    }
    .bootstrap-select>.dropdown-toggle.bs-placeholder{
        color: #495057;
        border: 1px solid #ced4da;
        background-color: #fff;
        font-weight: normal;
    }
    .bootstrap-select>.dropdown-toggle.bs-placeholder:hover{
        color: #495057;
    }
    .btn-light {
        background-color: #fff;
        border: 1px solid #ced4da;
    }
</style>
<div class="search">
    <form action="" method="post" name="search" id="search">
        <table width="100%" align="center">
            <tbody id="show_search">
            <tr>
                <td>关键词：
                    <input id="search" name="search" value="<?=input::getInput("post.search")?>" class="form-control w-auto custom-control-inline"  />
                    <label><input name="field" type="radio" value="subject" checked="checked" />平台名称&nbsp;</label>
                    <label><input type="radio" name="field" value="corporation_name" />第一依托单位&nbsp;</label>
                    <label><input type="radio" name="field" value="principal_name" />负责人&nbsp;</label>
                    <label><input type="radio" name="field" value="linkman_name" />联系人&nbsp;</label>
                    <label><input type="radio" name="field" value="user_name" />登录账号&nbsp;</label>
                    <?=btn('button','搜索','submit','find')?>
                </td>
            </tr>
            <tr>
                <td>
                    <select name="level" id="level" class="form-control w-auto custom-control-inline">
                        <option value="">=平台级别=</option>
                        <?=getSelectFromArray(['国家级','省级'],input::getInput("mix.level"))?>
                    </select>
                    <select name="type_current_group" id="type_current_group" class="form-control w-auto custom-control-inline">
                        <option value="">=项目批次=</option>
                        <?=getSelectFromArray(array('1'=>'第一批','2'=>'第二批'),input::getInput("mix.type_current_group"),false)?>
                    </select>
                    <!--
                            <select name="district" id="district" class="form-control w-auto custom-control-inline">
                                <option value="">=所属片区=</option>
                                <?=getSelectFromArray(getDistrictList(),input::getInput("mix.district"))?>
                            </select>
                            -->
                    <select name="cat_id" id="cat_id" class="form-control w-auto custom-control-inline">
                        <option value="">=平台类别=</option>
                        <?=getSelectFromArray(get_select_data('cat'),input::getMix('cat_id'),false)?>
                    </select>
                    <select name="department_id" id="department_id_sel" class="form-control w-auto custom-control-inline" data-live-search="true">
                        <option value="">=主管部门=</option>
                        <?=getDepartmentList(input::getMix('department_id'))?>
                    </select>
                    <select name="statement" id="statement"  class="form-control w-auto custom-control-inline">
                        <option value="">=状态=</option>
                        <?=projectStateSelect(input::getInput("mix.statement"))?>
                    </select>
                    <select name="is_test" id="is_test"  class="form-control w-auto custom-control-inline">
                        <option value="">=测试项目=</option>
                        <?=getSelectFromArray([1=>'不含',2=>'包含'],input::getInput("mix.is_test"),false)?>
                    </select>
                </td>
            </tr>
            </tbody>
        </table>
    </form>
</div>

<script>
    $(document).ready(function () {
        $('#department_id_sel').selectpicker({
            size:10
        });
    });

    $("#department_id_sel").change(function () {
        $("#department_id").val($(this).val());
    });
</script>