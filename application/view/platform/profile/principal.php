<script language="javascript" type="text/javascript">
    $(function(){
        //选择职称级别
        $(document).on("change",".title_rank",function(){
            var id= $(this).val();
            var me= $(this);
            if(id==0){
                me.parent().find('.title').html('<option value="0">==请选择==</option>');
                return;
            }
            var url = baseurl+'common/HonorAjax';
            ajaxData(url,{id:id},'html',function(){},function(data){
                me.parent().find('.title').html(data);
            });
        });

    });

</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            平台基本资料
        </h2>
        <div>
            <?=$htmlStr?>
            <?php
            if(input::session('userlevel')!=5):
                ?>
                <?=btn('返回','javascript:history.go(-1);','fa fa-chevron-left','info')?>
            <?php endif;?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):$method = $method=='index'?'base':$method;?>
                        <li class="nav-item" class="tab-pane">
                            <a class="<?php if($tab['method'] == $method)echo 'nav-link active';else echo 'nav-link'; ?>" href="<?=$tab['url']?>"><?=$tab['text']?></a>
                        </li>
                        <?php $i++;endforeach;?>
                </ul>
                <div class="block-content tab-content block-content-full">
                    <div class="tab-content">
                        <div role="tabpanel" class="tab-pane p-3 active">
                            <form class="form-horizontal" id="validateForm" action="" method="post">
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">姓名</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="leader[user_name]" class="form-control col-sm-5" value="<?=$platform->getPrincipalName()?>" required readonly />
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">证件类型</label>
                                    <div class="col-sm-10">
                                        <select name="leader[idcard_type]" id="idcard_type" class="form-control col-sm-5">
                                            <?=getSelectFromArray(['身份证','护照号','其他'],$leader->getIdcardType())?>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">证件号码</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="leader[idcard]" class="form-control col-sm-5" value="<?=$leader->getIdcard()?>" required />
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">性别</label>
                                    <div class="col-sm-10">
                                        <select name="leader[user_sex]" id="user_sex" class="form-control col-sm-5" required>
                                            <?=getSelectFromArray(['男','女'],$leader->getUserSex())?>
                                        </select>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label for="code" class="col-sm-2 col-form-label">出生年月</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="leader[user_birthday]" class="form-control col-sm-5" value="<?=$leader->getUserBirthday()?>" data-com="date" data-icon="none" />
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">职务</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="leader[user_duty]" class="form-control col-sm-5" value="<?=$leader->getUserDuty()?>" required />
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">职称</label>
                                    <div class="col-sm-10">
                                        <select name="leader[title_rank]" class="title_rank form-control w-auto custom-control-inline" required>
                                            <?php $data=['0'=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'其他']?>
                                            <?=getSelectFromArray($data,$leader->getTitleRank(),false)?>
                                        </select>
                                        <select name="leader[user_title]" class="title form-control w-auto custom-control-inline" required>
                                            <?=getOptionByParent($leader->getTitleRank(),$leader->getUserTitle())?>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">学历</label>
                                    <div class="col-sm-10">
                                        <select name="leader[education]" id="education" class="form-control col-sm-5">
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(get_select_data('education'),$leader->getEducation())?>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">学位</label>
                                    <div class="col-sm-10">
                                        <select name="leader[degree]" id="degree" class="form-control col-sm-5">
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(['学士','硕士','博士','其他'],$leader->getDegree())?>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="area_code" class="col-sm-2 col-form-label">荣誉称号</label>
                                    <div class="col-sm-10">
                                        <input class="form-control col-sm-5" type="text" name="leader[honor]" value="<?=$leader->getHonor()?>">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">专业</label>
                                    <div class="col-sm-10">
                                        <input class="form-control col-sm-5" type="text" name="leader[major]" value="<?=$leader->getMajor()?>">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">工作单位</label>
                                    <div class="col-sm-10">
                                        <input class="form-control col-sm-5" type="text" name="leader[corporation_name]" value="<?=$leader->getCorporationName()?>">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">研究领域</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="leader[research_area]" class="form-control col-sm-5" value="<?=$leader->getResearchArea()?>"  />
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">备注</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="leader[note]" class="form-control col-sm-5" value="<?=$leader->getNote()?>"  />
                                        <small class="help-block">备注中请注明该人员所获得的优秀称号等信息</small>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <div class="col-xs-12 col-sm-10 col-md-9">
                                        <button type="submit" class="btn btn-info btn-sm">保存资料</button>
                                    </div>
                                </div>
                                <p style="clear:both"></p>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>