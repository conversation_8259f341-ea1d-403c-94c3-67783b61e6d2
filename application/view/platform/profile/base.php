<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            平台基本资料
        </h2>
        <div>
            <?=$htmlStr?>
            <?php
            if(input::session('userlevel')!=5):
                ?>
                <?=btn('返回','javascript:history.go(-1);','fa fa-chevron-left','info')?>
            <?php endif;?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):$method = $method=='index'?'base':$method;?>
                        <li class="nav-item" class="tab-pane">
                            <a class="<?php if($tab['method'] == $method)echo 'nav-link active';else echo 'nav-link'; ?>" href="<?=$tab['url']?>"><?=$tab['text']?></a>
                        </li>
                        <?php $i++;endforeach;?>
                </ul>
                <div class="block-content tab-content block-content-full">
                    <div class="tab-content">
                        <div role="tabpanel" class="tab-pane p-3 active">
                            <form class="form-horizontal" id="validateForm" action="" method="post">
                                <?php
                                $disabled = '';
                                if($platform->getStatement()==10) $disabled = 'disabled';
                                ?>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">平台类型</label>
                                    <div class="col-sm-10">
                                        <select name="cat_id" id="cat_id" class="form-control" <?=$disabled?>>
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(get_select_data('cat'),$platform->getCatId(),false)?>
                                        </select>
                                    </div>
                                </div>
                                <?php
                                if($platform->getCatId()):
                                ?>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">平台名称</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="subject" id="subject" class="form-control"
                                               value="<?= $platform->getSubject() ?>" <?=$disabled?>/>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">第一依托单位</label>
                                    <div class="col-sm-10">
                                        <?php 
                                        $disabled = '';
                                        if($platform->getCorporationName()) $disabled = 'disabled';?>
                                        <input type="text" name="corporation_name" id="corporation_name" class="form-control"
                                               value="<?= $platform->getCorporationName() ?>" <?=$disabled?>/>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">第一依托单位性质</label>
                                    <div class="col-sm-10">
                                        <select class="form-control" name="corporation_property" id="corporation_property" >
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(get_select_data('property'),$platform->getCorporationProperty())?>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">其他依托单位</label>
                                    <div class="col-sm-10">
                                        <?php if($platform->getCorporationName()):?>
                                            <input type="text" class="form-control"
                                                   value="<?= $platform->getOtherCooperatationNames() ?>" disabled/>
                                        <?php else:?>
                                            <table class="table table-bordered">
                                                <tbody>
                                                <tr>
                                                    <td align="center">
                                                        单位名称
                                                    </td>
                                                    <td align="center">
                                                        单位性质
                                                    </td>
                                                    <td width="100" align="center">
                                                        操作
                                                    </td>
                                                </tr>
                                                <?php
                                                $others = $platform->getCooperatationArray();
                                                if(empty($others)){
                                                    $others[0] = [];
                                                }
                                                $i=0;
                                                foreach($others as $k=>$other):
                                                    $i++;
                                                    ?>
                                                    <tr data-row="<?=$k?>">
                                                        <td>
                                                            <input class="form-control text-left" type="text" name="cooperatation_name[]" value="<?=$other['subject']?>">
                                                        </td>
                                                        <td>
                                                            <select class="form-control" name="cooperatation_property[]" >
                                                                <option value="">请选择</option>
                                                                <?=getSelectFromArray(get_select_data('property'),$other['property'])?>
                                                            </select>
                                                        </td>
                                                        <td width="100" style="text-align: center">
                                                            <?php
                                                            if($i==count($others)):
                                                                ?>
                                                                <button type="button" class="btn btn-primary add-row"><span class="fa fa-plus" aria-hidden="true"></span></button>
                                                            <?php
                                                            else:
                                                                ?>
                                                                <button type="button" class="btn btn-danger remove-row"><span class="fa fa-minus" aria-hidden="true"></span></button>
                                                            <?php
                                                            endif;
                                                            ?>
                                                        </td>
                                                    </tr>
                                                <?php
                                                endforeach;
                                                ?>
                                                </tbody>
                                            </table>
                                        <?php endif;?>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label for="code" class="col-sm-2 col-form-label">产业领域</label>
                                    <div class="col-sm-10">
                                        <select name="industry" id="industry" class="form-control">
                                            <option value="">请选择</option>
                                            <?= getSelectFromArray(get_select_data('industry'), $platform->getIndustry()) ?>
                                        </select>
                                        <input type="text" name="industry_other" id="industry_other" class="form-control" placeholder="其他请注明" style="<?=$platform->getIndustry()=='其他'?'':'display: none'?>" value="<?=$platform->getIndustryOther()?>">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">负责人姓名</label>
                                    <div class="col-sm-10">
                                        <?php
                                        $disabled = '';
                                        if($platform->getPrincipalName()) $disabled = 'disabled';?>
                                        <input type="text" name="principal_name" id="principal_name" class="form-control"
                                               value="<?= $platform->getPrincipalName() ?>" <?=$disabled?>/>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">负责人电话</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="principal_mobile" id="principal_mobile" class="form-control" value="<?=$platform->getPrincipalMobile()?>"/>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">联系人姓名</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="linkman_name" id="linkman_name" class="form-control" value="<?=$platform->getLinkmanName()?>"/>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">联系人电话</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="linkman_mobile" id="linkman_mobile" class="form-control" value="<?=$platform->getLinkmanMobile()?>"/>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="area_code" class="col-sm-2 col-form-label">所属地区</label>
                                    <div class="col-sm-9">
                                        <div class="cxselect" data-selects="area_code1,area_code2,area_code3" data-url="<?= site_path('json/region.json') ?>?v=1" data-json-value="v" style="float: left">
                                            <select class="form-control w-auto custom-control-inline area_code area_code1" name="area_code1" data-value="<?=$platform->getAreaCode()?(substr($platform->getAreaCode(),0,2).'0000'):510000?>" required></select>
                                            <select class="form-control w-auto custom-control-inline area_code area_code2" data-first-title="请选择" data-value="<?=$platform->getAreaCode()?(substr($platform->getAreaCode(),0,4).'00'):''?>" required></select>
                                            <select class="form-control w-auto custom-control-inline area_code area_code3" data-first-title="请选择" data-value="<?=$platform->getAreaCode()?:''?>" required></select>
                                        </div>
                                        <input type="hidden" name="area_code" value="<?=$platform->getAreaCode()?>">
                                        <div style="clear: both"></div>
                                        <p class="help-block">请选择到最后一级。</p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">通讯地址</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="address" id="address" class="form-control" value="<?=$platform->getAddress()?>"/>
                                    </div>
                                </div>
                                <?php
                                    if(in_array($platform->getCatId(),[271,327])):
                                    //重点实验室
                                ?>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">研究方向</label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <table class="table table-bordered">
                                            <tbody>
                                            <tr>
                                                <td>
                                                    <p align="left">名称</p>
                                                </td>
                                                <td width="100">
                                                    <p align="center">操作</p>
                                                </td>
                                            </tr>
                                            <?php
                                            $directions = $platform->getResearchDirectionArray();
                                            if(empty($directions)){
                                                $directions[0] = '';
                                            }
                                            $i=0;
                                            foreach($directions as $k=>$value):
                                                $i++;
                                                ?>
                                                <tr data-row="<?=$k?>">
                                                    <td>
                                                        <input class="form-control text-left" type="text" name="research_direction[]" value="<?=$value?>">
                                                    </td>
                                                    <td width="100" style="text-align: center">
                                                        <?php
                                                        if($i==count($directions)):
                                                            ?>
                                                            <button type="button" class="btn btn-primary add-row"><span class="fa fa-plus" aria-hidden="true"></span></button>
                                                        <?php
                                                        else:
                                                            ?>
                                                            <button type="button" class="btn btn-danger remove-row"><span class="fa fa-minus" aria-hidden="true"></span></button>
                                                        <?php
                                                        endif;
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php
                                            endforeach;
                                            ?>
                                            </tbody>
                                        </table>
                                        <?php
                                        if($platform->getCatId()==271):
                                            ?>
                                            <small class="help-block">研究方向最多可以填5个</small>
                                        <?php
                                        endif;
                                        ?>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <?php
                                    endif;
                                ?>

                                <div class="form-group row">
                                    <div class="col-xs-12 col-sm-10 col-md-9">
                                        <button type="submit" class="btn btn-info btn-sm">保存资料</button>
                                    </div>
                                </div>
                                <?php endif;?>
                                <p style="clear:both"></p>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $(document).on('click','.add-row',function(){
        //克隆行
        var rows = $(this).parents('#activeForm').find('other_corporations');
        var tr = $(this).parents('tr').clone();
        var line = rows.length;
        var row = tr.data('row');
        //替换行id
        var reg = '/\\['+row+'\\]/ig';
        var new_tr = tr.html().replace(eval(reg), '['+line+']');
        new_tr = "<tr data-row='"+line+"'>"+new_tr+"</tr>";
        //追加至表格
        $(this).parents('tbody').append(new_tr);
        //按钮变为减号
        $(this).removeClass('add-row').removeClass('btn-primary').addClass('remove-row').addClass('btn-danger');
        $(this).find('span').removeClass('fa-plus').addClass('fa-minus');
    });

    $(document).on('click','.remove-row',function(){
        $(this).parents('tr').remove();
    });

    $(document).ready(function () {
        $('#validateForm').bootstrapValidator({
            message: '填写内容无效',
            feedbackIcons: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {
                'industry': {
                    validators: {
                        notEmpty: {
                            message: '产业领域必须选择'
                        }
                    }
                },
                'corporation_name': {
                    validators: {
                        notEmpty: {
                            message: '第一依托单位名称必须填写'
                        },
                    }
                },
                'corporation_property': {
                    validators: {
                        notEmpty: {
                            message: '第一依托单位性质必须选择'
                        },
                    }
                },
                'principal_name': {
                    validators: {
                        notEmpty: {
                            message: '负责人姓名必须填写'
                        },
                    }
                },
                'principal_mobile': {
                    validators: {
                        notEmpty: {
                            message: '负责人电话必须填写'
                        },
                    }
                },
                'linkman_name': {
                    validators: {
                        notEmpty: {
                            message: '联系人姓名必须填写'
                        },
                    }
                },
                'linkman_mobile': {
                    validators: {
                        notEmpty: {
                            message: '联系人电话必须填写'
                        },
                    }
                },
                'address': {
                    validators: {
                        notEmpty: {
                            message: '通讯地址必须填写'
                        },
                    }
                }
            }
        });

        $("#industry").change(function (){
            if($(this).val()=='其他'){
                $("#industry_other").show();
                $("#industry_other").focus();
            }else{
                $("#industry_other").hide();
            }
        });

    });

    $('.cxselect').cxSelect();
    $(function (){
        $(".area_code").change(function (){
            var code = $(this).val();
            $("input[name='area_code']").val(code);
        });
        $("#cat_id").change(function (){
            var cat_id = $(this).val();
            location.href='<?=site_url('platform/profile/base')?>?cat_id='+cat_id;
        });
    });
</script>