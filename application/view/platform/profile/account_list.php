<div class="main">
  <div class="search">
    <form id="form1" name="form1" method="post" action="">
      关键词
      <input name="search" type="text" id="search" size="15" class="form-control w-auto custom-control-inline" />
      <label>
        <input name="field" type="radio" id="radio" value="user_username" checked="checked" />
        姓名</label>
       <label>
        <input name="field" type="radio" id="radio" value="user_name" />
        账号</label>
      <label>
        <input name="field" type="radio" id="radio" value="user_idcard" />
        身份证号码</label>
      <label>
        <input type="radio" name="field" id="radio2" value="user_mobile" />
        手机</label>
      <label>
        <input type="radio" name="field" id="radio2" value="user_email" />
        电子邮箱</label>
      &nbsp;
        <?=btn('button','搜索','submit','find')?>
      </label>
    </form>
  </div>
  <div class="box">
    <table class="table">
      <tr>
        <th class="text-center"><?=getColumnStr('姓名','user_username')?></th> 
        <th class="text-center"><?=getColumnStr('身份证号','user_idcard')?></th>
        <th class="text-center"><?=getColumnStr('手机','user_mobile')?></th>
        <th class="text-center"><?=getColumnStr('邮箱','user_email')?></th>
        <th class="text-center">操作</th>
      </tr>
      <?php while($user = $pager->getObject()):?>
      <tr>
        <td align="center"><?=$user->getUserUsername()?></td>
        <td align="center"><?=$user->getUserIdcard()?></td>
        <td align="center"><?=$user->getUserMobile()?></td>
        <td align="center"><?=$user->getUserEmail()?></td>
        <td align="center">
            <?=Button::setEvent("parent.todo({username:'".$user->getUserUsername()."',userid:'".$user->getUserId()."'});")->setIcon('check')->button('选取')?>
        </td>
      </tr>
      <?php endwhile; ?>
      <tr>
        <td colspan="5"><?=$pager->navbar(5)?></td>
      </tr>
    </table>
  </div>
</div>
