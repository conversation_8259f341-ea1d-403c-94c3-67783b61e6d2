<script type="text/javascript">
    function todo(json)
    {
        var id = '<?=$platform->getPlatformId()?>';
        $.post('<?=site_url("platform/profile/coupling")?>',{id:id,userid:json.userid,username:json.username},function(json){
            eval("json = '"+json+"'");
            if(json.msg) alert(json.msg);
            document.location.reload();
        });
    }
</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            平台基本资料
        </h2>
        <div>
            <?=btn2('查看记录','admin/history/index/type/platform/id/'.$platform->getPlatformId(),'fa fa-file-text-o','info','window','600','620')?>
            <?php
            if(input::session('userlevel')==3):
                ?>
                <?php
                if($platform->getIsLock()==10):
                    ?>
                    <?=btn2('修改资料','platform/profile/base/lock/1/userid/'.$platform->getPlatformId(),'fa fa-edit','warning')?>
                <?php endif;?>
            <?php endif;?>
            <?php
            if(in_array(input::session('userlevel'),[1,6])):
                ?>
                <!--
                <?=btn2('修改资料','platform/profile/base/userid/'.$platform->getPlatformId(),'fa fa-edit','warning')?>
                -->
                <?=btn2('返回','javascript:history.go(-1);','fa fa-chevron-left','info')?>
            <?php endif;?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block" data-toggle="tabs" role="tablist">
                    <?php $i=0;foreach($tabs as $tab):?>
                        <li class="nav-item">
                            <a class="<?php if($tab['method'] == $method || $i==0)echo 'nav-link active';else echo 'nav-link'; ?>" href="#<?=$tab['method']?>"><?=$tab['text']?></a>
                        </li>
                        <?php $i++;endforeach;?>
                    <li class="nav-item"><a href="#managers" class="<?php if($tab['method'] == $method)echo 'nav-link active';else echo 'nav-link'; ?>">平台管理员</a></li>
                </ul>
                <div class="block-content tab-content">
                    <div class="page-body">
                        <div class="tab-content form-horizontal">
                            <div role="tabpanel" class="tab-pane p-3 active" id="base">
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">平台类型</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?= $platform->getPlatformType() ?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">平台名称</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?= $platform->getSubject() ?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">第一依托单位</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?= $platform->getCorporationName() ?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">第一依托单位性质</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?= $platform->getCorporationProperty() ?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">其他依托单位</label>
                                    <div class="col-sm-10">
                                        <?php
                                        $others = $platform->getCooperatationArray();
                                        if(empty($others)):
                                            ?>
                                            无
                                        <?php else:?>
                                            <table class="table table-bordered">
                                                <tbody>
                                                <tr>
                                                    <td align="center">
                                                        单位名称
                                                    </td>
                                                    <td align="center">
                                                        单位性质
                                                    </td>
                                                </tr>
                                                <?php
                                                $others = $platform->getCooperatationArray();
                                                foreach($others as $k=>$other):
                                                    ?>
                                                    <tr>
                                                        <td>
                                                            <?=$other['subject']?>
                                                        </td>
                                                        <td>
                                                            <?=$other['property']?>
                                                        </td>
                                                    </tr>
                                                <?php
                                                endforeach;
                                                ?>
                                                </tbody>
                                            </table>
                                        <?php endif;?>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">产业领域<font color="red">*</font></label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?= $platform->getIndustry() ?><?= $platform->getIndustry()=='其他'?'：'.$platform->getIndustryOther():'' ?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="property" class="col-sm-2 col-form-label">负责人姓名<font color="red">*</font></label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$platform->getPrincipalName()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="property" class="col-sm-2 col-form-label">负责人电话<font color="red">*</font></label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$platform->getPrincipalMobile()?></p>
                                    </div>
                                </div>
                                <div class="form-group row" id="hightech_no_div">
                                    <label for="code" class="col-sm-2 col-form-label">联系人姓名<font color="red">*</font></label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$platform->getLinkmanName()?></p>
                                    </div>
                                </div>
                                <div class="form-group row" id="k_no_div">
                                    <label for="code" class="col-sm-2 col-form-label">联系人电话<font color="red">*</font></label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$platform->getLinkmanMobile()?></p>
                                    </div>
                                </div>
                                <div class="form-group row" id="d_no_div">
                                    <label for="code" class="col-sm-2 col-form-label">所在地区<font color="red">*</font></label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$platform->getArea()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="address" class="col-sm-2 col-form-label">通讯地址<font color="red">*</font></label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$platform->getAddress()?></p>
                                    </div>
                                </div>
                                <p style="clear:both"></p>
                                </form>
                            </div>
                            <?php
                            if(in_array($platform->getCatId(),[271])):
                            $leader = $platform->getLeader();
                            ?>
                            <div role="tabpanel" class="tab-pane p-3" id="principal">
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">姓名</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$platform->getPrincipalName()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">证件类型</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getIdcardType()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">证件号码</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getIdcard()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">性别</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getUserSex()?></p>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label for="code" class="col-sm-2 col-form-label">出生年月</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getUserBirthday()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">职务</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getUserDuty()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">职称</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getTitleRank()?><?=$leader->getUserTitle()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">学历</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getEducation()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">学位</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getDegree()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="area_code" class="col-sm-2 col-form-label">荣誉称号</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getHonor()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">专业</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getMajor()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">工作单位</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getCorporationName()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">研究领域</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getResearchArea()?></p>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label for="tel" class="col-sm-2 col-form-label">备注</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static"><?=$leader->getNote()?></p>
                                    </div>
                                </div>
                                <?php
                                if(in_array($platform->getCatId(),[271,327])):
                                    //重点实验室
                                    ?>
                                    <div class="form-group row">
                                        <label for="tel" class="col-sm-2 col-form-label">研究方向</label>
                                        <div class="col-sm-10">
                                            <p class="form-control-static"><?=$platform->getResearchDirectionStr()?></p>
                                        </div>
                                    </div>
                                <?php
                                endif;
                                ?>
                            </div>
                            <div role="tabpanel" class="tab-pane p-3" id="staff">
                                <?php
                                $allowCount = $platform->getCatId()==271 ? 20 : 30;
                                ?>
                                <?php
                                if($platform->getCatId()==271):
                                    ?>
                                    <div class="header no-margin-top">固定人员信息</div>
                                    <div align="center">
                                        <?php
                                        $directions = $platform->getResearchDirectionArray();
                                        $directionNo = 0;
                                        $startNo = 0;
                                        foreach ($directions as $directionkey=>$direction):
                                            $directionNo++;
                                            $staffs = $platform->getStaffByResearch($direction);
                                            ?>
                                            <table class="table table-bordered table-sm">
                                                <tbody>
                                                <tr>
                                                    <td colspan="15">
                                                        <p align="left"><strong>研究方向<?=$directionNo?> - <?=$direction?></strong></p>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td width="100">
                                                        <p align="center"></p>
                                                    </td>
                                                    <td>
                                                        <p align="center">姓名</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">证件号码</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">性别</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">出生年月</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">职务/职称</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">学历</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">学位</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">荣誉称号</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">专业</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">工作单位</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">研究领域</p>
                                                    </td>
                                                </tr>
                                                <?php
                                                $j=0;
                                                while($staff = $staffs->getObject()):
                                                    ?>
                                                    <tr>
                                                        <td class="text-center">
                                                            <?=$staff->getType()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getUserName()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getIdcard()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getUserSex()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getUserBirthday()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getUserDuty()?> / <?=$staff->getUserTitle()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getEducation()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getDegree()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getHonor()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getMajor()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getCorporationName()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getResearchArea()?>
                                                        </td>
                                                    </tr>
                                                    <?php
                                                    $j++;
                                                endwhile;
                                                ?>
                                                </tbody>
                                            </table>
                                            <br>
                                            <br>
                                        <?php
                                        endforeach;
                                        ?>
                                        <?php
                                        $staffs = $platform->getStaffByNoResearch();
                                        if($staffs!==false && $staffs->getTotal()>0):
                                            ?>
                                            <table class="table table-bordered">
                                                <tbody>
                                                <tr>
                                                    <td colspan="15">
                                                        <p align="left"><strong style="color:red">未对应研究方向的人员（请点击编辑选择对应的研究方向）：</strong></p>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td width="100">
                                                        <p align="center"></p>
                                                    </td>
                                                    <td>
                                                        <p align="center">姓名</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">证件号码</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">性别</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">出生年月</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">职务/职称</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">学历</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">学位</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">荣誉称号</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">专业</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">工作单位</p>
                                                    </td>
                                                    <td>
                                                        <p align="center">研究领域</p>
                                                    </td>
                                                </tr>
                                                <?php
                                                while($staff = $staffs->getObject()):
                                                    ?>
                                                    <tr>
                                                        <td class="text-center">
                                                            <?=$staff->getType()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getUserName()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getIdcard()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getUserSex()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getUserBirthday()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getUserDuty()?> / <?=$staff->getUserTitle()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getEducation()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getDegree()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getHonor()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getMajor()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getCorporationName()?>
                                                        </td>
                                                        <td align="center">
                                                            <?=$staff->getResearchArea()?>
                                                        </td>
                                                    </tr>
                                                    <?php
                                                    $j++;
                                                endwhile;
                                                ?>
                                                </tbody>
                                            </table>
                                            <br>
                                            <br>
                                        <?php endif;?>
                                    </div>
                                <?php
                                else:
                                    ?>
                                    <div class="header no-margin-top">固定人员信息（已录入<?=$platform->getStaffCount()?>人，还可以录入<?=($allowCount-$platform->getStaffCount())?>人）</div>
                                    <div align="center" style="overflow:scroll;">
                                        <?php
                                        $staffs = $platform->getStaffs()->toArray();
                                        ?>
                                        <table class="table table-bordered" style="width:3000px;text-align:center">
                                            <tbody>
                                            <tr>
                                                <td width="100">
                                                    <p align="center"></p>
                                                </td>
                                                <td width="150">
                                                    <p align="center">姓名</p>
                                                </td>
                                                <td width="150">
                                                    <p align="center">身份证</p>
                                                </td>
                                                <td width="100">
                                                    <p align="center">性别</p>
                                                </td>
                                                <td width="150">
                                                    <p align="center">出生年月</p>
                                                </td>
                                                <td width="150">
                                                    <p align="center">职务</p>
                                                </td>
                                                <td width="350">
                                                    <p align="center">职称</p>
                                                </td>
                                                <td width="100">
                                                    <p align="center">学历</p>
                                                </td>
                                                <td width="100">
                                                    <p align="center">学位</p>
                                                </td>
                                                <td width="300">
                                                    <p align="center">荣誉称号</p>
                                                </td>
                                                <td width="200">
                                                    <p align="center">专业</p>
                                                </td>
                                                <td width="200">
                                                    <p align="center">工作单位</p>
                                                </td>
                                                <td width="300">
                                                    <p align="center">研究领域</p>
                                                </td>
                                                <td width="300">
                                                    <p align="center">研究方向</p>
                                                </td>
                                                <td >
                                                    <p align="center">备注</p>
                                                </td>
                                            </tr>
                                            <?php
                                            $j=0;
                                            for($i=0;$i<30;$i++):
                                                ?>
                                                <tr>
                                                    <td class="text-center">
                                                        <?php
                                                        if(empty($staffs[$i]['type'])) $staffs[$i]['type']='成员';
                                                        ?>
                                                        <?=$staffs[$i]['type']?>?>
                                                    </td>
                                                    <td>
                                                        <?=$staffs[$i]['user_name']?>
                                                    </td>
                                                    <td>
                                                        <?=$staffs[$i]['idcard']?>
                                                    </td>
                                                    <td>
                                                        <?=$staff[$i]['user_sex']?>
                                                    </td>
                                                    <td>
                                                        <?=$staffs[$i]['user_birthday']?>
                                                    </td>
                                                    <td>
                                                       <?=$staffs[$i]['user_duty']?>
                                                    </td>
                                                    <td style="text-align: left">
                                                        <?=$staffs[$i]['user_title']?>
                                                    </td>
                                                    <td>
                                                       <?=$staffs[$i]['education']?>
                                                    </td>
                                                    <td>
                                                        <?=$staffs[$i]['degree']?>
                                                    </td>

                                                    <td>
                                                        <?=$staffs[$i]['honor']?>
                                                    </td>
                                                    <td>
                                                        <?=$staffs[$i]['major']?>
                                                    </td>
                                                    <td>
                                                        <?=$staffs[$i]['corporation_name']?>
                                                    </td>
                                                    <td>
                                                        <?=$staffs[$i]['research_area']?>
                                                    </td>
                                                    <td>
                                                       <?=$staffs[$i]['research_direction']?>
                                                    </td>
                                                    <td>
                                                        <?=$staffs[$i]['note']?>
                                                    </td>
                                                </tr>
                                                <?php
                                                $j++;
                                            endfor;
                                            ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php
                                endif;
                                ?>
                            </div>
                            <div role="tabpanel" class="tab-pane p-3" id="committee">
                                <table class="table table-bordered table-sm">
                                    <tbody>
                                    <tr>
                                        <th colspan="10" height="33" class="text-center">学术委员会</th>
                                    </tr>
                                    <?php
                                    $committees = $platform->getCommittees()->toArray();
                                    ?>
                                    <tr>
                                        <td height="33">　</td>
                                        <td>姓名</td>
                                        <td>证件号码</td>
                                        <td>性别</td>
                                        <td>出生年月</td>
                                        <td>职务/职称</td>
                                        <td>学历</td>
                                        <td>工作单位</td>
                                        <td>研究领域</td>
                                        <td width="200px">备注*</td>
                                    </tr>
                                    <?php
                                    $j=0;
                                    for($i=0;$i<20;$i++):
                                        ?>
                                        <tr>
                                            <td height="33"><?=$committees[$i]['type']?></td>
                                            <td><?=$committees[$i]['user_name']?></td>
                                            <td><?=$committees[$i]['idcard']?></td>
                                            <td><?=$committees[$i]['user_sex']?></td>
                                            <td><?=$committees[$i]['user_birthday']?></td>
                                            <td><?=$committees[$i]['user_duty']?>/<?=$committees[$i]['user_title']?></td>
                                            <td><?=$committees[$i]['education']?></td>
                                            <td><?=$committees[$i]['corporation_name']?></td>
                                            <td><?=$committees[$i]['research_area']?></td>
                                            <td><?=$committees[$i]['note']?></td>
                                        </tr>
                                    <?php
                                    endfor;
                                    ?>
                                    <tr>
                                        <td colspan="9" height="33">*备注中请注明该人员所获得的优秀称号等信息</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif;?>
                            <div role="tabpanel" class="tab-pane p-3" id="managers">
                                <p>平台管理人员</p>
                                <table width="100%" class="table table-hover">
                                    <thead>
                                    <tr>
                                        <td width="119" align="center">姓名</td>
                                        <td width="188" align="center">身份证</td>
                                        <td width="177" align="center">手机</td>
                                        <td width="193" align="center">电话</td>
                                        <td width="295" align="center">邮箱</td>
                                        <td width="200" align="center">操作</td>
                                    </tr>
                                    </thead>
                                    <tbody id="paper-content">
                                    <?php
                                    $users = $platform->selectUsers();
                                    while($user = $users->getObject()):
                                        ?>
                                        <tr>
                                            <td align="center"><?=$user->getUserUsername()?><?php if($platform->isManager($user->getUserId())):?><span class="badge badge-pill badge-primary">默认</span><?php endif;?></td>
                                            <td align="center"><?=getMask($user->getUserIdcard(),6,9)?></td>
                                            <td align="center"><?=$user->getUserMobile()?></td>
                                            <td align="center"><?=$user->getUserPhone()?></td>
                                            <td align="center"><?=$user->getUserEmail()?></td>
                                            <td align="center">
                                                <?=Button::setUrl(site_url("platform/profile/decoupling/id/".$platform->getPlatformId()."/userid/".$user->getUserId()))->setIcon('fa fa-user-slash')->setClass('btn-alt-danger')->window('解除绑定')?>
                                            </td>
                                        </tr>
                                    <?php endwhile;?>
                                    <?php if(in_array(input::session('userlevel'),[1,3,6]) || $platform->isManager(input::getInput("session.userid"))):?>
                                    <tfoot>
                                    <tr>
                                        <td colspan="6" class="text-center">
                                            <?=Button::setUrl(site_url("platform/profile/search_account/id/".$platform->getPlatformId()))->setIcon('fa fa-user-plus')->window('增加管理员')?>
                                        </td>
                                    </tr>
                                    </tfoot>
                                    <?php endif;?>
                                    </tbody>
                                </table>
                                <p style="clear:both"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
