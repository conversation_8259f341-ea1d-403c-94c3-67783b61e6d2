<form action="" method="post" name="search" id="search">
    <tbody id="show_search">
    <tr>
        <td><label>搜索关键词
                <input name="search" type="text" id="search" value="<?=$search?>"/>
            </label>
            <label>
                <input name="field" type="radio" id="radio" value="subject" checked="checked" />
                实验室名称</label>
            <label>
                <input name="field" type="radio" id="radio" value="corporation_name" <?=($field=='corporation_name')?'checked="checked"':''?> />
                依托单位</label>
            <label><select name="frontier">
                    <option value="">=前沿领域分类=</option>
                    <?=getSelectFromArray(['信息','农业','制造','医药','工程','能源','数理','化学','生物','环境','海洋','天文与空间','地球科学','材料','综合交叉'],input::getMix('frontier'))?>
                </select></label>
            <label><select name="industry">
                    <option value="">=所属产业=</option>
                    <?=getSelectFromArray(['农林业','水利','煤炭','电力','新能源','核能','石油、天然气','钢铁','有色金属','黄金','石化化工','建材','医药','机械','城市轨道交通装备','汽车','船舶','航空航天','轻工','纺织','建筑','城镇基础设施','铁路','公路及道路运输（含城市客运）','水运','航空运输','综合交通运输','信息产业','现代物流业','金融服务业','科技服务业','商务服务业','商贸服务业','旅游业','邮政业','教育','卫生健康','文化','体育','养老与托育服务','家政','其他服务业','环境保护与资源节约综合利用','公共安全与应急产品','民爆产品','人力资源和人力资本服务业','人工智能','烟草','消防','采矿','其他'],input::getMix('industry'))?>
                </select></label>
            <input type="submit" name="button" id="button" value="搜索" /></td>
    </tr>
    <tr>
        <td>
            <div style="clear: both"></div>
            <label>
                <div style="float: left">学科方向：</div>
                <div class="cxselect" data-selects="subject_id1,subject_id2,subject_id3" data-url="<?= site_path('json/subjects.json') ?>" data-json-value="v" style="float: left">
                    <select class="subject_id1" data-value="<?=$subject_ids['subject_id1']?>" name="subject_ids[subject_id1]"></select>
                    <select class="subject_id2" name="subject_ids[subject_id2]" data-first-title="请选择" data-value="<?=$subject_ids['subject_id2']?>"></select>
                    <select class="subject_id3" name="subject_ids[subject_id3]" data-first-title="请选择" data-value="<?=$subject_ids['subject_id3']?>"></select>
                </div>
            </label>
        </td>
    </tr>
    </tbody>
</form>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
</script>