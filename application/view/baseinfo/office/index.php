<div class="">
    <div class="btn-group btn-group-sm" role="group">
        <a href="<?=site_url('baseinfo/office/export')?>" class="btn btn-info"><i class="glyphicon glyphicon-export"></i> 导出所有的基本资料</a>
        <p style="clear:both;"></p>
    </div>
    <div class="search">
        <?php include_once('search_part.php') ?>
    </div>
    <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
        <div class="no-padding no-margin panel panel-default" >
            <div class="panel-heading">
                <i class="ace-icon fa fa-list"></i> 基本资料综合搜索
            </div>
            <table align="center" cellpadding="3" cellspacing="1" class="table">
                <thead>
                <tr>
                    <th class="hidden-480 text-center"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                    <th class="text-center">详</th>
                    <th class="text-center">实验室名称</th>
                    <th class="text-center">第一依托单位</th>
                    <th class="text-center">主管部门</th>
                    <th class="text-center">实验室主任</th>
                    <th class="text-center">联系人</th>
                    <th class="text-center">手机</th>
                    <th class="text-center">状态</th>
                </tr>
                </thead>
                <tbody>
                <?php while($pager = $pagers->getObject()):?>
                    <tr>
                        <td align="center" class="hidden-480"><input name="select_id[]" type="checkbox" value="<?=$pager->getId()?>" /></td>
                        <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$pager->getId()?>').toggle()">+</label></td>
                        <td align="left" class="hidden-480"><a target="_blank" href="<?=site_url('baseinfo/apply/show/id/'.$pager->getLabId())?>"><?=$pager->getSubject()?></a></td>
                        <td align="left" class="hidden-480"><?=$pager->getCorporationName()?></td>
                        <td align="left" class="hidden-480"><?=$pager->getDepartmentName()?></td>
                        <td align="center" class="hidden-480"><?=$pager->getLeaderName()?></td>
                        <td align="center" class="hidden-480"><?=$pager->getLinkmanName()?></td>
                        <td align="center" class="hidden-480"><?=$pager->getLinkmanMobile()?></td>
                        <td align="center" class="hidden-480"><?=$pager->getState()?></td>
                    </tr>
                    <tr id="show_<?=$pager->getId()?>" style="display:none;">
                        <td colspan="11">
                            <?php include('more_part.php')?>
                        </td>
                    </tr>
                <?php endwhile;?>
                </tbody>
                <tfooter>
                    <tr>
                        <td colspan="8" align="right">&nbsp;<?=$pagers->fromTo().$pagers->navbar(10)?></td>
                    </tr>
                </tfooter>
            </table>
        </div>
    </form>
</div>