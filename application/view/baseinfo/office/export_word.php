<table width="600" cellpadding="3" cellspacing="1" border="1" style="border:solid 1px #000;border-collapse:collapse;font-size:13px;">
    <tbody>
    <tr>
        <th colspan="9" height="33" class="text-center"><?=$base->getCategory()?>基础信息</th>
    </tr>
    <tr>
        <td height="33" width="90">重点实验室名称</td>
        <td colspan="5" width="410"><?=$base->getSubject()?></td>
        <td width="100">成立时间</td>
        <td colspan="2" width="100"><?=$base->getBuildAt()?></td>
    </tr>
    <tr>
        <td height="33">主管部门</td>
        <td colspan="8" id="unit"><?=$base->getDepartmentName()?>
        </td>
    </tr>
    <tr>
        <td height="33">类别</td>
        <td colspan="3"><?=getCheckedStr(['学科类','企业类'],$base->getType())?></td>
        <td colspan="2">是否厅市（州）共建</td>
        <td colspan="3"><?=getCheckedStr(['是','否'],$base->getBuildTogether())?></td>
    </tr>
    <tr>
        <td height="33">学科分类</td>
        <td colspan="8"><?=$base->getGbSubjectStr()?></td>
    </tr>
    <tr>
        <td height="33">前沿领域分类</td>
        <td colspan="3"><?=$base->getFrontier()?></td>
        <td colspan="2">所属产业</td>
        <td colspan="3"><?=$base->getIndustry()?></td>
    </tr>
    <tr>
        <td height="33">第一依托单位</td>
        <td colspan="8"><?=$base->getCorporationName()?></td>
    </tr>
    <?php
    $others = $base->getOtherCorporationArray();
    ?>
    <tr>
        <td rowspan="4" >其他依托单位</td>
        <td colspan="8"><?=$others[0]?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?=$others[1]?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?=$others[2]?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?=$others[3]?></td>
    </tr>
    <tr>
        <td height="33">第一依托单位地址</td>
        <td colspan="8"><?=$base->getAddress()?></td>
    </tr>
    <tr>
        <td rowspan="2" height="66">重点实验室<br />联系人信息</td>
        <td>姓名</td>
        <td colspan="3"><?=$base->getLinkmanName()?></td>
        <td>邮箱</td>
        <td colspan="3"><?=$base->getLinkmanEmail()?></td>
    </tr>
    <tr>
        <td height="33">固定电话</td>
        <td colspan="3"><?=$base->getLinkmanTel()?></td>
        <td>手机</td>
        <td colspan="3"><?=$base->getLinkmanMobile()?></td>
    </tr>
    <?php
    $directions = $base->getResearchDirectionArray();
    ?>
    <tr>
        <td rowspan="5" height="165">研究方向</td>
        <td colspan="8"><?=$directions[0]?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?=$directions[1]?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?=$directions[2]?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?=$directions[3]?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?=$directions[4]?></td>
    </tr>
    </tbody>
</table>
<table width="600" cellpadding="3" cellspacing="1" border="1" style="border:solid 1px #000;border-collapse:collapse;font-size:13px;">
    <tbody>
    <tr>
        <th colspan="9" height="33" class="text-center">重点实验室主任信息</th>
    </tr>
    <?php
    $leader = $base->getLeader();
    ?>
    <tr>
        <td height="33">姓名</td>
        <td>性别</td>
        <td colspan="2">出生年月</td>
        <td>职务/职称</td>
        <td style="width: 100px;">学历</td>
        <td colspan="2">研究领域</td>
        <td>备注*</td>
    </tr>
    <tr>
        <td height="33"><?=$leader->getUserName()?></td>
        <td><?=$leader->getUserSex()?></td>
        <td colspan="2"><?=$leader->getUserBirthday()?></td>
        <td><?=$leader->getUserDuty()?>/<?=$leader->getUserTitle()?></td>
        <td><?=$leader->getEducation()?></td>
        <td colspan="2"><?=$leader->getResearchArea()?></td>
        <td><?=$leader->getNote()?></td>
    </tr>
    <tr>
        <th colspan="9" height="33" class="text-center">固定人员信息</th>
    </tr>
    <?php
    $directions = $base->getResearchDirectionArray();
    $directionNo = 0;
    $startNo = 0;
    foreach ($directions as $direction):
        $directionNo++;
        $staffs = $base->getStaffByResearch($direction);
        ?>
        <tr>
            <th colspan="9" height="33" class="text-center">研究方向<?=$directionNo?> - <?=$direction?></th>
        </tr>
        <tr>
            <td height="33">　</td>
            <td>姓名</td>
            <td>性别</td>
            <td style="width: 80px">出生年月</td>
            <td>职务/职称</td>
            <td>学历</td>
            <td colspan="2">研究领域</td>
            <td>备注*</td>
        </tr>
        <?php
        $j=0;
        while($staff = $staffs->getObject()):
            ?>
            <tr>
                <td height="33"><?=$staff->getType()?></td>
                <td><?=$staff->getUserName()?></td>
                <td><?=$staff->getUserSex()?></td>
                <td><?=$staff->getUserBirthday()?></td>
                <td><?=$staff->getUserDuty()?> / <?=$staff->getUserTitle()?></td>
                <td><?=$staff->getEducation()?></td>
                <td colspan="2"><?=$staff->getResearchArea()?></td>
                <td><?=$staff->getNote()?></td>
            </tr>
            <?php
            $j++;
        endwhile;
        ?>
        <?php
    endforeach;
    ?>
    <tr>
        <th colspan="9" height="33" class="text-center">学术委员会</th>
    </tr>
    <?php
    $committees = $base->getCommittees()->toArray();
    ?>
    <tr>
        <td height="33">　</td>
        <td>姓名</td>
        <td>性别</td>
        <td>出生年月</td>
        <td>职务/职称</td>
        <td>学历</td>
        <td>工作单位</td>
        <td>研究领域</td>
        <td>备注*</td>
    </tr>
    <?php
    $j=0;
    for($i=0;$i<20;$i++):
        ?>
        <tr>
            <td height="33"><?=$committees[$i]['type']?></td>
            <td><?=$committees[$i]['user_name']?></td>
            <td><?=$committees[$i]['user_sex']?></td>
            <td><?=$committees[$i]['user_birthday']?></td>
            <td><?=$committees[$i]['user_duty']?>/<?=$committees[$i]['user_title']?></td>
            <td><?=$committees[$i]['education']?></td>
            <td><?=$committees[$i]['corporation_name']?></td>
            <td><?=$committees[$i]['research_area']?></td>
            <td><?=$committees[$i]['note']?></td>
        </tr>
        <?php
    endfor;
    ?>
    <tr>
        <td colspan="9" height="33">*备注中请注明该人员所获得的优秀称号等信息</td>
    </tr>
    </tbody>
</table>