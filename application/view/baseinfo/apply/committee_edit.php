<style>
    table caption{
        font-size: 16px;
        line-height: 26px;
        margin-bottom: 20px;
    }
</style>
<script language="javascript" type="text/javascript" src="<?=site_path("js/datepicker/WdatePicker.js")?>"></script>
<script language="javascript" type="text/javascript">
    $(function(){
        //选择职称级别
        $(document).on("change",".title_rank",function(){
            var id= $(this).val();
            var me= $(this);
            if(id==0){
                me.parent().find('.title').html('<option value="0">==请选择==</option>');
                return;
            }
            var url = baseurl+'common/HonorAjax';
            ajaxData(url,{id:id},'html',function(){},function(data){
                me.parent().find('.title').html(data);
            });
        });

    });

</script>

<div class="page-body">
    <div class="tab-content">
        <div class="tab-pane active">
            <div role="tabpanel" class="tab-pane active">
                <form class="form-horizontal" name="form1" id="activeForm" action="" method="post">
                    <input type="hidden" name="id" value="<?=$platform->getPlatformId()?>">
                    <input type="hidden" name="pid" value="<?=$committee->getId()?>">

                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">类型</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <select name="type" id="type" class="form-control">
                                <?=getSelectFromArray(['主任','副主任','成员'],$committee->getType())?>
                            </select>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">姓名</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <input type="text" name="user_name" class="form-control" value="<?=$committee->getUserName()?>" required />
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">证件类型</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <select name="idcard_type" id="idcard_type" class="form-control col-sm-5">
                                <?=getSelectFromArray(['身份证','护照号','其他'],$committee->getIdcardType())?>
                            </select>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">证件号码</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <input type="text" name="idcard" class="form-control" value="<?=$committee->getIdcard()?>" required />
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">性别</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <select name="user_sex" id="user_sex" class="form-control col-sm-5" required>
                                <?=getSelectFromArray(['男','女'],$committee->getUserSex())?>
                            </select>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">出生年月</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <input type="date" name="user_birthday" class="form-control" value="<?=$committee->getUserBirthday()?>"/>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">职务</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <input type="text" name="user_duty" class="form-control" value="<?=$committee->getUserDuty()?>" required />
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">职称</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <select name="title_rank" class="title_rank form-control w-auto custom-control-inline" required>
                                <?php $data=['0'=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'其他']?>
                                <?=getSelectFromArray($data,$committee->getTitleRank(),false)?>
                            </select>
                            <select name="user_title" class="title form-control w-auto custom-control-inline" required>
                                <?=getOptionByParent($committee->getTitleRank(),$committee->getUserTitle())?>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">学历</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <select name="education" id="education" class=" form-control w-auto custom-control-inline">
                                <option value="">请选择</option>
                                <?=getSelectFromArray(get_select_data('education'),$committee->getEducation())?>
                            </select>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">学位</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <select name="degree" id="degree" class=" form-control w-auto custom-control-inline">
                                <option value="">请选择</option>
                                <?=getSelectFromArray(['学士','硕士','博士','其他'],$committee->getDegree())?>
                            </select>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">荣誉称号</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <input class="form-control" type="text" name="honor" value="<?=$committee->getHonor()?>">
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">专业</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <input class="form-control" type="text" name="major" value="<?=$committee->getMajor()?>">
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">工作单位</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <input class="form-control" type="text" name="corporation_name" value="<?=$committee->getCorporationName()?>">
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">研究领域</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <input type="text" name="research_area" class="form-control" value="<?=$committee->getResearchArea()?>" required />
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">备注</label>
                        <div class="col-xs-12 col-sm-5 col-md-5">
                            <input type="text" name="note" class="form-control" value="<?=$committee->getNote()?>" required />
                            <small class="help-block">备注中请注明该人员所获得的优秀称号等信息</small>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="clearfix"></div>
                    <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                        <div style="width:200px;margin: 0 auto">
                            <a href="javascript:void(0);" onclick="return form1.submit();" class="btn btn-info btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存资料</a>
                        </div>
                    </div>
                </form>
                <p style="clear:both"></p>
            </div>
        </div>
    </div>
</div>
<div class="page-footer">

</div>
