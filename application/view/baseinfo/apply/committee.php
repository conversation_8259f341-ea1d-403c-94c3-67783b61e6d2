<script language="javascript" type="text/javascript">
    $(function(){
        //选择职称级别
        $(document).on("change",".title_rank",function(){
            var id= $(this).val();
            var me= $(this);
            if(id==0){
                me.parent().find('.title').html('<option value="0">==请选择==</option>');
                return;
            }
            var url = baseurl+'common/HonorAjax';
            ajaxData(url,{id:id},'html',function(){},function(data){
                me.parent().find('.title').html(data);
            });
        });

    });

</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            平台基本资料
        </h2>
        <div>
            <?=$htmlStr?>
            <?php
            if(input::session('userlevel')!=5):
                ?>
                <?=btn('返回','javascript:history.go(-1);','fa fa-chevron-left','info')?>
            <?php endif;?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):$method = $method=='index'?'base':$method;?>
                        <li class="nav-item" class="tab-pane">
                            <a class="<?php if($tab['method'] == $method)echo 'nav-link active';else echo 'nav-link'; ?>" href="<?=$tab['url']?>"><?=$tab['text']?></a>
                        </li>
                        <?php $i++;endforeach;?>
                </ul>
                <div class="block-content tab-content block-content-full">
                    <div class="tab-content">
                        <div role="tabpanel" class="tab-pane p-3 active">
                            <div class="header no-margin-top">学术委员会</div>
                            <?php
                            if($platform->getCatId()==271):
                                ?>
                                <div align="center" style="overflow:scroll;">
                                    <?php
                                    $committees = $platform->getCommittees();
                                    ?>
                                    <table class="table table-bordered table-sm">
                                        <tbody>
                                        <tr>
                                            <td width="100">
                                                <p align="center"></p>
                                            </td>
                                            <td width="150">
                                                <p align="center">姓名</p>
                                            </td>
                                            <td width="150">
                                                <p align="center">证件号码</p>
                                            </td>
                                            <td width="100">
                                                <p align="center">性别</p>
                                            </td>
                                            <td width="100">
                                                <p align="center">出生年月</p>
                                            </td>
                                            <td width="150">
                                                <p align="center">职务</p>
                                            </td>
                                            <td width="200">
                                                <p align="center">职称</p>
                                            </td>
                                            <td width="100">
                                                <p align="center">学历</p>
                                            </td>
                                            <td width="100">
                                                <p align="center">学位</p>
                                            </td>
                                            <td width="200">
                                                <p align="center">荣誉称号</p>
                                            </td>
                                            <td width="200">
                                                <p align="center">专业</p>
                                            </td>
                                            <td width="200">
                                                <p align="center">工作单位</p>
                                            </td>
                                            <td width="200">
                                                <p align="center">研究领域</p>
                                            </td>
                                            <td width="150px"  class="text-center">
                                                操作
                                            </td>
                                        </tr>
                                        <?php
                                        while($committee = $committees->getObject()):
                                            ?>
                                            <tr>
                                                <td class="text-center"><?=$committee->getType()?></td>
                                                <td><?=$committee->getUserName()?></td>
                                                <td><?=$committee->getIdcard()?></td>
                                                <td class="text-center"><?=$committee->getUserSex()?></td>
                                                <td><?=$committee->getUserBirthday()?></td>
                                                <td><?=$committee->getUserDuty()?></td>
                                                <td><?=$committee->getUserTitle()?></td>
                                                <td><?=$committee->getEducation()?></td>
                                                <td><?=$committee->getDegree()?></td>
                                                <td><?=$committee->getHonor()?></td>
                                                <td><?=$committee->getMajor()?></td>
                                                <td><?=$committee->getCorporationName()?></td>
                                                <td><?=$committee->getResearchArea()?></td>
                                                <td class="text-center">
                                                    <a href="javascript:void(0);" onclick="return showWindow('编辑','<?=site_url('platform/profile/committee_edit/id/'.$committee->getPlatformId().'/pid/'.$committee->getId())?>',600,460)" class="btn btn-primary btn-sm" role="button">编辑</a>
                                                    <a href="<?=site_url('platform/profile/committee_delete/id/'.$committee->getPlatformId().'/pid/'.$committee->getId())?>" onclick="return confirm('确定要删除吗？')" class="btn btn-danger btn-sm">删除</a>
                                                </td>
                                            </tr>
                                        <?php
                                        endwhile;
                                        ?>
                                        </tbody>
                                    </table>
                                    <a href="javascript:void(0);" onclick="return showWindow('编辑','<?=site_url('platform/profile/committee_edit/id/'.$platform->getPlatformId())?>',600,460)" class="btn btn-primary"><i class="glyphicon glyphicon-plus"></i> 添加人员</a>
                                </div>
                            <?php
                            else:
                                ?>
                                <div align="center" style="overflow:scroll;">
                                    <?php
                                    $committees = $platform->getCommittees()->toArray();
                                    ?>
                                    <table class="table table-bordered" style="width:2700px;text-align:center">
                                        <tbody>
                                        <tr>
                                            <td width="100">
                                                <p align="center"></p>
                                            </td>
                                            <td width="150">
                                                <p align="center">姓名</p>
                                            </td>
                                            <td width="150">
                                                <p align="center">身份证</p>
                                            </td>
                                            <td width="100">
                                                <p align="center">性别</p>
                                            </td>
                                            <td width="150">
                                                <p align="center">出生年月</p>
                                            </td>
                                            <td width="150">
                                                <p align="center">职务</p>
                                            </td>
                                            <td width="350">
                                                <p align="center">职称</p>
                                            </td>
                                            <td width="100">
                                                <p align="center">学历</p>
                                            </td>
                                            <td width="100">
                                                <p align="center">学位</p>
                                            </td>
                                            <td width="300">
                                                <p align="center">荣誉称号</p>
                                            </td>
                                            <td width="200">
                                                <p align="center">专业</p>
                                            </td>
                                            <td width="300">
                                                <p align="center">工作单位</p>
                                            </td>
                                            <td width="300">
                                                <p align="center">研究领域</p>
                                            </td>
                                            <td >
                                                <p align="center">备注</p>
                                            </td>
                                        </tr>
                                        <?php
                                        $j=0;
                                        for($i=0;$i<15;$i++):
                                            ?>
                                            <tr>
                                                <td class="text-center">
                                                    <?php
                                                    if($committees[$i]['type']=='成员') $committees[$i]['type']='委员';
                                                    ?>
                                                    <select name="committee[<?=$i?>][type]">
                                                        <?=getSelectFromArray(['主任','副主任','委员'],$committees[$i]['type'])?>
                                                    </select>
                                                    <input type="hidden" name="committee[<?=$i?>][id]" value="<?=$committees[$i]['id']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="committee[<?=$i?>][user_name]" value="<?=$committees[$i]['user_name']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="committee[<?=$i?>][idcard]" value="<?=$committees[$i]['idcard']?>">
                                                </td>
                                                <td>
                                                    <select class="form-control" name="committee[<?=$i?>][user_sex]">
                                                        <?=getSelectFromArray(['男','女'],$committees[$i]['user_sex'])?>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="committee[<?=$i?>][user_birthday]" value="<?=$committees[$i]['user_birthday']?>" onclick="WdatePicker({startDate:'1970-01-01',dateFmt:'yyyy-MM'})">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="committee[<?=$i?>][user_duty]" value="<?=$committees[$i]['user_duty']?>">
                                                </td>
                                                <td style="text-align: left">
                                                    <select name="committee[<?=$i?>][title_rank]" class="title_rank" required>
                                                        <?php $data=['0'=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'其他']?>
                                                        <?=getSelectFromArray($data,$committees[$i]['title_rank'],false)?>
                                                    </select>
                                                    <select name="committee[<?=$i?>][user_title]" class="title" required>
                                                        <?=getOptionByParent($committees[$i]['title_rank'],$committees[$i]['user_title'])?>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select name="committee[<?=$i?>][education]" id="education">
                                                        <option value="">请选择</option>
                                                        <?=getSelectFromArray(get_select_data('education'),$committees[$i]['education'])?>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select name="committee[<?=$i?>][degree]" id="degree">
                                                        <option value="">请选择</option>
                                                        <?=getSelectFromArray(['学士','硕士','博士','其他'],$committees[$i]['degree'])?>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="committee[<?=$i?>][honor]" value="<?=$committees[$i]['honor']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="committee[<?=$i?>][major]" value="<?=$committees[$i]['major']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="committee[<?=$i?>][corporation_name]" value="<?=$committees[$i]['corporation_name']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="committee[<?=$i?>][research_area]" value="<?=$committees[$i]['research_area']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="committee[<?=$i?>][note]" value="<?=$committees[$i]['note']?>">
                                                </td>
                                            </tr>
                                        <?php
                                        endfor;
                                        ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php
                            endif;
                            ?>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>