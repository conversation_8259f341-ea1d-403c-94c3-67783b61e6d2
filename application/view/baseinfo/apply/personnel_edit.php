<style>
    table caption{
        font-size: 16px;
        line-height: 26px;
        margin-bottom: 20px;
    }
</style>
<script language="javascript" type="text/javascript" src="<?=site_path("js/datepicker/WdatePicker.js")?>"></script>
<script language="javascript" type="text/javascript">
    $(function(){
        //选择职称级别
        $(document).on("change",".title_rank",function(){
            var id= $(this).val();
            var me= $(this);
            if(id==0){
                me.parent().find('.title').html('<option value="0">==请选择==</option>');
                return;
            }
            var url = baseurl+'common/HonorAjax';
            ajaxData(url,{id:id},'html',function(){},function(data){
                me.parent().find('.title').html(data);
            });
        });

    });

</script>

<div class="page-body">
    <div class="tab-content">
        <div class="tab-pane active">
        <div role="tabpanel" class="tab-pane active">
            <form class="form-horizontal" name="form1" id="activeForm" action="" method="post">
                <input type="hidden" name="id" value="<?=$base->getLabId()?>">
                <input type="hidden" name="pid" value="<?=$staff->getId()?>">

                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">研究方向</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="research_direction" id="research_direction" class="form-control">
                            <option value="">请选择</option>
                            <?=getSelectFromArray($base->getResearchDirectionArray(),$staff->getResearchDirection())?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>

                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">类型</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="type" id="type" class="form-control">
                            <?=getSelectFromArray(['学科带头人','成员'],$staff->getType())?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">姓名</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="user_name" class="form-control" value="<?=$staff->getUserName()?>" required />
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">证件类型</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="idcard_type" id="idcard_type" class="form-control col-sm-5">
                            <?=getSelectFromArray(['身份证','护照号','其他'],$staff->getIdcardType())?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">证件号码</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="idcard" class="form-control" value="<?=$staff->getIdcard()?>" required />
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">性别</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="user_sex" id="user_sex" required>
                            <?=getSelectFromArray(['男','女'],$staff->getUserSex())?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">出生年月</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="date" name="user_birthday" class="form-control" value="<?=$staff->getUserBirthday()?>"/>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">职务</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="user_duty" class="form-control" value="<?=$staff->getUserDuty()?>" required />
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">职称</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="title_rank" class="title_rank" required>
                            <?php $data=['0'=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'其他']?>
                            <?=getSelectFromArray($data,$staff->getTitleRank(),false)?>
                        </select>
                        <select name="user_title" class="title" required>
                            <?=getOptionByParent($staff->getTitleRank(),$staff->getUserTitle())?>
                        </select>
                    </div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">学历</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="education" id="education">
                            <option value="">请选择</option>
                            <?=getSelectFromArray(get_select_data('education'),$staff->getEducation())?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">学位</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="degree" id="degree">
                            <option value="">请选择</option>
                            <?=getSelectFromArray(['学士','硕士','博士','其他'],$staff->getDegree())?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">荣誉称号</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input class="form-control" type="text" name="honor" value="<?=$staff->getHonor()?>">
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">专业</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input class="form-control" type="text" name="major" value="<?=$staff->getMajor()?>">
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">工作单位</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input class="form-control" type="text" name="corporation_name" value="<?=$staff->getCorporationName()?>">
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">研究领域</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="research_area" class="form-control" value="<?=$staff->getResearchArea()?>" required />
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">备注</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="note" class="form-control" value="<?=$staff->getNote()?>" required />
                        <small class="help-block">备注中请注明该人员所获得的优秀称号等信息</small>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="clearfix"></div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <div style="width:200px;margin: 0 auto">
                        <a href="javascript:void(0);" onclick="return form1.submit();" class="btn btn-info btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存资料</a>
                    </div>
                </div>
            </form>
            <p style="clear:both"></p>
        </div>
    </div>
    </div>
</div>
<div class="page-footer">

</div>
