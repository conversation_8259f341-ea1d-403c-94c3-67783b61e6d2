<table width="680" align="center" cellpadding="5" cellspacing="0" class="table" border="1" style="overflow:wrap">
    <tbody>
    <tr>
        <th colspan="9" height="33" class="text-center"><?= $base->getCategory() ?>基础信息</th>
    </tr>
    <tr>
        <td width="150" height="33">重点实验室名称</td>
        <td colspan="5"><?= $base->getSubject() ?></td>
        <td width="120" >成立时间</td>
        <td colspan="2"><?= $base->getBuildAt() ?></td>
    </tr>
    <tr>
        <td height="33">主管部门</td>
        <td colspan="8" id="unit"><?= $base->getDepartmentName() ?>
        </td>
    </tr>
    <tr>
        <td height="33">类别</td>
        <td colspan="3"><?= getCheckedStr(['学科类', '企业类'], $base->getType()) ?></td>
        <td colspan="2">是否厅市（州）共建</td>
        <td colspan="3"><?= getCheckedStr(['是', '否'], $base->getBuildTogether()) ?></td>
    </tr>
    <tr>
        <td height="33">第一依托单位</td>
        <td colspan="8"><?= $base->getCorporationName() ?></td>
    </tr>
    <?php
    $others = $base->getOtherCorporationArray();
    $count = count($others) > 4 ? count($others) : 4;
    ?>
    <tr>
        <td rowspan="<?=$count?>">其他依托单位</td>
        <td colspan="8"><?=$others[0]?></td>
    </tr>
    <?php
    for($i=1;$i<$count;$i++):
        ?>
        <tr>
            <td colspan="8" height="33"><?=$others[$i]?></td>
        </tr>
    <?php endfor;?>
    <tr>
        <td height="33">第一依托单位地址</td>
        <td colspan="8"><?= $base->getAddress() ?></td>
    </tr>
    <tr>
        <td rowspan="3" height="66">重点实验室<br/>联系人信息</td>
        <td>姓名</td>
        <td colspan="3"><?= $base->getLinkmanName() ?></td>
        <td>邮箱</td>
        <td colspan="3"><?= $base->getLinkmanEmail() ?></td>
    </tr>
    <tr>
        <td height="33">固定电话</td>
        <td colspan="3"><?= $base->getLinkmanTel() ?></td>
        <td>手机</td>
        <td colspan="3"><?= $base->getLinkmanMobile() ?></td>
    </tr>
    <tr>
        <td height="33">证件号码</td>
        <td colspan="7"><?= $base->getLinkmanIdcard() ?></td>
    </tr>
    <?php
    $directions = $base->getResearchDirectionArray();
    ?>
    <tr>
        <td rowspan="5" height="165">研究方向</td>
        <td colspan="8"><?= $directions[0] ?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?= $directions[1] ?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?= $directions[2] ?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?= $directions[3] ?></td>
    </tr>
    <tr>
        <td colspan="8" height="33"><?= $directions[4] ?></td>
    </tr>
    </tbody>
</table>
<pagebreak sheet-size="297mm 210mm" />
<table width="980" align="center" cellpadding="5" cellspacing="0" class="table" border="1" style="overflow:wrap">
    <tbody>
    <tr>
        <th colspan="9" height="33" class="text-center">重点实验室主任信息</th>
    </tr>
    <?php
    $leader = $base->getLeader();
    ?>
    <tr>
        <td height="33" width="70">姓名</td>
        <td style="width:100px;">证件号码</td>
        <td>性别</td>
        <td>出生年月</td>
        <td>职务<br>/职称</td>
        <td style="width: 100px;">学历</td>
        <td colspan="2">研究<br>领域</td>
        <td>备注*</td>
    </tr>
    <tr>
        <td height="33"><?= $leader->getUserName() ?></td>
        <td><?= $leader->getIdcard() ?></td>
        <td><?= $leader->getUserSex() ?></td>
        <td><?= $leader->getUserBirthday() ?></td>
        <td><?= $leader->getUserDuty() ? $leader->getUserDuty().'/' : '' ?><?= $leader->getUserTitle() ?></td>
        <td><?= $leader->getEducation() ?></td>
        <td colspan="2"><?= $leader->getResearchArea() ?></td>
        <td><?= $leader->getNote() ?></td>
    </tr>
    <tr>
        <th colspan="9" height="33" class="text-center">固定人员信息</th>
    </tr>
    <?php

    $directions = $base->getResearchDirectionArray();
    $directionNo = 0;
    $startNo = 0;
    foreach ($directions as $direction):
        $directionNo++;
        $staffs = $base->getStaffByResearch($direction);
        ?>
        <tr>
            <th colspan="9" height="33" class="text-center">研究方向<?= $directionNo ?> - <?= $direction ?></th>
        </tr>
        <tr>
            <td height="33">　</td>
            <td>姓名</td>
            <td>证件号码</td>
            <td>性别</td>
            <td style="width: 80px">出生年月</td>
            <td>职务/职称</td>
            <td>学历</td>
            <td>研究领域</td>
            <td width="200px">备注*</td>
        </tr>
        <?php
        $j = 0;
        while ($staff = $staffs->getObject()):
            ?>
            <tr>
                <td height="33"><?= $staff->getType() ?></td>
                <td><?= $staff->getUserName() ?></td>
                <td><?= $staff->getIdcard() ?></td>
                <td><?= $staff->getUserSex() ?></td>
                <td><?= $staff->getUserBirthday() ?></td>
                <td><?= $staff->getUserDuty() ? $staff->getUserDuty().'/' : ''?><?= $staff->getUserTitle() ?></td>
                <td><?= $staff->getEducation() ?></td>
                <td><?= $staff->getResearchArea() ?></td>
                <td><?= $staff->getNote() ?></td>
            </tr>
            <?php
            $j++;
        endwhile;
        ?>
    <?php
    endforeach;
    ?>
    </tbody>
</table>
<table width="980" align="center" cellpadding="5" cellspacing="0" class="table" border="1" style="overflow:wrap">
    <tbody>
    <tr>
        <th colspan="10" height="33" class="text-center">学术委员会</th>
    </tr>
    <?php
    $committees = $base->getCommittees()->toArray();
    ?>
    <tr>
        <td height="33">　</td>
        <td>姓名</td>
        <td>证件号码</td>
        <td>性别</td>
        <td>出生年月</td>
        <td>职务/职称</td>
        <td>学历</td>
        <td>工作单位</td>
        <td>研究领域</td>
        <td width="200px">备注*</td>
    </tr>
    <?php
    $j = 0;
    for ($i = 0; $i < 20; $i++):
        if(empty($committees[$i]['user_name'])) continue;
        ?>
        <tr>
            <td height="33"><?= $committees[$i]['type'] ?></td>
            <td><?= $committees[$i]['user_name'] ?></td>
            <td><?= $committees[$i]['idcard'] ?></td>
            <td><?= $committees[$i]['user_sex'] ?></td>
            <td><?= $committees[$i]['user_birthday'] ?></td>
            <td><?= $committees[$i]['user_duty'] ? $committees[$i]['user_duty'].'/' : '' ?><?= $committees[$i]['user_title'] ?></td>
            <td><?= $committees[$i]['education'] ?></td>
            <td><?= $committees[$i]['corporation_name'] ?></td>
            <td><?= $committees[$i]['research_area'] ?></td>
            <td><?= $committees[$i]['note'] ?></td>
        </tr>
    <?php
    endfor;
    ?>
    <tr>
        <td colspan="10" height="33">*备注中请注明该人员所获得的优秀称号等信息</td>
    </tr>
    </tbody>
</table>
