<script language="javascript" type="text/javascript">
    function unit(json)
    {
        save(json.id);
        $("#unit").html(json.subject);
        closeWindow();
    }

    function save(cid){
        $.post('<?=site_url("baseinfo/office/change")?>',{department_id:cid,id:'<?=$base->getLabId()?>'},function(json){
            if(json != 'OK') return false;
        });
        return true;
    }
</script>
<div class="page-header">
    <div class="btn-group btn-group-sm" role="group">
        <a href="javascript:history.go(-1);" class="btn btn-info"><i class="glyphicon glyphicon-arrow-left"></i> 返回</a>
        <a href="<?=site_url('baseinfo/office/exportWord/id/'.$base->getLabId())?>" class="btn btn-danger"><i class="glyphicon glyphicon-export"></i> 导出</a>
        <p style="clear:both;"></p>
    </div>
</div>
<div class="page-body">
    <div class="tab-content">
        <div class="tab-pane active">
            <div role="tabpanel" class="tab-pane active">
                <table class="table table-bordered" style="width: 1200px;">
                    <tbody>
                    <tr>
                        <th colspan="9" width="1016" height="33" class="text-center"><?=$base->getCategory()?>基础信息</th>
                    </tr>
                    <tr>
                        <td height="33">重点实验室名称</td>
                        <td colspan="5"><?=$base->getSubject()?></td>
                        <td>成立时间</td>
                        <td colspan="2"><?=$base->getBuildAt()?></td>
                    </tr>
                    <tr>
                        <td height="33">主管部门</td>
                        <td colspan="3" id="unit"><?=$base->getDepartmentName()?>
                            <?php if(input::getInput("session.userlevel") == 1):?>
                                <a href="javascript::void();" onclick="return showWindow('调整主管部门','<?=site_url("common/department_search")?>',600,460)" class="btn btn-sm btn-danger">调整主管部门</a>
                            <?php endif;?>
                        </td>
                        <td colspan="2">填报人</td>
                        <td colspan="3"><?=$base->getUserName()?>
                            <?php if(input::getInput("session.userlevel") == 1):?>
                                <a href="javascript::void();" onclick="return showWindow('变更填报人','<?=site_url("admin/super/changeUsername/id/".$base->getLabId())?>',600,460)" class="btn btn-sm btn-danger">变更</a>
                            <?php endif;?>
                        </td>
                    </tr>
                    <tr>
                        <td height="33">类别</td>
                        <td colspan="8"><?=getCheckedStr(['学科类','企业类'],$base->getType())?></td>
                    </tr>
                    <tr>
                        <td height="33">学科分类</td>
                        <td colspan="8"><?=$base->getGbSubjectStr()?></td>
                    </tr>
                    <tr>
                        <td height="33">前沿领域分类</td>
                        <td colspan="3"><?=$base->getFrontier()?></td>
                        <td colspan="2">所属产业</td>
                        <td colspan="3"><?=$base->getIndustry()?></td>
                    </tr>
                    <tr>
                        <td height="33">第一依托单位</td>
                        <td colspan="8"><?=$base->getCorporationName()?></td>
                    </tr>
                    <?php
                    $others = $base->getOtherCorporationArray();
                    ?>
                    <tr>
                        <td rowspan="4" height="132">其他依托单位</td>
                        <td colspan="8"><?=$others[0]?></td>
                    </tr>
                    <tr>
                        <td colspan="8" height="33"><?=$others[1]?></td>
                    </tr>
                    <tr>
                        <td colspan="8" height="33"><?=$others[2]?></td>
                    </tr>
                    <tr>
                        <td colspan="8" height="33"><?=$others[3]?></td>
                    </tr>
                    <tr>
                        <td height="33">第一依托单位地址</td>
                        <td colspan="8"><?=$base->getAddress()?></td>
                    </tr>
                    <tr>
                        <td rowspan="2" width="135" height="66">重点实验室<br />联系人信息</td>
                        <td>姓名</td>
                        <td colspan="3"><?=$base->getLinkmanName()?>
                            <?php if(input::getInput("session.userlevel") == 1):?>
                                <a href="javascript::void();" onclick="return showWindow('变更联系人','<?=site_url("admin/super/changeLinkman/id/".$base->getLabId())?>',600,460)" class="btn btn-sm btn-danger">变更</a>
                            <?php endif;?>
                        </td>
                        <td>邮箱</td>
                        <td colspan="3"><?=$base->getLinkmanEmail()?></td>
                    </tr>
                    <tr>
                        <td height="33">固定电话</td>
                        <td colspan="3"><?=$base->getLinkmanTel()?></td>
                        <td>手机</td>
                        <td colspan="3"><?=$base->getLinkmanMobile()?></td>
                    </tr>
                    <?php
                    $directions = $base->getResearchDirectionArray();
                    ?>
                    <tr>
                        <th colspan="9" height="33" class="text-center">重点实验室主任信息</th>
                    </tr>
                    <?php
                        $leader = $base->getLeader();
                    ?>
                    <tr>
                        <td height="33">姓名</td>
                        <td>性别</td>
                        <td colspan="2">出生年月</td>
                        <td>职务/职称</td>
                        <td style="width: 100px;">学历</td>
                        <td colspan="2">研究领域</td>
                        <td>备注*</td>
                    </tr>
                    <tr>
                        <td height="33"><?=$leader->getUserName()?>
                            <?php if(input::getInput("session.userlevel") == 1):?>
                                <a href="javascript::void();" onclick="return showWindow('变更中心主任','<?=site_url("admin/super/changeLeader/id/".$base->getLabId())?>',600,460)" class="btn btn-sm btn-danger">变更</a>
                            <?php endif;?>
                        </td>
                        <td><?=$leader->getUserSex()?></td>
                        <td colspan="2"><?=$leader->getUserBirthday()?></td>
                        <td><?=$leader->getUserDuty()?>/<?=$leader->getUserTitle()?></td>
                        <td><?=$leader->getEducation()?></td>
                        <td colspan="2"><?=$leader->getResearchArea()?></td>
                        <td><?=$leader->getNote()?></td>
                    </tr>
                    <tr>
                        <th colspan="9" height="33" class="text-center">固定人员信息</th>
                    </tr>
                    <?php
                        $staffs = $base->getStaffs()->toArray();
                    ?>
                    <tr>
                        <td height="33">　</td>
                        <td>姓名</td>
                        <td>性别</td>
                        <td style="width: 80px">出生年月</td>
                        <td>职务/职称</td>
                        <td>学历</td>
                        <td>研究领域</td>
                        <td>研究方向</td>
                        <td>备注*</td>
                    </tr>
                    <?php
                    $j=0;
                    for($i=0;$i<30;$i++):
                    ?>
                    <tr>
                        <td height="33"><?=$staffs[$i]['type']?></td>
                        <td><?=$staffs[$i]['user_name']?></td>
                        <td><?=$staffs[$i]['user_sex']?></td>
                        <td><?=$staffs[$i]['user_birthday']?></td>
                        <td><?=$staffs[$i]['user_duty']?>/<?=$staffs[$i]['user_title']?></td>
                        <td><?=$staffs[$i]['education']?></td>
                        <td><?=$staffs[$i]['research_area']?></td>
                        <td><?=$staffs[$i]['research_direction']?></td>
                        <td><?=$staffs[$i]['note']?></td>
                    </tr>
                        <?php
                        $j++;
                    endfor;
                    ?>
                    <tr>
                        <th colspan="9" height="33" class="text-center">学术委员会</th>
                    </tr>
                    <?php
                    $committees = $base->getCommittees()->toArray();
                    ?>
                    <tr>
                        <td height="33">　</td>
                        <td>姓名</td>
                        <td>性别</td>
                        <td>出生年月</td>
                        <td>职务/职称</td>
                        <td>学历</td>
                        <td>工作单位</td>
                        <td>研究领域</td>
                        <td>备注*</td>
                    </tr>
                    <?php
                    $j=0;
                    for($i=0;$i<20;$i++):
                        if(empty($committees[$i]['type']) || $committees[$i]['type']=='成员') $committees[$i]['type']='委员';
                    ?>
                    <tr>
                        <td height="33"><?=$committees[$i]['type']?></td>
                        <td><?=$committees[$i]['user_name']?></td>
                        <td><?=$committees[$i]['user_sex']?></td>
                        <td><?=$committees[$i]['user_birthday']?></td>
                        <td><?=$committees[$i]['user_duty']?>/<?=$committees[$i]['user_title']?></td>
                        <td><?=$committees[$i]['education']?></td>
                        <td><?=$committees[$i]['corporation_name']?></td>
                        <td><?=$committees[$i]['research_area']?></td>
                        <td><?=$committees[$i]['note']?></td>
                    </tr>
                        <?php
                    endfor;
                    ?>
                    <tr>
                        <td colspan="9" height="33">*备注中请注明该人员所获得的优秀称号等信息</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="page-footer">

</div>
