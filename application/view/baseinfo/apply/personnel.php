<style>
    table caption{
        font-size: 16px;
        line-height: 26px;
        margin-bottom: 20px;
    }
</style>
<script language="javascript" type="text/javascript" src="<?=site_path("js/datepicker/WdatePicker.js")?>"></script>
<script language="javascript" type="text/javascript">
    $(function(){
        //选择职称级别
        $(document).on("change",".title_rank",function(){
            var id= $(this).val();
            var me= $(this);
            if(id==0){
                me.parent().find('.title').html('<option value="0">==请选择==</option>');
                return;
            }
            var url = baseurl+'common/HonorAjax';
            ajaxData(url,{id:id},'html',function(){},function(data){
                me.parent().find('.title').html(data);
            });
        });

    });

</script>
<div class="page-header">
    <div class="btn-group btn-group-sm" role="group">
        <a href="javascript:history.go(-1);" class="btn btn-info"><i class="glyphicon glyphicon-arrow-left"></i> 返回</a>
        <?php
            if(!$base->isNew()):
        ?>
        <a href="<?=site_url("baseinfo/apply/show/id/".$base->getLabId())?>" class="btn btn-primary btn-xs"><i class="glyphicon glyphicon-search"></i> 查看预览</a>
        <a href="<?=site_url("baseinfo/apply/submit/id/".$base->getLabId())?>" class="btn btn-danger btn-xs btn-submit"><i class="glyphicon glyphicon-open"></i> 资料上报</a>
        <?php
            endif;
        ?>
        <p style="clear:both;"></p>
    </div>
</div>
<div class="page-body">
    <ul class="nav nav-tabs" role="tablist">
        <?php foreach($tabs as $tab):?>
            <li role="presentation"<?php if($tab['method'] == $method):?> class="active"<?php endif;?>><a href="<?=$tab['url']?>" aria-controls="<?=$tab['method']?>" role="tab">
                    <?=$tab['text']?>
                </a></li>
        <?php endforeach;?>
    </ul>
    <div class="tab-content">
        <div class="tab-pane active">
        <div role="tabpanel" class="tab-pane active">
            <form class="form-horizontal" name="form1" id="activeForm" action="" method="post">
                <input type="hidden" name="id" value="<?=$base->getLabId()?>">
                <div class="header no-margin-top">重点实验室主任信息</div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">姓名</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="leader[user_name]" class="form-control" value="<?=$base->getLeaderName()?>" required />
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">证件类型</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="leader[idcard_type]" id="idcard_type" class="form-control col-sm-5">
                            <?=getSelectFromArray(['身份证','护照号','其他'],$base->getLeaderIdcardType())?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">证件号码</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="leader[idcard]" class="form-control" value="<?=$base->getLeaderIdcard()?>" required />
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">性别</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="leader[user_sex]" id="user_sex" required>
                            <?=getSelectFromArray(['男','女'],$leader->getUserSex())?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">出生年月</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="leader[user_birthday]" class="form-control" value="<?=$leader->getUserBirthday()?>" onclick="WdatePicker({startDate:'1970-01-01',dateFmt:'yyyy-MM'})" />
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">职务</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="leader[user_duty]" class="form-control" value="<?=$leader->getUserDuty()?>" required />
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">职称</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="leader[title_rank]" class="title_rank" required>
                            <?php $data=['0'=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'其他']?>
                            <?=getSelectFromArray($data,$leader->getTitleRank(),false)?>
                        </select>
                        <select name="leader[user_title]" class="title" required>
                            <?=getOptionByParent($leader->getTitleRank(),$leader->getUserTitle())?>
                        </select>
                    </div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">学历</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="leader[education]" id="education">
                            <option value="">请选择</option>
                            <?=getSelectFromArray(get_select_data('education'),$leader->getEducation())?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">学位</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <select name="leader[degree]" id="degree">
                            <option value="">请选择</option>
                            <?=getSelectFromArray(['学士','硕士','博士','其他'],$leader->getDegree())?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">荣誉称号</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input class="form-control" type="text" name="leader[honor]" value="<?=$leader->getHonor()?>">
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">专业</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input class="form-control" type="text" name="leader[major]" value="<?=$leader->getMajor()?>">
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">工作单位</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input class="form-control" type="text" name="leader[corporation_name]" value="<?=$leader->getCorporationName()?>">
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">研究领域</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="leader[research_area]" class="form-control" value="<?=$leader->getResearchArea()?>" required />
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3">备注</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <input type="text" name="leader[note]" class="form-control" value="<?=$leader->getNote()?>" required />
                        <small class="help-block">备注中请注明该人员所获得的优秀称号等信息</small>
                    </div>
                    <div class="clearfix"></div>
                </div>
                <div class="clearfix"></div>
                <?php
                    $allowCount = $base->getCategory()=='省级重点实验室' ? 20 : 30;
                ?>
                <?php
                    if($base->getCategory()=='省级重点实验室'):
                ?>
                <div class="header no-margin-top">固定人员信息</div>
                <div align="center">
                    <?php
                    $directions = $base->getResearchDirectionArray();
                    $directionNo = 0;
                    $startNo = 0;
                    foreach ($directions as $directionkey=>$direction):
                        $directionNo++;
                        $staffs = $base->getStaffByResearch($direction);
                    ?>
                    <table class="table table-bordered">
                        <tbody>
                        <tr>
                            <td colspan="15">
                                <p align="left"><strong>研究方向<?=$directionNo?> - <?=$direction?></strong></p>
                            </td>
                        </tr>
                        <tr>
                            <td width="100">
                                <p align="center"></p>
                            </td>
                            <td>
                                <p align="center">姓名</p>
                            </td>
                            <td>
                                <p align="center">证件号码</p>
                            </td>
                            <td>
                                <p align="center">性别</p>
                            </td>
                            <td>
                                <p align="center">出生年月</p>
                            </td>
                            <td>
                                <p align="center">职务/职称</p>
                            </td>
                            <td>
                                <p align="center">学历</p>
                            </td>
                            <td>
                                <p align="center">学位</p>
                            </td>
                            <td>
                                <p align="center">荣誉称号</p>
                            </td>
                            <td>
                                <p align="center">专业</p>
                            </td>
                            <td>
                                <p align="center">工作单位</p>
                            </td>
                            <td>
                                <p align="center">研究领域</p>
                            </td>
                            <td width="120px">
                                <p align="center">操作</p>
                            </td>
                        </tr>
                        <?php
                        $j=0;
                        while($staff = $staffs->getObject()):
                        ?>
                        <tr>
                            <td class="text-center">
                                <?=$staff->getType()?>
                            </td>
                            <td align="center">
                                <?=$staff->getUserName()?>
                            </td>
                            <td align="center">
                                <?=$staff->getIdcard()?>
                            </td>
                            <td align="center">
                                <?=$staff->getUserSex()?>
                            </td>
                            <td align="center">
                                <?=$staff->getUserBirthday()?>
                            </td>
                            <td align="center">
                                <?=$staff->getUserDuty()?> / <?=$staff->getUserTitle()?>
                            </td>
                            <td align="center">
                                <?=$staff->getEducation()?>
                            </td>
                            <td align="center">
                                <?=$staff->getDegree()?>
                            </td>
                            <td align="center">
                                <?=$staff->getHonor()?>
                            </td>
                            <td align="center">
                                <?=$staff->getMajor()?>
                            </td>
                            <td align="center">
                                <?=$staff->getCorporationName()?>
                            </td>
                            <td align="center">
                                <?=$staff->getResearchArea()?>
                            </td>
                            <td align="center">
                                <a href="javascript:void(0);" onclick="return showWindow('编辑','<?=site_url('baseinfo/apply/personnel_edit/id/'.$staff->getLabId().'/pid/'.$staff->getId())?>',600,460)" class="btn btn-primary btn-sm" role="button">编辑</a>
                                <a href="<?=site_url('baseinfo/apply/personnel_delete/id/'.$staff->getLabId().'/pid/'.$staff->getId())?>" onclick="return confirm('确定要删除吗？')" class="btn btn-danger btn-sm">删除</a>
                            </td>
                        </tr>
                        <?php
                            $j++;
                            endwhile;
                        ?>
                        </tbody>
                    </table>

                        <a href="javascript:void(0);" onclick="return showWindow('编辑','<?=site_url('baseinfo/apply/personnel_edit/id/'.$base->getLabId().'/direction_key/'.$directionkey)?>',600,460)" class="btn btn-primary"><i class="glyphicon glyphicon-plus"></i> 添加人员</a>
                        <br>
                        <br>
                    <?php
                    endforeach;
                    ?>
                    <?php
                        $staffs = $base->getStaffByNoResearch();
                        if($staffs!==false && $staffs->getTotal()>0):
                    ?>
                    <table class="table table-bordered">
                        <tbody>
                        <tr>
                            <td colspan="15">
                                <p align="left"><strong style="color:red">未对应研究方向的人员（请点击编辑选择对应的研究方向）：</strong></p>
                            </td>
                        </tr>
                        <tr>
                            <td width="100">
                                <p align="center"></p>
                            </td>
                            <td>
                                <p align="center">姓名</p>
                            </td>
                            <td>
                                <p align="center">证件号码</p>
                            </td>
                            <td>
                                <p align="center">性别</p>
                            </td>
                            <td>
                                <p align="center">出生年月</p>
                            </td>
                            <td>
                                <p align="center">职务/职称</p>
                            </td>
                            <td>
                                <p align="center">学历</p>
                            </td>
                            <td>
                                <p align="center">学位</p>
                            </td>
                            <td>
                                <p align="center">荣誉称号</p>
                            </td>
                            <td>
                                <p align="center">专业</p>
                            </td>
                            <td>
                                <p align="center">工作单位</p>
                            </td>
                            <td>
                                <p align="center">研究领域</p>
                            </td>
                            <td width="120px">
                                <p align="center">操作</p>
                            </td>
                        </tr>
                        <?php
                        while($staff = $staffs->getObject()):
                            ?>
                            <tr>
                                <td class="text-center">
                                    <?=$staff->getType()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getUserName()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getIdcard()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getUserSex()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getUserBirthday()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getUserDuty()?> / <?=$staff->getUserTitle()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getEducation()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getDegree()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getHonor()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getMajor()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getCorporationName()?>
                                </td>
                                <td align="center">
                                    <?=$staff->getResearchArea()?>
                                </td>
                                <td align="center">
                                    <a href="javascript:void(0);" onclick="return showWindow('编辑','<?=site_url('baseinfo/apply/personnel_edit/id/'.$staff->getLabId().'/pid/'.$staff->getId())?>',600,460)" class="btn btn-primary btn-sm" role="button">编辑</a>
                                    <a href="<?=site_url('baseinfo/apply/personnel_delete/id/'.$staff->getLabId().'/pid/'.$staff->getId())?>" onclick="return confirm('确定要删除吗？')" class="btn btn-danger btn-sm">删除</a>
                                </td>
                            </tr>
                            <?php
                            $j++;
                        endwhile;
                        ?>
                        </tbody>
                    </table>
                    <br>
                    <br>
                    <?php endif;?>
                    <small class="help-block">备注中请注明该人员所获得的优秀称号等信息</small>
                </div>
                <?php
                    else:
                ?>
                <div class="header no-margin-top">固定人员信息（已录入<?=$base->getStaffCount()?>人，还可以录入<?=($allowCount-$base->getStaffCount())?>人）</div>
                <div align="center" style="overflow:scroll;">
                    <?php
                    $staffs = $base->getStaffs()->toArray();
                    ?>
                    <table class="table table-bordered" style="width:3000px;text-align:center">
                        <tbody>
                        <tr>
                            <td width="100">
                                <p align="center"></p>
                            </td>
                            <td width="150">
                                <p align="center">姓名</p>
                            </td>
                            <td width="150">
                                <p align="center">身份证</p>
                            </td>
                            <td width="100">
                                <p align="center">性别</p>
                            </td>
                            <td width="150">
                                <p align="center">出生年月</p>
                            </td>
                            <td width="150">
                                <p align="center">职务</p>
                            </td>
                            <td width="350">
                                <p align="center">职称</p>
                            </td>
                            <td width="100">
                                <p align="center">学历</p>
                            </td>
                            <td width="100">
                                <p align="center">学位</p>
                            </td>
                            <td width="300">
                                <p align="center">荣誉称号</p>
                            </td>
                            <td width="200">
                                <p align="center">专业</p>
                            </td>
                            <td width="200">
                                <p align="center">工作单位</p>
                            </td>
                            <td width="300">
                                <p align="center">研究领域</p>
                            </td>
                            <td width="300">
                                <p align="center">研究方向</p>
                            </td>
                            <td >
                                <p align="center">备注</p>
                            </td>
                        </tr>
                        <?php
                        $j=0;
                        for($i=0;$i<30;$i++):
                            ?>
                            <tr>
                                <td class="text-center">
                                    <?php
                                        if(empty($staffs[$i]['type'])) $staffs[$i]['type']='成员';
                                    ?>
                                    <select name="direction[<?=$i?>][type]">
                                        <?=getSelectFromArray(['主任','副主任','成员'],$staffs[$i]['type'])?>
                                    </select>
                                    <input type="hidden" name="direction[<?=$i?>][id]" value="<?=$staffs[$i]['id']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="direction[<?=$i?>][user_name]" value="<?=$staffs[$i]['user_name']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="direction[<?=$i?>][idcard]" value="<?=$staffs[$i]['idcard']?>">
                                </td>
                                <td>
                                    <select class="form-control" name="direction[<?=$i?>][user_sex]">
                                        <?=getSelectFromArray(['男','女'],$staffs[$i]['user_sex'])?>
                                    </select>
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="direction[<?=$i?>][user_birthday]" value="<?=$staffs[$i]['user_birthday']?>" onclick="WdatePicker({startDate:'1970-01-01',dateFmt:'yyyy-MM'})">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="direction[<?=$i?>][user_duty]" value="<?=$staffs[$i]['user_duty']?>">
                                </td>
                                <td style="text-align: left">
                                    <select name="direction[<?=$i?>][title_rank]" class="title_rank" required>
                                        <?php $data=['0'=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'其他']?>
                                        <?=getSelectFromArray($data,$staffs[$i]['title_rank'],false)?>
                                    </select>
                                    <select name="direction[<?=$i?>][user_title]" class="title" required>
                                        <?=getOptionByParent($staffs[$i]['title_rank'],$staffs[$i]['user_title'])?>
                                    </select>
                                </td>
                                <td>
                                    <select name="direction[<?=$i?>][education]" id="education">
                                        <option value="">请选择</option>
                                        <?=getSelectFromArray(get_select_data('education'),$staffs[$i]['education'])?>
                                    </select>
                                </td>
                                <td>
                                    <select name="direction[<?=$i?>][degree]" id="degree">
                                        <option value="">请选择</option>
                                        <?=getSelectFromArray(['学士','硕士','博士','其他'],$staffs[$i]['degree'])?>
                                    </select>
                                </td>

                                <td>
                                    <input class="form-control" type="text" name="direction[<?=$i?>][honor]" value="<?=$staffs[$i]['honor']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="direction[<?=$i?>][major]" value="<?=$staffs[$i]['major']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="direction[<?=$i?>][corporation_name]" value="<?=$staffs[$i]['corporation_name']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="direction[<?=$i?>][research_area]" value="<?=$staffs[$i]['research_area']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="direction[<?=$i?>][research_direction]" value="<?=$staffs[$i]['research_direction']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="direction[<?=$i?>][note]" value="<?=$staffs[$i]['note']?>">
                                </td>
                            </tr>
                            <?php
                            $j++;
                        endfor;
                        ?>
                        </tbody>
                    </table>
                    <small class="help-block">备注中请注明该人员所获得的优秀称号等信息</small>
                </div>
                <?php
                    endif;
                ?>
                <div class="clearfix"></div>
                <div class="header no-margin-top">学术委员会</div>
                <?php
                    if($base->getCategory()=='省级重点实验室'):
                ?>
                <div align="center" style="overflow:scroll;">
                    <?php
                    $committees = $base->getCommittees();
                    ?>
                        <table class="table table-bordered">
                            <tbody>
                            <tr>
                                <td width="100">
                                    <p align="center"></p>
                                </td>
                                <td width="150">
                                    <p align="center">姓名</p>
                                </td>
                                <td width="150">
                                    <p align="center">证件号码</p>
                                </td>
                                <td width="100">
                                    <p align="center">性别</p>
                                </td>
                                <td width="100">
                                    <p align="center">出生年月</p>
                                </td>
                                <td width="150">
                                    <p align="center">职务</p>
                                </td>
                                <td width="200">
                                    <p align="center">职称</p>
                                </td>
                                <td width="100">
                                    <p align="center">学历</p>
                                </td>
                                <td width="100">
                                    <p align="center">学位</p>
                                </td>
                                <td width="200">
                                    <p align="center">荣誉称号</p>
                                </td>
                                <td width="200">
                                    <p align="center">专业</p>
                                </td>
                                <td width="200">
                                    <p align="center">工作单位</p>
                                </td>
                                <td width="200">
                                    <p align="center">研究领域</p>
                                </td>
                                <td width="140px"  class="text-center">
                                    操作
                                </td>
                            </tr>
                            <?php
                            while($committee = $committees->getObject()):
                            ?>
                                <tr>
                                    <td class="text-center"><?=$committee->getType()?></td>
                                    <td><?=$committee->getUserName()?></td>
                                    <td><?=$committee->getIdcard()?></td>
                                    <td class="text-center"><?=$committee->getUserSex()?></td>
                                    <td><?=$committee->getUserBirthday()?></td>
                                    <td><?=$committee->getUserDuty()?></td>
                                    <td><?=$committee->getUserTitle()?></td>
                                    <td><?=$committee->getEducation()?></td>
                                    <td><?=$committee->getDegree()?></td>
                                    <td><?=$committee->getHonor()?></td>
                                    <td><?=$committee->getMajor()?></td>
                                    <td><?=$committee->getCorporationName()?></td>
                                    <td><?=$committee->getResearchArea()?></td>
                                    <td class="text-center">
                                        <a href="javascript:void(0);" onclick="return showWindow('编辑','<?=site_url('baseinfo/apply/committee_edit/id/'.$committee->getLabId().'/pid/'.$committee->getId())?>',600,460)" class="btn btn-primary btn-sm" role="button">编辑</a>
                                        <a href="<?=site_url('baseinfo/apply/committee_delete/id/'.$committee->getLabId().'/pid/'.$committee->getId())?>" onclick="return confirm('确定要删除吗？')" class="btn btn-danger btn-sm">删除</a>
                                    </td>
                                </tr>
                            <?php
                            endwhile;
                            ?>
                            </tbody>
                        </table>
                    <a href="javascript:void(0);" onclick="return showWindow('编辑','<?=site_url('baseinfo/apply/committee_edit/id/'.$base->getLabId().'/direction_key/'.$directionkey)?>',600,460)" class="btn btn-primary"><i class="glyphicon glyphicon-plus"></i> 添加人员</a>
                    <small class="help-block">备注中请注明该人员所获得的优秀称号等信息</small>
                </div>
                <?php
                    else:
                ?>
                <div align="center" style="overflow:scroll;">
                    <?php
                    $committees = $base->getCommittees()->toArray();
                    ?>
                    <table class="table table-bordered" style="width:2700px;text-align:center">
                        <tbody>
                        <tr>
                            <td width="100">
                                <p align="center"></p>
                            </td>
                            <td width="150">
                                <p align="center">姓名</p>
                            </td>
                            <td width="150">
                                <p align="center">身份证</p>
                            </td>
                            <td width="100">
                                <p align="center">性别</p>
                            </td>
                            <td width="150">
                                <p align="center">出生年月</p>
                            </td>
                            <td width="150">
                                <p align="center">职务</p>
                            </td>
                            <td width="350">
                                <p align="center">职称</p>
                            </td>
                            <td width="100">
                                <p align="center">学历</p>
                            </td>
                            <td width="100">
                                <p align="center">学位</p>
                            </td>
                            <td width="300">
                                <p align="center">荣誉称号</p>
                            </td>
                            <td width="200">
                                <p align="center">专业</p>
                            </td>
                            <td width="300">
                                <p align="center">工作单位</p>
                            </td>
                            <td width="300">
                                <p align="center">研究领域</p>
                            </td>
                            <td >
                                <p align="center">备注</p>
                            </td>
                        </tr>
                        <?php
                        $j=0;
                        for($i=0;$i<15;$i++):
                            ?>
                            <tr>
                                <td class="text-center">
                                    <?php
                                    if($committees[$i]['type']=='成员') $committees[$i]['type']='委员';
                                    ?>
                                    <select name="committee[<?=$i?>][type]">
                                        <?=getSelectFromArray(['主任','副主任','委员'],$committees[$i]['type'])?>
                                    </select>
                                    <input type="hidden" name="committee[<?=$i?>][id]" value="<?=$committees[$i]['id']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="committee[<?=$i?>][user_name]" value="<?=$committees[$i]['user_name']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="committee[<?=$i?>][idcard]" value="<?=$committees[$i]['idcard']?>">
                                </td>
                                <td>
                                    <select class="form-control" name="committee[<?=$i?>][user_sex]">
                                        <?=getSelectFromArray(['男','女'],$committees[$i]['user_sex'])?>
                                    </select>
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="committee[<?=$i?>][user_birthday]" value="<?=$committees[$i]['user_birthday']?>" onclick="WdatePicker({startDate:'1970-01-01',dateFmt:'yyyy-MM'})">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="committee[<?=$i?>][user_duty]" value="<?=$committees[$i]['user_duty']?>">
                                </td>
                                <td style="text-align: left">
                                    <select name="committee[<?=$i?>][title_rank]" class="title_rank" required>
                                        <?php $data=['0'=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'其他']?>
                                        <?=getSelectFromArray($data,$committees[$i]['title_rank'],false)?>
                                    </select>
                                    <select name="committee[<?=$i?>][user_title]" class="title" required>
                                        <?=getOptionByParent($committees[$i]['title_rank'],$committees[$i]['user_title'])?>
                                    </select>
                                </td>
                                <td>
                                    <select name="committee[<?=$i?>][education]" id="education">
                                        <option value="">请选择</option>
                                        <?=getSelectFromArray(get_select_data('education'),$committees[$i]['education'])?>
                                    </select>
                                </td>
                                <td>
                                    <select name="committee[<?=$i?>][degree]" id="degree">
                                        <option value="">请选择</option>
                                        <?=getSelectFromArray(['学士','硕士','博士','其他'],$committees[$i]['degree'])?>
                                    </select>
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="committee[<?=$i?>][honor]" value="<?=$committees[$i]['honor']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="committee[<?=$i?>][major]" value="<?=$committees[$i]['major']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="committee[<?=$i?>][corporation_name]" value="<?=$committees[$i]['corporation_name']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="committee[<?=$i?>][research_area]" value="<?=$committees[$i]['research_area']?>">
                                </td>
                                <td>
                                    <input class="form-control" type="text" name="committee[<?=$i?>][note]" value="<?=$committees[$i]['note']?>">
                                </td>
                            </tr>
                            <?php
                        endfor;
                        ?>
                        </tbody>
                    </table>
                    <small class="help-block">备注中请注明该人员所获得的优秀称号等信息</small>
                </div>
                <?php
                    endif;
                ?>
                <div class="clearfix"></div>

                <div class="form-group col-xs-12 col-sm-12 col-md-12">
                    <div style="width:200px;margin: 0 auto">
                        <a href="javascript:void(0);" onclick="return form1.submit();" class="btn btn-info btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存资料</a>
                    </div>
                </div>
            </form>
            <p style="clear:both"></p>
        </div>
    </div>
    </div>
</div>
<div class="page-footer">

</div>
