<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            平台基本资料
        </h2>
        <div>
            ?php
            if(!$platform->isNew()):
            ?>
            <?=Button::setUrl(site_url("baseinfo/apply/show/id/".$platform->getPlatformId()))->setIcon('show')->link('查看预览')?>
            <?=Button::setUrl(site_url("baseinfo/apply/submit/id/".$platform->getPlatformId()))->setIcon('submit')->setClass('btn-alt-danger')->link('资料上报')?>
            <?php
            endif;
            ?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):$method = $method=='index'?'base':$method;?>
                        <li class="nav-item" class="tab-pane">
                            <a class="<?php if($tab['method'] == $method)echo 'nav-link active';else echo 'nav-link'; ?>" href="<?=$tab['url']?>"><?=$tab['text']?></a>
                        </li>
                        <?php $i++;endforeach;?>
                </ul>
                <div class="block-content tab-content block-content-full">
                    <div class="tab-content">
                        <div role="tabpanel" class="tab-pane p-3 active">
                            <form class="form-horizontal" name="form1" id="activeForm" action="" method="post">
                                <input type="hidden" name="id" value="<?=$platform->getPlatformId()?>">
                                <input type="hidden" name="cat_id" value="<?=$platform->getCatId()?>">
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">平台类型</label>
                                    <div class="col-sm-10">
                                        <select class="form-control" disabled>
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(get_select_data('cat'),$platform->getCatId(),false)?>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">重点实验室名称</label>
                                    <div class="col-sm-10">
                                        <?php
                                            if(!$platform->IsNew()){
                                                $subjects[] = $platform->getSubject();
                                            }else{
                                                $subjects = getProjectsByCompany($platform->getCorporationId());
                                            }
                                        ?>
                                        <select class="form-control" name="subject" id="subject">
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray($subjects,$platform->getSubject())?>
                                        </select>
                                        <p class="help-block">如下拉框中没有找到，说明贵单位还没有已批建的重点实验室</p>
                                    </div>

                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">第一依托单位</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="corporation_name" class="form-control" value="<?=$platform->getCorporationName()?>" readonly />
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">第一依托单位性质</label>
                                    <div class="col-sm-10">
                                        <select class="form-control" name="corporation_property" id="corporation_property" >
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(get_select_data('property'),$platform->getCorporationProperty())?>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">其他依托单位</label>
                                    <div class="col-sm-10">
                                        <table class="table table-bordered">
                                            <tbody>
                                            <tr>
                                                <td align="center">
                                                    单位名称
                                                </td>
                                                <td align="center">
                                                    单位性质
                                                </td>
                                                <td width="100" align="center">
                                                    操作
                                                </td>
                                            </tr>
                                            <?php
                                            $others = $platform->getCooperatationArray();
                                            if(empty($others)){
                                                $others[0] = [];
                                            }
                                            $i=0;
                                            foreach($others as $k=>$other):
                                                $i++;
                                                ?>
                                                <tr data-row="<?=$k?>">
                                                    <td>
                                                        <input class="form-control text-left" type="text" name="cooperatation_name[]" value="<?=$other['subject']?>">
                                                    </td>
                                                    <td>
                                                        <select class="form-control" name="cooperatation_property[]" >
                                                            <option value="">请选择</option>
                                                            <?=getSelectFromArray(get_select_data('property'),$other['property'])?>
                                                        </select>
                                                    </td>
                                                    <td width="100" style="text-align: center">
                                                        <?php
                                                        if($i==count($others)):
                                                            ?>
                                                            <button type="button" class="btn btn-primary add-row"><span class="fa fa-plus" aria-hidden="true"></span></button>
                                                        <?php
                                                        else:
                                                            ?>
                                                            <button type="button" class="btn btn-danger remove-row"><span class="fa fa-minus" aria-hidden="true"></span></button>
                                                        <?php
                                                        endif;
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php
                                            endforeach;
                                            ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">上级主管部门</label>
                                    <div class="col-sm-10">
                                        <select id="department_id_sel"  class="form-control selectpicker" data-live-search="true">
                                            <option value="">--请选择上级主管部门--</option>
                                            <?=getDepartmentList($platform->getDepartmentId(),'9,11,12,13,413,653')?>
                                        </select>
                                        <input type="hidden" name="department_id" id="department_id" value="<?=$platform->getDepartmentId()?>" >
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">成立时间</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="radicate_year" class="form-control" value="<?=$platform->getRadicateYear()?>" required data-com="date" data-format="yyyy" />
                                        <small class="help-block">填写成立的年份，例如：2018。</small>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">类别</label>
                                    <div class="col-sm-10">
                                        <select name="data[type]" id="data" class="form-control" required>
                                            <?=getSelectFromArray(['学科类','企业类'],$platform->getData('type'))?>
                                        </select>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>

                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">学科分类</label>
                                    <div class="col-sm-10">
                                        <div class="cxselect" data-selects="subject_id1,subject_id2,subject_id3" data-url="<?= site_path('json/subjects.json') ?>" data-json-value="v" style="float: left">
                                            <select class="form-control w-auto custom-control-inline subject_id1" data-value="<?=substr($platform->getData('subject_id'),0,3)?>" name="subject_ids[subject_id1]"></select>
                                            <select class="form-control w-auto custom-control-inline subject_id2" name="subject_ids[subject_id2]" data-first-title="请选择" data-value="<?=substr($platform->getData('subject_id'),0,6)?>"></select>
                                            <select class="form-control w-auto custom-control-inline subject_id3" name="subject_ids[subject_id3]" data-first-title="请选择" data-value="<?=$platform->getData('subject_id')?>"></select>
                                        </div>
                                        <div class="clearfix"></div>
                                        <small class="help-block">请选择到最后一级。</small>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">前沿领域分类</label>
                                    <div class="col-sm-10">
                                        <select name="data[frontier]" id="frontier" class="form-control">
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(['信息','农业','制造','医药','工程','能源','数理','化学','生物','环境','海洋','天文与空间','地球科学','材料','综合交叉'],$platform->getData('frontier'))?>
                                        </select>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">所属产业</label>
                                    <div class="col-sm-10">
                                        <select name="data[industry]" class="form-control">
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(['农林业','水利','煤炭','电力','新能源','核能','石油、天然气','钢铁','有色金属','黄金','石化化工','建材','医药','机械','城市轨道交通装备','汽车','船舶','航空航天','轻工','纺织','建筑','城镇基础设施','铁路','公路及道路运输（含城市客运）','水运','航空运输','综合交通运输','信息产业','现代物流业','金融服务业','科技服务业','商务服务业','商贸服务业','旅游业','邮政业','教育','卫生健康','文化','体育','养老与托育服务','家政','其他服务业','环境保护与资源节约综合利用','公共安全与应急产品','民爆产品','人力资源和人力资本服务业','人工智能','烟草','消防','采矿','其他'],$platform->getData('industry'))?>
                                        </select>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label for="code" class="col-sm-2 col-form-label">产业领域</label>
                                    <div class="col-sm-10">
                                        <select name="industry" id="industry" class="form-control">
                                            <option value="">请选择</option>
                                            <?= getSelectFromArray(get_select_data('industry'), $platform->getIndustry()) ?>
                                        </select>
                                        <input type="text" name="industry_other" id="industry_other" class="form-control" placeholder="其他请注明" style="<?=$platform->getIndustry()=='其他'?'':'display: none'?>" value="<?=$platform->getIndustryOther()?>">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">是否厅市（州）共建</label>
                                    <div class="col-sm-10">
                                        <select name="data[build_together]" id="build_together"  class="form-control" required>
                                            <?=getSelectFromArray(['否','是'],$platform->getData('build_together'))?>
                                        </select>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">联系人姓名</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="linkman_name" class="form-control" required value="<?=$platform->getLinkmanName()?>" />
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">联系人证件类型</label>
                                    <div class="col-sm-10">
                                        <select name="data[linkman_idcard_type]" id="linkman_idcard_type" class="form-control col-sm-5">
                                            <?=getSelectFromArray(['身份证','护照号','其他'],$platform->getData('linkman_idcard_type'))?>
                                        </select>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">联系人证件号码</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="data[linkman_idcard]" class="form-control" required value="<?=$platform->getData('linkman_idcard')?>" />
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">联系人手机</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="linkman_mobile" class="form-control" required value="<?=$platform->getLinkmanMobile()?>" />
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">联系人邮箱</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="data[linkman_email]" class="form-control" required value="<?=$platform->getData('linkman_email')?>" />
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">联系人固定电话</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="data[linkman_tel]" class="form-control" required value="<?=$platform->getData('linkman_tel')?>" />
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">第一依托单位地址</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="address" class="form-control" value="<?=$platform->getAddress()?>" />
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="form-group row">
                                    <label for="area_code" class="col-sm-2 col-form-label">所属地区</label>
                                    <div class="col-sm-9">
                                        <div class="cxselect" data-selects="area_code1,area_code2,area_code3" data-url="<?= site_path('json/region.json') ?>?v=1" data-json-value="v" style="float: left">
                                            <select class="form-control w-auto custom-control-inline area_code area_code1" name="area_code1" data-value="<?=$platform->getAreaCode()?(substr($platform->getAreaCode(),0,2).'0000'):510000?>" required></select>
                                            <select class="form-control w-auto custom-control-inline area_code area_code2" data-first-title="请选择" data-value="<?=$platform->getAreaCode()?(substr($platform->getAreaCode(),0,4).'00'):''?>" required></select>
                                            <select class="form-control w-auto custom-control-inline area_code area_code3" data-first-title="请选择" data-value="<?=$platform->getAreaCode()?:''?>" required></select>
                                        </div>
                                        <input type="hidden" name="area_code" value="<?=$platform->getAreaCode()?>">
                                        <div style="clear: both"></div>
                                        <small class="help-block">请选择到最后一级。</small>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label">研究方向</label>
                                    <div class="col-sm-10">
                                        <table class="table table-bordered">
                                            <tbody>
                                            <tr>
                                                <td>
                                                    <p align="left">名称</p>
                                                </td>
                                                <td width="100">
                                                    <p align="center">操作</p>
                                                </td>
                                            </tr>
                                            <?php
                                            $directions = $platform->getResearchDirectionArray();
                                            if(empty($directions)){
                                                $directions[0] = '';
                                            }
                                            $i=0;
                                            foreach($directions as $k=>$value):
                                                $i++;
                                                ?>
                                                <tr data-row="<?=$k?>">
                                                    <td>
                                                        <input class="form-control text-left" type="text" name="research_direction[]" value="<?=$value?>">
                                                    </td>
                                                    <td width="100" style="text-align: center">
                                                        <?php
                                                        if($i==count($directions)):
                                                            ?>
                                                            <button type="button" class="btn btn-primary add-row"><span class="fa fa-plus" aria-hidden="true"></span></button>
                                                        <?php
                                                        else:
                                                            ?>
                                                            <button type="button" class="btn btn-danger remove-row"><span class="fa fa-minus" aria-hidden="true"></span></button>
                                                        <?php
                                                        endif;
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php
                                            endforeach;
                                            ?>
                                            </tbody>
                                        </table>
                                        <small class="help-block">研究方向最多可以填5个</small>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="clearfix"></div>

                                <div class="form-group row">
                                    <div style="width:200px;margin: 0 auto">
                                        <?=Button::setType('submit')->setClass('btn-alt-primary btn-loading')->setDatas(['loading-text' => '保存中...'])->setIcon('save')->button('保存资料')?>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script type="text/javascript">
    $(document).on('click','.add-row',function(){
        //克隆行
        var rows = $(this).parents('#activeForm').find('other_corporations');
        var tr = $(this).parents('tr').clone();
        var line = rows.length;
        var row = tr.data('row');
        //替换行id
        var reg = '/\\['+row+'\\]/ig';
        var new_tr = tr.html().replace(eval(reg), '['+line+']');
        new_tr = "<tr data-row='"+line+"'>"+new_tr+"</tr>";
        //追加至表格
        $(this).parents('tbody').append(new_tr);
        //按钮变为减号
        $(this).removeClass('add-row').removeClass('btn-primary').addClass('remove-row').addClass('btn-danger');
        $(this).find('span').removeClass('fa-plus').addClass('fa-minus');
    });

    $(document).on('click','.remove-row',function(){
        $(this).parents('tr').remove();
    });

    $('.cxselect').cxSelect();


    $(function (){
        $(".area_code").change(function (){
            var code = $(this).val();
            $("input[name='area_code']").val(code);
        });
        $("#industry").change(function (){
            if($(this).val()=='其他'){
                $("#industry_other").show();
                $("#industry_other").focus();
            }else{
                $("#industry_other").hide();
            }
        });
    });
</script>