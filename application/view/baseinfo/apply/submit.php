<div class="page-header">
    <div class="btn-group btn-group-sm" role="group"> <a href="javascript:history.back();" class="btn btn-info"><i class="ace-icon fa fa-arrow-left"></i> 返回</a>&nbsp;<a href="<?=site_url("baseinfo/apply/edit/id/".$base->getLabId())?>" class="btn btn-info"><i class="ace-icon glyphicon glyphicon-pencil"></i> 编辑</a>
    </div>
</div>
<div class="page-body">
    <div class="alert alert-warning" role="alert">
        <h4>温馨提示：</h4>
        <ul>
            <li>上报前请仔细校对基本信息，一经上报将不可更改。</li>
        </ul>
    </div>
    <?php if(count($msg)):?>
        <div class="alert alert-danger" role="alert">
            <h4>系统检查到有如下错误，请检查并修改：</h4>
            <ul>
                <?php for($i=0,$n=count($msg);$i<$n;$i++):?>
                    <li>
                        <?=($i+1)?>
                        、
                        <?=$msg[$i]?>
                    </li>
                <?php endfor;?>
            </ul>
        </div>
    <?php endif;?>
    <div class="alert alert-warning" role="alert">
        <form name="up" id="up" method="post" action="">
            <?php if(count($msg)): ?>
                <button type="button" class="btn btn-danger" disabled="disabled" ><i class="ace-icon glyphicon glyphicon-ban-circle"></i> 基本信息有误，请返回修改后再上报！</button>
            <?php else:?>
                <button type="submit" class="btn btn-warning" onclick="return confirm('一经上报将不可更改，您确定要上报吗？')"><i class="ace-icon glyphicon glyphicon-send"></i> 上报资料</button>
            <?php endif;?>
            <input type="hidden" name="id" value="<?=$base->getLabId()?>">
        </form>
    </div>
</div>