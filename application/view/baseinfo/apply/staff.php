<script language="javascript" type="text/javascript">
    $(function(){
        //选择职称级别
        $(document).on("change",".title_rank",function(){
            var id= $(this).val();
            var me= $(this);
            if(id==0){
                me.parent().find('.title').html('<option value="0">==请选择==</option>');
                return;
            }
            var url = baseurl+'common/HonorAjax';
            ajaxData(url,{id:id},'html',function(){},function(data){
                me.parent().find('.title').html(data);
            });
        });

    });

</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            平台基本资料
        </h2>
        <div>
            <?=$htmlStr?>
            <?php
            if(input::session('userlevel')!=5):
                ?>
                <?=btn('返回','javascript:history.go(-1);','fa fa-chevron-left','info')?>
            <?php endif;?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):$method = $method=='index'?'base':$method;?>
                        <li class="nav-item" class="tab-pane">
                            <a class="<?php if($tab['method'] == $method)echo 'nav-link active';else echo 'nav-link'; ?>" href="<?=$tab['url']?>"><?=$tab['text']?></a>
                        </li>
                        <?php $i++;endforeach;?>
                </ul>
                <div class="block-content tab-content block-content-full">
                    <div class="tab-content">
                        <div role="tabpanel" class="tab-pane p-3 active">
                            <?php
                            $allowCount = $platform->getCatId()==271 ? 20 : 30;
                            ?>
                            <?php
                            if($platform->getCatId()==271):
                                ?>
                                <div class="header no-margin-top">固定人员信息</div>
                                <div align="center">
                                    <?php
                                    $directions = $platform->getResearchDirectionArray();
                                    $directionNo = 0;
                                    $startNo = 0;
                                    foreach ($directions as $directionkey=>$direction):
                                        $directionNo++;
                                        $staffs = $platform->getStaffByResearch($direction);
                                        ?>
                                        <table class="table table-bordered table-sm">
                                            <tbody>
                                            <tr>
                                                <td colspan="15">
                                                    <p align="left"><strong>研究方向<?=$directionNo?> - <?=$direction?></strong></p>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="100">
                                                    <p align="center"></p>
                                                </td>
                                                <td>
                                                    <p align="center">姓名</p>
                                                </td>
                                                <td>
                                                    <p align="center">证件号码</p>
                                                </td>
                                                <td>
                                                    <p align="center">性别</p>
                                                </td>
                                                <td>
                                                    <p align="center">出生年月</p>
                                                </td>
                                                <td>
                                                    <p align="center">职务/职称</p>
                                                </td>
                                                <td>
                                                    <p align="center">学历</p>
                                                </td>
                                                <td>
                                                    <p align="center">学位</p>
                                                </td>
                                                <td>
                                                    <p align="center">荣誉称号</p>
                                                </td>
                                                <td>
                                                    <p align="center">专业</p>
                                                </td>
                                                <td>
                                                    <p align="center">工作单位</p>
                                                </td>
                                                <td>
                                                    <p align="center">研究领域</p>
                                                </td>
                                                <td width="120px">
                                                    <p align="center">操作</p>
                                                </td>
                                            </tr>
                                            <?php
                                            $j=0;
                                            while($staff = $staffs->getObject()):
                                                ?>
                                                <tr>
                                                    <td class="text-center">
                                                        <?=$staff->getType()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getUserName()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getIdcard()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getUserSex()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getUserBirthday()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getUserDuty()?> / <?=$staff->getUserTitle()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getEducation()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getDegree()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getHonor()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getMajor()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getCorporationName()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getResearchArea()?>
                                                    </td>
                                                    <td align="center">
                                                        <a href="javascript:void(0);" onclick="return showWindow('编辑','<?=site_url('platform/profile/staff_edit/id/'.$staff->getPlatformId().'/pid/'.$staff->getId())?>',600,460)" class="btn btn-primary btn-sm" role="button">编辑</a>
                                                        <a href="<?=site_url('platform/profile/staff_delete/id/'.$staff->getPlatformId().'/pid/'.$staff->getId())?>" onclick="return confirm('确定要删除吗？')" class="btn btn-danger btn-sm">删除</a>
                                                    </td>
                                                </tr>
                                                <?php
                                                $j++;
                                            endwhile;
                                            ?>
                                            </tbody>
                                        </table>
                                        <?=Button::setUrl(site_url('platform/profile/staff_edit/id/'.$platform->getPlatformId().'/direction_key/'.$directionkey))->setIcon('add')->window('添加人员')?>
                                        <br>
                                        <br>
                                    <?php
                                    endforeach;
                                    ?>
                                    <?php
                                    $staffs = $platform->getStaffByNoResearch();
                                    if($staffs!==false && $staffs->getTotal()>0):
                                        ?>
                                        <table class="table table-bordered">
                                            <tbody>
                                            <tr>
                                                <td colspan="15">
                                                    <p align="left"><strong style="color:red">未对应研究方向的人员（请点击编辑选择对应的研究方向）：</strong></p>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="100">
                                                    <p align="center"></p>
                                                </td>
                                                <td>
                                                    <p align="center">姓名</p>
                                                </td>
                                                <td>
                                                    <p align="center">证件号码</p>
                                                </td>
                                                <td>
                                                    <p align="center">性别</p>
                                                </td>
                                                <td>
                                                    <p align="center">出生年月</p>
                                                </td>
                                                <td>
                                                    <p align="center">职务/职称</p>
                                                </td>
                                                <td>
                                                    <p align="center">学历</p>
                                                </td>
                                                <td>
                                                    <p align="center">学位</p>
                                                </td>
                                                <td>
                                                    <p align="center">荣誉称号</p>
                                                </td>
                                                <td>
                                                    <p align="center">专业</p>
                                                </td>
                                                <td>
                                                    <p align="center">工作单位</p>
                                                </td>
                                                <td>
                                                    <p align="center">研究领域</p>
                                                </td>
                                                <td width="120px">
                                                    <p align="center">操作</p>
                                                </td>
                                            </tr>
                                            <?php
                                            while($staff = $staffs->getObject()):
                                                ?>
                                                <tr>
                                                    <td class="text-center">
                                                        <?=$staff->getType()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getUserName()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getIdcard()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getUserSex()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getUserBirthday()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getUserDuty()?> / <?=$staff->getUserTitle()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getEducation()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getDegree()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getHonor()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getMajor()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getCorporationName()?>
                                                    </td>
                                                    <td align="center">
                                                        <?=$staff->getResearchArea()?>
                                                    </td>
                                                    <td align="center">
                                                        <a href="javascript:void(0);" onclick="return showWindow('编辑','<?=site_url('baseinfo/apply/personnel_edit/id/'.$staff->getPlatformId().'/pid/'.$staff->getId())?>',600,460)" class="btn btn-primary btn-sm" role="button">编辑</a>
                                                        <a href="<?=site_url('baseinfo/apply/personnel_delete/id/'.$staff->getPlatformId().'/pid/'.$staff->getId())?>" onclick="return confirm('确定要删除吗？')" class="btn btn-danger btn-sm">删除</a>
                                                    </td>
                                                </tr>
                                                <?php
                                                $j++;
                                            endwhile;
                                            ?>
                                            </tbody>
                                        </table>
                                        <br>
                                        <br>
                                    <?php endif;?>
                                </div>
                            <?php
                            else:
                                ?>
                                <div class="header no-margin-top">固定人员信息（已录入<?=$platform->getStaffCount()?>人，还可以录入<?=($allowCount-$platform->getStaffCount())?>人）</div>
                                <div align="center" style="overflow:scroll;">
                                    <?php
                                    $staffs = $platform->getStaffs()->toArray();
                                    ?>
                                    <table class="table table-bordered" style="width:3000px;text-align:center">
                                        <tbody>
                                        <tr>
                                            <td width="100">
                                                <p align="center"></p>
                                            </td>
                                            <td width="150">
                                                <p align="center">姓名</p>
                                            </td>
                                            <td width="150">
                                                <p align="center">身份证</p>
                                            </td>
                                            <td width="100">
                                                <p align="center">性别</p>
                                            </td>
                                            <td width="150">
                                                <p align="center">出生年月</p>
                                            </td>
                                            <td width="150">
                                                <p align="center">职务</p>
                                            </td>
                                            <td width="350">
                                                <p align="center">职称</p>
                                            </td>
                                            <td width="100">
                                                <p align="center">学历</p>
                                            </td>
                                            <td width="100">
                                                <p align="center">学位</p>
                                            </td>
                                            <td width="300">
                                                <p align="center">荣誉称号</p>
                                            </td>
                                            <td width="200">
                                                <p align="center">专业</p>
                                            </td>
                                            <td width="200">
                                                <p align="center">工作单位</p>
                                            </td>
                                            <td width="300">
                                                <p align="center">研究领域</p>
                                            </td>
                                            <td width="300">
                                                <p align="center">研究方向</p>
                                            </td>
                                            <td >
                                                <p align="center">备注</p>
                                            </td>
                                        </tr>
                                        <?php
                                        $j=0;
                                        for($i=0;$i<30;$i++):
                                            ?>
                                            <tr>
                                                <td class="text-center">
                                                    <?php
                                                    if(empty($staffs[$i]['type'])) $staffs[$i]['type']='成员';
                                                    ?>
                                                    <select name="direction[<?=$i?>][type]">
                                                        <?=getSelectFromArray(['主任','副主任','成员'],$staffs[$i]['type'])?>
                                                    </select>
                                                    <input type="hidden" name="direction[<?=$i?>][id]" value="<?=$staffs[$i]['id']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="direction[<?=$i?>][user_name]" value="<?=$staffs[$i]['user_name']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="direction[<?=$i?>][idcard]" value="<?=$staffs[$i]['idcard']?>">
                                                </td>
                                                <td>
                                                    <select class="form-control" name="direction[<?=$i?>][user_sex]">
                                                        <?=getSelectFromArray(['男','女'],$staffs[$i]['user_sex'])?>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="direction[<?=$i?>][user_birthday]" value="<?=$staffs[$i]['user_birthday']?>" onclick="WdatePicker({startDate:'1970-01-01',dateFmt:'yyyy-MM'})">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="direction[<?=$i?>][user_duty]" value="<?=$staffs[$i]['user_duty']?>">
                                                </td>
                                                <td style="text-align: left">
                                                    <select name="direction[<?=$i?>][title_rank]" class="title_rank" required>
                                                        <?php $data=['0'=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'其他']?>
                                                        <?=getSelectFromArray($data,$staffs[$i]['title_rank'],false)?>
                                                    </select>
                                                    <select name="direction[<?=$i?>][user_title]" class="title" required>
                                                        <?=getOptionByParent($staffs[$i]['title_rank'],$staffs[$i]['user_title'])?>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select name="direction[<?=$i?>][education]" id="education">
                                                        <option value="">请选择</option>
                                                        <?=getSelectFromArray(get_select_data('education'),$staffs[$i]['education'])?>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select name="direction[<?=$i?>][degree]" id="degree">
                                                        <option value="">请选择</option>
                                                        <?=getSelectFromArray(['学士','硕士','博士','其他'],$staffs[$i]['degree'])?>
                                                    </select>
                                                </td>

                                                <td>
                                                    <input class="form-control" type="text" name="direction[<?=$i?>][honor]" value="<?=$staffs[$i]['honor']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="direction[<?=$i?>][major]" value="<?=$staffs[$i]['major']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="direction[<?=$i?>][corporation_name]" value="<?=$staffs[$i]['corporation_name']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="direction[<?=$i?>][research_area]" value="<?=$staffs[$i]['research_area']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="direction[<?=$i?>][research_direction]" value="<?=$staffs[$i]['research_direction']?>">
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="direction[<?=$i?>][note]" value="<?=$staffs[$i]['note']?>">
                                                </td>
                                            </tr>
                                            <?php
                                            $j++;
                                        endfor;
                                        ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php
                            endif;
                            ?>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>