<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                等待分组的项目
            </h3>
        </div>
        <div class="block-content">
            <div class="main">
              <div class="tools" style="display: none">
                <?=btn('back')?>
                <?=btn('window','自动分组',site_url("complete/grouper/config_list"),'group')?>
                <p style="clear:both;"></p>
              </div>
              <div class="search">
                <?php include_once('search_part.php') ?>
              </div>
              <?php if($pager->getTotal()):?>
                <div class="box">
                  <form id="validateForm" name="validateForm" method="post" action="<?=site_url("complete/group/doGroup/type_id/".$type_id)?>">
                    <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                        <thead>
                      <tr>
                        <th width="50"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                        <th width="50">详</th>
                        <th width="30%"><?=getColumnStr('项目名称','subject')?></th>
                        <th><?=getColumnStr('项目负责人','user_id')?></th>
                        <th width="20%"><?=getColumnStr('申报单位','corporation_id')?></th>
                        <th class="text-center">主管部门</th>
                        <th class="text-center">立项年度</th>
                      </tr>
                        </thead>
                        <tbody>
                      <tr>
                        <td colspan="13" align="left" class="note">组号：（<?=$group_name_part1?><input name="group_name_part1" type="hidden" id="group_name_part1" value="<?=$group_name_part1?>" />-<input name="group_name_part2" id="group_name_part2" size="3" maxlength="3" class="number form-control w-auto custom-control-inline" title="请输入3位数字" value="<?=$group_name_part2?>"/>）
                          备注：
                          <input class="form-control w-auto custom-control-inline" name="group_note" id="group_note" size="15" value="<?=$group_note?>"/>
                          <label>
                            <select name="group_id" id="group_id" class="form-control w-auto custom-control-inline">
                              <option value="0">选择已有的分组</option>
                              <?php while($group = $groups->getObject()):?>
                                <option value="<?=$group->getId()?>"><?=$group->getGroupSubject()?>(<?=$group->getGroupNote(20)?>)</option>
                              <?php endwhile;?>
                            </select>
                          </label>
                            <?=Button::setType('submit')->setName('确定分组')->setEvent('return confirm(\'确定要这样分组吗?\')')->button()?>
                          <input name="type_id" type="hidden" id="type_id" value="<?=$type_id?>" />
                          <input name="guide_id" type="hidden" id="guide_id" value="<?=intval(input::get('guide_id'))?>" />
                        </td>
                      </tr>
                      <?php while($project = $pager->getObject()):?>
                        <tr>
                          <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
                           <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                          <td><?=$project->getMark()?><?=link_to("complete/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">平台级别：<?=$project->getLevel()?><br>立项年度：<?=$project->getDeclareYear()?><br>所属专科：<?=$project->getProject()->getSubjectName()?><br>平台类别：<?=$project->getCatSubject()?></p></td>
                          <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                          <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
                          <td align="center">
                              <?=$project->getDepartmentName()?>
                          </td>
                          <td align="center">
                              <?=$project->getDeclareYear()?>
                          </td>
                        </tr>
                          <tr class="fold_body">
                          <td colspan="15">
                            <?php include('more_part.php') ?>
                          </td>
                        </tr>
                      <?php endwhile;?>
                        </tbody>
                        <tfoot>
                      <tr>
                        <td colspan="15" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
                      </tr>
                        </tfoot>
                    </table>
                  </form>
                </div>
              <?php else:?>
                <div class="box">
                  <p class="note">暂无符合条件的项目，请选择上面搜索条件重新搜索定位。</p>
                </div>
              <?php endif;?>
            </div>
        </div>
    </div>
</div>
