<div class="row">
    <?php if(count($warning)):?>
        <div class="col-12">
            <div class="alert alert-warning alert-dismissable" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                <h3 class="alert-heading font-size-h4 my-2">
                    提示
                </h3>
                <?php for($i=0,$n=count($warning);$i<$n;$i++):?>
                    <p class="mb-0">
                        <?=$warning[$i]?>
                    </p>
                <?php endfor;?>
            </div>
        </div>
    <?php
    endif;
    ?>
    <div class="col-lg-7 col-md-12">

        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    通知公告
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <?php
                $articles = sf::getModel('Articles')->selectAll("is_release = 1 and user_group_id like '%|5|%'","order by created_at desc",10);
                ?>
                <table class="table table-hover table-myprojects">
                    <tr>
                        <th style="width: 50%">标题</th>
                        <th style="width: 10%">发布时间</th>
                    </tr>
                    <?php
                    while($article = $articles->getObject()):
                        ?>
                        <tr>
                            <td>
                                <div>
                                    <span><a href="<?=site_url('html/'.$article->getId().'.html')?>"><?=$article->getSubject()?></a></span>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span><?=$article->getCreatedAt('Y/m/d')?></span>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile;
                    if($articles->getTotal()==0):
                        ?>
                        <tr>
                            <td colspan="2" class="text-center">无记录</td>
                        </tr>
                    <?php endif;?>
                </table>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    帐号登录日志
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <table class="table table-hover table-myprojects">
                    <tr>
                        <th style="width: 50%">内容</th>
                        <th style="width: 25%">时间</th>
                        <th style="width: 25%">地点</th>
                    </tr>
                    <?php  $logs = sf::getModel('Logs')->getLogs(input::session('userid'),5);
                    while($log = $logs->getObject()):?>
                        <tr>
                            <td>
                                <div>
                                    <span><?=$log->getContent()?></span>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span><?=$log->getUpdatedAt()?></span>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span><?=$log->getUserIp()?></span>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile;?>
                    <?php
                    if($logs->getTotal()==0):?>
                        <tr>
                            <td colspan="3" class="text-center">无记录</td>
                        </tr>
                    <?php
                    endif;
                    ?>
                </table>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    用户操作指南
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <table class="table table-hover table-myprojects">
                    <tr>
                        <td>
                            <div>
                                <span>平台考核资料填写上报后还需要依托单位、主管部门审核；</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>
                                <span>如果您的考核报告被退回，请认真阅读退回意见并进行修改、重新上报；</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>
                                <span>修改平台考核报告步骤：依次点击【考核管理】=>【年度考核管理】=>【填写中的年度考核】。</span>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="col-lg-5 col-sm-12">
        <div class="block">
            <div class="block-content block-content-full">
                <div class="user-center"  style="padding: 0">
                    <div class="user-avatar item item-circle bg-body-light float-left">
                        <i class="fa fa-user"></i>
                    </div>
                    <div class="ml-7 text-left">
                        <p class="font-w600 mb-0">
                            <?=$user->getSubject()?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            登录账号：<?=$user->getUser()->getUserName()?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            当前身份：<?=input::getInput("session.groupname")?><a class="ml-3" href="<?=site_url('manager/choose')?>" >切换角色</a>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            平台管理员：<?=$user->getManagerNames()?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            平台状态：<b class="<?=$user->getStatement()==0?'text-success':'text-xplay'?>"><?=$user->getStatement()==0?'已授牌':'申请中'?></b>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            上次登录时间：<?=input::getInput("session.lastlogin")?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            上次登录IP：<?=input::getInput("session.userip")?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    待办事项
                </h3>
            </div>
            <div class="block-content block-content-full todolist">
                <div class="row">

                </div>
            </div>
        </div>
        <?php include('contact.php')?>
    </div>
</div>
