<div class="row">
    <?php if(count($warning)):?>
        <div class="col-12">
            <div class="alert alert-warning alert-dismissable" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                <h3 class="alert-heading font-size-h4 my-2">
                    提示
                </h3>
                <?php for($i=0,$n=count($warning);$i<$n;$i++):?>
                    <p class="mb-0">
                        <?=$warning[$i]?>
                    </p>
                <?php endfor;?>
            </div>
        </div>
    <?php
    endif;
    ?>
    <div class="col-lg-7 col-md-12">

        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    通知公告
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <?php
                $articles = sf::getModel('Articles')->selectAll("is_release = 1 and user_group_id like '%|4|%'","order by created_at desc",10);
                ?>
                <table class="table table-hover table-myprojects">
                    <tr>
                        <th style="width: 50%">标题</th>
                        <th style="width: 10%">发布时间</th>
                    </tr>
                    <?php
                    while($article = $articles->getObject()):
                        ?>
                        <tr>
                            <td>
                                <div>
                                    <span><a href="<?=site_url('html/'.$article->getId().'.html')?>"><?=$article->getSubject()?></a></span>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span><?=$article->getCreatedAt('Y/m/d')?></span>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile;
                    if($articles->getTotal()==0):
                        ?>
                        <tr>
                            <td colspan="2" class="text-center">无记录</td>
                        </tr>
                    <?php endif;?>
                </table>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    帐号登录日志
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <table class="table table-hover table-myprojects">
                    <tr>
                        <th style="width: 50%">内容</th>
                        <th style="width: 25%">时间</th>
                        <th style="width: 25%">地点</th>
                    </tr>
                    <?php  $logs = sf::getModel('Logs')->getLogs(input::session('userid'),5);
                    while($log = $logs->getObject()):?>
                        <tr>
                            <td>
                                <div>
                                    <span><?=$log->getContent()?></span>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span><?=$log->getUpdatedAt()?></span>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span><?=$log->getUserIp()?></span>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile;?>
                    <?php
                    if($logs->getTotal()==0):?>
                        <tr>
                            <td colspan="3" class="text-center">无记录</td>
                        </tr>
                    <?php
                    endif;
                    ?>
                </table>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    用户操作指南
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <table class="table table-hover table-myprojects">
                    <tr>
                        <td>
                            <div>
                                <span>平台申报资料审核上报后还需要科技厅审核；</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>
                                <span>审核平台申报资料步骤：依次点击【申报管理】=>【申报审核管理】=>【待审核的申报书】。</span>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="col-lg-5 col-sm-12">
        <div class="block">
            <div class="block-content block-content-full">
                <div class="user-center"  style="padding: 0">
                    <div class="user-avatar item item-circle bg-body-light float-left">
                        <i class="fa fa-user"></i>
                    </div>
                    <div class="ml-7 text-left">
                        <p class="font-w600 mb-0">
                            <?=$user->getSubject()?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            登录账号：<?=$user->getUser()->getUserName()?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            当前身份：<?=input::getInput("session.groupname")?><a class="ml-3" href="<?=site_url('manager/choose')?>" >切换角色</a>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            上次登录时间：<?=input::getInput("session.lastlogin")?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            上次登录IP：<?=input::getInput("session.userip")?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    待办事项
                </h3>
            </div>
            <div class="block-content block-content-full todolist">
                <div class="row">
                    <?php
                    if($user->getWaitNum()):
                    ?>
                    <div class="col-6 col-md-6 col-xl-4">
                        <a class="block text-left" href="<?=site_url("gather/project/wait_list")?>" style="background-color: #f3f4f7">
                            <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                <div>
                                    <div class="font-w400 text-black text-left">
                                        待审申报书
                                    </div>
                                    <div class="font-w400 text-left mt-2 <?=$user->getWaitNum()?'text-danger':''?>" style="font-size: 18px;">
                                        <span><?=$user->getWaitNum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">个</em></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <?php endif;?>
                    <?php
                    if($user->getTaskWaitnum()):
                    ?>
                    <div class="col-6 col-md-6 col-xl-4">
                        <a class="block text-left" href="<?=site_url("gather/task/wait_list")?>" style="background-color: #f3f4f7">
                            <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                <div>
                                    <div class="font-w400 text-black text-left">
                                        待审任务书
                                    </div>
                                    <div class="font-w400 text-left mt-2 <?=$user->getTaskWaitnum()?'text-danger':''?>" style="font-size: 18px;">
                                        <span><?=$user->getTaskWaitnum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">个</em></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <?php endif;?>
                    <?php
                    if($user->getSummaryWaitnum()):
                        ?>
                        <div class="col-6 col-md-6 col-xl-4">
                            <a class="block text-left" href="<?=site_url("summary/department/wait_list")?>" style="background-color: #f3f4f7">
                                <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                    <div>
                                        <div class="font-w400 text-black text-left">
                                            待审年度考核
                                        </div>
                                        <div class="font-w400 text-left mt-2 <?=$user->getSummaryWaitnum()?'text-danger':''?>" style="font-size: 18px;">
                                            <span><?=$user->getSummaryWaitnum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">个</em></span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endif;?>
                    <?php
                    if($user->getCompleteWaitnum()):
                        ?>
                        <div class="col-6 col-md-6 col-xl-4">
                            <a class="block text-left" href="<?=site_url("unit/complete/wait_list")?>" style="background-color: #f3f4f7">
                                <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                    <div>
                                        <div class="font-w400 text-black text-left">
                                            待审验收书
                                        </div>
                                        <div class="font-w400 text-left mt-2 <?=$user->getCompleteWaitnum()?'text-danger':''?>" style="font-size: 18px;">
                                            <span><?=$user->getCompleteWaitnum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">个</em></span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endif;?>
                    <?php
                    if($user->getUnitWaitnum()):
                    ?>
                    <div class="col-6 col-md-6 col-xl-4">
                        <a class="block text-left" href="<?=site_url("gather/corporation/wait_list")?>" style="background-color: #f3f4f7">
                            <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                <div>
                                    <div class="font-w400 text-black text-left">
                                        待审申报单位
                                    </div>
                                    <div class="font-w400 text-left mt-2 <?=$user->getUnitWaitnum()?'text-danger':''?>" style="font-size: 18px;">
                                        <span><?=$user->getUnitWaitnum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">个</em></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <?php endif;?>
                    <?php
                    if($user->getChangeWaitnum()):
                        ?>
                        <div class="col-6 col-md-6 col-xl-4">
                            <a class="block text-left" href="<?=site_url("change/gather/wait_list")?>" style="background-color: #f3f4f7">
                                <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                    <div>
                                        <div class="font-w400 text-black text-left">
                                            待审变更申请
                                        </div>
                                        <div class="font-w400 text-left mt-2 <?=$user->getChangeWaitnum()?'text-danger':''?>" style="font-size: 18px;">
                                            <span><?=$user->getChangeWaitnum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">项</em></span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endif;?>
                </div>
            </div>
        </div>
        <?php include('contact.php')?>
    </div>
</div>
<!-- 预警提醒 -->
<div class="modal" id="modal-block" tabindex="-1" role="dialog" aria-labelledby="modal-block-large" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="block block-themed block-transparent mb-0">
                <div class="block-header bg-primary-dark">
                    <h3 class="block-title">
                        预警提醒
                    </h3>
                    <div class="block-options">
                        <button type="button" class="btn-block-option" data-dismiss="modal" aria-label="Close"><i class="fa fa-fw fa-times"></i></button>
                    </div>
                </div>
                <div class="block-content">
                    <p style="font-size: 20px">
                        你有<b class="text-danger"><?=$user->getCityWaitNum()?></b>个市、县级项目还处于待立项状态，为避免对2024年省临床重点专科项目遴选造成影响，请对市、县级项目进行审核和立项受理。凡上报项目与立项文件不符的，一经发现按照弄虚作假处理。
                    </p>
                </div>
                <div class="block-content block-content-full text-right bg-light">
                    <button type="button" class="btn btn-sm btn-light" data-dismiss="modal">关闭</button>
                    <?=Button::setUrl(site_url('gather/radicate/wait_list'))->setClass('btn-primary')->link('去处理')?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
    if(false && $user->getCityWaitNum()>0):
?>
<script type="text/javascript">
    window.onload = function (){
        $('#modal-block').modal('show');
    }
</script>
<?php endif;?>
