<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title><?=config::get("site_name")?></title>
    <link rel="shortcut icon" href="<?=site_path('assets/media/favicons/favicon.png')?>">
    <link rel="icon" type="image/png" sizes="192x192" href="<?=site_path('assets/media/favicons/favicon-192x192.png')?>">
    <link rel="apple-touch-icon" sizes="180x180" href="<?=site_path('assets/media/favicons/apple-touch-icon-180x180.png')?>">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap">
    <link rel="stylesheet" id="css-main" href="<?=site_path('assets/css/compress.min.css')?>?v=<?=config::get('css.main_version')?>">
    <script src="<?=site_path('assets/js/compress.core.min.js')?>"></script>
    <script src="<?=site_path('assets/js/compress.app.min.js')?>"></script>
    <style>
        .user-avatar {
            display: block;
            flex: 0 0 64px;
            height: 64px;
            font-size: 0;
            margin-right: 24px;
            background-image: url(data:image/svg+xml;base64,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);
            background-size: cover;
        }

        .text-success {
            color: #0abf5b !important;
        }

        .text-danger {
            color: #ff7200 !important;
        }

        .display-5 {
            font-size: 2.5rem;
            font-weight: 300;
            line-height: 1.25;
        }
        .table-myprojects{
            table-layout: fixed;
            word-break: break-all;
        }
        .table-myprojects th{
            font-size: 14px;
            color:rgba(0,0,0,.4);
        }
        .table-myprojects td{
            font-size: 14px;
        }
        .table-myprojects td div{
            display: inline-block;vertical-align: middle;width: 100%;padding: 0;word-wrap: break-word;word-break: break-word;
        }
        .table-myprojects td div span{
            display: inline-block;vertical-align: middle;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;max-width: 100%;
        }

        .dropdown-menu{
            display: none !important;
        }
        .dropdown-menu.show{
            display: block !important;
        }
    </style>
</head>
<body >
<input type="hidden" id="baseUrl" value="<?=site_url('/')?>" />
<div id="page-container" class="sidebar-o enable-page-overlay side-scroll page-header-fixed">
    <!-- Side Overlay-->
    <!-- Sidebar -->
    <?= view::part('layouts/page_sidebar')?>
    <!-- END Sidebar -->
    <!-- Header -->
    <?= view::part('layouts/page_header')?>
    <!-- END Header -->
    <!-- Main Container -->
    <main id="main-container">
        <div class="content">
            <?=$inc_body?>
        </div>
    </main>
    <!-- END Main Container -->
    <!-- Footer -->
    <?= view::part('layouts/page_footer')?>
    <!-- END Footer -->
</div>
<script src="<?=site_path('js/jquery.cookie.min.js') ?>"></script>
<script src="<?=site_path('js/lay/layer/layer.js') ?>"></script>
<script src="<?=site_path('js/func.js') ?>?v=<?=config::get('js.func_version')?>"></script>
<script src="<?=site_path('js/common.js') ?>?v=<?=config::get('js.common_version')?>"></script>
<script>
    var html = `
<div class="ant-empty ant-empty-normal" style="margin:32px auto;">
                <div class="ant-empty-image">
                    <svg class="ant-empty-img-simple" width="64" height="41" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg">
                        <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
                            <ellipse class="ant-empty-img-simple-ellipse" cx="32" cy="33" rx="32" ry="7"></ellipse>
                            <g class="ant-empty-img-simple-g" fill-rule="nonzero">
                                <path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path>
                                <path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" class="ant-empty-img-simple-path"></path>
                            </g>
                        </g>
                    </svg>
                </div>
                <div class="ant-empty-description">暂无数据</div>
</div>`;
    $(function () {
        if ($('.todolist .row div').length==0) {
            $('.todolist .row').html(html);
        }
    });
</script>
</body>
</html>