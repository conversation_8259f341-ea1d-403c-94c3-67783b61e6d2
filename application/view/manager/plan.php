<div class="row">
    <?php if(count($warning)):?>
        <div class="col-12">
            <div class="alert alert-warning alert-dismissable" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                <h3 class="alert-heading font-size-h4 my-2">
                    提示
                </h3>
                <?php for($i=0,$n=count($warning);$i<$n;$i++):?>
                    <p class="mb-0">
                        <?=$warning[$i]?>
                    </p>
                <?php endfor;?>
            </div>
        </div>
    <?php
    endif;
    ?>
    <div class="col-lg-7 col-md-12 <?=in_array(input::session('username'),['szx'])?'d-none':''?>">
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    账号统计
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <div class="row">
                    <div class="col-lg-6 col-md-6">
                        <a class="block block-rounded" href="<?=site_url('admin/declarer/index')?>">
                            <div class="block-content block-content-full">
                                <div class="item item-circle bg-body-light float-left">
                                    <i class="fa fa-user"></i>
                                </div>
                                <div class="mt-2 text-left" style="margin-left:5rem">
                                    <p class="font-w600 mb-0">
                                        平台申报人
                                    </p>
                                    <p class="font-size-sm text-muted mb-0">
                                        已注册：<?=$user->getResearcherNum()?>人，已认证：<?=$user->getVerifiedResearcherNum()?>人
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <a class="block block-rounded" href="<?=site_url('admin/expert/index')?>">
                            <div class="block-content block-content-full">
                                <div class="item item-circle bg-body-light float-left">
                                    <i class="fa fa-user-secret"></i>
                                </div>
                                <div class="mt-1 text-left" style="margin-left:5rem">
                                    <p class="font-w600 mb-0">
                                        评审专家
                                    </p>
                                    <p class="font-size-sm text-muted mb-0">
                                        已注册：<?=$user->getExpertNum()?>人，已认证：<?=$user->getVerifiedExpertNum()?>人
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <a class="block block-rounded" href="<?=site_url('admin/corporation/index')?>">
                            <div class="block-content block-content-full">
                                <div class="item item-circle bg-body-light float-left">
                                    <i class="fa fa-building"></i>
                                </div>
                                <div class="mt-1 text-left" style="margin-left:5rem">
                                    <p class="font-w600 mb-0">
                                        依托单位
                                    </p>
                                    <p class="font-size-sm text-muted mb-0">
                                        已注册：<?=$user->getCompanyNum()?>家，已认证：<?=$user->getVerifiedCompanyNum()?>家
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <a class="block block-rounded" href="<?=site_url('admin/department/index')?>">
                            <div class="block-content block-content-full">
                                <div class="item item-circle bg-body-light float-left">
                                    <i class="fa fa-university "></i>
                                </div>
                                <div class="mt-1 text-left" style="margin-left:5rem">
                                    <p class="font-w600 mb-0">
                                        主管部门
                                    </p>
                                    <p class="font-size-sm text-muted mb-0">
                                        <?=$user->getDepartmentNum()?>家
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    平台统计
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <div class="row">
                    <div class="col-lg-6 col-md-6">
                        <a class="block block-rounded" href="<?=site_url('office/search/index')?>">
                            <div class="block-content block-content-full">
                                <div class="item item-circle bg-body-light float-left">
                                    <i class="fa fa-cube"></i>
                                </div>
                                <div class="mt-2 text-left" style="margin-left:5rem">
                                    <p class="font-w600 mb-0">
                                        国家级平台
                                    </p>
                                    <p class="font-size-sm text-muted mb-0">
                                        <?=$user->getPlatformNum('国家级')?>家
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <a class="block block-rounded" href="<?=site_url('office/search/index')?>">
                            <div class="block-content block-content-full">
                                <div class="item item-circle bg-body-light float-left">
                                    <i class="fa fa-cube"></i>
                                </div>
                                <div class="mt-2 text-left" style="margin-left:5rem">
                                    <p class="font-w600 mb-0">
                                        省级平台
                                    </p>
                                    <p class="font-size-sm text-muted mb-0">
                                        <?=$user->getPlatformNum('省级')?>家
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    通知公告
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <?php
                $articles = sf::getModel('Articles')->selectAll("is_release = 1","order by created_at desc",10);
                ?>
                <table class="table table-hover table-myprojects">
                    <tr>
                        <th style="width: 50%">标题</th>
                        <th style="width: 10%">发布时间</th>
                    </tr>
                    <?php
                    while($article = $articles->getObject()):
                        ?>
                        <tr>
                            <td>
                                <div>
                                    <span><a href="<?=site_url('html/'.$article->getId().'.html')?>"><?=$article->getSubject()?></a></span>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span><?=$article->getCreatedAt('Y/m/d')?></span>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile;
                    if($articles->getTotal()==0):
                        ?>
                        <tr>
                            <td colspan="2" class="text-center">无记录</td>
                        </tr>
                    <?php endif;?>
                </table>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    帐号登录日志
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <table class="table table-hover table-myprojects">
                    <tr>
                        <th style="width: 50%">内容</th>
                        <th style="width: 25%">时间</th>
                        <th style="width: 25%">地点</th>
                    </tr>
                    <?php  $logs = sf::getModel('Logs')->getLogs(input::session('userid'),5);
                    while($log = $logs->getObject()):?>
                        <tr>
                            <td>
                                <div>
                                    <span><?=$log->getContent()?></span>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span><?=$log->getUpdatedAt()?></span>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span><?=$log->getUserIp()?></span>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile;?>
                    <?php
                    if($logs->getTotal()==0):?>
                        <tr>
                            <td colspan="3" class="text-center">无记录</td>
                        </tr>
                    <?php
                    endif;
                    ?>
                </table>
            </div>
        </div>
    </div>
    <div class="col-lg-5 col-sm-12">
        <div class="block">
            <div class="block-content block-content-full">
                <div class="user-center"  style="padding: 0">
                    <div class="user-avatar item item-circle bg-body-light float-left">
                        <i class="fa fa-user"></i>
                    </div>
                    <div class="ml-7 text-left">
                        <p class="font-w600 mb-0">
                            <?=$user->getUserUsername()?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            登录账号：<?=$user->getUser()->getUserName()?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            当前身份：<?=input::getInput("session.groupname")?><a class="ml-3" href="<?=site_url('manager/choose')?>" >切换角色</a>
                        </p>

                        <p class="font-size-sm text-muted mb-3 mt-3">
                            上次登录时间：<?=input::getInput("session.lastlogin")?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            上次登录IP：<?=input::getInput("session.userip")?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="block <?=in_array(input::session('username'),['szx'])?'d-none':''?>">
            <div class="block-header">
                <h3 class="block-title">
                    待办事项
                </h3>
            </div>
            <div class="block-content block-content-full todolist">
                <div class="row">
                    <?php
                    if($user->getAcceptWaitNum()):
                    ?>
                    <div class="col-6 col-md-6 col-xl-4">
                        <a class="block text-left" href="<?=site_url("office/accept/wait_list")?>" style="background-color: #f3f4f7">
                            <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                <div>
                                    <div class="font-w400 text-black text-left">
                                        待审申报书
                                    </div>
                                    <div class="font-w400 text-left mt-2 <?=$user->getAcceptWaitNum()?'text-danger':''?>" style="font-size: 18px;">
                                        <span><?=$user->getAcceptWaitNum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">项</em></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <?php endif;?>
                    <?php
                    if($user->getUnitWaitnum()):
                    ?>
                    <div class="col-6 col-md-6 col-xl-4">
                        <a class="block text-left" href="<?=site_url("admin/corporation/wait_list")?>" style="background-color: #f3f4f7">
                            <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                <div>
                                    <div class="font-w400 text-black text-left">
                                        待认证的单位
                                    </div>
                                    <div class="font-w400 text-left mt-2 <?=$user->getUnitWaitnum()?'text-danger':''?>" style="font-size: 18px;">
                                        <span><?=$user->getUnitWaitnum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">个</em></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <?php endif;?>
                    <?php
                    if($user->getRadicateWaitNum()):
                    ?>
                    <div class="col-6 col-md-6 col-xl-4">
                        <a class="block text-left" href="<?=site_url("office/radicate/wait_list")?>" style="background-color: #f3f4f7">
                            <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                <div>
                                    <div class="font-w400 text-black text-left">
                                        待立项项目
                                    </div>
                                    <div class="font-w400 text-left mt-2 <?=$user->getRadicateWaitNum()?'text-danger':''?>" style="font-size: 18px;">
                                        <span><?=$user->getRadicateWaitNum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">项</em></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <?php endif;?>
                    <?php
                    if($user->getTaskWaitnum()):
                    ?>
                    <div class="col-6 col-md-6 col-xl-4">
                        <a class="block text-left" href="<?=site_url("office/task/wait_list")?>" style="background-color: #f3f4f7">
                            <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                <div>
                                    <div class="font-w400 text-black text-left">
                                        待审任务书
                                    </div>
                                    <div class="font-w400 text-left mt-2 <?=$user->getTaskWaitnum()?'text-danger':''?>" style="font-size: 18px;">
                                        <span><?=$user->getTaskWaitnum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">项</em></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <?php endif;?>
                    <?php
                    if($user->getSummaryWaitnum()):
                    ?>
                    <div class="col-6 col-md-6 col-xl-4">
                        <a class="block text-left" href="<?=site_url("summary/office/wait_list")?>" style="background-color: #f3f4f7">
                            <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                <div>
                                    <div class="font-w400 text-black text-left">
                                        待审年度考核
                                    </div>
                                    <div class="font-w400 text-left mt-2 <?=$user->getSummaryWaitnum()?'text-danger':''?>" style="font-size: 18px;">
                                        <span><?=$user->getSummaryWaitnum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">项</em></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <?php endif;?>
                    <?php
                    if($user->getCompleteWaitnum()):
                    ?>
                    <div class="col-6 col-md-6 col-xl-4">
                        <a class="block text-left" href="<?=site_url("office/complete/review_list")?>" style="background-color: #f3f4f7">
                            <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                <div>
                                    <div class="font-w400 text-black text-left">
                                        待审验收书
                                    </div>
                                    <div class="font-w400 text-left mt-2 <?=$user->getCompleteWaitnum()?'text-danger':''?>" style="font-size: 18px;">
                                        <span><?=$user->getCompleteWaitnum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">项</em></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <?php endif;?>
                    <?php
                    if($user->getChangeWaitnum()):
                        ?>
                        <div class="col-6 col-md-6 col-xl-4">
                            <a class="block text-left" href="<?=site_url("change/office/wait_list")?>" style="background-color: #f3f4f7">
                                <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                    <div>
                                        <div class="font-w400 text-black text-left">
                                            待审变更申请
                                        </div>
                                        <div class="font-w400 text-left mt-2 <?=$user->getChangeWaitnum()?'text-danger':''?>" style="font-size: 18px;">
                                            <span><?=$user->getChangeWaitnum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">项</em></span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endif;?>
                </div>
            </div>
        </div>
    </div>
</div>