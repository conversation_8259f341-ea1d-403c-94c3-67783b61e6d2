<div class="row">
    <?php if(count($warning)):?>
    <div class="col-12">
        <div class="alert alert-warning alert-dismissable" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
            <h3 class="alert-heading font-size-h4 my-2">
                提示
            </h3>
            <?php for($i=0,$n=count($warning);$i<$n;$i++):?>
            <p class="mb-0">
                <?=$warning[$i]?>
            </p>
            <?php endfor;?>
        </div>
    </div>
    <?php
        endif;
    ?>
    <div class="col-lg-7 col-md-12">
        <div class="row row-deck">
            <div class="col-md-12 col-xl-12">
                <a class="block d-flex justify-content-center align-items-start text-center bg-xpro" href="<?=site_url('engine/guide/index')?>">
                    <div class="block-content block-content-full bg-white mt-1 align-self-stretchv">
                        <div class="py-4">
                            <i class="fa fa-2x fa-paper-plane text-xpro"></i>
                            <p class="font-size-lg font-w600 mt-3 mb-1">
                                平台申报
                            </p>
                        </div>
                    </div></a>
            </div>
            <div class="col-md-6 col-xl-6 d-none">
                <a class="block d-flex justify-content-center align-items-start text-center bg-xinspire" href="<?=site_url('user/task/index')?>">
                    <div class="block-content block-content-full bg-white mt-1 align-self-stretch">
                        <div class="py-4">
                            <i class="fa fa-2x fa-book-open text-xinspire"></i>
                            <p class="font-size-lg font-w600 mt-3 mb-1">
                                任务书填报
                            </p>
                        </div>
                    </div></a>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    通知公告
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <?php
                    $articles = sf::getModel('Articles')->selectAll("is_release = 1 and user_group_id like '%|2|%'","order by created_at desc",10);
                ?>
                <table class="table table-hover table-myprojects">
                    <tr>
                        <th style="width: 50%">标题</th>
                        <th style="width: 10%">发布时间</th>
                    </tr>
                    <?php
                        while($article = $articles->getObject()):
                    ?>
                    <tr>
                        <td>
                            <div>
                                <span><a href="<?=site_url('html/'.$article->getId().'.html')?>"><?=$article->getSubject()?></a></span>
                            </div>
                        </td>
                        <td>
                            <div>
                                <span><?=$article->getCreatedAt('Y/m/d')?></span>
                            </div>
                        </td>
                    </tr>
                    <?php endwhile;
                    if($articles->getTotal()==0):
                    ?>
                    <tr>
                        <td colspan="2" class="text-center">无记录</td>
                    </tr>
                    <?php endif;?>
                </table>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    我的平台
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <?php
                    $projects = $user->selectProject(5);
                ?>
                <table class="table table-hover table-myprojects">
                    <tr>
                        <th style="width: 30%">平台名称</th>
                        <th style="width: 25%">依托单位</th>
                        <th style="width: 15%">申报年度</th>
                        <th style="width: 15%" class="text-center">状态</th>
                        <th style="width: 15%" class="text-center">操作</th>
                    </tr>
                    <?php
                        while($project = $projects->getObject()):
                    ?>
                    <tr>
                        <td>
                            <div>
                                <span><?=$project->getSubject()?>(<?=$project->getLevel()?>)</span>
                            </div>
                        </td>
                        <td>
                            <div>
                                <span><?=$project->getCorporationName()?></span>
                            </div>
                        </td>
                        <td>
                            <div>
                                <span><?=$project->getDeclareYear()?></span>
                            </div>
                        </td>
                        <td class="text-center"><?=$project->getState()?></td>
                        <td class="text-center" style="position:relative;">
                            <?php
                            if($project->enableWrite()):
                               $urls = [
                                   [
                                       'name'=>'编辑',
                                       'url'=>site_url("apply/project/edit/id/".$project->getProjectId()),
                                       'icon'=>'edit',
                                   ],
                                   [
                                       'name'=>'查看',
                                       'url'=>site_url("apply/project/show/id/".$project->getProjectId()),
                                       'icon'=>'show',
                                   ],
                                   [
                                       'name'=>'上报',
                                       'url'=>site_url("user/project/doSubmit/id/".$project->getProjectId()),
                                       'icon'=>'submit',
                                   ],
                                   [
                                       'name'=>'分割线',
                                       'url'=>'-',
                                   ],
                                   [
                                       'name'=>'删除',
                                       'url'=>site_url("user/project/dodelete/id/".$project->getProjectId()),
                                       'icon'=>'trash',
                                       'class'=>'text-danger',
                                       'event'=>'return showConfirm(\'删除后将不可恢复，确定要删除吗？\',this);',
                                   ]
                               ];
                                echo Button::setClass('btn-primary')->group('操作',$urls);
                            else:
                            ?>
                            <a href="<?=site_url('apply/project/show/id/'.$project->getProjectId())?>">详情</a>
                        <?php endif;?>
                        </td>
                    </tr>
                    <?php endwhile;
                    if($projects->getTotal()==0):
                    ?>
                    <tr>
                        <td colspan="5" class="text-center">无记录</td>
                    </tr>
                    <?php endif;?>
                </table>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    帐号登录日志
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <table class="table table-hover table-myprojects">
                    <tr>
                        <th style="width: 50%">内容</th>
                        <th style="width: 25%">时间</th>
                        <th style="width: 25%">地点</th>
                    </tr>
                    <?php  $logs = sf::getModel('Logs')->getLogs(input::session('userid'),5);
                    while($log = $logs->getObject()):?>
                    <tr>
                        <td>
                            <div>
                                <span><?=$log->getContent()?></span>
                            </div>
                        </td>
                        <td>
                            <div>
                                <span><?=$log->getUpdatedAt()?></span>
                            </div>
                        </td>
                        <td>
                            <div>
                                <span><?=$log->getUserIp()?></span>
                            </div>
                        </td>
                    </tr>
                    <?php endwhile;?>
                    <?php
                        if($logs->getTotal()==0):?>
                    <tr>
                        <td colspan="3" class="text-center">无记录</td>
                    </tr>
                    <?php
                        endif;
                    ?>
                </table>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    用户操作指南
                </h3>
            </div>
            <div class="block-content" style="padding-top: 0">
                <table class="table table-hover table-myprojects">
                    <tr>
                        <td>
                            <div>
                                <span>平台申报书填写上报后还需要依托单位进行审核；</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>
                                <span>如果您的申报书被退回，请认真阅读退回意见并进行修改、重新上报；</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>
                                <span>修改平台申报书资料步骤：依次点击【申报管理】=>【平台申报管理】=>【填写中的平台】。</span>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="col-lg-5 col-sm-12">
        <div class="block">
            <div class="block-content block-content-full">
                <div class="user-center"  style="padding: 0">
                    <div class="user-avatar item item-circle bg-body-light float-left">
                        <i class="fa fa-user"></i>
                    </div>
                    <div class="ml-7 text-left">
                        <p class="font-w600 mb-0">
                            <?=$user->getUserName()?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            登录账号：<?=$user->getUser()->getUserName()?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            当前身份：<?=input::getInput("session.groupname")?><a class="ml-3" href="<?=site_url('manager/choose')?>" >切换角色</a>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            实名认证：<b class="<?=$user->getIsLock()==0?'text-success':'text-xplay'?>"><?=$user->getIsLock()==0?'已认证':'未认证'?></b><a class="ml-3" href="<?=site_url('user/profile')?>" >立即查看</a>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            上次登录时间：<?=input::getInput("session.lastlogin")?>
                        </p>
                        <p class="font-size-sm text-muted mb-3 mt-3">
                            上次登录IP：<?=input::getInput("session.userip")?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="block">
            <div class="block-header">
                <h3 class="block-title">
                    待办事项
                </h3>
            </div>
            <div class="block-content block-content-full">
                <div class="row">
                    <div class="col-6 col-md-6 col-xl-6">
                        <a class="block text-left" href="<?=site_url("user/project/deformity_list")?>" style="background-color: #f3f4f7">
                            <div class="block-content block-content-full d-flex" style="padding: 0.7rem">
                                <div>
                                    <div class="font-w400 text-black text-left">
                                        待上报申报书
                                    </div>
                                    <div class="font-w400 text-left mt-2 <?=$user->getDeformityNum()?'text-danger':''?>" style="font-size: 18px;">
                                        <span><?=$user->getDeformityNum()?> <em class="ml-1" style="color:inherit;font-style: normal;font-size: 12px;">个</em></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php include('contact.php')?>
    </div>
</div>
