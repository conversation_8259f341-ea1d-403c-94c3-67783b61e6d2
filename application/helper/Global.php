<?php

use Sofast\Core\Log;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Models\Fieldtype;
use App\Models\CmsArchives;
use App\Models\UserFieldtype;
use App\Models\Subject;
use App\Models\Guide;
use App\Models\BookMap;
use Illuminate\Database\Capsule\Manager as DB;
use App\Models\Region;
use App\Model\BookMaps;
use App\Models\CmsArctype;
use App\Models\DocumentType;
use App\Models\Personnel as PersonnelModel;
use App\Lib\BeanstalkClient;
use Sofast\Support\template;

/**
 * 一些有用的全局函数
 *
 */
function getYearList($select = 0,$from=2019,$to=0)
{
    if(!$to) $to = config::get('current_declare_year',date("Y"));
    $htmlStr = '';
    for($i=$to;$i >=$from;$i--){
        $htmlStr .= '<option value="'.$i.'" ';
        if($select == $i) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$i."</option>\r\n";
    }
    return $htmlStr;
}

function getMonthList($select = 1)
{
    $htmlStr = '';
    for($i=1;$i <= 12;$i++){
        $value = $i<10 ? '0'.$i : $i;
        $htmlStr .= '<option value="'.$value.'" ';
        if($select == $i) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$i."月</option>\r\n";
    }
    return $htmlStr;
}

function getFormList()
{
    $forms = sf::getModel('Forms')->selectAll();
    $arr = [];
    while($form = $forms->getObject()){
        $arr[$form->getId()] = $form->getSubject();
    }
    return $arr;
}

function getQuarterList($select = 0,$from=1,$to=4)
{
    $htmlStr = '';
    for($i=$from;$i <= $to;$i++){
        $htmlStr .= '<option value="'.$i.'" ';
        if($select == $i) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$i."</option>\r\n";
    }
    return $htmlStr;
}

/**
 * 取得项目类型
 */
function getTypesList($select = -1)
{
    $result = sf::getModel("Types")->selectAll(" is_show = 1 ","order by id");
    $htmlStr = '';
    while($type = $result->getObject()){
        $htmlStr .= '<option value="'.$type->getId().'" ';
        if($select == $type->getId()) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$type->getSubject()."</option>\r\n";
    }
    return $htmlStr;
}

function getGuidesList()
{
    $addWhere = "is_show = 1 and is_lastnode = 1";
    if(!isTester()) $addWhere .= " AND unix_timestamp(start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') and unix_timestamp(end_at) > unix_timestamp('".date("Y-m-d H:i:s")."') ";
    $guides = sf::getModel('Guides')->selectAll($addWhere);
    $datas = [];
    while($guide = $guides->getObject()){
        $datas[$guide->getId()] = $guide->getSubject();
    }
    return $datas;
}

function getTopGuidesList()
{
    $addWhere = "is_show = 1 and level = 1";
    $guides = sf::getModel('Guides')->selectAll($addWhere,"order by orders asc,id asc");
    $datas = [];
    while($guide = $guides->getObject()){
        $datas[$guide->getId()] = $guide->getSubject();
    }
    return $datas;
}

function getSubjectList($keyType='id',$isShow=1,$parentId=1)
{
    $addWhere = "is_lastnode = 1 and level = 2";
    if($parentId) $addWhere.=" and parent_id = {$parentId}";
    if($isShow) $addWhere.=" and is_show = 1";
    $guides = sf::getModel('Guides')->selectAll($addWhere,"order by parent_id asc,orders asc,id asc");
    $datas = [];
    while($guide = $guides->getObject()){
        if($keyType=='id'){
            $datas[$guide->getId()] = $guide->getSubject();
        }else{
            $datas[$guide->getMark()] = $guide->getSubject();
        }

    }
    return $datas;
}

function getZdzkList($keyType='id')
{
    $addWhere = "level = 1";
    $categorys = sf::getModel('categorys','','subject')->selectAll($addWhere,"order by orders asc,id asc");
    $datas = [];
    while($category = $categorys->getObject()){
        if($keyType=='id'){
            $datas[$category->getId()] = $category->getSubject();
        }else{
            $datas[$category->getCode()] = $category->getSubject();
        }

    }
    return $datas;
}

function getQyzxList($keyType='id')
{
    $addWhere = "level = 1";
    $categorys = sf::getModel('categorys','','qyzx')->selectAll($addWhere,"order by orders asc,id asc");
    $datas = [];
    while($category = $categorys->getObject()){
        if($keyType=='id'){
            $datas[$category->getId()] = $category->getSubject();
        }else{
            $datas[$category->getCode()] = $category->getSubject();
        }

    }
    return $datas;
}

function getYxzxList($keyType='id')
{
    $addWhere = "level = 1";
    $categorys = sf::getModel('categorys','','yxzx')->selectAll($addWhere,"order by orders asc,id asc");
    $datas = [];
    while($category = $categorys->getObject()){
        if($keyType=='id'){
            $datas[$category->getId()] = $category->getSubject();
        }else{
            $datas[$category->getCode()] = $category->getSubject();
        }

    }
    return $datas;
}

function getSubjectCode($subjectName)
{
    $subjects = getSubjectList('code',0,0);
    $keys = array_keys($subjects,$subjectName);
    return $keys[0] ? $keys[0] : false;
}
function getSubjectName($subjectCode,$isShow=1,$parentId=1)
{
    $subjectCode = strtoupper($subjectCode);
    $subjects = getZdzkList('code');
    return $subjects[$subjectCode] ? $subjects[$subjectCode] : false;
}
function getGuideName($guideId)
{
    $guide = sf::getModel('Guides',$guideId);
    return $guide->isNew() ? '无' : $guide->getSubject();
}
function getAllSubjectName($subjectCode,$isShow=1,$parentId=1)
{
    $subjectCode = strtoupper($subjectCode);
    $subjects = getZdzkList('code');
    return $subjects[$subjectCode] ? $subjects[$subjectCode] : false;
}
function getQyzxName($subjectCode)
{
    $subjectCode = strtoupper($subjectCode);
    $subjects = getQyzxList('code');
    return $subjects[$subjectCode] ? $subjects[$subjectCode] : false;
}
function getYxzxName($subjectCode)
{
    $subjectCode = strtoupper($subjectCode);
    $subjects = getYxzxList('code');
    return $subjects[$subjectCode] ? $subjects[$subjectCode] : false;
}

function getCompanyList()
{
    $addWhere = "1";
    $companys = sf::getModel('Corporations')->selectAll($addWhere);
    $datas = [];
    while($company = $companys->getObject()){
        $datas[$company->getUserId()] = $company->getSubject();
    }
    return $datas;
}

function getDistrictList()
{
    return ['成都片区','川北片区','川东片区','川南片区','川西片区'];
}

/**
 * 取得项目类型
 */
function getTypesArray($select = -1)
{
    $result = sf::getModel("Types")->selectAll("","ORDER BY id");
    $arr=array();
    while($type = $result->getObject()){
        $arr[$type->getId()] = $type->getSubject();
    }
    return $arr;
}

/**
 * 取得指南
 */
function getGuideArray($select = -1)
{
    $result = Guide::where('is_show',1)->where('is_lastnode',1)->orderBy('id')->get();
    $arr=array();
    foreach($result as $guide){
        $arr[$guide->id] = $guide->subject;
    }
    return $arr;
}

/**
 * 取得bookmap
 */
function getBookmapArray($select = -1)
{
    $result = BookMap::where('is_show',1)->orderBy('id')->get();
    $arr=array();
    foreach($result as $bookmap){
        $arr[$bookmap->id] = $bookmap->subject;
    }
    return $arr;
}

/**
 * 取得归口部门列表
 */
function getDepartmentList($select = '',$type=0)
{
    $addWhere = ' 1';
    $type && $addWhere .= " and type IN (".$type.")";
    $result = sf::getModel("departments")->selectAll($addWhere,'ORDER BY `rank` ASC,id asc ');
    $htmlStr = '';
    while($type = $result->getObject()){
        $htmlStr .= '<option value="'.$type->getUserId().'" ';
        if($select == $type->getUserId()) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$type->getSubject()."</option>\r\n";
    }
    return $htmlStr;
}

/**
 * 取得归口部门列表
 */
function getDepartments($type=0)
{
    $addWhere = ' 1';
    $type && $addWhere .= " and type IN (".$type.")";
    $departments = sf::getModel("departments")->selectAll($addWhere,'ORDER BY `rank` ASC,id asc ');
    $arr = [];
    while($department = $departments->getObject()){
        $arr[$department->getUserId()] = $department->getSubject();
    }
    return $arr;
}

/**
 * 取得上级单位列表
 */
function getParentList($select = '',$level='1')
{
    $addWhere = ' id not in ("1","5","8")';
    $level && $addWhere .= " and level <= ".$level;
    $result = sf::getModel("Corporations")->selectAll($addWhere,'ORDER BY id ASC,`level` ASC ');
    $htmlStr = '';
    while($level = $result->getObject()){
        $htmlStr .= '<option value="'.$level->getUserId().'" ';
        if($select == $level->getUserId()) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$level->getSubject()."</option>\r\n";
    }
    return $htmlStr;
}
/**
 * 取得申报单位列表
 */
function getCorporationList($select = '')
{
    $addWhere = ' id >=10 ';
    $result = sf::getModel("Corporations")->selectAll($addWhere,'ORDER BY id ASC,`level` ASC ');
    $htmlStr = '';
    while($level = $result->getObject()){
        $htmlStr .= '<option value="'.$level->getUserId().'" ';
        if($select == $level->getUserId()) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$level->getSubject()."</option>\r\n";
    }
    return $htmlStr;
}
/**
 * 取得归口部门列表
 */
function getShiftList($select = '')
{
    return getSelectFromArray(array('2'=>'转移支付','3'=>'苗子工程','4'=>'自筹项目'),$select,false);
}


/**
 * 取得归口部门类型
 */
function getDepartmentType($select = '')
{
    return get_select_option('department',$select,false,0);
}

/**
 * 取得科室列表
 */
function getOfficeList($select = 0)
{
    return get_select_option('office',$select,false,0);
}

/**
 * 取得项目领域列表
 */
function getDomainList($select = 0)
{
    return get_select_option('domain',$select,true,0);
}
/**
 * 取得学科列表
 */
//function getSubjectList($select = 0,$level=0,$type=1,$return_type='string')
//{
//	$data=[];
//	$addWhere = '1=1 ';
//	$level && $addWhere .= " and level = '".$level."' ";
//	$type && $addWhere.=" and type={$type} order by code asc";
//
//	$result = sf::getModel("subjects")->selectAll($addWhere);
//	$htmlStr = '';
//	while($subject = $result->getObject()){
//		$htmlStr .= '<option value="'.$subject->getCode().'" ';
//		if($select == $subject->getCode()) $htmlStr .= 'selected="selected" ';
//		$htmlStr .= '>'.$subject->getSubject()."</option>\r\n";
//		$data[$subject->getCode()] = $subject->getSubject();
//	}
//	if($return_type=='array')
//		return $data;
//	return $htmlStr;
//}

function getSubjectArray($level=1,$type=1,$subject_ids=[])
{
    if(!$subject_ids)
    {
        $guide = Guide::find($_SESSION['guide_id']);
        if($guide)
        {
            if($type==1)
                $subject_ids = $guide->getSubjectIds();
            if($type==3)
                $subject_ids = $guide->getIndustryIds();
        }
        else $subject_ids=[];
    }

    $result=[];
    $subject=Subject::where('level',$level)->where('type',$type);
    if($subject->count()>0)
        if(count($subject_ids)>0)
            return $subject->whereIn('code',$subject_ids)->lists('subject','code')->toArray();
        else
            return $subject->lists('subject','code')->toArray();
}

/**
 * 取得职称列表
 */
function getWorkList($select = 0)
{
    return get_select_option('work',$select,true,0);
}
/**
 * 取得职业资格列表
 */
function getCategorys($select = 0,$type)
{
    return get_select_option($type,$select,false,0);
}
/**
 * 取得项目类别列表
 */
function getProjectTypes($select = 0)
{
    return get_select_option('project_type',$select,false,0);
}
/**
 * 取得地区列表
 */
function getRegionOld($select = 0,$parent_id=0,$type=0)
{
    $result = Region::where('parent_id',$parent_id)->where('region_type',$type)->get();
    $htmlStr = '';
    if(!empty($result)){
        $result = $result->toArray();
        foreach($result as $item){
            $htmlStr .= '<option value="'.$item['region_id'].'" ';
            if($select == $item['region_id']) $htmlStr .= 'selected="selected" ';
            $htmlStr .= '>'.$item['region_name']."</option>\r\n";
        }
    }
    return $htmlStr;
}
/**
 * 取得地区列表
 */
function getRegion($select = 0,$type=0)
{
    if(strlen($select)<6 || $type==1){
        $result = Region::where('level',$type)->get();
        $select = substr($select,0,2).'0000';
    }else{
        switch($type){
            case 2:
                $code = substr($select,0,2);
                $select = substr($select,0,4).'00';
                break;
            case 3:
                $code = substr($select,0,4);
                break;
            default:
                $code = substr($select,0,2);
                break;
        }
        $result = Region::where('code','like',$code.'%')->where('level',$type)->orderBy('code','asc')->get();
    }

    $htmlStr = '';
    if(!empty($result)){
        $result = $result->toArray();
        foreach($result as $item){
            $htmlStr .= '<option value="'.$item['code'].'" ';
            if($select == $item['code']) $htmlStr .= 'selected="selected" ';
            $htmlStr .= '>'.getReginName($item['code'],$type)."</option>\r\n";
        }
    }
    return $htmlStr;
}

/**
 * 取得民族列表
 */
function getNationList($select = 0)
{
    return get_select_option('nation',$select,true,0);
}

/**
 * 取得用户组数据
 */
function getUserGroupList()
{
    $data = array();
    $groups = sf::getModel("UserGroups")->selectAll();
    while($group = $groups->getObject()){
        $data[str_pad($group->getId(),2,'0',STR_PAD_LEFT)] = $group->getUserGroupName();
    }
    return $data;
}

/**
 * 取得单位性质列表
 */
function getPropertyList($select = 0)
{
    return get_select_option('unit_property',$select,true,0);
}

/**
 * 取得学历列表
 */
function getDegreeList($select = 0)
{
    return get_select_option('degree',$select,true,0);
}

/**
 * 取得学位列表
 */
function getEducationList($select = 0)
{
    return get_select_option('education',$select,true,0);
}

/**
 * 取得帮助分类列表
 */
function getHelpList($select = 0)
{
    return get_select_option('help',$select,false,0);
}


function filter($var)
{
    if($var == '') return false;
    else return true;
}

function getNews($category_id=1,$showMax=5)
{
    $result = sf::getModel("Articles")->selectByCategoryId($category_id,$showMax);
    $htmlStr = "<ul>";
    if($category_id == 217)
    {
        while($news = $result->getObject())
        {
            $htmlStr .= '<li>'.link_to("home/show/id/".$news->getId(),$news->getSubject(20))."<em>(".$news->getUpdatedAt("Y-m").")</em>"."</li>\r\n";
        }

    }
    else if($category_id == 219)
    {
        while($news = $result->getObject())
        {
            $htmlStr .= '<li>'.link_to("home/show/id/".$news->getId(),$news->getSubject(20))."</li>\r\n";
        }

    }
    while($news = $result->getObject())
    {
        $htmlStr .= '<li>'.link_to("home/show/id/".$news->getId(),$news->getSubject(20))."</li>\r\n";
    }
    $htmlStr .= "</ul>";
    return $htmlStr;
}

function getHelps($showMax='')
{
    $level = input::getInput("session.userlevel");
    $result = sf::getModel("Helps")->selectAll("user_group_id LIKE '".$level.",%' OR user_group_id LIKE '%,".$level.",%' OR user_group_id LIKE '%,".$level."' OR user_group_id = '".$level."' OR user_group_id = 'all' ","ORDER BY `updated_at` DESC",$showMax);

    $htmlStr = "<ul>\r\n";
    while($helps = $result->getObject()){
        $htmlStr .= '<li>'.link_to("help/show/cid/".$helps->getCategoryId()."/id/".$helps->getId()."/type/".$helps->getFileType(),$helps->getSubject(20))."</li>\r\n";
    }
    $htmlStr .= "</ul>";

    return $htmlStr;
}

function getSexList($select = '男')
{
    $htmlStr  = '<option value="">=性别=</option> ';
    $htmlStr  .= '<option value="男" ';
    if($select == '男') $htmlStr .='selected="selected"';
    $htmlStr .='>男</option>';
    $htmlStr .= '<option value="女" ';
    if($select == '女') $htmlStr .='selected="selected"';
    $htmlStr .='>女</option>';
    return $htmlStr;
}

function text_to_html($char)
{
    $char=htmlspecialchars($char);
    $char=str_replace(" ","&nbsp;",$char);
    $char=nl2br($char);
    $char=str_replace("<?","< ?",$char);
    return $char;
}

/**
 * 动态生成下拉列表
 */
function getSelectFromArray($rst=array(),$select = 0,$foce=true)
{
    $htmlStr = '';
    foreach($rst as $key => $val){
        $foce && $key = $val;
        $htmlStr .= '<option value="'.$key.'" ';
        if(!empty($select) && $select == $key) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}

function getOptionString($select = 0,$rst = array())
{
    $htmlStr = '';
    foreach($rst as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($select == $key) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}

/**
 * 取得单选控件
 */
function get_radio($options=array(),$name='radio',$sel='',$class='',$accord=true)
{
    $htmlStr = '';
    $class = $class ? ($class=='none'?'':'ace '.$class) : 'ace ';
    foreach($options as $key => $val){
        $accord && $key = $val;//是否强制键值一致
        if($sel == $key) $check = 'checked="checked"';
        else $check = '';
        $htmlStr .= '<div class="form-check form-check-inline"><input type="radio" class="form-check-input" name="'.$name.'" '.$check.' id="'.$name.$key.'" value="'.$key.'"> <label class="form-check-label" for="'.$name.$key.'">'.$val.'</label></div>';
    }
    return $htmlStr;
}

function get_checkbox($options=array(),$name='radio',$sel='',$class='',$accord=true,$col=12)
{
    $htmlStr='';
    $class = $class ? ($class=='none'?'':'ace '.$class) : 'ace ';
    if(!$sel) $sel = array();
    foreach($options as $key => $val){
        $accord && $key = $val;//是否强制键值一致
        if(in_array($key,$sel) || $sel=='all')
        {
            $open = 'open';
            $check = 'checked="checked"';
        }
        else
        {
            $open = '';
            $check = '';
        }
        if(!$col)
            $colstr = '';
        else $colstr = 'col-xs-12 col-md-'.$col.' col-sm-'.$col;
        $htmlStr .= '<div class="form-check form-check-inline"><input id="'.$name.$key.'" name="'.$name.'[]" type="checkbox" class="'.$class.' '.$open.'" value="'.$key.'" '.$check.' /><label class="form-check-label '.$colstr.' text-left no-margin" for="'.$name.$key.'">'.$val.'</label></div>';
    }
    return $htmlStr;
}

function get_checkboxNew($options=array(),$name='radio',$sel='',$class='',$accord=true,$col=12)
{
    $htmlStr='';
    $class = $class ? ($class=='none'?'':'ace '.$class) : 'ace ';
    if(!$sel) $sel = array();
    foreach($options as $key => $val){
        $accord && $key = $val;//是否强制键值一致
        if(in_array($key,$sel) || $sel=='all')
        {
            $open = 'open';
            $check = 'checked="checked"';
        }
        else
        {
            $open = '';
            $check = '';
        }
        if(!$col)
            $colstr = '';
        else $colstr = 'col-xs-12 col-md-'.$col.' col-sm-'.$col;
        $htmlStr .= '<div class="form-check form-check-inline"><input id="'.$name.$key.'" name="'.$name.'[]" type="checkbox" class="'.$class.' '.$open.'" value="'.$key.'" '.$check.' /><label class="form-check-label" for="'.$name.$key.'">'.$val.'</label></div>';
    }
    return $htmlStr;
}


/**
 * 取得下拉列表的数据内容
 */
function get_select_data($type='nation',$level=0)
{
    $options = array();
    $addWhere = '';
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
    if(!$options = $cache->getCache($type.$level)){
        $level && $addWhere .= "level = '".$level."'";
        $result = sf::getModel("categorys",0,$type)->selectAll($addWhere);
        while($option = $result->getObject())
            $options[$option->getId()] = $option->getSubject();
        $cache->setCache($type.$level,$options);
    }
    return $options;
}

/**
 * 取得数据字典内容的下拉option
 */
function get_select_option($type='nation',$sel='',$accord = false,$level=0)
{
    $options = get_select_data($type,$level);
    return getSelectFromArray($options,$sel,$accord);
}

/**
 * 取得数据字典内容的下拉option
 */
function get_budget_select($type='nation',$sel='',$accord = false,$level=0)
{
    $result = sf::getModel("categorys",0,$type)->selectAll($addWhere);
    while($option = $result->getObject()){
        $_data['id'] = $option->getId();
        $_data['subject'] = $option->getSubject();
        $_data['cover'] = $option->getCover();
        $options[] = $_data;
    }

    foreach($options as $val){
        $accord && $key = $val;
        $htmlStr .= '<option value="'.$val['subject'].'"';
        if($val['subject'] == $sel) $htmlStr .= ' selected="selected" ';
        $htmlStr .= ' title="'.$val['cover'].'">'.$val['subject'].'</option>';
    }
    return $htmlStr;
}

/**
 * 序列化 替换特殊符号
 */
function get_SafeSerialize($v = '')
{
    if(empty($v)) return $v;
    $v = str_replace('"',"”",$v);
    $v = str_replace('\'',"’",$v);
    $v = str_replace("\\","/",$v);
    return $v;
}

function checkStr($v)
{
    if(is_array($v)){
        foreach($v as $key => $val){
            $v[$key] = checkStr($val);
        }
    }else{
        $v = str_replace('"','"',$v);
        $v = str_replace('\'','’',$v);
        $v = str_replace('\\','/',$v);
    }
    return $v;
}

/**
 * 取得目录
 */
function getFolder($type_id=1)
{
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),3600);
    if(!$data = $cache->getCache('type_cache')){
        $types = sf::getModel("types")->selectAll();
        while($type = $types->getObject()){
            $data[$type->getId()] = $type->getDir();
        }
        $cache->setCache('type_cache',$data);
    }
    return $data[$type_id];
}

/**
 * 搜索相关函数
 */
function getYearSearch($name='year',$sel=array())
{
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
    if(!$data = $cache->getCache($name)){
        $data = array();
        for($i=2005,$n = config::get('current_declare_year',date("Y"));$i<=$n;$i++)
            $data[$i] = $i.'年';
        $cache->setCache($name,$data);
    }
    return get_checkbox($data,$name,$sel,' ',false,2);
}

function getOfficeSearch($name='office',$sel=array())
{
    return get_checkbox(get_select_data('office'),$name,$sel,' ',false,2);
}

function getTypeSearch($name='types',$sel=array())
{
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
    if(!$data = $cache->getCache($name)){
        $types = sf::getModel("Types")->selectAll();
        while($type = $types->getObject()){
            $data[$type->getId()] = $type->getSubject();
        }
        $cache->setCache($name,$data);
    }
    return get_checkbox($data,$name,$sel,' ',false);
}

function getGatherSearch($name='gather',$sel=array(),$type='')
{
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
    $addWhere ='';

    $temp = array();
    if(strstr($type,','))
    {
        $temp = explode(',',$type);
        $addWhere = "`type` = '".$temp[0]."'";
        for($i=1;$i<count($temp);$i++)
        {
            $addWhere .= " or `type` = '".$temp[$i]."'";
        }
    }
    else $addWhere = "`type` = '".$type."'";

//	if(!$data = $cache->getCache($name)){
    $types = sf::getModel("departments")->selectAll($addWhere,"ORDER BY type ASC");
    while($type = $types->getObject()){
        $data[$type->getUserId()] = $type->getSubject();
    }
//		$cache->setCache($name,$data);
//	}
    return get_checkbox($data,$name,$sel,' ',false);
}

function getStateSearch($name='state',$sel=array())
{
    $data = array('2'=>'等待承担单位审核','5'=>'等待归口部门审核','9'=>'等待科技创新部受理','10'=>'科技创新部已受理','18'=>'项目评审中','20'=>'项目已分流','25'=>'分管科室已推荐','29'=>'项目已立项','30'=>'项目已结题');
    return get_checkbox($data,$name,$sel,' ',false);
}

function getStateWait($name='state',$sel=array())
{
    $data = array('1'=>'进入备选库','2'=>'未进入备选库');
    return get_radio($data,$name,$sel,' ',false);
}

function addAssess($project_id='',$content='',$is_back=0,$is_lock=0)
{
    $Assess = sf::getModel("Assess")->getByProjectId($project_id);
    //如过有没有锁定的直接打开编辑
    $Assess->setUserId(input::getInput("session.userid"));
    $Assess->setUserName(input::getInput("session.nickname"));
    $Assess->setUserGroupId(input::getInput("session.userlevel"));
    $Assess->setProjectId($project_id);
    $Assess->setIsBack($is_back?$is_back:0);
    $Assess->setContent($content);
    $Assess->setIsLock($is_lock);
    $Assess->setUpdatedAt(date("Y-m-d H:i:s"));
    return $Assess->save();
}

function addAudit($project_id='',$content='',$is_back=0,$is_lock=0)
{
    $audit = sf::getModel("Audits")->getByProjectId($project_id);
    //如过有没有锁定的直接打开编辑
    $audit->setUserId(input::getInput("session.userid"));
    $audit->setUserName(input::getInput("session.nickname"));
    $audit->setUserGroupId(input::getInput("session.userlevel"));
    $audit->setProjectId($project_id);
    $audit->setIsBack($is_back?$is_back:0);
    $audit->setContent($content);
    $audit->setIsLock($is_lock);
    $audit->setUpdatedAt(date("Y-m-d H:i:s"));
    return $audit->save();
}

function addHistory($project_id='',$content='',$type='project',$isHidden=0)
{
    $History = sf::getModel('Historys');
    $History->setUserId(input::getInput("session.userid"));
    $History->setUserName(input::getInput("session.nickname"));
    $History->setUserGroupId(input::getInput("session.userlevel"));
    $History->setType($type);
    $History->setProjectId($project_id);
    $History->setContent($content);
    $History->setUserIp(input::getIp());
    $History->setIsHiden($isHidden);
    $History->setUpdatedAt(date("Y-m-d H:i:s"));
    return $History->save();
}

/**
 * 发送电子邮件
 */
function sendMail($email='',$subject='',$content='')
{
    try{
        $client = new SoapClient(null, array('location'=>"http://soap.tccxfw.com/mail.php", 'uri'=>"http://soap.tccxfw.com"));
        return $client->sendmail($email,$subject,$content,'HTML',config::get('mail_hash','2568077dae558079693aef6c5fab5799:test'));
    }catch(SoapFault $fault){
        return $fault->faultstring;
    }
}

/**
 * 发送短信息
 */
function sendSms($mobile='',$content='',$send_at='',$item_id='',$item_type='projects')
{
    if(!isMobile($mobile) || empty($content)) return;
    $send_at = $send_at?$send_at:date("Y-m-d H:i:s");
    $taskId = uniqid('', true);
    $sms = sf::getModel("ShortMessages");
    $sms->setUserId(input::getInput("session.userid"));
    $sms->setUserName(input::getInput("session.nickname"));
    $sms->setItemType($item_type);
    $sms->setItemId($item_id);
    $sms->setTaskId($taskId);
    $sms->setMobile($mobile);
    $sms->setMessage($content);
    $sms->setSendAt($send_at);
    $sms->setUserIp(input::getIp());
    $sms->setStatement(10);//等待发送
    $sms->setUpdatedAt(date("Y-m-d H:i:s"));
    $searchid = $sms->save();
    return postSms($mobile,$content,$taskId,$send_at);
}

/**
 * HTTP直接提交短信息
 */
function postSms($mobile,$content,$taskId='',$send_at='') {
    if(!isMobile($mobile) || empty($content)) return;
    if(!$taskId) $taskId=uniqid();
    $url = 'http://userinterface.vcomcn.com/Opration.aspx';
    $content = iconv("UTF-8","gbk//TRANSLIT",$content);
    $data='<Group Login_Name="'.config::get('sms.username').'" Login_Pwd="'.config::get('sms.password').'" OpKind="0" InterFaceID="" SerType="msg">
	<E_Time>'.date('Y-m-d H:i:s').'</E_Time>
	<Item>
		<Task>
			<Recive_Phone_Number>'.$mobile.'</Recive_Phone_Number>
			<Content><![CDATA['.$content.']]></Content>
			<Search_ID>'.$taskId.'</Search_ID>
		</Task>
	</Item>
</Group>';
    $result = curlPost($url,$data);
    if($result=='00'){
        $shortMessage = sf::getModel('ShortMessages')->selectByTaskId($taskId,$mobile);
        if(!$shortMessage->isNew()){
            $shortMessage->setStatement(20);
            $shortMessage->save();
        }
        return true;
    }
    return false;
}
function curlPost($url = '', $postData = '', $options = array())
{
    if (is_array($postData)) {
        $postData = http_build_query($postData);
    }
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5); //设置cURL允许执行的最长秒数
    if (!empty($options)) {
        curl_setopt_array($ch, $options);
    }
    //https请求 不验证证书和host
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}

/**
 * 评审打分控件
 */
function get_score($name='grade[]',$sel='',$start=0,$end=10,$step=1)
{
    for($i=$end;$i>=$start;$i=$i-$step){
        if($i<$step) $i = 0;
        if(sprintf("%.2f",$sel) == sprintf("%.2f",$i)) $check = 'checked="checked"';
        else $check = '';
        $htmlStr .= '<li><label><input type="radio" name="'.$name.'" value="'.$i.'" '.$check.' class="required" />'.$i.'</label></li>';
    }
    return $htmlStr;
}

function json_convert($str='')
{
    return preg_replace("#\\\u([0-9a-f]+)#ie", "iconv('UCS-2', 'UTF-8', pack('H4', '\\1'))", $str);
}

function object_array($array){
    if(is_object($array)){
        $array = (array)$array;
    }
    if(is_array($array)){
        foreach($array as $key=>$value){
            $array[$key] = object_array($value);
        }
    }
    return $array;
}

//专家研究领域
function expert_subject_select($sel=0)
{
    $_arry = array('数学','信息科学与系统科学','力学','物理学','化学','天文学','地球科学','生物学','农学','林学','畜牧、兽医科学','水产学','基础医学','临床医学','预防医学与卫生学','军事医学与特种医学','药学','中医学与中药学','工程与技术科学基础学科','测绘科学技术','材料科学','矿山工程技术','冶金工程技术','机械工程','动力与电气工程','能源科学技术','核科学技术','电子、通信与自动控制技术','计算机科学技术','化学工程','食品科学技术','木建筑工程','水利工程','交通运输工程','航空、航天科学技术','环境科学技术','安全科学技术','管理学','经济学','政治学','法学','军事学','社会学','民族学','新闻学与传播学','教育学','体育学','统计学');
    return getSelectFromArray($_arry,$sel);
}

//判断机构代码的正确性
function isUnitCode($text)
{
    //如果位数不够，连接线出错直接报错
    if(strlen($text) != 10) return false;

    $s = $z = $c9 = $v =  0;//初始化
    $w = array(3,7,9,10,5,8,4,2);//加权因子
    $text = strtoupper($text);//全部转化成大写

    for($i=0;$i<8;$i++){
        $c = substr($text,$i,1);
        if(ord($c) >=65 && ord($c) <= 90) $z = (ord($c) - 55)*$w[$i];
        elseif($c >= 0 && $c <= 9) $z = $c*$w[$i];
        else return false;//非法字符
        $s += $z;//求和
    }

    //取得校验位c9
    $v = 11 - ($s%11);
    if($v == 10) $c9 = 'X';
    elseif($v == 11) $c9 = 0;
    else $c9 = trim($v);

    //判断是否有效
    if(substr($text,9,1) == $c9) return true;
    return false;
}

//判断机构代码的正确性
function isIdcard($text)
{
    if(sf::getLib("Idcard",$text)->valid()) return true;
    else return false;
}

function isCreditCode($text)
{
    $text = strtoupper($text);//全部转化成大写
    $d = array('0'=>0,'1'=>1,'2'=>2,'3'=>3,'4'=>4,'5'=>5,'6'=>6,'7'=>7,'8'=>8,'9'=>9,'A'=>10,'B'=>11,'C'=>12,'D'=>13,'E'=>14,'F'=>15,'G'=>16,'H'=>17,'J'=>18,'K'=>19,'L'=>20,'M'=>21,'N'=>22,'P'=>23,'Q'=>24,'R'=>25,'T'=>26,'U'=>27,'W'=>28,'X'=>29,'Y'=>30);//字典
    $w = array(1,3,9,27,19,26,16,17,20,29,25,13,8,24,10,30,28);//加权因子
    //求级数
    for($i=0;$i<17;$i++)
        $_sum += $d[$text[$i]]*$w[$i];
    //求验证码
    $v = 31- ($_sum%31);
    //if($v == 31) $v = 0;
    //if($v == 30) $v = 'Y';
    $v = array_search($v,$d);
    //比较验证码
    if(substr($text,-1,1) == $v) return true;
    else return false;
}

/**
 * 手机号验证
 */
function isMobile($mobile){
    // $exp = "/^13[0-9]{1}[0-9]{8}$|15[0-9]{1}[0-9]{8}$|18[0-9]{1}[0-9]{8}$|14[57]{1}[0-9]{8}$|17[578]{1}[0-9]{8}$/";
    // $exp = "/^13[0-9]{1}[0-9]{8}$|15[0-9]{1}[0-9]{8}$|18[0-9]{1}[0-9]{8}$|14[57]{1}[0-9]{8}$|17[0-9]{1}[0-9]{8}$|19[9]{1}[0-9]{8}$/";
    $exp = "/^1[3456789]\d{9}$/";
    if(preg_match($exp,trim($mobile))) return true;
    else return false;
}
/**
 * Email验证
 */
function isEmail($email){
    $exp = "/^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$/";
    if(preg_match($exp,trim($email))) return true;
    else return false;
}

/**
 * 生成文件
 */
function createFile($project_id,$type='budget')
{
    $project = sf::getModel("projects")->selectByProjectId($project_id);

    if($project->isNew())
        $this->page_debug(lang::get("The project do not exist!"),getFromUrl());

    //保存文件
    $opts = array('http'=>array('method'=>"GET",'header'=>"USER_AGENT: PHPER_".substr(md5(date("YdmH")),-5)."\r\n"));
    $context = stream_context_create($opts);
    $context = @file_get_contents(site_url("declare/budget/download/id/".$project_id),false,$context);
    $file_name = date("YmdHis").rand(1000,9999).'.doc';
    @file_put_contents(WEBROOT.'/up_files/doument/budget/'.$file_name,$context);
    //保存版本记录
    $ver = sf::getModel("Versions");
    $ver->setItemId($project->getProjectId());
    $ver->setSubject($project->getSubject());
    $ver->setFilePath($file_name);
    $ver->setType('budget');
    $ver->setUpdatedAt(date("Y-m-d H:i:s"));
    return $ver->save();
}

function Evaluate($str)
{
    $score = 0;
    if(preg_match("/[0-9]+/",$str)) $score ++;
    if(preg_match("/[0-9]{3,}/",$str)) $score ++;
    if(preg_match("/[a-z]+/",$str)) $score ++;
    if(preg_match("/[a-z]{3,}/",$str)) $score ++;
    if(preg_match("/[A-Z]+/",$str)) $score ++;
    if(preg_match("/[A-Z]{3,}/",$str)) $score ++;
    if(preg_match("/[_|\-|+|=|*|!|@|#|$|%|^|&|(|)]+/",$str)) $score += 2;
    if(preg_match("/[_|\-|+|=|*|!|@|#|$|%|^|&|(|)]{3,}/",$str)) $score ++ ;
    if(strlen($str) >= 6) $score ++;
    return $score;
}

function getBookMapList($type_id=0,$is_show=false)
{
    if($is_show) $addWhere = "is_show > 0 ";
    //任务合同
    if($type_id==1){
        $tasks = [];
        $arrs = sf::getModel("EngineWorkers")->selectAll("type = 'Task' and is_show > 0",' order by id DESC ')->toArray();
        array_map(function ($v) use(&$tasks){
            $tasks[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $tasks;
    }
    //申报书
    if($type_id==5){
        $declares = [];
        $arrs = sf::getModel("Types")->selectAll($addWhere,' order by id DESC ')->toArray();
        array_map(function ($v) use(&$declares){
            $declares[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $declares;
    }
    //评审指标
    if($type_id==4){
        $rules = [];
        $addwhere = "is_show > 0 ";
        $arrs = sf::getModel("Rules")->selectAll($addwhere,' order by id DESC')->toArray();
        array_map(function ($v) use(&$rules){
            $rules[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $rules;
    }
    //评审批次
    if($type_id==44){
        $rules = [];
        $addwhere = "1 ";
        $arrs = sf::getModel("Assess")->selectAll($addwhere,' order by id DESC')->toArray();
        array_map(function ($v) use(&$rules){
            $rules[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $rules;
    }
    //验收书
    if($type_id==2){
        $tasks = [];
        $arrs = sf::getModel("EngineWorkers")->selectAll("type = 'Complete' and is_show > 0",' order by id DESC ')->toArray();
        array_map(function ($v) use(&$tasks){
            $tasks[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $tasks;
    }
    //中期报告
    if($type_id==3){
        $stages = [];
        $arrs = sf::getModel("EngineWorkers")->selectAll("type = 'Stage' and is_show > 0",' order by id DESC ')->toArray();
        array_map(function ($v) use(&$stages){
            $stages[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $stages;
    }

    return $data;
}

function getAssessList()
{
    $arr = [];
    $assess = sf::getModel("Assess")->selectAll('',' order by id DESC');
    while($ass = $assess->getObject()){
        $arr[$ass->getId()] = '['.$ass->getId().']'.$ass->getSubject();
    }
    return $arr;
}

function createWord($output,$hash='meetcd')
{
    //$output = preg_replace("/<\!--\[if.*?\]>.*?<\!\[endif\]-->/si","",$output);
    //$output = preg_replace("/<\!--\[if.*?\]-->/si","",$output);
    $hash = trim($hash);
    $mht = sf::getLib("MhtMaker");
    $mht->setSubject("四川省临床重点专科建设项目申报材料");
    $mht->AddContents("index.htm",$mht->GetMimeType("index.htm"),$output);
    $mht->AddContents("header.htm",$mht->GetMimeType("header.htm"),file_get_contents(WEBROOT."/tpl/header.htm"));
    $mht->AddContents("filelist.xml",$mht->GetMimeType("filelist.xml"),file_get_contents(WEBROOT."/tpl/filelist.xml"));
    $mht->AddContents("colorschememapping.xml",$mht->GetMimeType("header.htm"),file_get_contents(WEBROOT."/tpl/colorschememapping.xml"));
    $mht->AddContents("themedata.thmx",$mht->GetMimeType("themedata.thmx"),file_get_contents(WEBROOT."/tpl/themedata.thmx"));
    $mht->AddContents("image001.gif",$mht->GetMimeType("image001.gif"),file_get_contents(site_url("q/image/hash/".$hash)));
    return $mht->GetFile();
}

function isHome()
{
    return true;
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
    if(!$homelist = $cache->getCache('is_home_list')){
        $types = sf::getModel("HomeList")->selectAll();
        while($type = $types->getObject()){
            $homelist[] = $type->getIp();
        }
        $cache->setCache('is_home_list',$homelist);
    }

    if(in_array(input::getIp(),$homelist)) return true;
    else return false;
}

function getSearchArray($type='base',$sel=array(),$is_config=false)
{
    $funcname = $type.'SearchList';
    $_data = $funcname();
    $default = $_data['default'];
    $data = $_data['data'];

    if($is_config)
        return $data;
    if(count($sel)>0)
    {
        $default = [];
        foreach($data as $key=>$value)
        {
            if(in_array($key,$sel))
                $default[$key] = $value;
        }
    }
    return $default;
}

function baseSearchList()
{
    $data['default'] = [
        'subject'=>'项目名称',
        'corporation_name'=>'单位名称'
    ];
    $data['data'] = [
        'subject'=>'项目名称',
        'corporation_name'=>'单位名称',
        'cooperation'=>'参与单位',
        'user_name'=>'项目负责人',
        'accept_id'=>'申报编号',
        'radicate_id'=>'立项编号',
        'subject_name'=>'研究领域'
    ];
    return $data;
}
function moreSearchList()
{
    $data['default'] = [
        'declare_year'=>'申报年度',
        'radicate_year'=>'立项年度'
    ];
    $data['data'] = [
        'declare_year'=>'申报年度',
        'radicate_year'=>'立项年度',
        'type_current_group'=>'项目批次',
        'office_id'=>'所属科室',
        'cat_id'=>'所属大类',
        'type_id'=>'所属小类',
        'department_id'=>'所属归口部门',
        'is_shift'=>'项目属性',
        'statement'=>'项目状态',
        'state_for_plan_book'=>'任务书状态',
        'state_for_complete_book'=>'验收书状态'
    ];
    return $data;
}

function searchContent($sel=array(),$input=array())
{
    $html='';
    $data = getSearchArray('more',$sel);
    foreach($data as $key=>$value)
    {
        $html .= '<select name="'.$key.'" id="'.$key.'">';
        $html .= '<option value="">='.$value.'=</option>';
        $html .= getSelect($key,$input[$key]);
        $html .= '</select>  ';
    }
    return $html;
}

function getSelect($name='',$sel='')
{
    switch($name)
    {
        case 'declare_year';{return getYearList($sel);}
        case 'radicate_year';{return getYearList($sel);}
        case 'type_current_group';{return getSelectFromArray(array('1'=>'第一批','2'=>'第二批','3'=>'第三批','4'=>'第四批','5'=>'第五批','6'=>'第六批','7'=>'第七批','8'=>'第八批'),$sel,false);}
        case 'office_id';{return getOfficeList($sel);}
        case 'cat_id';{return get_select_option('cat',$sel);}
        case 'type_id';{return getTypesList($sel);}
        case 'department_id';{return getDepartmentList($sel);}
        case 'is_shift';{return getShiftList($sel);}
        case 'statement';{return projectStateSelect($sel);}
        case 'state_for_plan_book';{return '<option value="2">待承担单位推荐</option>
		<option value="4">等待归口部门审核</option>
		<option value="6">等待科技创新部受理</option>
		<option value="10">科技创新部同意签署</option>';}
        case 'state_for_complete_book';{return '<option value="2">待承担单位推荐</option>
		<option value="4">待归口部门推荐</option>
		<option value="6">待科技创新部受理</option>
		<option value="10">分管科室已推荐</option>
		<option value="12">科技创新部已经受理</option>
		<option value="14">科技创新部同意结题</option>';}
        default:return '';
    }
}

function getSearchPart($input=array(),$sign='')
{
    $filter = sf::getModel('filters')->selectBySign($sign);
    return $filter->setTemplate('search')->getSearchPart($input);
}

function getSearchArea($role='default')
{
    $search = new \App\Module\Filter\Search;
    return $search->setRole($role)->render();
}

function getFilters($sign='')
{
    if(!$sign) return '';
    return sf::getModel('filters')->getFilterList($sign);
}

function getFilterArray($sign='')
{
    return sf::getModel('filters')->getFilterArray($sign);
}

/**
 * 字符截取 支持UTF8/GBK
 */
function strcut($string, $length, $dot = '...') {
    $charset = 'utf-8';
    if (strlen($string) <= $length) return $string;
    $string  = str_replace(array('&amp;', '&quot;', '&lt;', '&gt;'), array('&', '"', '<', '>'), $string);
    $strcut  = '';
    if (strtolower($charset) == 'utf-8') {
        $n   = $tn = $noc = 0;
        while ($n < strlen($string)) {
            $t = ord($string[$n]);
            if ($t == 9 || $t == 10 || (32 <= $t && $t <= 126)) {
                $tn = 1; $n++; $noc++;
            } elseif (194 <= $t && $t <= 223) {
                $tn = 2; $n += 2; $noc += 2;
            } elseif (224 <= $t && $t <= 239) {
                $tn = 3; $n += 3; $noc += 2;
            } elseif (240 <= $t && $t <= 247) {
                $tn = 4; $n += 4; $noc += 2;
            } elseif (248 <= $t && $t <= 251) {
                $tn = 5; $n += 5; $noc += 2;
            } elseif ($t == 252 || $t == 253) {
                $tn = 6; $n += 6; $noc += 2;
            } else {
                $n++;
            }
            if($noc >= $length) break;
        }
        if ($noc > $length) $n -= $tn;
        $strcut = substr($string, 0, $n);
    } else {
        for ($i = 0; $i < $length; $i++) {
            $strcut .= ord($string[$i]) > 127 ? $string[$i] . $string[++$i] : $string[$i];
        }
    }
    $strcut = str_replace(array('&', '"', '<', '>'), array('&amp;', '&quot;', '&lt;', '&gt;'), $strcut);
    return $strcut . $dot;
}

function clearTags($html='')
{
    return preg_replace("/(\s|\&nbsp\;|　|\xc2\xa0)/", "", strip_tags($html));
}

function role($role_id='')
{
    if(!$role_id) return false;
    $role = [
        '1'=>'admin',
        '2'=>'researcher',
        '10'=>'expert',
        '3'=>'company',
    ];
    return $role[$role_id];
}

//百度ip接口
function get_city_baidu($ip=null) {
    $ch = curl_init();
    $url = "http://apis.baidu.com/apistore/iplookupservice/iplookup?ip=$ip";
    $header = array(
        'apikey: ',
    );
    // 添加apikey到header
    curl_setopt($ch, CURLOPT_HTTPHEADER  , $header);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    // 执行HTTP请求
    curl_setopt($ch , CURLOPT_URL , $url);
    $res = curl_exec($ch);

    var_dump(json_decode($res));
}

//新浪ip接口
function get_city($ip=null,$api='sina')
{
    $outernet = @file_get_contents("http://int.dpool.sina.com.cn/iplookup/iplookup.php?format=json");
    $outernet = json_decode($outernet);dd($location);
}

function getFiledTypeArray($column_key='id',$column_value='subject')
{
    return Fieldtype::where('status',1)
        ->orderBy('type')
        ->orderBy('sort')
        ->lists($column_value,$column_key)
        ->toArray();
}

//获取字段类型
function getFiledType($select='',$type='',$column_key='id',$column_value='subject')
{
    $htmlStr = '';
    $array_base = [];
    $array_extend = [];
    $types = Fieldtype::where('status',1)->orderBy('type')->orderBy('sort');
    if($type)
        $types = $types->where('type',$type);
    $types = $types->get()->toArray();

    foreach($types as $fieldtype)
        if($fieldtype['type']=='base')
            $array_base[$fieldtype[$column_key]] = $fieldtype[$column_value];
        else if($fieldtype['type']=='extend')
            $array_extend[$fieldtype[$column_key]] = $fieldtype[$column_value];

    $htmlStr = '<option value="" class="grey">请选择字段类型</option>'."\r\n";
    $htmlStr .= '<option disabled class="alert alert-info">基础字段</option>'."\r\n";
    foreach($array_base as $id=>$subject)
    {
        $htmlStr .= '<option value="'.$id.'" ';
        if($select == $id) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$subject."</option>\r\n";
    }
    $htmlStr .= '<option disabled class="alert alert-info">预设字段</option>'."\r\n";

    foreach($array_extend as $id=>$subject)
    {
        $htmlStr .= '<option value="'.$id.'" ';
        if($select == $id) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$subject."</option>\r\n";
    }
    return $htmlStr;
}
function getUserFiledType($select='',$type='')
{
    $htmlStr = '';
    $array_base = [];
    $array_extend = [];
    $types = UserFieldtype::where('status',0)->orderBy('type')->orderBy('sort');
    if($type)
        $types = $types->where('type',$type);
    $types = $types->get()->toArray();

    foreach($types as $fieldtype){
        switch($fieldtype['type']){
            case 'base':
                $array_base[$fieldtype['id']] = $fieldtype['subject'];
                break;
            case 'extend':
                $array_extend[$fieldtype['id']] = $fieldtype['subject'];
                break;
            case 'expert':
                $array_expert[$fieldtype['id']] = $fieldtype['subject'];
                break;
            case 'declarer':
                $array_declarer[$fieldtype['id']] = $fieldtype['subject'];
                break;
            case 'unit':
                $array_units[$fieldtype['id']] = $fieldtype['subject'];
                break;
        }
    }
    if($fieldtype['type']=='base')
        $array_base[$fieldtype['id']] = $fieldtype['subject'];
    else if($fieldtype['type']=='extend')
        $array_extend[$fieldtype['id']] = $fieldtype['subject'];

    $htmlStr = '<option value="" class="grey">请选择字段类型</option>'."\r\n";
    $htmlStr .= '<option disabled class="alert alert-info">基础字段</option>'."\r\n";
    foreach($array_base as $id=>$subject)
    {
        $htmlStr .= '<option value="'.$id.'" ';
        if($select == $id) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$subject."</option>\r\n";
    }
    $htmlStr .= '<option disabled class="alert alert-info">预设字段</option>'."\r\n";

    foreach($array_extend as $id=>$subject)
    {
        $htmlStr .= '<option value="'.$id.'" ';
        if($select == $id) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$subject."</option>\r\n";
    }
    $htmlStr .= '<option disabled class="alert alert-info">专家字段</option>'."\r\n";

    foreach($array_expert as $id=>$subject)
    {
        $htmlStr .= '<option value="'.$id.'" ';
        if($select == $id) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$subject."</option>\r\n";
    }
    $htmlStr .= '<option disabled class="alert alert-info">项目负责人字段</option>'."\r\n";

    foreach($array_declarer as $id=>$subject)
    {
        $htmlStr .= '<option value="'.$id.'" ';
        if($select == $id) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$subject."</option>\r\n";
    }
    $htmlStr .= '<option disabled class="alert alert-info">申报单位字段</option>'."\r\n";

    foreach($array_units as $id=>$subject)
    {
        $htmlStr .= '<option value="'.$id.'" ';
        if($select == $id) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$subject."</option>\r\n";
    }
    return $htmlStr;
}

function getLayoutList()
{
    return ['linear'=>'列表','table'=>'表格','tab'=>'标签'];
}

/**
 * 取得单位类别列表
 */
function company_category($sel=0)
{
    $array = array();
    return getSelectFromArray($array,$sel);
}

/**
 * 打印最后执行的sql语句
 */
function ds()
{
    $sql = DB::getQueryLog();
    $query = end($sql);
    dd($query);
}

/**
 * 获取配置信息并生成select选项
 * @param int $select 选中的选项
 * @param string $value	配置类型
 * @param string $type select|checkbox|array select:返回select的html；array：返回原生数组
 * @param string $inputName 表单名
 * @return string
 */
function getConfig($select=0,$value='nation',$type='select',$inputName='')
{
    if($select!=0 && strstr($select,',')) $select = explode(',',$select);
    $expert = config::get($value);
    $htmlStr = '';
    if($type=='select'){
        foreach($expert as $k=>$v){
            $htmlStr .= '<option value="'.$k.'" ';
            if($select == $k) $htmlStr .= 'selected="selected" ';
            $htmlStr .= '>'.$v."</option>\r\n";
        }
        return $htmlStr;
    }elseif($type=='checkbox'){
        foreach($expert as $k=>$v){
            $htmlStr .= '<label class="checkbox-inline"><input type="checkbox" name="'.$inputName.'" value="'.$k.'"';
            if(is_array($select)){
                foreach($select as $se){
                    if($se == $k) $htmlStr .= 'checked="checked" ';
                }
            }else{
                if($select == $k) $htmlStr .= 'checked="checked" ';
            }
            $htmlStr .= '> '.$v."</label>\r\n";
        }
        return $htmlStr;
    }elseif($type=='text'){
        if(empty($select)){
            return '';
        }else{
            if(is_array($select)){
                $str = '';
                foreach($select as $se){
                    $str[] = $expert[$se];
                }
                return implode(',',$str);
            }else{
                return $expert[$select];
            }
        }
    }else{
        return empty($select) ? $expert : $expert[$select];
    }
}

/**
 * 获取专家类别html
 */
function getExpertType($select=0)
{
    if($select!=0) $select = explode(',',$select);
    $expert = config::get('expert_type');
    $htmlStr = '';
    foreach($expert as $k=>$v){
        $htmlStr .= '<label class="checkbox-inline"><input type="checkbox" name="baseinfo[expert_type][]" value="'.$k.'"';
        if(is_array($select)){
            foreach($select as $se){
                if($se == $k) $htmlStr .= 'checked="checked" ';
            }
        }else{
            if($select == $k) $htmlStr .= 'checked="checked" ';
        }
        $htmlStr .= '> '.$v."</label>\r\n";
    }
    return $htmlStr;
}

function getTypesListForMemo($sel='')
{
    $array = array('0'=>'————请选择项目类型————',
                   '1'=>'863计划',
                   '2'=>'国家科技支撑计划',
                   '3'=>'973计划',
                   '4'=>'科技基础条件平台建设计划',
                   '16'=>'———政策引导类科技计划———计划',
                   '5'=>'星火计划',
                   '6'=>'农业科技成果转化资金',
                   '7'=>'火炬计划',
                   '8'=>'科技型中小企业技术创新基金',
                   '9'=>'国家重点新产品',
                   '10'=>'国际科技合作计划',
                   '11'=>'国家软科学研究计划',
                   '12'=>'科技兴贸行动专项',
                   '13'=>'科技基础性工作专项',
                   '14'=>'重大专项',
                   '16'=>'国家科技富民强县专项',
                   '15'=>'其他');
    $htmlStr = '';
    foreach($array as $key => $val)
    {
        $htmlStr .= '<option value="'.$key.'" ';
        if($val === $sel) $htmlStr .= 'selected="selected"';
        $htmlStr .= '/>'.$val."</option>\r\n";
    }
    return $htmlStr;
}

function getTypeSubject($id=0)
{
    $array = array('0'=>'————请选择项目类型————',
                   '1'=>'863计划',
                   '2'=>'国家科技支撑计划',
                   '3'=>'973计划',
                   '4'=>'科技基础条件平台建设计划',
                   '16'=>'———政策引导类科技计划———计划',
                   '5'=>'星火计划',
                   '6'=>'农业科技成果转化资金',
                   '7'=>'火炬计划',
                   '8'=>'科技型中小企业技术创新基金',
                   '9'=>'国家重点新产品',
                   '10'=>'国际科技合作计划',
                   '11'=>'国家软科学研究计划',
                   '12'=>'科技兴贸行动专项',
                   '13'=>'科技基础性工作专项',
                   '14'=>'重大专项',
                   '16'=>'国家科技富民强县专项',
                   '15'=>'其他');
    if($id) return $array[$id];
    else return '未知类型';
}
/*
研究对象
 */
function getProjectType($select = 0)
{
    return getProjectSource($select);
}
/*
项目来源
 */
function getProjectSource($select = 0)
{
    return getSelectFromArray(get_select_data('cat'),$select,false);
}

/**
 * 研究对象
 */
function  getResearchObject($select = 0)
{
    return getSelectFromArray(getBookMapList(5, true), $select, false);
}
/*
指南领域
 */
function getGuideField($select = -1)
{
    $result = Guide::where('is_show',1)->where('is_lastnode',1)->whereNotNull('guide_field')->orderBy('id')->get();
    $htmlStr = '';
    foreach($result as $guide){
        $htmlStr .= '<option value="'.$guide->id.'" ';
        if($select == $guide->id) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$guide->guide_field."</option>\r\n";
    }
    return $htmlStr;
}

function time2string($second){
    $day = floor($second/(3600*24));
    $second = $second%(3600*24);//除去整天之后剩余的时间
    $hour = floor($second/3600);
    $second = $second%3600;//除去整小时之后剩余的时间
    $minute = floor($second/60);
    $second = $second%60;//除去整分钟之后剩余的时间
    //返回字符串
    //return $day.'天'.$hour.'小时'.$minute.'分'.$second.'秒';
    return $day.'天'.$hour.'小时'.$minute.'分';
}

function getMask($str,$len=4,$start=-3,$mask='*')
{
    $size = strlen($str);
    $size = $size > $len ? $len : ceil($size*0.6);
    $str = substr_replace($str,str_pad('',$size,$mask),$start,$len);
    return $str;
}

/**
 * 验证专利号
 * @param array $noArray
 */
function checkPatentSn($noArray=array())
{
    return true;
    //专利号验证规则参见：http://www.cnblogs.com/unruledboy/archive/2007/04/03/PatenVerify.html
    foreach($noArray as $no){
        //1.专利号总长度为12位
        if(strlen($no)!=12){
            return false;
        }
        //2.是否以“ZL”开头
        if(strtoupper(substr($no,0,2))!='ZL'){
            return false;
        }
        //3.“ZL”后8位是否为纯数字
        if(!is_numeric(substr($no,2,8))){
            return false;
        }
        //4.“ZL”后第3位数字是否为1,2,3其中的一个
        if(substr($no,4,1)!='1' && substr($no,4,1)!='2' && substr($no,4,1)!='3'){
            return false;
        }
        //5.8位数字后是否为小数点
        if(substr($no,10,1)!='.'){
            return false;
        }
        //6.验证最后一位校验码
        //校验码这样计算：用前8位数依次与2、3、4、5、6、7、8、9相乘,
        //将它们的乘积相加所得之和再用11除后的余数，当余数为10时，用X表示
        $code = $no[2]*2+$no[3]*3+$no[4]*4+$no[5]*5+$no[6]*6+$no[7]*7+$no[8]*8+$no[9]*9;
        $code = $code%11;
        $code = $code==10 ? 'X' : $code;
        if(strtoupper($no[11])!=$code){
            return false;
        }

    }
    return true;
}

/**
 * 登录密码加密函数
 * @param string $password
 * @return string
 */
function encode_password($password='')
{
    $str = substr(md5(uniqid()),-3);
    return md5($password.$str).":".$str;
}

/**
 * 获取选中的选项的中文
 * @param $str
 * @param $configName
 * @return string
 */
function getSelectText($str,$configName)
{
    if(strstr($str,',')){
        $arr = explode(',',$str);
        $str = $arr[0];
    }
    return getConfig($str,$configName,'text');
}

/**
 * 检查组织机构代码
 * @param string $text
 * @return string
 */
function checkCorporationCode($text=''){
    if(strlen($text) == 18){
        if(!isCreditCode($text)) return false;
    }else{
        if(!isUnitCode($text)) return false;
    }
    return true;
}

/**
 * 获取文章列表
 * @param string $attr 文章属性
 * @param int $count	调用数量
 * @param string $orderby 排序字段
 * @param string $orderway 排序方式
 * @param string $field 查询字段
 * @return mixed
 */
function getArcList($attr='',$count=5,$orderby='sortrank',$orderway='desc',$field='id,title')
{
    $field = explode(',',$field);
    if(!empty($attr)){
        $result = CmsArchives::orderby($orderby,$orderway)->where('channel',1)->where('flag','like',"%{$attr}%")->limit($count)->get($field);
    }else{
        $result = CmsArchives::orderby($orderby,$orderway)->where('channel',1)->limit($count)->get($field);
    }
    return $result;
}

/**
 * 获取专题列表
 * @param string $attr 文章属性
 * @param int $count	调用数量
 * @param string $orderby 排序字段
 * @param string $orderway 排序方式
 * @param string $field 查询字段
 * @return mixed
 */
function getSpecialList($attr='',$count=5,$orderby='sortrank',$orderway='desc',$field='id,title')
{
    $field = explode(',',$field);
    if(!empty($attr)){
        $result = CmsArchives::orderby($orderby,$orderway)->where('channel',-1)->where('flag','like',"%{$attr}%")->limit($count)->get($field);
    }else{
        $result = CmsArchives::orderby($orderby,$orderway)->where('channel',-1)->limit($count)->get($field);
    }
    return $result;
}

/**
 * 获取区域名称
 * @param $code
 * @param $level
 */
function getReginName($code,$level=1)
{
    $region_name = Region::where('code',$code)->value('region_name');
    if($level==1){
        return $region_name;
    }
    if($level==2){
        $code = substr($code, 0, 2).'0000';
        $province_name =  Region::where('code',$code)->value('region_name');
        $region_name = str_replace($province_name,'',$region_name);		//替换掉省份名称
    }elseif($level==3){
        $code = substr($code, 0, 4).'00';
        $city_name = Region::where('code',$code)->value('region_name');
        $region_name = str_replace($city_name,'',$region_name);			//1.先替换掉城市名称

        $code = substr($code, 0, 2).'0000';
        $province_name =  Region::where('code',$code)->value('region_name');
        $region_name = str_replace($province_name,'',$region_name);		//2.再替换掉省份名称

    }
    return $region_name;
}

//根据指定学科代码获取联动关系 type:学科类型，返回类型：$return_type string|array
function resolveSubjectByCode($code='',$type=1,$return_type='array')
{
    $result=[];
    switch($type)
    {
        case 1:
            $result[] = substr($code,0,3);
            $result[] = substr($code,0,6);
            $result[] = substr($code,0,8);
            break;
        case 2:

            break;
        case 3:
            $result[] = substr($code,0,2);
            $result[] = substr($code,0,3);
            $result[] = substr($code,0,4);
            break;
        default:
            break;
    }

    $result = array_unique($result);

    if($return_type=='string')
        return implode(',',$result);
    else return $result;
}

/**
 * 区域代码转为区域名称
 */
function areaCode2Name($code)
{
    if(empty($code)){
        return;
    }
    return Region::where('code',$code)->value('region_name');
}

/**
 * 获取顶级栏目列表
 */
function getTopChannel()
{
    $acttypes  = CmsArctype::where('topid',0)->orderBy('sortrank', 'asc')->orderBy('id', 'asc')->get()->toArray();
    return $acttypes;
}

function getRoleList($is_all=false)
{
    $rolelist = array();
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
    $hash = 'role_list_'.input::getInput("session.userid");
    if(!$rolelist = $cache->getCache($hash)){
        $roles = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"))->selectRoles();
        while($role = $roles->getObject()){
            $rolelist[$role->getRoleId()] = $role->getRoleName();
        }
        $cache->setCache($hash,$rolelist);
    }
    $htmlStr = '';
    if($is_all)
    {
        foreach($rolelist as $key => $val){
            $htmlStr .= '<li><a href="'.site_url('manager/role/role_id/'.$key).'" title="点击进入用户中心" class="show"> <i class="ace-icon glyphicon glyphicon-transfer"></i> '.$val.'</a> </li>';
        }
    }
    else
    {
        foreach($rolelist as $key => $val){
            if($key != input::getInput("session.userlevel"))
                $htmlStr .= '<li><a href="javascript:void(0)" title="点击切换角色" class="show" onClick="return showConfirm(\'是否切换角色？\',\''.site_url('manager/role/role_id/'.$key).'\')"> <i class="ace-icon glyphicon glyphicon-transfer"></i> '.$val.'</a> </li>';
        }
    }
    $htmlStr && $htmlStr .= '<li class="divider"></li>';
    return $htmlStr;
}

function changeRole($role_id)
{
    if(!$role_id) return false;
    $user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
    if($user->hasRole($role_id)){
        $_SESSION['userlevel'] 	=  $role_id;
        $_SESSION['groupname'] 	=  sf::getModel("UserGroups",$role_id)->getUserGroupName();
        $_SESSION['roleuserid'] =  $user->getRoleUserId($role_id);
        $_SESSION['office_id']  =  $user->getOfficeId();
        $_SESSION['group_id']   =  $user->getGroupId();
        return true;
    }else return false;
}


function is_article_manager()
{
    //文章管理员用户组id
    $group_id=[
        1,  //超级管理员
        6,  //科技创新部科长
        //		7,  //科室工作人员
        8,  //科室科长
        38,  //办公室主任
    ];

    $session_level = $_SESSION['userlevel'];
    if(!$session_level) return false;

    if(in_array($session_level,$group_id))
        return true;
    return false;
}

function is_leader()
{
    $group_id=[
        1,  //超级管理员
        6,  //科技创新部科长
        8,  //科室科长
        14,  //副局长
        36,  //科室副科长
        37,  //办公室副主任
        38,  //办公室主任
        39,  //局长
        42,  //所长
        43,  //副所长
        47,  //局知识产权科科长
        48,  //局成果科科长
        49,  //局高新科科长
        50,  //局农业科科长
        52,  //中心党委书记
        55,  //所技术科科长
        56,  //所办公室主任
        59,  //所信息科主任
    ];

    $session_level = $_SESSION['userlevel'];
    if(!$session_level) return false;

    if(in_array($session_level,$group_id))
        return true;
    return false;
}

/**
 * 是否是办公室主任
 * @return bool
 */
function is_office_leader()
{
    $group_id=[
        1,  //超级管理员
        38,  //办公室主任
        56, //所办公室主任
    ];

    $session_level = $_SESSION['userlevel'];
    if(!$session_level) return false;

    if(in_array($session_level,$group_id))
        return true;
    return false;
}

// 系统工作人员下拉菜单
function worker()
{
    $html = '';
    $personnels = PersonnelModel::all();
    $office = array_filter(PersonnelModel::lists('office')->unique()->toArray());
    foreach ($office as $key => $value) {
        $html .= '<option class="NO" disabled value="'.$value.'">'.$value.'</option><br>';
        foreach ($personnels as $personnel) {
            if($personnel->office == $value){
                $html .= '<option value="'.$personnel->id.'">'.$personnel->name.'（'.$personnel->position.'）</option><br>';
            }
        }
    }
    return $html;
}
// 个人文档柜--文档分类下拉菜单
function get_doctype_option($user_id,$type_id)
{
    $html = '';
    $d = DocumentType::where('user_id',$user_id)->get();
    foreach ($d as $key => $value) {
        if($type_id == $value->id){
            $html .= '<option selected value="'.$value->id.'">'.$value->name.'</option><br>';
        }else{
            $html .= '<option value="'.$value->id.'">'.$value->name.'</option><br>';
        }
    }
    return $html;
}

function page_loading($subject,$url='',$message='')
{
    $message = $message?'/message/'.base64_encode($message):'';
    $url = site_url('common/page_loading/url/'.base64_encode($url)).$message;
    return '<a target="_black" href="'.$url.'" >'.$subject.'</a>';
}

/**
 * 获取广告位
 * @param int $id 广告位id
 * @param bool $img 只获取图片src地址
 */
function getAds($id=0,$imgsrc=false)
{
    $content = '';	//广告内容
    if($id==0) $content = '';
    $ad = M('Ads');
    $ads = $ad->selectAll("id = {$id}");
    if($ads->getTotal()==0) $content = '';
    $obj = $ads->getObject();
    if($obj->getIsLimit()){
        //限制了广告位投放时间
        $now_time = time();
        $start_at = strtotime($obj->getStartAt());
        $end_at = strtotime($obj->getEndAt());
        if($now_time>=$start_at && $now_time<$end_at){
            $content = $obj->getContent();
        }else{
            $content = $obj->getexpContent();
        }
    }else{
        $content = $obj->getContent();
    }
    if($imgsrc){
        //匹配图片src
        preg_match_all('/<img.*?src="(.*?)".*?>/is',$content,$array);
        $content = $array[1][0];
    }
    return $content;
}

/**
 * 获取友情链接
 * @return mixed
 */
function getFlinks()
{
    $flinksModel = M('Flinks');
    $flinks = $flinksModel->selectAll("`is_show` = 1 and `logo` is not null","ORDER BY `orders` ASC,`id` ASC",7);
    return $flinks;
}

function console_log($data)
{
    if(is_array($data) || is_object($data))
    {
        echo("<script>console.log('".json_encode($data)."');</script>");
    }
    else
    {
        echo("<script>console.log('".$data."');</script>");
    }
}

/**
 * 导出excel
 * @param $head array 表头数据
 * @param $body  array 主体数据
 * @param $name string 表格标签名
 * @param $filename string 导出的文件名
 *
 * Demo:
 * 		$head = ['姓名','身份证','性别','地址'];
$body = [
['周杰伦','500242197605300015','男','四川省成都市双流县成都信息工程大学航空港校区'],
['蔡依林','500242197605300014','女','成都市武侯区成科西路3号'],
];
excel_out($head,$body,'测试','test');
 */
function excel_out($head,$body,$name,$filename,$widths=[],$superHead='',$savePath='',$mergeCells=[],$sheetIndex=0,$createFile=true,$objPHPExcel=null)
{
    if(is_null($objPHPExcel)) $objPHPExcel = new \PHPExcel();
    if($sheetIndex>0) $objPHPExcel->createSheet();
    $objPHPExcel->setActiveSheetIndex($sheetIndex);
    $objActSheet = $objPHPExcel->getActiveSheet();
    $startRow = 1;  //从第一行开始
    //超级表头（合并单元格，显示一行字）
    if($superHead){
        $j = $i = 'A';
        $count = count($head)-1;
        for($index=0;$index<$count;$index++){
            $j++;
        }
        $objActSheet->mergeCells($i.$startRow.':'.$j.$startRow);   //合并
        $objActSheet->setCellValue($i.$startRow, $superHead);
        $objActSheet->getStyle($i.$startRow)->applyFromArray(
            array(
                'font' => array (
                    'bold' => true
                ),
                'alignment' => array(
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER
                )
            )
        );
        $objActSheet->getStyle($i.$startRow.':'.$j.$startRow)->getFont()->setSize(20);
        $startRow++;
    }

    //表头
    $i='A';
    foreach($head as $item){
        $objActSheet->setCellValue($i.$startRow, $item);
        $i++;
    }
    $count = count($head);

    //主体数据
    foreach($body as $key=>$value){
        $startRow++;
        $index='A';
        for($i=0;$i<$count;$i++){
            $objActSheet->getStyle($index.$startRow)->getNumberFormat()->setFormatCode("@"); //文本格式
//            $objActSheet->setCellValueExplicit($index.$startRow,$value[$i],PHPExcel_Cell_DataType::TYPE_STRING);
            if(strlen($value[$i]) < 10 && floatval($value[$i])==$value[$i]){
                //数字
                $objActSheet->setCellValue($index.$startRow,$value[$i]);
            }else{
                //文本
                $objActSheet->setCellValueExplicit($index.$startRow,$value[$i],PHPExcel_Cell_DataType::TYPE_STRING);
            }

            $index++;
        }
    }

    //设置宽度
    if($widths){
        $index='A';
        for($i=0;$i<$count;$i++){
            $objActSheet->getColumnDimension($index)->setWidth($widths[$i]);
            $index++;
        }
    }

    //合并单元格
    if($mergeCells){
        foreach ($mergeCells as $mergeCell){
            $objActSheet->mergeCells($mergeCell[0].':'.$mergeCell[1]);
            $objActSheet->getStyle($mergeCell[0])->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
        }
    }

//    $objPHPExcel->setActiveSheetIndex(0)->mergeCells('D2:D7');

    $objActSheet->setTitle($name);
    if($createFile){
        $objPHPExcel->setActiveSheetIndex(0);
        if(!$savePath){
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="'.$filename.'.xls"');
            header('Cache-Control: max-age=0');
            $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
            ob_end_clean();
            $objWriter->save('php://output');
            exit;
        }else{
            $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
            $objWriter->save($savePath);
        }
    }else{
        return $objPHPExcel;
    }
}

function excel_outs($heads,$bodys,$sheetNames,$filename)
{
    $objPHPExcel = new \PHPExcel();

    foreach ($heads as $sheetIndex=>$head){
        if($sheetIndex>0) $objPHPExcel->createSheet();
        $objPHPExcel->setActiveSheetIndex($sheetIndex);
        $objActSheet = $objPHPExcel->getActiveSheet();
        $startRow = 1;  //从第一行开始
        $i='A';
        foreach($head as $item){
            $objActSheet->setCellValue($i.$startRow, $item);
            $i++;
        }
        $count = count($head);

        //主体数据
        foreach($bodys[$sheetIndex] as $key=>$value){
            $startRow++;
            $index='A';
            for($i=0;$i<$count;$i++){
                $objActSheet->getStyle($index.$startRow)->getNumberFormat()->setFormatCode("@"); //文本格式
//            $objActSheet->setCellValueExplicit($index.$startRow,$value[$i],PHPExcel_Cell_DataType::TYPE_STRING);
                if(strlen($value[$i]) < 10 && floatval($value[$i])==$value[$i]){
                    //数字
                    $objActSheet->setCellValue($index.$startRow,$value[$i]);
                }else{
                    //文本
                    $objActSheet->setCellValueExplicit($index.$startRow,$value[$i],PHPExcel_Cell_DataType::TYPE_STRING);
                }

                $index++;
            }
        }

        $objActSheet->setTitle($sheetNames[$sheetIndex]);
    }

    $objPHPExcel->setActiveSheetIndex(0);
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="'.$filename.'.xls"');
    header('Cache-Control: max-age=0');
    $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
    ob_end_clean();
    $objWriter->save('php://output');
    exit;

}

function excel_out_assess($head,$body,$name,$filename,$widths=[],$superHead='',$savePath='',$footer='')
{
    $objPHPExcel = new \PHPExcel();

    $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');

    $startRow = 1;  //从第一行开始
    //超级表头（合并单元格，显示一行字）
    if($superHead){
        $j = $i = 'A';
        $count = count($head)-1;
        for($index=0;$index<$count;$index++){
            $j++;
        }
        $objPHPExcel->setActiveSheetIndex(0)->mergeCells($i.$startRow.':'.$j.$startRow);   //合并
        $objPHPExcel->setActiveSheetIndex(0)->setCellValue($i.$startRow, $superHead);
        $objPHPExcel->getActiveSheet()->getRowDimension($startRow)->setRowHeight(51.75);
        $objPHPExcel->getActiveSheet()->getStyle($i.$startRow)->applyFromArray(
            array(
                'font' => array (
                    'bold' => true
                ),
                'alignment' => array(
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER
                )
            )
        );
        $objPHPExcel->getActiveSheet()->getStyle($i.$startRow.':'.$j.$startRow)->getFont()->setSize(20);
        $objPHPExcel->getActiveSheet()->getStyle($i.$startRow.':'.$j.$startRow)->getAlignment()->setWrapText(true);
        $startRow++;
    }

    //表头
    $i='A';
    foreach($head as $item){
        //字体
        $objPHPExcel->getActiveSheet()->getStyle($i.$startRow)->getFont()->setName('黑体');
        //行高
        $objPHPExcel->getActiveSheet()->getRowDimension($startRow)->setRowHeight(18.75);
        //居中
        $objPHPExcel->getActiveSheet()->getStyle($i.$startRow)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $objPHPExcel->getActiveSheet()->getStyle($i.$startRow)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::VERTICAL_CENTER);
        $objPHPExcel->setActiveSheetIndex(0)->setCellValue($i.$startRow, $item);
        $i++;
    }
    $count = count($head);

    //主体数据
    foreach($body as $key=>$value){
        $startRow++;
        $index='A';
        for($i=0;$i<$count;$i++){
            //字体
            $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getFont()->setName('黑体');

            //水平居中
//            if($index!='D'){
            $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
//            }
            //垂直居中
            $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);

            $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getAlignment()->setWrapText(TRUE);
            $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getNumberFormat()->setFormatCode("@"); //文本格式
            if(is_numeric($value[$i]) && strlen($value[$i])>10){
                //大于10位的数字设置为文本格式，防止变为科学计数
                $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit($index.$startRow, $value[$i], PHPExcel_Cell_DataType::TYPE_STRING);
            }else{
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue($index.$startRow, $value[$i]);
            }
            $index++;
        }
    }

    //设置表尾
    if($footer){
        $startRow+=2;
        $j = $i = 'A';
        $count = count($head)-1;
        for($index=0;$index<$count;$index++){
            $j++;
        }
        $objPHPExcel->getActiveSheet()->getStyle($i.$startRow)->getFont()->setName('黑体');
        $objPHPExcel->setActiveSheetIndex(0)->mergeCells($i.$startRow.':'.$j.$startRow);   //合并
        $objPHPExcel->setActiveSheetIndex(0)->setCellValue($i.$startRow, $footer);
        $objPHPExcel->getActiveSheet()->getRowDimension($startRow)->setRowHeight(127.75);
        $objPHPExcel->getActiveSheet()->getStyle($i.$startRow.':'.$j.$startRow)->getAlignment()->setWrapText(true);
        $startRow++;
    }

    //设置宽度
    if($widths){
        $index='A';
        for($i=0;$i<$count;$i++){
            $objPHPExcel->getActiveSheet()->getColumnDimension($index)->setWidth($widths[$i]);
            $index++;
        }
    }

    //设置边框
    //设置单元格边框
    $styleArray = array(
        'borders' => array(
            'allborders' => array(
                'style' => \PHPExcel_Style_Border::BORDER_THIN,//细边框
            ),
        ),
    );
    $objPHPExcel->getActiveSheet()->getStyle('A2:Q'.($startRow-2))->applyFromArray($styleArray);

    //设置横向打印
    $objPHPExcel->getActiveSheet()->getPageSetup()->setOrientation(PHPExcel_Worksheet_PageSetup::ORIENTATION_LANDSCAPE);

    //设置页边距
//    $margin = 0.5 / 2.54;
    $objPHPExcel->getActiveSheet()->getPageMargins()->setTop(0.8/ 2.54);
    $objPHPExcel->getActiveSheet()->getPageMargins()->setRight(0.3/ 2.54);
    $objPHPExcel->getActiveSheet()->getPageMargins()->setBottom(1.9/ 2.54);
    $objPHPExcel->getActiveSheet()->getPageMargins()->setLeft(0.5/ 2.54);

    //自动填充到页面的宽度
    $objPHPExcel->getActiveSheet()->getPageSetup()->setFitToWidth(1);
    //自动填充到页面的高度
    $objPHPExcel->getActiveSheet()->getPageSetup()->setFitToHeight(0);

    $objPHPExcel->getActiveSheet()->setTitle($name);
    $objPHPExcel->setActiveSheetIndex(0);
    if(!$savePath){
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="'.$filename.'.xls"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        ob_end_clean();
        $objWriter->save('php://output');
        exit;
    }else{
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter->save($savePath);
    }
}

/**
 * 设置单元格背景色
 * @param $objPHPExcel
 * @param $cells
 * @param $color
 * @return void
 */
function setCellColor($objPHPExcel,$cells,$color){
    $objPHPExcel->getActiveSheet()->getStyle($cells)->getFill()->applyFromArray(array(
        'type' => PHPExcel_Style_Fill::FILL_SOLID,
        'startcolor' => array(
            'rgb' => $color
        )
    ));
}

/**
 * 合并单元格
 * 示例：将A1至A5的单元格合并：mergeCells($objPHPExcel,'A1:A5')
 * @param $objPHPExcel
 * @param $cells
 * @return void
 */
function mergeCells($objPHPExcel,$cells)
{
    if(is_array($cells)){
        foreach ($cells as $cell){
            $cellArr = explode(':',$cell);
            $objPHPExcel->getActiveSheet()->mergeCells($cell);
            $objPHPExcel->getActiveSheet()->getStyle($cellArr[0])->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        }
    }else{
        $cellArr = explode(':',$cells);
        $objPHPExcel->getActiveSheet()->mergeCells($cells);
        $objPHPExcel->getActiveSheet()->getStyle($cellArr[0])->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
    }
}

function word_out($values,$sourceFile,$filename='',$targetFile='',$download=false)
{
    require_once APPPATH.'/plugin/phpoffice/common/src/Common/Text.php';
    require_once APPPATH.'/plugin/phpoffice/phpword/src/PhpWord/Settings.php';
    require_once APPPATH.'/plugin/phpoffice/phpword/src/Phpword/PhpWord.php';
    require_once APPPATH.'/plugin/phpoffice/phpword/src/Phpword/TemplateProcessor.php';
    require_once APPPATH.'/plugin/phpoffice/phpword/src/Phpword/Shared/ZipArchive.php';

    $templateProcessor = new \PhpOffice\PhpWord\TemplateProcessor($sourceFile);
    foreach ($values as $key=>$value){
        if(is_array($value)){
            foreach ($value as $k=>$v){
                if(is_array($v)){
                    $keys = array_keys($v);
                    $newData = [];
                    $i=0;
                    foreach ($values[$key] as $vv){
                        foreach ($vv as $kkk=>$vvv){
                            $newData[$i][$key.'.'.$kkk] =$vvv;
                        }
                        $i++;
                    }
                    $templateProcessor->cloneRowAndSetValues($key.'.'.$keys[0],$newData);
                    break;
                }else{
                    $templateProcessor->setValue($key,$value);
                }
            }
        }else{
            //$templateProcessor->setImageValue('yingye_cover', ['path' => $model->yingye_cover, 'width' => 364, 'height' => 500, 'ratio' => true]);
            if(isImage($value)!==false) {
                $templateProcessor->setImageValue($key, $value);
            }
            else $templateProcessor->setValue($key,$value);
        }
    }
    if(empty($targetFile)) $targetFile = tempnam(sys_get_temp_dir(), 'tmp');
    $templateProcessor->saveAs($targetFile);
    if($download){
        ob_start();
        $date=date("Ymd-H:i:m");
        $size=readfile($targetFile);
        header( "Content-type: application/octet-stream ");
        header( "Accept-Ranges: bytes");
        header( "Content-Disposition: attachment; filename=".rtrim($filename,'.docx').'.docx');
        header( "Accept-Length: " .$size);
        exit();
    }
}

function excel_in($excelPath,$startRow=1,$maxColumn=50)
{
    $cacheMethod = \PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;

    $cacheSettings = array();
    \PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $inputFileType = PHPExcel_IOFactory::identify($excelPath);
    $objReader = \PHPExcel_IOFactory::createReader($inputFileType);
    $objReader->setReadDataOnly(true);
    $objPHPExcel = $objReader->load($excelPath);
    $sheet = $objPHPExcel->getSheet(0);
    $highestRow = $sheet->getHighestRow();           //取得总行数
    $highestColumn = $sheet->getHighestColumn();     //取得总列数
    $objWorksheet = $objPHPExcel->getActiveSheet();
    $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);//总列数
    if ($highestColumnIndex > $maxColumn) $highestColumnIndex = $maxColumn;
    $bodys = [];
    $line = 0;
    for ($row = $startRow; $row <= $highestRow; $row++) {
        $line++;
        $tmp = [];
        for ($col = 0; $col < $highestColumnIndex; $col++) {
            $value = (string)$objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
            if(strlen($value)>0) $tmp[$col+1] = $value;
        }
        if($tmp) $bodys[$line] = $tmp;

    }
    return $bodys;
}

function getLastSql()
{
    dd(base64_decode($_SESSION['last_sql']));
}

function getSelectGroup($select='',$f=false)
{
    $htmlStr = '';
    $result = get_select_data('group');
    foreach((array)$result as $key => $val){
        if(!$f && !in_array($key,(array)input::getInput("session.group_id"))) continue;
        $htmlStr .= '<option value="'.$key.'" ';
        if($select == $key) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}

function getSelectGroupArray($f=false)
{
    $data = [];
    $result = get_select_data('group',1);
    foreach((array)$result as $key => $val){
        if(!$f && !in_array($key,(array)input::getInput("session.group_id"))) continue;
        $data[$key] = $val;
    }
    return $data;
}

function getStateOption($select=0)
{
    $options = array('2'=>'等待申报单位审核','3'=>'申报单位退回','5'=>'等待推荐部门审核','6'=>'推荐部门退回','9'=>'等待科知局受理','10'=>'科知局已受理','12'=>'科知局退回','25'=>'网络评审结束','28'=>'大会评审结束','29'=>'项目不授奖','30'=>'项目已授奖');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}

function getGroupList($select='')
{
    $addWhere = "`year` = '".config::get('current_declare_year',date("Y"))."' AND group_id IN ('".implode("','",input::getInput("session.office_id"))."') ";
    $result = sf::getModel("ProjectAwardGroup")->selectAll($addWhere);
    $htmlStr = '';
    while($nation = $result->getObject()){
        $htmlStr .= '<option value="'.$nation->getSubject().'" ';
        if($select == $nation->getSubject()) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$nation->getSubject()."(".$nation->getNote().")</option>\r\n";
    }
    return $htmlStr;
}

function getGroupListPropertyright($select='')
{
    $addWhere = "`year` = '".config::get('current_declare_year',date("Y"))."' AND group_id IN ('".implode("','",input::getInput("session.office_id"))."') ";
    $result = sf::getModel("PropertyrightGroup")->selectAll($addWhere);
    $htmlStr = '';
    while($nation = $result->getObject()){
        $htmlStr .= '<option value="'.$nation->getSubject().'" ';
        if($select == $nation->getSubject()) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$nation->getSubject()."(".$nation->getNote().")</option>\r\n";
    }
    return $htmlStr;
}

function getSelectCglx($select='')
{
    $result = sf::getModel("guideline")->selectAll();
    $htmlStr = '';
    while($nation = $result->getObject()){
        $htmlStr .= '<option value="'.$nation->getCode().'" ';
        if($select == $nation->getCode()) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$nation->getSubject()."</option>\r\n";
    }
    return $htmlStr;
}

function getSelectXylx($select='')
{
    $array = array('J'=>'经济效益为主','S'=>'社会效益为主','Z'=>'综合类型');
    $htmlStr = '';
    foreach($array as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($select == $key) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}

/**
 * 弹出消息(适用于在子窗口弹出消息并刷新父窗口)
 * @param $str
 */
function showMsg($str)
{
    echo "<script>alert('{$str}');parent.document.location.reload();</script>";
    exit();
}

/**
 * 生成按钮
 */
function btn()
{
    $agrs = func_get_args();
    $type = array_shift($agrs);
    return call_user_func_array(['\\App\\Lib\\Button',$type],$agrs);
}


/**
 * 生成按钮(兼容老系统)
 */
function btn2($name,$link,$icon='',$className='primary',$type='button',$width='600',$height='460',$attr='')
{
    $link = strpos($link,'javascript')===0 ? $link : site_url($link);
    $onclick = $type=='window' ? $onclick='onclick="return showWindow(\''.$name.'\',\''.$link.'\',{area:[\''.$width.'px\',\''.$height.'px\']});"' : '';
    $url = $type=='window' ? 'javascript:void(0);' : $link;
    $iconHtml = $icon ? '<i class="'.$icon.'"></i> ' : '';
    return '<a href="'.$url.'" '.$onclick.' class="btn btn-sm btn-alt-'.$className.' waves-effect waves-light" role="button" '.$attr.'>'.$iconHtml.$name.'</a>';
}

/**
 * 51:四川省
 */
function get_region_data($code='51',$level=0,$fields=['code','region_name'])
{
    $options = array();
    $addWhere = '';
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
    if(!$options = $cache->getCache($code.$level)){

        $result = Region::where('code','like',$code.'%');
        $level && $result = $result->where('level',$level);
        $result = $result->get();

        foreach($result as $option)
            $options[$option->$fields[0]] = $option->$fields[1];
        $cache->setCache($code.$level,$options);
    }
    return $options;
}

function isSuper()
{
    if(in_array(input::getInput("session.userlevel"),[1,6])) return true;
    else return false;
}

function is_serialized($data)
{
    if(is_array($data)) return false;
    $data = trim( $data );
    if ( 'N;' == $data )
        return true;
    if ( !preg_match( '/^([adObis]):/', $data, $badions ) )
        return false;
    switch ( $badions[1] ) {
        case 'a' :
        case 'O' :
        case 's' :
            if ( preg_match( "/^{$badions[1]}:[0-9]+:.*[;}]\$/s", $data ) )
                return true;
            break;
        case 'b' :
        case 'i' :
        case 'd' :
            if ( preg_match( "/^{$badions[1]}:[0-9.E-]+;\$/", $data ) )
                return true;
            break;
    }
    return false;
}

function unserializeAll($str)
{
    $arr = [];
    if(is_serialized($str)){
        $arr = unserialize($str);
        if(is_array($arr)){
            foreach ($arr as $k=>&$v){
                $v = unserializeAll($v);
            }
        }
        return $arr;

    }else return $str;
}

function makeTemplate($id)
{
    $form = sf::getModel('Forms',$id);
    if($form->isNew() || !$form->getTemplate() || !$form->getConfig()) return;
    $filename = APPPATH.'view/declare/pdefault/'.$form->getTemplate();    //写入的文件名
    $content =  $form->getConfig();    //写入的内容
    sf::getLib('MakeTemplate')->make($id,$filename,$content);
}

/**
 * 判断是否是ajax请求
 * @return bool
 */
function isAjax()
{
    return (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && (strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'));
}

/**
 * 取得表单列表
 */
function get_form_option()
{
    $options = array();
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
    if(!$options = $cache->getCache('get_form_option')){
        $result = sf::getModel("Forms")->selectAll('is_show > 0','ORDER BY id ASC');
        while($option = $result->getObject())
            $options[$option->getId()] = $option->getSubject();
        $cache->setCache('get_form_option',$options);
    }
    return $options;
}

/*
	选中的选项加黑
 */
function getCheckedStr($array,$selstr,$br=false,$checkType='value')
{
    $flag = "□";
    $str = "";
    foreach ($array as $key => $item){
        if(is_array($selstr)){
            if(in_array($item,$selstr)!==false){
                $flag='☑';
            }else{
                $flag='□';
            }
            if($br){
                if($key == count($array)-1){
                    $str.=$flag.$item;
                }else{
                    $str.=$flag.$item.'<br/>';
                }
            }else{
                $str.=$flag.$item.'  ';
            }
        }else{
            $flag='□';
            if($checkType=='value' && $item==$selstr) $flag='☑';
            if($checkType=='key' && $key===$selstr) $flag='☑';
            if($br){
                if($key == count($array)-1){
                    $str.=$flag.$item;
                }else{
                    $str.=$flag.$item.'<br/>';
                }
            }else{
                $str.=$flag.$item.'  ';
            }
        }
    }
    return $str;
}

/**
 * 精确加法
 */
function mathAdd($a,$b,$scale = '2') {
    return bcadd($a,$b,$scale);
}

/**
 * 精确减法
 */
function mathSub($a,$b,$scale = '2') {
    return bcsub($a,$b,$scale);
}
/**
 * 精确乘法
 */
function mathMul($a,$b,$scale = '2') {
    return bcmul($a,$b,$scale);
}
/**
 * 精确除法
 */
function mathDiv($a,$b,$scale = '2') {
    return bcdiv($a,$b,$scale);
}
function methodSelect(){
    return getSelectFromArray([
        '1'=>'填写中',
        '2'=>'待单位审核',
        '3'=>'单位退回',
        '9'=>'待省卫生健康委审核',
        '12'=>'省卫生健康委退回',
        '10'=>'审核通过'],'',false);
}
function patentSelect($statement=''){
    return getSelectFromArray([
        '1'=>'填写中',
        '2'=>'待单位审核',
        '3'=>'单位退回',
        '9'=>'省卫生健康委退回',
        '10'=>'审核通过'],$statement,false);
}
function paperSelect($statement=''){
    return getSelectFromArray([
        '1'=>'填写中',
        '2'=>'待单位审核',
        '3'=>'单位退回',
        '9'=>'待省卫生健康委审核',
        '12'=>'省卫生健康委退回',
        '10'=>'审核通过'],$statement,false);
}
function workSelect(){
    return getSelectFromArray([
        '1'=>'填写中',
        '2'=>'待单位审核',
        '3'=>'单位退回',
        '9'=>'待省卫生健康委审核',
        '12'=>'省卫生健康委退回',
        '10'=>'审核通过'],'',false);
}
//判断是否是上级单位（二级单位）
function isParent($user_id='')
{
    $corporation = sf::getModel("Corporations")->selectByUserId($user_id);
    if(in_array($corporation->getLevel(),['0','1'])){
        return true;
    }else{
        return false;
    }
}
//单位级别
function getUnitLevelName()
{
    if(!input::getInput("session.unitlevel")) return;
    if(input::getInput("session.unitlevel")==1){
        return '（一级公司）';
    }
    if(input::getInput("session.unitlevel")==2){
        return '（二级公司）';
    }
    return '（三级公司）';
}
//申报书状态
function projectStateSelect($select=0){
    $options = array('1'=>'填写中','2'=>'待依托单位审核','3'=>'依托单位退回','5'=>'待主管部门推荐','6'=>'主管部门退回','9'=>'待科技厅受理','12'=>'科技厅退回','10'=>'科技厅已受理');
    if(in_array(input::session('userlevel'),[1,5,6])){
        $options['13'] = '项目已分组';
        $options['18'] = '已分配专家';
        $options['20'] = '评审完毕（未立项）';
    }
    $options['2029'] = '评审完毕';
    $options['28'] = '未立项';
    $options['29'] = '已立项';
    $options['30'] = '已验收';
    $options['2930'] = '已立项（含已验收）';
    $options['40'] = '已终止';
    $options['99'] = '已过期';
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}

function monthStateSelect($select=0)
{
    $options = array('1'=>'填写中','5'=>'待主管部门审核','6'=>'主管部门退回','9'=>'待科技厅审核','12'=>'科技厅退回','10'=>'科技厅已审核');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//任务书书状态
function getTaskOption($select=0)
{
    $options = array('1'=>'填写中','2'=>'待单位审核','3'=>'单位退回','4'=>'待科技厅审核','5'=>'科技厅退回','6'=>'待科技厅审核','10'=>'科技厅已审核','7'=>'科技厅退回');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//中期报告状态
function getStageOption($select='')
{
    $options = array('-1'=>'未填报','1'=>'填写中','2'=>'待单位审核','3'=>'单位退回','5'=>'待主管部门审核','6'=>'主管部门退回','9'=>'待科技厅审核','10'=>'科技厅已审核','12'=>'科技厅退回');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//验收报告状态
function getCompleteOption($select='')
{
    $options = array('-1'=>'未填报','1'=>'填写中','2'=>'待单位审核','3'=>'单位退回','5'=>'待主管部门审核','6'=>'主管部门退回','9'=>'待科技厅审核','10'=>'科技厅已审核','12'=>'科技厅退回');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//年度考核告状态
function getReviewOption($select='')
{
    $options = array('-1'=>'未填报','1'=>'填写中','5'=>'待主管部门审核','6'=>'主管部门退回','9'=>'待科技厅审核','10'=>'科技厅已审核','12'=>'科技厅退回');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//验收书状态
function getCompleOption($select=0)
{
    $options = array('1'=>'填写中','2'=>'待单位审核','3'=>'单位退回','6'=>'待科技厅审核','24'=>'科技厅退回','14'=>'科技厅已审核');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//进步奖状态
function getAwardOption($select=0)
{
    $options = array('1'=>'填写中','2'=>'待申报单位审核','3'=>'申报单位退回','5'=>'待上级单位审核','6'=>'上级单位退回','9'=>'待科技创新部受理','10'=>'科技创新部已受理');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}

function getPlanOption($select=0)
{
    $plans = sf::getModel("ProjectPlans")->selectAll("first_id = '9B581D83-B2BA-7337-0678-A875B156B111' and statement = 10");
    while($plan = $plans->getObject()){
        $htmlStr .= '<option value="'.$plan->getPlanId().'" ';
        if($plan->getPlanId() == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$plan->getSubject()."</option>\r\n";
    }
    return $htmlStr;
}
//项目负责人等级
function getDeclareLevel($select=0)
{
    $options = array('A'=>'A 级别（正高级）','B'=>'B 级别（副高级）','C'=>'C 级别（中级）','D'=>'D 级别（初级）','E'=>'E 级别（其他）');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}

function moneyMark($money)
{
    switch($money)
    {
        case 'device':
            return '设备费';
            break;
        case 'device1':
            return '购置设备费';
            break;
        case 'device2':
            return '设备试制、改造、租赁费';
            break;
        case 'device3':
            return '设备改造与租赁费';
            break;
        case 'material':
            return '材料费';
            break;
        case 'test':
            return '测试化验加工费';
            break;
        case 'power':
            return '燃料动力费';
            break;
        case 'travel':
            return '差旅费';
            break;
        case 'conference':
            return '会议费';
            break;
        case 'cooperation':
            return '差旅费/会议费/国际合作与交流费';
            break;
        case 'reference':
            return '出版/文献/信息传播/知识产权事务费';
            break;
        case 'manpower':
            return '劳务费';
            break;
        case 'consultancy':
            return '专家咨询费';
            break;
        case 'other':
            return '其他支出';
            break;
        case 'indirect':
            return '间接费用';
            break;
        case 'manage':
            return '管理费（税费）';
            break;
        case 'performance':
            return '绩效支出';
            break;
        default:
            return '未知项';
            break;
    }
}
//经费支出
function getPaymentOption($select=0)
{
    $options = array('1'=>'填写中','2'=>'待单位审核','3'=>'单位退回','10'=>'已审定');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//项目类别
function getFlowType($select=0)
{
    $options = array('1'=>'集团审批类','2'=>'二级备案类','3'=>'三级备案类');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//项目外协申请状态
function getProjectOutOption($select=0)
{
    $options = array('1'=>'填写中','2'=>'待单位审核','3'=>'单位审核退回','4'=>'待科技创新部审核','5'=>'科技创新部退回','10'=>'已审批');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//项目结题申请状态
function getProjectApplyOption($select=0)
{
    $options = array('1'=>'填写中','2'=>'待单位审核','3'=>'单位审核退回','4'=>'待科技创新部审核','5'=>'科技创新部退回','10'=>'已审批');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}

//调整申请状态
function getProjectChangeOption($select='')
{
    $options = array('1'=>'填写中','2'=>'待承担单位审核','3'=>'承担单位退回','4'=>'待上级单位推荐','5'=>'上级单位退回','6'=>'待科技创新部受理','7'=>'科技创新部退回','10'=>'科技创新部已受理');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if(($key == $select) && $key != '') $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//研究性质
function getResearchType($select=0)
{
    $options = array('1'=>'创新引导课题','2'=>'技术攻关课题','3'=>'工程服务课题','4'=>'成果推广课题');
    foreach($options as $key => $val){
        $htmlStr .= '<option value="'.$key.'" ';
        if($key == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$val."</option>\r\n";
    }
    return $htmlStr;
}
//根据条件计算项目数
function selectNumBySql($data=[],$year){
    $addWhere = "SELECT * FROM `projects` WHERE declare_year = ".$year." and corporation_id not in ('D9F89BA0-7526-5119-8FD8-1215FA2850F9')";
    foreach ($data as $key => $value) {
        if(is_array($value)){
            $addWhere .= " and ".$key." in ('".implode("','",$value)."')";
        }else{
            $addWhere .= " and ".$key." = '".$value."' ";
        }
    }
    $db = sf::getLib("db");
    $query = $db->query($addWhere);
    return $db->num_rows($query);
}
//根据条件算经费
function selectMoneyBySql($data=[],$year){
    $addWhere = "SELECT sum(total_money) as money FROM `projects` WHERE declare_year = ".$year." and corporation_id not in ('D9F89BA0-7526-5119-8FD8-1215FA2850F9')";
    foreach ($data as $key => $value) {
        if(is_array($value)){
            $addWhere .= " and ".$key." in ('".implode("','",$value)."')";
        }else{
            $addWhere .= " and ".$key." = '".$value."' ";
        }
    }
    $db = sf::getLib("db");
    $query = $db->query($addWhere);
    return round($db->fetch_array($query)['money'],2);
}
//根据条件计算项目数
function selectNumByBetween($data=[],$start,$end){
    $addWhere = "SELECT * FROM `projects` WHERE declare_year between ".$start." and ".$end." and corporation_id not in ('D9F89BA0-7526-5119-8FD8-1215FA2850F9')";
    foreach ($data as $key => $value) {
        if(is_array($value)){
            $addWhere .= " and ".$key." in ('".implode("','",$value)."')";
        }else{
            $addWhere .= " and ".$key." = '".$value."' ";
        }
    }
    $db = sf::getLib("db");
    $query = $db->query($addWhere);
    return $db->num_rows($query);
}
//根据条件算经费
function selectMoneyByBetween($data=[],$start,$end){
    $addWhere = "SELECT sum(total_money) as money FROM `projects` WHERE declare_year between ".$start." and ".$end." and corporation_id not in ('D9F89BA0-7526-5119-8FD8-1215FA2850F9')";
    foreach ($data as $key => $value) {
        if(is_array($value)){
            $addWhere .= " and ".$key." in ('".implode("','",$value)."')";
        }else{
            $addWhere .= " and ".$key." = '".$value."' ";
        }
    }
    $db = sf::getLib("db");
    $query = $db->query($addWhere);
    return round($db->fetch_array($query)['money'],2);
}
//删除项目名称为空的项目
function delEmpPro(){
    $db = sf::getLib("db");
    $sql = "DELETE FROM projects WHERE subject is NULL";
    $db->query($sql);
}
function itemType($item_type){
    switch ($item_type) {
        case 'must_sjbg':
            return '企业年度财务审计报告';
            break;
        case 'must_cxxcl':
            return '项目创新性突出的证明材料';
            break;
        case 'must_jjxycl':
            return '项目经济效益或社会效益证明材料';
            break;
        case 'must_zscqzm':
            return '核心知识产权证明';
            break;
        case 'must_yyqk':
            return '应用情况和效益佐证材料';
            break;
        case 'must_pzwj':
            return '国家法律法规要求审批的批准文件';
            break;
        case 'must_cns':
            return '承诺书签字页';
            break;
        case 'must_hxzscq':
            return '核心知识产权证明';
            break;
        case 'must_hzb':
            return '完成人合作关系说明及情况汇总表';
            break;
        case 'must_fblw':
            return '公开发表的代表性论文及专著';
            break;
        case 'must_yylw':
            return '他人引用的代表性论文、专著';
            break;
        case 'must_zscqzm':
            return '知识产权证明';
            break;
        case 'must_hjzs':
            return '重要获奖证书';
            break;
        case 'must_zp':
            return '候选人近期标准照片及工作照片';
            break;
        case 'other_gfcl':
            return '其他主要知识产权和标准规范证明材料';
            break;
        case 'other_kgpj':
            return '客观评价材料';
            break;
        case 'other_hygx':
            return '完成单位行业贡献的证明材料';
            break;
        case 'other_xsgx':
            return '完成人学术贡献的证明材料';
            break;
        default:
            return "其他附件";
            break;
    }
}

function getBudgetMoneyByType($budget = []){
    $data['zxjf'] = $data['zcjf'] = $data['total'] = 0;
    foreach ($budget as $key => $value) {
        if($value['from'] == '专项经费'){
            $data['zxjf'] += $value['money'];
        }
        if($value['from'] == '自筹经费'){
            $data['zcjf'] += $value['money'];
        }
        $data['total'] += $value['money'];
    }
    return $data;
}


if (! function_exists('removeQuotation')) {
    function removeQuotation($char)
    {
        if(is_array($char)){
            foreach($char as $key => $val){
                $char[$key] = removeQuotation($val);
            }
        }

        $char = str_replace('\"','”',$char);
        $char = str_replace("\'",'’',$char);
        $char = str_replace('\\','|',$char);
        return $char;
    }
}


/**
 * 是否为测试人员
 */
function isTester()
{
    if(in_array(input::session("roleuserid"),['963049C2-EB30-1A8B-22A3-2CD688C40449','D9F89BA0-7526-5119-8FD8-1215FA2850F9','81555E08-63E4-BEC9-FA66-1EE888AA0D9F'])) return true;
    else return false;
}

/**
 * 取得执行器列表
 */
function get_worker_option()
{
    $options = array();
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
    if(true || !$options = $cache->getCache('get_worker_option')){
        $result = sf::getModel("EngineWorkers")->selectAll('','ORDER BY id desc');
        while($option = $result->getObject())
            $options[$option->getId()] = $option->getSubject();
        $cache->setCache('get_worker_option',$options);
    }
    return $options;
}

//获取二维码
function getQRcode($msg='')
{
    require_once WEBROOT.'/../vendor/mpdf/mpdf/qrcode/qrcode.class.php';
    $err = 'L';
    if (!in_array($err, array('L', 'M', 'Q', 'H'))) $err = 'L';

    $qrcode = new QRcode(utf8_encode($msg), $err);
    $qrcode->disableBorder();
    $imageData = base64_encode($qrcode->displayPNG());
    $src = 'data:image/png;base64,' . $imageData;
    return $src;
}


/**
 * 获取安全token
 */
function get_security_token($str)
{
    return md5(trim($str).date("YmdH").'20190923');
}

/**
 * 加密解密函数
 */
function myencrypt($string,$operation='E')
{
    $key = md5('MY-'.date("Ymd"));
    $key_length=strlen($key);
    $string=$operation=='D'?base64_decode(str_replace('-','/',$string)):substr(md5($string.$key),0,8).$string;
    $string_length=strlen($string);
    $rndkey=$box=array();
    $result='';
    for($i=0;$i<=255;$i++){
        $rndkey[$i]=ord($key[$i%$key_length]);
        $box[$i]=$i;
    }
    for($j=$i=0;$i<256;$i++){
        $j=($j+$box[$i]+$rndkey[$i])%256;
        $tmp=$box[$i];
        $box[$i]=$box[$j];
        $box[$j]=$tmp;
    }
    for($a=$j=$i=0;$i<$string_length;$i++){
        $a=($a+1)%256;
        $j=($j+$box[$a])%256;
        $tmp=$box[$a];
        $box[$a]=$box[$j];
        $box[$j]=$tmp;
        $result.=chr(ord($string[$i])^($box[($box[$a]+$box[$j])%256]));
    }
    if($operation=='D'){
        if(substr($result,0,8)==substr(md5(substr($result,8).$key),0,8)){
            return substr($result,8);
        }else{
            return'';
        }
    }else{
        return str_replace(['/','='],['-',''],base64_encode($result));
    }
}

/**
 * 根据父ID获取下级
 */
function getOptionByParent($parent_id,$subject)
{
    if(!$parent_id){
        return "<option value=''>==请选择==</option>";
    }
    $cats = sf::getModel('CategoryWorks','','work')->selectAll('parent_id = '.$parent_id);
    $htmlStr = '';
    while ($cat = $cats->getObject()) {
        $sub = $cat->getSubject();
        $htmlStr .= '<option value="'.$sub.'" ';
        if($subject == $sub) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$sub."</option>\r\n";
    }
    return $htmlStr;
}

//分类是否有子分类
function hasSon($code,$type = 1)
{
    if(!in_array($type,array('1','4'))) $type = 1;
    $db = sf::getLib("Db");
    $row = $db->fetch_first("SELECT count(*) as num FROM `subjects` WHERE `type` = '".$type."' and `code` like '".$code."%' ");
    if($row['num'] > 1) return true;
    else return false;
}

function getAreaCodeName($code,$type=1)
{
    $subject = sf::getModel('Subjects')->selectByCode($code,$type);
    return $subject->isNew() ? '' : $subject->getSubject();
}

/**
 * 开启经费支出填写
 * @param $projectId
 */
function openPayment($projectId){
    $db = sf::getLib("db");
    $project = sf::getModel("Projects")->selectByProjectId($projectId);
    //申报书
    $type = 'apply';
    if($project->isSupportProject() && $project->getStateForTask()==10){
        //资助类项目从任务书中抓取
        $type = 'task';
    }
    $query = $db->query("SELECT device1,device2,material,test,power,cooperation,reference,manpower,consultancy,other,indirect,datas FROM `project_moneys` WHERE project_id = '{$projectId}' and type = '{$type}'");
    $items = $db->fetch_array($query);
    $sql = "DELETE FROM project_money WHERE project_id = '{$projectId}' and type = '{$type}'";
    $db->exec($sql);
    foreach ($items as $mark => $value){
        $item = json_decode($value,true);
        if($mark=='datas'){
            $value = json_decode($value,true);
            $item = $value['performance'];
            $mark = 'performance';
        }
        $projectMoney = sf::getModel("ProjectMoney")->selectByProjectIdAndMark($projectId,$mark,$type);
        $projectMoney->setMarkSubject(moneyMark($mark));
        $projectMoney->setApplySpecial($item['cz']);
        $projectMoney->setApplyOwn($item['zc']);
        $projectMoney->setApplyOther($item['qt']);
        $projectMoney->setApplyTotal($item['hj']);
        $projectMoney->save();
    }
}

/**
 * 更新经费支出
 * @param $projectId
 */
function updatePayment($projectId){
    $db = sf::getLib("db");
    $project = sf::getModel("Projects")->selectByProjectId($projectId);
    //申报书
    $type = 'apply';
    if($project->isSupportProject() && $project->getStateForTask()==10){
        //资助类项目从任务书中抓取
        $type = 'task';
    }
    $query = $db->query("SELECT device1,device2,material,test,power,cooperation,reference,manpower,consultancy,other,indirect FROM `project_moneys` WHERE project_id = '{$projectId}' and type = '{$type}'");
    $items = $db->fetch_array($query);
    foreach ($items as $mark => $value){
        $item = json_decode($value,true);
        $projectMoney = sf::getModel("ProjectMoney")->selectByProjectIdAndMark($projectId,$mark,$type);
        $projectMoney->setMarkSubject(moneyMark($mark));
        $projectMoney->setApplySpecial($item['cz']);
        $projectMoney->setApplyOwn($item['zc']);
        $projectMoney->setApplyOther($item['qt']);
        $projectMoney->setApplyTotal($item['hj']);
        $projectMoney->save();
    }
}

/**
 * 推送OA待办提醒
 */
function pushOAMsg($empnos=[],$url,$roleId,$title='')
{
    return;
    $title = $title ?: '你有一个待办事项需要处理';
    foreach ($empnos as $empno){
        $count = sf::getModel('OaMessages')->selectAll("is_read = 1 and empno = '{$empno}'")->getTotal();
        if($count==0){
            $log = sf::getModel('OaMessages');
            $log->setEmpno($empno);
            $log->setUrl('manager/index');
            $log->setRoleId($roleId);
            $log->setTitle('你有待办事项需要处理');
            $log->setUserId(input::getInput("session.roleuserid"));
            $log->setUserName(input::getInput("session.nickname"));
            $log->setCreatedAt(date('Y-m-d H:i:s'));
            $log->save();
        }
    }
}

/**
 * 推送消息至单位管理员
 * @param $msg 消息标题
 * @param $url 跳转的地址
 * @param string $companyId 单位id
 */
function pushMsgToCompanyManager($msg,$url,$companyId='')
{
    return;
    $users = false;
    if($companyId){
        $users = sf::getModel('Corporations')->selectByUserId($companyId)->users();
    }else{
        switch (input::session('userlevel')){
            case 2:
                $researcher = sf::getModel('Declarers')->selectByUserId(input::session('roleuserid'));
                $users = sf::getModel('Corporations')->selectByUserId($researcher->getCorporationId())->users();
                break;
            case 3:
                $users = sf::getModel('Corporations')->selectByUserId(input::session('roleuserid'))->users();
                break;
            case 10:
                $users = sf::getModel('Experts')->selectByUserId(input::session('roleuserid'))->users();
                break;
            default:
                $users = sf::getModel('Corporations')->selectByUserId($companyId)->users();
                break;
        }
    }
    if($users){
        while($user = $users->getObject()){
            if(!$user->getEmpno()) continue;
            $empno = $user->getEmpno();
            $roleId = $user->getRoleId(3);
            pushOAMsg([$empno],$url,$roleId,$msg);
        }
    }
}

/**
 * 推送消息至科研人员
 * @param $msg 标题
 * @param $url 跳转的地址
 * @param $userId 科研人员id
 */
function pushMsgToResearcher($msg,$url,$userId)
{
    return;
    $reseacher = sf::getModel('Declarers')->selectByUserId($userId);
    if(!$reseacher->isNew()){
        $users = $reseacher->users();
        while($user = $users->getObject()){
            if(!$user->getEmpno()) continue;
            $empno = $user->getEmpno();
            $roleId = $user->getRoleId(2);
            pushOAMsg([$empno],$url,$roleId,$msg);
        }
    }
}

/**
 * 推送消息至省卫生健康委管理员
 * @param $msg 标题
 * @param $url 跳转的地址
 * @param $userId 科研人员id
 */
function pushMsgToOffice($msg,$url)
{
    return;
    $managers = sf::getModel('Managers')->selectAll("user_group_id = 6");
    while($manager = $managers->getObject()){
        $users = $manager->users();
        while($user = $users->getObject()){
            if(!$user->getEmpno()) continue;
            $empno = $user->getEmpno();
            $roleId = $user->getRoleId(6);
            pushOAMsg([$empno],$url,$roleId,$msg);
        }
    }
}

function isImage($filename)
{
    $exts = ['.gif','.jpg','.jpeg','.png','.bmp']; //定义检查的图片类型
    $ext = strrchr($filename,'.');
    return in_array($ext,$exts);
}

function img_path($filePath)
{
    return isImage($filePath) ? site_path('images/index.html').'?url='.site_path($filePath) : site_path($filePath);
}

function getIframeUrl()
{
    $scheme = $_SERVER['HTTPS']=='on' ? 'https://' : 'http://';
    if($_SERVER['QUERY_STRING']) $_SERVER['QUERY_STRING'] = '?'.$_SERVER['QUERY_STRING'];
    $baseUrl = $scheme.$_SERVER['HTTP_HOST'].$_SERVER['PHP_SELF'].'/open_model/frame'.$_SERVER['QUERY_STRING'];
    return $baseUrl;
}

function getReadMe($configs,$key='')
{
    $html = '';
    $isPreviewModel = false;    //是否为预览模式
//    if(\Sofast\Core\router::getMethod()=='read' && input::getMix('type')!='download'){
    if(\Sofast\Core\router::getMethod()=='read'){
        $isPreviewModel = true;
    }
    $showReadMe = false;    //是否展示填写说明
    if($configs['action']['edit'] && $configs['readme'][$key]){
        $showReadMe = true;
    }
    if($isPreviewModel && $configs['readme'][$key]){
        $showReadMe = true;
    }
    if ($showReadMe):
        $text = $key ? showText($configs['readme'][$key]) : showText($configs['readme']);
        $html = <<<HTML
    <div class="alert alert-warning alert-dismissable" role="alert">
            <h3 class="alert-heading font-size-h6 my-2">
                {$title}
            </h3>
            <p>{$text}</p>
    </div>
HTML;
    endif;
    return $html;
}

function copyCompanyInfo2Project($project,$type='apply')
{
    $declarer = $project->user();
    $company = $project->getCorporation();
    $project->setCorporationId($declarer->getCorporationId());
    $project->setCorporationName($declarer->getCorporationName());
    $project->save();
    $baseModel = $project->getBaseinfo($type);
    $baseModel->setHospitalType($company->getType());
    $baseModel->setHospitalLevel($company->getLevel());
    $baseModel->setAddress($company->getAddress());
    $baseModel->setPostcode($company->getPostalcode());
    $baseModel->setBeds($company->getBedcount());
    $baseModel->setArea($company->getFloorage());
    $baseModel->setFormalTotal($company->getPersoncount());
    $baseModel->setFormalTech($company->getTechcount());
    $baseModel->setFormalManage($company->getManagecount());
    $data['legal_name'] = $company->getPrincipal()?:'无';
    $data['legal_phone'] = $company->getPhone()?:'无';
    $data['legal_mobile'] = $company->getMobile()?:'无';
    $data['user_name'] = $declarer->getPersonname()?:'无';
    $data['user_email'] = $declarer->getUserEmail()?:'无';
    $data['user_phone'] = $declarer->getUserPhone()?:'无';
    $data['user_mobile'] = $declarer->getUserMobile()?:'无';
    $data['tel'] = $company->getPhone()?:'无';
    $data['fax'] = $company->getLinkmanFax()?:'无';
    $baseModel->setData($data);
    return $baseModel->save();
}

function getRuleItems($subjectCode='',$type='zdzk',$moment='')
{
    $db = sf::getLib('db');
    $addwhere = "i.pid = 0 and r.`type` = '{$type}'";
    if($subjectCode) $addwhere.=" and r.code like '{$subjectCode}%'";
    if($moment) $addwhere.=" and r.moment = '{$moment}'";
    $sql="SELECT i.* FROM `rule_items` i LEFT JOIN rules r on i.rule_id = r.id where {$addwhere} GROUP BY subject3 ORDER BY i.sort asc";
    $query = $db->query($sql);
    $datas = [];
    if ($db->num_rows($query)) {
        $i=0;
        while ($data = $db->fetch_array($query)) {
            $i++;
            $subject = $data['subject'] ?:  $data['subject3'];
            $datas[$data['mark']] = '('.$i.')'.$subject;
        }
    }
    return $datas;
}

function getTopRuleItems($ruleId)
{
    $items = sf::getModel('RuleItems')->selectAll("level in (1,2) and rule_id = '{$ruleId}' and is_objective = 1","order by sort asc,id asc");
    $datas = [];
    while($item = $items->getObject()){
        $datas[$item->getId()] = ($item->getLevel()==2 ? '——' : '') . $item->getSubject3().'-'.$item->getMark();
    }
    return $datas;
}



function rsa_decode($str)
{
    //私钥
    $private_key = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

    $hex_encrypt_data = trim($str); //十六进制数据
    $encrypt_data = pack("H*", $hex_encrypt_data);//对十六进制数据进行转换
    openssl_private_decrypt($encrypt_data, $decrypt_data, $private_key);
    return $decrypt_data;
}

function checkFingerPrint($fingerprintStr='')
{
    if(!$_SESSION['userid']){
        exit(new Sofast\Core\Exception('非法来源'));
    }
    if(noCheckUser($_SESSION['userid'])) return true;
    if(!in_array($_SESSION['userlevel'],[1,6])) return true;
    $user = sf::getModel('Users')->selectByUserId($_SESSION['userid']);
    if(empty($fingerprintStr)){
        exit(new Sofast\Core\Exception('非法来源'));
    }
    if($user->checkFingerPrint($fingerprintStr)===false && isMobile($user->getUserMobile())){
        //指纹不匹配
        //1.登出
        sf::getPlugin("authentic")->logout();
        //2.跳转至手机验证页面
        $url = site_url('login/safe/userid/'.$user->getUserId());
        header("Location:{$url}");exit();
    }
    return true;
}

function noCheckUser($userid)
{
    $userids = [
        '963049C2-EB30-1A8B-22A3-2CD688C40449',     //test2009
        '7D1DCF82-C9AA-6B4D-E0F4-F203B082E5A9'     //szx
    ];
    return in_array($userid,$userids);
}

/**
 * 将IP加入黑名单
 */
function addBlacklist($ip,$time='')
{
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),1800);
    $options = $cache->getCache('ip_black_list');
    $options[$ip] = $time;
    $cache->setCache('ip_black_list',$options);
    return $options;
}

/**
 * 判断IP是否受限制
 */
function isForbid($ip)
{
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),1800);
    if(!$options = $cache->getCache('ip_black_list')) return false;
    return in_array($ip,array_keys($options));
}


function _cName($name)
{
    if(!strpos($name,"_")) return ucfirst($name);
    $name = explode("_",$name);
    $str = '';
    foreach($name as $val)
    {
        $str .= ucfirst($val);
    }
    return $str;
}

function getTrueCorporationName($name)
{
    $sql = "SELECT * FROM `corporation_maps` where old_name = '{$name}'";
    $db = sf::getLib("db");
    $query = $db->query($sql);
    if ($db->num_rows($query)) {
        while ($data = $db->fetch_array($query)) {
            return $data['name'];
        }
    }
    return $name;
}
function getTrueSubjectName($name)
{
    $name = str_replace('　',' ',$name);
    $name = str_replace(' ',' ',$name);
    $sql = "SELECT * FROM `subject_maps` where old_name = '{$name}'";
    $db = sf::getLib("db");
    $query = $db->query($sql);
    if ($db->num_rows($query)) {
        while ($data = $db->fetch_array($query)) {
            return $data['name'];
        }
    }
    return $name;
}

/**
 * 将姓名为两个字的中间加上空格
 * @param $str
 * @return string
 */
function filterName($str)
{
    if(strlen($str)==6){
        $str = substr($str,0,3).'　'.substr($str,3,3);
    }
    return $str;
}

function getChildAssessCount($id)
{
    $item = sf::getModel("RuleItems",$id);
    return $item->getChildren()->getTotal();
}

function getDisease($subjectCode='')
{
    $addwhere = "1";
    if($subjectCode) $addwhere.=" and subject_code = '{$subjectCode}'";
    $diseases = sf::getModel('Diseases')->selectAll($addwhere);
    $diseasesArr = [];
    while($disease = $diseases->getObject()){
        $diseasesArr[$disease->getId()] = $disease->getSubject();
    }
    return $diseasesArr;
}


/**
 * 是否是省直属单位
 */
function isDirectlyUnit($department_id='')
{
    if(!$department_id) return false;
    if($department_id=='13894171-8420-8EB4-9A29-E1FD260FE14F'){
        //归口id是科技厅的，都是省直属单位
        return true;
    }else return false;
}

function deleteFiles($itemId,$itemType)
{
//    $files = sf::getModel('Filemanager')->selectAll("item_id = '{$itemId}' and item_type = '{$itemType}'");
//    while($file = $files->getObject()){
//        $file->delete();
//    }
//    return true;
    return sf::getLib('db')->exec("delete from filemanager where item_id = '{$itemId}' and item_type = '{$itemType}'");
}

/**
 * 创建新进程处理
 * @param $path  控制器/方法 例如：cmd/test
 */
function createNewProcess($path)
{
    require_once APPPATH.'/plugin/process/Process.php';
    require_once APPPATH.'/plugin/process/ProcessUtils.php';
    require_once APPPATH.'/plugin/process/Pipes/PipesInterface.php';
    require_once APPPATH.'/plugin/process/Pipes/AbstractPipes.php';
    require_once APPPATH.'/plugin/process/Pipes/WindowsPipes.php';
    $process = new \Symfony\Component\Process\Process(['php',WEBROOT.'/../cmder/CLI',$path]);
    $process->setOptions(['create_new_console' => true]);
    $process->start();
}


/**
 * 获取三家医院名单
 */
function getTop3Companys()
{
    $companys = sf::getModel('Companys2')->selectAll();
    $datas = [];
    while($company = $companys->getObject()){
        $datas[$company->getCode()] = $company->getSubject();
    }
    return $datas;
}


/**
 * 返回数组的维度
 * @param  [type] $arr [description]
 * @return [type]      [description]
 */
function arrayLevel($arr)
{
    $al = array(0);
    aL($arr, $al);
    return max($al);
}

function aL($arr, &$al, $level = 0)
{
    if (is_array($arr)) {
        $level++;
        $al[] = $level;
        foreach ($arr as $v) {
            aL($v, $al, $level);
        }
    }
}

function makeShortUrl($longurl)
{
    $host = 'https://www.dwz.lc';
    $path = '/api/url/add';
    $url = $host . $path;
    $method = 'POST';
    $content_type = 'application/json';
    //设置Token
    $token = 'TZwrI1cxLRLTLP7VjHpA';
    //长网址
    $bodys = array('url'=>$longurl);
    // 配置headers
    $headers = array('Content-Type:'.$content_type, 'Authorization:'.'Token '.$token);
    // 创建连接
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_FAILONERROR, false);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HEADER, false);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($bodys));
    // 发送请求
    $response = curl_exec($curl);
    if($response === false)
    {
        echo 'Curl error: ' . curl_error($curl);exit();
    }
    curl_close($curl);
    $arr = json_decode($response,true);
    if($arr['error']==0){
        return $arr['short'];
    }
    return $longurl;
}

function makeShortUrlByBaidu($longurl)
{
    $host = 'https://dwz.cn';
    $path = '/admin/v2/create';
    $url = $host . $path;
    $method = 'POST';
    $content_type = 'application/json';
    //设置Token
    $token = 'd14d3b0a020c37e4ab07798c512f8f2a';
    //长网址
    $bodys = array('Url'=>$longurl);
    // 配置headers
    $headers = array('Content-Type:'.$content_type, 'Token:'.$token);
    // 创建连接
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_FAILONERROR, false);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HEADER, false);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($bodys));
    // 发送请求
    $response = curl_exec($curl);
    if($response === false)
    {
        echo 'Curl error: ' . curl_error($curl);exit();
    }
    curl_close($curl);
    $arr = json_decode($response,true);
    if($arr['Code']==0){
        return $arr['ShortUrl'];
    }
    return $longurl;
}

function getUserNameByMobile($mobile)
{
    $user = sf::getModel('Users')->selectByMobile($mobile);
    return $user!==false ? $user->getUserUsername() : false;
}

function getProjects($level = '')
{
    $addwhere = "statement in (29,30)";
    if(input::session('userlevel')==3){
        $addwhere .=" and corporation_id = '".input::session('roleuserid')."'";
    }
    if(input::session('userlevel')==4){
        $addwhere .=" and department_id = '".input::session('roleuserid')."'";
    }
    if($level){
        $addwhere .=" and level IN ({$level})";
    }
    $projects = sf::getModel('Projects')->selectAll($addwhere);
    $datas = [];
    while($project = $projects->getObject()){
        $subject = $project->getSubject().'('.$project->getLevel().')';
        if(input::session('userlevel')==4) $subject .= '-'.$project->getCorporationName();
        $datas[$project->getProjectId()] = $subject;
    }
    return $datas;
}

function getProjectCount($level='',$radiateYear='')
{
    $addWhere = getMonthProjectCondition($level,$radiateYear);
    if(input::session('userlevel')==3){
        $addWhere .=" and corporation_id = '".input::session('roleuserid')."'";
    }
    if(input::session('userlevel')==4){
        $addWhere .=" and department_id = '".input::session('roleuserid')."'";
    }
    return (int)sf::getLib('db')->result_first("select count(*) c from projects where {$addWhere}");

}

/**
 * 获取需填写月度报告的筛选条件
 */
function getMonthProjectCondition($level='',$radiateYear='')
{
    //国重：未验收，经费未执行完毕的
    //省重：未验收，2023经费未执行完毕的
    //2022年度还需要报进度的项目有：西南医科大附院：麻醉科、肿瘤科、呼吸内科、心血管外科，省院：心血管外科，凉山：凉山州第一人民医院心血管外科。
    //西南医科大附院国家级：重症医学科
    $addWhere = ' statement = 29 and month_open = 1';
    if($level) $addWhere.=" and `level` = '{$level}'";
    if(is_array($radiateYear)) $addWhere.=" and `radicate_year` IN ('".implode("','",$radiateYear)."')";
    elseif($radiateYear) $addWhere.=" and `radicate_year` = '{$radiateYear}'";
    return $addWhere;

//    if(empty($radiateYear)) $radiateYear = config::get('current_declare_year',date('Y'));
//    $addWhere = ' statement = 29 ';
//    if($level){
//        if($level=='国家级') $addWhere.= " and level = '国家级' and money_use_rate < 100 ";
//        else $addWhere.= " and level = '{$level}' and radicate_year = {$radiateYear}";
//        return $addWhere;
//    }
//
//    return $addWhere." and ((level = '国家级' and money_use_rate < 100) or (level != '国家级' and radicate_year = 2023) or (project_id in ('4E6D974D-96A6-7E8B-EB83-CC7EC3734484','C4928CA8-E267-2D4E-040D-937D5514E433','F2FF949A-9369-8792-DFBA-9B763A0618BF','081CA805-48B0-9E38-B897-C9B22F4B8B1D','717AE9C9-6402-2290-336C-6C4848ADC31D','05B85570-EDE4-916F-E092-6918C67D398E','5D60C19D-161E-65CB-7652-A78ACD7481AD'))) ";
}

/**
 * 获取需填写月度报告的项目数量
 * @param $levels
 * @param $radicateYear
 * @return int
 */
function getMonthProjectCount()
{

    $addWhere = getMonthProjectCondition();
    if(input::session('userlevel')==3){
        $addWhere .=" and corporation_id = '".input::session('roleuserid')."'";
    }
    if(input::session('userlevel')==4){
        $addWhere .=" and department_id = '".input::session('roleuserid')."'";
    }

    return (int)sf::getLib('db')->result_first("select count(*) c from projects where {$addWhere}");

}


/**
 * 获取需填写月度报告的项目
 * @param $levels
 * @param $radicateYear
 * @return int
 */
function getMonthProjects()
{
    $addWhere = getMonthProjectCondition();
    if(input::session('userlevel')==3){
        $addWhere .=" and corporation_id = '".input::session('roleuserid')."'";
    }
    if(input::session('userlevel')==4){
        $addWhere .=" and department_id = '".input::session('roleuserid')."'";
    }
    $projects = sf::getModel('Projects')->selectAll($addWhere);
    $datas = [];
    while($project = $projects->getObject()){
        $subject = $project->getSubject().'('.$project->getLevel().')';
        $datas[$project->getProjectId()] = $subject;
    }
    return $datas;
}

function getIndexWeight($indexCode,$guide)
{
    $rule = $guide->getRule();
    if($rule->isNew()) return 0;
    $ruleItem = sf::getModel('RuleItems')->selectByRuleIdAndMark($rule->getId(),$indexCode);
    if($ruleItem->isNew()) return 0;
    return $ruleItem->getWeight();
}

function getIndexWeightBySubject1($subject1,$guide)
{
    $rule = $guide->getRule();
    if($rule->isNew()) return 0;
    $ruleItems = sf::getModel('RuleItems')->selectAll("level = 1 and rule_id = '".$rule->getId()."' and subject1 = '{$subject1}'");
    $weight = 0;
    while($ruleItem = $ruleItems->getObject()){
        $weight+=$ruleItem->getWeight();
    }
    return $weight;
}

function getIndexWeightBySubject2($subject1,$subject2,$guide)
{
    $rule = $guide->getRule();
    if($rule->isNew()) return 0;
    $ruleItems = sf::getModel('RuleItems')->selectAll("level = 1 and rule_id = '".$rule->getId()."' and subject1 = '{$subject1}'and subject2 = '{$subject2}'");
    $weight = 0;
    while($ruleItem = $ruleItems->getObject()){
        $weight+=$ruleItem->getWeight();
    }
    return $weight;
}

function getIndexCodesBySubject1($subject1,$guide)
{
    $rule = $guide->getRule();
    if($rule->isNew()) return 0;
    $ruleItems = sf::getModel('RuleItems')->selectAll("level = 1 and rule_id = '".$rule->getId()."' and subject1 = '{$subject1}'");
    $indexCodes = [];
    while($ruleItem = $ruleItems->getObject()){
        $indexCodes[]=$ruleItem->getMark();
    }
    return $indexCodes;
}


function numberFormat($number)
{
    $number = sprintf('%.2f',$number);
    if(ends_with($number,'.00')){
        $number = str_replace_last('.00','',$number);
    }
    return $number;
}

function getLastMonth($format='n',$step=1)
{
    $now=date("Y-m-01");
    return date($format,strtotime("$now -{$step} month"));
}

function getLastYearByWeek($format='Y',$step=1)
{
    $now=date("Y-m-d");
    return date($format,strtotime("$now -{$step} week"));
}

function getLastMonthByWeek($format='n',$step=1)
{
    $now=date("Y-m-d");
    return date($format,strtotime("$now -{$step} week"));
}

function getLastWeek($format='W',$step=1)
{
    $now=date("Y-m-d");
    return date($format,strtotime("$now -{$step} week"));
}

function getLastQuarter()
{
    $lastQuarter =  (ceil((date('n'))/3)-1);
    return $lastQuarter ?: 4;
}

function getLastYear($format='Y')
{
    if(getLastQuarter()==4) return date('Y')-1;
    $now=date("Y-m-01");
    return date($format,strtotime("$now -1 month"));
}

function getDiagnosisList()
{
    $addWhere = "level = 2";
    $subjects = sf::getModel('DiagnosisSubjects')->selectAll($addWhere,"order by code asc,id asc");
    $datas = [];
    while($subject = $subjects->getObject()){
        $datas[$subject->getCode()] = $subject->getSubject();

    }
    return $datas;
}

function getRankDiagnosisList()
{
    $addWhere = "1";
    $subjects = sf::getModel('IndexRanks')->selectAll($addWhere,"GROUP BY diagnosis_code ORDER BY diagnosis_code asc");
    $datas = [];
    while($subject = $subjects->getObject()){
        $datas[$subject->getDiagnosisCode()] = $subject->getDiagnosisSubject();

    }
    return $datas;
}

function getReportDiagnosisList()
{
    $addWhere = "1";
    $subjects = sf::getModel('IndexReports')->selectAll($addWhere,"GROUP BY diagnosis_code ORDER BY diagnosis_code asc");
    $datas = [];
    while($subject = $subjects->getObject()){
        $datas[$subject->getDiagnosisCode()] = $subject->getDiagnosisSubject();

    }
    return $datas;
}

function getDiagnosisSubjects()
{
    $subjects = sf::getModel('DiagnosisSubjects')->selectAll('',"order by code asc,id asc");
    $datas = [];
    while($subject = $subjects->getObject()){
        $datas[$subject->getId()] = $subject->getSubject();
    }
    return $datas;
}

function getDiagnosisSubject($code)
{
    return sf::getModel('DiagnosisSubjects')->selectByCode($code)->getSubject();
}

function getDiagnosisCode($subject)
{
    return sf::getModel('DiagnosisSubjects')->selectBySubject($subject,2)->getCode();
}

function getSubjectCodeByDiagnosisCode($code)
{
    return sf::getModel('Guides')->selectByDiagnosisCode($code)->getMark();
}

function getDiagnosisCodeBySubjectCode($subjectCode)
{
    $guide = sf::getModel('Guides')->selectByMark($subjectCode);
    return $guide->isNew() ? '' : $guide->getDiagnosisCode();
}

function getNoRadicateDepartmentCount()
{
    return (int)sf::getLib('db')->result_first("select count(*) c from departments where no_radicate_reason is not null and no_radicate_reason !=''");
}

function getStatisticRankStr($str)
{
    if($str=='未上榜') return '无';
    $arr = [];
    $a = explode('、',$str);
    foreach ($a as $v){
        if(strstr($v,'提名')!==false){
            $arr[] = '提名';
        }else{
            $arr[] = '上榜';
        }
    }
    $arr = array_unique($arr);
    return empty($arr) ? '无' : implode('、',$arr);
}

function getSmallNumber($num=0)
{
    if($num<=0 || $num>10) return $num;
    $arr = ['①','②','③','④','⑤','⑥','⑦','⑧','⑨','⑩'];
    return $arr[$num-1];
}

function getScoreButton($projectId,$mark,$text='',$class='float-right')
{
    if(input::session('userlevel')==1 && input::session('username')=='super') {
        if(empty($text)) $text = '计算得分';
        return '<span class="'.$class.'">'.Button::setUrl(site_url('index/score/common/getSingleScore/code/'.$mark.'/id/'.$projectId))->setClass('btn-alt-danger')->window($text).'</span>';
    }
    return '';
}

function getStageScoreButton($projectId,$mark,$text='',$class='float-right')
{
    if(input::session('userlevel')==1 && input::session('username')=='super') {
        if(empty($text)) $text = '计算得分';
        return '<span class="'.$class.'">'.Button::setUrl(site_url('index/score/stage/getSingleScore/code/'.$mark.'/id/'.$projectId))->setClass('btn-alt-danger')->window($text).'</span>';
    }
    return '';
}
function getCompleteScoreButton($projectId,$mark,$text='',$class='float-right')
{
    if(input::session('userlevel')==1 && input::session('username')=='super') {
        if(empty($text)) $text = '计算得分';
        return '<span class="'.$class.'">'.Button::setUrl(site_url('index/score/complete/getSingleScore/code/'.$mark.'/id/'.$projectId))->setClass('btn-alt-danger')->window($text).'</span>';
    }
    return '';
}
function getFakeDataButton($url,$text='',$class='float-left')
{
    if(input::session('userlevel')==1 && input::session('username')=='super') {
        if(empty($text)) $text = '虚拟数据';
        return '<span class="'.$class.'">'.Button::setUrl($url)->setClass('btn-alt-warning')->setIcon('edit')->window($text).'</span>';
    }
    return '';
}

function getQqUserCount()
{
    return (int) sf::getLib('db')->result_first("select count(*) c from users where qq_openid is not null and qq_openid !=''");
}

function formatNumber($num)
{
    $len = numberOfDecimals($num);
    if($len>2) return sprintf('%.2f',$num);
    return $num;
}

function numberOfDecimals($value)
{
    if ((int)$value == $value)
    {
        return 0;
    }
    else if (! is_numeric($value))
    {
        // throw new Exception('numberOfDecimals: ' . $value . ' is not a number!');
        return false;
    }

    return strlen($value) - strrpos($value, '.') - 1;
}

/**
 * 计算资金执行率
 * @param $usedMoney
 * @param $radicateMoney
 * @return mixed|string
 */
function getUsedPercent($usedMoney,$radicateMoney)
{
    $usedPercent = 0;//资金执行率
    if($usedMoney>0 && $radicateMoney>0){
        $usedPercent = sprintf('%.2f',($usedMoney / $radicateMoney * 100));
        if($usedPercent>=100) $usedPercent = 100;
    }
    return formatNumber($usedPercent);
}

function arrayMultisort(&$arr,$field)
{
    //获取要排序的字段
    $field = array_column($arr,$field);
    //先进行列排序，然后让$data数组按该字段来排序。
    array_multisort($field,SORT_DESC,$arr);
}


/**
 * 计算工龄
 * @param $date1
 * @param $date2
 * @param $tags
 * @return int
 */
function getYearNum($date1, $date2='', $tags = '-')
{
    $monthNum = getMonthNum($date1,$date2);
    return formatNumber($monthNum/12);
}


/**
 * 计算工龄（月）
 * @param $date1
 * @param $date2
 * @param $tags
 * @return int
 */
function getMonthNum($date1, $date2='', $tags = '-')
{
    if(empty($date2)) $date2 = date('Y-m-d');
    $d1 = explode($tags, $date1);
    $d2 = explode($tags, $date2);
    if (strtotime($date1) - strtotime($date2) > 0) {
        $monthsFromYear = abs($d1[0] - $d2[0]) * 12;
        $monthsFromMonth = $d1[1] - $d2[1];
        return $monthsFromYear + $monthsFromMonth;
    } else {
        $monthsFromYear = abs($d2[0] - $d1[0]) * 12;
        $monthsFromMonth = $d2[1] - $d1[1];
        return $monthsFromYear + $monthsFromMonth;
    }
}

/**
 * 获取该专科重点病种
 * @param $subjectCode
 * @return array
 */
function getZdbz($subjectCode,$guideId=0,$workerType='apply',$catId='')
{
    if($guideId) {
        $worker = sf::getModel('Guides',$guideId)->types()->worker();
    }else{
        $worker = sf::getModel("EngineWorkers")->selectBySubjectCode($subjectCode,$workerType);
    }
    $configs = $worker->getConfigs('safety');
    $zdbz = [];
    foreach ($configs['zdbz'] as $type=>$ischeck){
        foreach ($configs['zdbz_'.$type] as $diseaseId){
            $diseases = sf::getModel('Diseases',$diseaseId);
            $zdbz['safety_ylzl_'.$type.'_'.$diseaseId] = $configs['langs']['zdbz.'.$type].'-'.$diseases->getSubject().'-'.$diseases->getCode();
        }
    }
    return $zdbz;
}

/**
 * 取得指南列表
 */
function getGuideYear($selectYear='')
{
    $selectYear = $selectYear ? intval($selectYear) : config::get('current_declare_year',date('Y'));
    $db = sf::getLib('db');
    $query = $db->query("SELECT `year` FROM `guides`  GROUP BY `year`");
    $htmlStr = '';
    while($row = $db->fetch_array($query)){
        $htmlStr .= '<option value="'.$row['year'].'" ';
        if($selectYear == $row['year']) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$row['year']."年</option>\r\n";
    }
    return $htmlStr;
}


/**
 * 取得指南列表
 */
function getGuideList($select = 0,$level=0,$return_type='string')
{
    $data=[];
    $addWhere = 'is_show = 1 ';
    $level && $addWhere .= " and level = '".$level."' ";
    $result = sf::getModel("Guides")->selectAll($addWhere,"order by id asc");
    $htmlStr = '';
    while($guide = $result->getObject()){
        $htmlStr .= '<option value="'.$guide->getId().'" ';
        if($select == $guide->getId()) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$guide->getSubject()."</option>\r\n";
        $data[$guide->getId()] = $guide->getSubject();
    }
    if($return_type=='array')
        return $data;
    return $htmlStr;
}

function clearMenuCache() {
    // 打开目录
    $dirPath = APPPATH . "../cache/menus";
    $dirHandle = opendir($dirPath);
    if (!$dirHandle) return;

    // 循环读取目录中的文件和子目录
    while (($file = readdir($dirHandle)) !== false) {
        // 忽略当前目录(.)和上级目录(..)
        if ($file != '.' && $file != '..') {
            // 构建文件路径
            $filePath = $dirPath . '/' . $file;
            // 删除文件
            unlink($filePath);
        }
    }

    // 关闭目录句柄
    closedir($dirHandle);
}

/**
 * 计算两个日期相差多少天
 * @param $startDate 开始日期
 * @param $endDate  结束日期
 * @return string
 * @throws Exception
 */
function getDateDiff($startDate,$endDate)
{
    // 将字符串转换为DateTime对象
    $datetimeStart = new DateTime($startDate);
    $datetimeEnd = new DateTime($endDate);

    // 计算日期差值并获取天数
    $interval = $datetimeStart->diff($datetimeEnd);
    $daysDiff = $interval->format('%a');

    return $daysDiff;
}

/**
 * 计算两个日期相差几个月
 * @param $startDate 开始日期
 * @param $endDate  结束日期
 * @return string
 * @throws Exception
 */
function getMonthDiff($startDate,$endDate)
{
    $startYear = substr($startDate,0,4);
    $endYear = substr($endDate,0,4);
    $startMonth = (int)substr($startDate,5,2);
    $endMonth = (int)substr($endDate,5,2);
    $monthCount = 0;
    if($startYear===$endYear){
        $monthCount = $endMonth-$startMonth;
    }else{
        $days = getDateDiff($startDate,$endDate);
        $monthCount = (int)round($days/30);
    }
    if($endYear==2024 && $endMonth>=5 && $monthCount>7){
        //这几个月没有填报月报，是填的周报，要减去7个月['2023-10','2023-11','2023-12','2024-01','2024-02','2024-03','2024-04']
        $monthCount = $monthCount-7;
    }
    return $monthCount;
}

/**
 * 计算两个日期相差几个周
 * @param $startDate 开始日期
 * @param $endDate  结束日期
 * @return string
 * @throws Exception
 */
function getWeekDiff($startDate,$endDate)
{
    $startYear = substr($startDate,0,4);
    $endYear = substr($endDate,0,4);
    $startWeek = (int)substr($startDate,5,2);
    $endWeek = (int)substr($endDate,5,2);
    $weekCount = 0;
    if($startYear===$endYear){
        $weekCount = $endWeek-$startWeek;
    }else{
        $startDate = getFirstDateOfWeek($startYear,$startWeek);
        $endDate = getFirstDateOfWeek($endYear,$endWeek);
        $days = getDateDiff($startDate,$endDate);
        $weekCount = (int)round($days/7);
    }
    return $weekCount;
}

/**
 * 将周转为日期
 * @param $year
 * @param $week
 * @return string
 */
function getFirstDateOfWeek($year, $week) {
    $firstDayOfYear = strtotime("$year-01-01");
    $firstWeekDay = date('w', $firstDayOfYear);
    // 如果第一周的起始日期不是周一，则是上一年的最后一周
    if ($firstWeekDay > 1) {
        $lastYear = $year - 1;
        $lastYearLastWeek = date('W', strtotime("$lastYear-12-31"));
        if ($week == 1) {
            return getFirstDateOfWeek($lastYear, $lastYearLastWeek);
        }
    }
    // 第一周的开始日期
    $startOfWeek = strtotime("+$week weeks", $firstDayOfYear);
    // 第一周的结束日期
    $endOfWeek = strtotime('+6 days', $startOfWeek);

//    return date('Y-m-d', $startOfWeek) . ' - ' . date('Y-m-d', $endOfWeek);
    return date('Y-m-d', $startOfWeek);
}

/**
 * 保留小数
 * @param $num 数字
 * @param $scale 小数位数
 * @return string
 */
function getDecimals($num,$scale=2)
{
    $num = number_format($num, $scale, '.', '');  //格式化为指定位数小数
    $num = rtrim(rtrim($num, '0'), '.');     //去掉末尾的零和小数点
    return $num;
}


function getCatSubject($catId)
{
    return sf::getModel('Categorys',$catId)->getSubject();
}

function getPlatformTotal($type='')
{
    $addwhere = '1 ';
    if($type) $addwhere .= " and platform_type = '{$type}'";
    return sf::getLib('db')->result_first("select count(*) c from platforms where {$addwhere}");
}

function getPlatformValidTotal()
{
    return sf::getLib('db')->result_first("select count(*) c from (SELECT platform_id FROM `quarters` GROUP BY platform_id) t");
}

function getFinishGuideIds($type='array',$platformId='')
{
    $platformId = $platformId?:input::session('roleuserid');
    $db = sf::getLib('db');
    $query = $db->query("SELECT guide_id FROM quarters where platform_id = '{$platformId}' and statement = 10 GROUP BY guide_id");
    $guideIds = [];
    while ($row = $db->fetch_array($query)) {
        $guideIds[] = $row['guide_id'];
    }
    return $type=='array' ? $guideIds : implode(',',$guideIds);
}

function getSubmitGuideIds($type='array',$platformId='')
{
    $platformId = $platformId?:input::session('roleuserid');
    $db = sf::getLib('db');
    $query = $db->query("SELECT guide_id FROM quarters where platform_id = '{$platformId}' and statement IN (9,10) GROUP BY guide_id");
    $guideIds = [];
    while ($row = $db->fetch_array($query)) {
        $guideIds[] = $row['guide_id'];
    }
    return $type=='array' ? $guideIds : implode(',',$guideIds);
}

function getTopIndexs()
{
    $addwhere =" pid = 0";
    return sf::getModel('Indexs')->selectAll($addwhere,"order by sort asc,id asc");
}

function getIndexs($pcode='')
{
    $addwhere =" 1";
    if($pcode)   $addwhere.=" and code like '{$pcode}%' and pid >0";
    return sf::getModel('Indexs')->selectAll($addwhere,"order by sort asc,id asc");

}

/**
 * 获取OPENID
 * @param  [type] $idcard 身份证件号
 * @return [type]         36位openid
 */
function getOpenId($idcard)
{
    if (! $idcard) {
        return false;
    }
    $_md5 = strtoupper(md5($idcard . array_sum(str_split($idcard, 1))));
    return substr($_md5, 0, 8) . '-' . substr($_md5, 8, 4) . '-' . substr($_md5, 12, 4) . '-' . substr($_md5, 16, 4) . '-' . substr($_md5, -12);
}

function is_json($string) {
    json_decode($string);
    return (json_last_error() == JSON_ERROR_NONE);
}

function getProjectsByCompany($companyId='')
{
    if(empty($companyId)) $companyId = input::getInput('session.roleuserid');
    $projects = sf::getModel('Projects')->selectAll("`corporation_id` = '{$companyId}' and statement = 29");
    $projectArr = [];
    while($project = $projects->getObject()){
//        $projectArr[$project->getProjectId()] = $project->getSubject();
        $projectArr[] = $project->getSubject();
    }
    return $projectArr;
}

function getPlatformLevel($catId)
{
    $catSubject = getCatSubject($catId);
    if(strstr($catSubject,'国家')===true) return '国家级';
    return '省级';
}