<?php
namespace App\Controller\Engine\Summary\Attachement;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use App\Facades\DCS;

class upload extends BaseController
{
    public $project = null;
    private $configs = [];
	private $type = 'apply';

    function load(){
        $item_type = input::mix("item_type") ? input::mix("item_type") : 'attachement';
        $widget_name = input::mix("widget") ? input::mix("widget") : 'attachement';

        $this->project = sf::getModel("projects")->selectByProjectId(input::mix("item_id")?:input::mix("id"));

		if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->view->set("project",$this->project);
        $this->view->set("item_type",$item_type);
        $this->view->set("actions",input::mix("actions"));
		switch(trim(input::mix("engine")))
		{
			case 'tasker':
				$this->type = 'task';
			break;
			case 'completer':
				$this->type = 'complete';
			break;
			default:
				$this->type = 'apply';
			break;
		}
        $this->view->set("configs",$this->project->getWidgetConfigs($widget_name,$this->type));
        $this->view->set("options",Configs::getConfigs());
    }

    function index(){
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        $isShow = ($this->project->enableWrite($this->type) && input::session('userlevel')==2 && input::session('roleuserid')==$this->project->getUserId() && !input::getMix('is_show')) ? false : true;
        if($isShow){
            $item_type = input::mix("item_type") ? input::mix("item_type") : 'attachement';
            $attachments = $this->project->getAttachment($item_type);
            if($attachments->getTotal()==1){
                //只有一个附件时，直接跳转到附件预览页
                $file = $attachments->getObject();
                //在新窗口打开
                //exit("<script>top.window.open('".$file->getPreviewUrl()."');parent.closeWindow();</script>");
                $this->jump($file->getPreviewUrl());
            }
        }
        $this->view->set("is_show",$isShow);
        $this->view->apply('inc_body','Upload/index');
        $this->view->display('page_blank');
    }
}
