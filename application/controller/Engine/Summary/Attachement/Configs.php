<?php
namespace App\Controller\Engine\Summary\Attachement;
class Configs
{   
    private static $default = [];
    private static $configs = [
        'attachement'=>['name'=>'文件上传',
            'value'=>true,
        ],
        'necessary'=>[
                'apply_stamp'=>'签字盖章页',
                'summary_tjb'=>'四川省重点实验室年度考核绩效统计表',
                'attachement'=>'其他',
            ]
    ];
    
    public static function getConfigs($key='')
    {
        if($key) return self::$configs[$key];
        else return self::$configs;
    }
    
    public static function getDefault($_configs=[],$default=[])
    {
        if(count($_configs) == 0) $_configs = self::getConfigs();
        foreach($_configs as $_key => $_val){
            $data = [];
            if(count($_val['childs'])) $default[$_key] = self::getDefault($_val['childs']);
            else $default[$_key] = $_val['value'];
        }
        return $default; 
    }
    
    public static function setConfigs(){}
    
}
?>