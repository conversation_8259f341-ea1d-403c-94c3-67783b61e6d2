<?php
namespace App\Controller\Engine\Summary\Attachement;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use App\Facades\DCS;

class edit extends BaseController
{
    public $summary = null;
    private $configs = [];
	private $type = 'apply';

    function load(){
        $widget_name = input::mix("widget") ? input::mix("widget") : 'attachement';

        $this->summary = sf::getModel("Summarys")->selectBySummaryId(input::mix("item_id")?:input::mix("id"));

		if($this->summary->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->view->set("summary",$this->summary);
        $this->view->set("item_type",$widget_name);
        $this->view->set("actions",input::mix("actions"));
		switch(trim(input::mix("worker")))
		{
			case 'tasker':
				$this->type = 'task';
			break;
			case 'completer':
				$this->type = 'complete';
			break;
			default:
				$this->type = 'apply';
			break;
		}
        $this->view->set("configs",$this->summary->getWidgetConfigs($widget_name,$this->type));
        $this->view->set("options",Configs::getConfigs());
    }

    function index(){
        $this->edit();
    }
	/**
	 * 附件列表
	 */
	function edit(){
        $this->view->display('Edit/edit');
	}

	/**
	 * 删除附件
	 */
	function delete()
	{
		$item = sf::getModel("SummaryAttachments",input::mix("id"));
		if($item->getUserId() != input::session("userid")) $this->page_debug("你没有权限删除该附件！",getFromUrl());
		if($item->delete()) $this->page_debug("附件删除成功！",getFromUrl());
		else $this->page_debug("附件删除失败！",getFromUrl());
	}

	/**
	 * 附件排序
	 */
	function doSort()
	{
		if(is_array(input::post("sorts"))){
			$sorts = input::post("sorts");
			asort($sorts);//对数据排序
			for($i=0,$n=count($sorts);$i<$n;$i++){
				$item = sf::getModel("SummaryAttachments",$sorts[$i]);
				if($item->isNew()) continue;
				if($item->getUserId() != input::session("userid")) continue;
				$item->setSort($i);
				$item->save();
				$i++;
			}
		}
		$this->page_debug("附件排序保存成功！",getFromUrl());
	}

    function deleteProjectAttachment(){

        $file = sf::getModel('SummaryAttachments',Input::getInput('post.id'));

        if($file->isNew()){
            exit(json_encode(['status'=>false,'message'=>'附件不存在']));
        }

        if($file->getItemId() != Input::getInput('post.item_id')){
            exit(json_encode(['status'=>false,'message'=>'没有权限']));
        }

        $file->remove(Input::getInput('post.id'),true);

        sf::getModel("historys")->addHistory(Input::getInput('session.roleuserid'),'删除附件，附件表：SummaryAttachments,id：'.Input::getInput('mix.id'),'projects');

        exit(json_encode(['status'=>true,'message'=>'已删除']));
    }

    function editProjectAttachmentNote(){

        $file = sf::getModel('SummaryAttachments',Input::getInput('post.id'));

        if($file->isNew()){
            exit(json_encode(['status'=>false,'message'=>'附件不存在']));
        }

        if($file->getItemId() != Input::getInput('post.item_id')){
            exit(json_encode(['status'=>false,'message'=>'没有权限']));
        }

        $file->setFileNote(Input::getInput('post.content'));

        $file->save();

        sf::getModel("historys")->addHistory(Input::getInput('session.roleuserid'),'编辑附件备注，附件表：SummaryAttachments,id：'.Input::getInput('mix.id'),'projects');

        exit(json_encode(['status'=>true,'message'=>'已保存']));
    }

    function downloadProjectAttachment(){

        $file = sf::getModel('SummaryAttachments',Input::getInput('mix.id'));

        if($file->isNew()){
            $this->page_debug('附件不存在');
        }

        //sf::getModel("historys")->addHistory(Input::getInput('session.roleuserid'),'下载附件，附件表：SummaryAttachments,id：'.Input::getInput('mix.id'),'projects');
        // $template = WEBROOT.'/up_files/'.$file->getFilePath();

        $template = site_path('up_files/'.$file->getFilePath());

        header("Content-type:".$file->getFileMinetype());
        // header("Content-type:application/octet-stream");
        // header("Accept-Ranges:bytes");
        header("Accept-Length:".filesize($template));
        header('Content-Disposition: attachment; filename='.$file->getFileSavename());
        header('Expires: 0');
        //主要是这个判断
        if (isset($_SERVER['HTTP_USER_AGENT']) && ((strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE') !== false)))
        {
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Pragma: public');
        }else
        {
            header('Pragma: no-cache');
        }

        ob_clean();
        flush();

        exit(file_get_contents($template));
    }

    function convertProjectAttachmentToPdf(){
        $file = sf::getModel('SummaryAttachments',Input::getInput('mix.id'));

        if($file->isNew()){
            exit(json_encode(['status'=>false,'message'=>'附件不存在']));
        }

        if($file->getItemId() != Input::getInput('mix.item_id')){
            exit(json_encode(['status'=>false,'message'=>'没有权限']));
        }

        $docFile = WEBROOT.'/up_files/'.$file->getFilePath();

        if(!is_file($docFile))
            exit(json_encode(['status'=>false,'message'=>'文件不存在，请重新上传']));

        if(!in_array(strtolower($file->getFileExt()),['doc','docx'])){
            exit(json_encode(['status'=>false,'message'=>'文件格式错误，仅支持doc或docx']));
        }

        $filePath = $file->getFilePath();

        $info = pathinfo($filePath);
        $pdfPath = $info['dirname'].'/'.$info['filename'].'.pdf';
        $pdfSaveName = $info['filename'];

        $sourceFile = site_path('up_files/'.$file->getFilePath());

        $msg = DCS::setDownloadUrl($sourceFile)->toPdf()->save($pdfSaveName,WEBROOT.'/up_files/'.$info['dirname']);

        if($msg['errorMsg']){
            exit(json_encode(['status'=>true,'message'=>'文件转换失败'.$msg['errorMsg']]));
        }else{
            $file->setPdfPath($pdfPath);
            $file->save();

            exit(json_encode(['status'=>true,'message'=>'文件已转换','data'=>$pdfPath]));
        }

    }

    function uploadProjectAttachment(){
        if($_FILES){
            /**
             * 未登录则不能上传
             */
            if(!$_SESSION['id'])
                exit(json_encode(['status'=>false,'message'=>'页面已过期'],JSON_UNESCAPED_UNICODE));

            $upload_type = input::getInput("post.upload_type") ? explode(",",input::getInput("post.upload_type")) : config::get("upload_type",array('jpg','bmp','pdf','png','gif','bmp','rar','doc','xls','zip','docx'));
            $upload_size = input::getInput("post.upload_size") ? input::getInput("post.upload_size") : config::get("upload_size","20971520");
            $upload = sf::getLib("upload","file",config::get("upload_path","./up_files/"),$upload_size,$upload_type);

            if($upload->upload())
            {
                $result = $upload->getSaveFileInfo();
                foreach($result as $index=>$files)
                {
                    $filemanager = sf::getModel('SummaryAttachments');
                    $filemanager->setFileName($files['name']);
                    $filemanager->setFileSavename($files['savename']);
                    $filemanager->setFilePath($files['path']);
                    $filemanager->setFileSize($files['size']);
                    $filemanager->setFileExt($files['type']);
                    $filemanager->setFileMinetype($files['minetype']);
                    $filemanager->setUserId(input::getInput('session.roleuserid'));
                    $filemanager->setUserName(input::getInput('session.nickname'));
                    $filemanager->setItemId(input::getInput("post.item_id"));
                    $filemanager->setFileNote(input::getInput("post.file_note")?input::getInput("post.file_note"):'');
                    $filemanager->setItemType(input::getInput("post.item_type")?input::getInput("post.item_type"):'content');
                    $filemanager->setAuthorization(trim($_SERVER['SERVER_ADDR']));
                    $id = $filemanager->save();

                    //对上传的文件进行检查
                    if($msg = $this->checkContent(['file_ext'=>$files['type'],'file_path'=>$files['path']])){
                        $filemanager->delete();
                        exit(json_encode(['hasError'=>true,'msg'=>$msg],JSON_UNESCAPED_UNICODE));
                    }

                    $result[$index]['file_name'] = $files['name'];
                    $result[$index]['file_savename'] = $files['savename'];
                    $result[$index]['time'] = date("Y-m-d H:i:s");
                    $result[$index]['size'] = $files['size'];
                    $result[$index]['path'] = $files['path'];
                    $result[$index]['id'] = $filemanager->getId();
                }
            }else exit(json_encode(['status'=>false,'message'=>$upload->getError()],JSON_UNESCAPED_UNICODE));
            exit(json_encode(['status'=>true,'message'=>'内容已上传','data'=>$result],JSON_UNESCAPED_UNICODE));
        }
    }

    private function checkContent($data=[]){
        $msg = '';
        if(strtolower($data['file_ext'])=='pdf'){
            if(strpos(file_get_contents(WEBROOT.'/up_files/'.$data['file_path']),'startxref')===false){
                $msg = 'PDF无法被系统识别!';
            }
        }
        return $msg;
    }

}
?>