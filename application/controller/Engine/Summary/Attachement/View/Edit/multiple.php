<?php
$items = $summary->attachements($item_type,$config['single_model']);?>
<div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">
    <?=$config['alias'][$item_type]?:$config['subject']?>
    <?php if($config['is_must'][$item_type]): ?>
        <label class="text-danger">必有附件</label>
    <?php endif; ?>
    <?php if(in_array($item_type,array('project_spqk','project_cn','complete_spqk'))):?>
    <a href="<?=site_url("engine/".($config['engine']=='completer'?'completer':'worker')."/printer/id/".$summary->getSummaryId())?>" class="btn btn-sm btn-success" role="button" target="_blank">打印</a>
    <?php endif;?>
</div>
<?php
//if($config['engine']=='tasker'){
//    $item_type = 'task_'.$item_type;
//}
//if($config['engine']=='completer'){
//    $item_type = 'complete_'.$item_type;
//}
?>
<div style="border:2px dotted #DDD;padding:10px;margin:10px 0;border-radius: 5px;">

<?php if($actions['edit']): ?>
<div id="haddle_filelist_<?=$item_type?>" class="text-info">
    文件大小不能超过：<b><?php if(!$config['file_size']) echo '20';else echo round($config['file_size'],2);?>M</b>,
        上传格式为：<b><?=$config['file_ext']?implode(',',$config['file_ext']):'jpg,png,doc,docx,pdf'?></b>
    <div class="pull-right" style="display:flex;">
        <div id="upload_<?=$item_type?>" class="picker">选择文件</div>
        <div style="position:relative;display:none">
        	<a href="#" role="button" class="btn btn-sm btn-success qrbtn" style="height:30px;margin-left:5px;">手机端上传</a>
        	<img src="<?=getQRcode(site_url('m/up/sign/'.myencrypt("item_id=".$summary->getSummaryId()."&item_type=".$item_type."&table=projects&session_id=".session_id(),'E')))?>" style="width: 150px;border: 1px #ccc solid;padding: 10px;display:none;position:absolute;top:-70px;left:-150px;background:#FFF;"/>
        </div>
        
    </div>
    <hr/>
</div>
<?php endif; ?>

<div class="space-10"></div>

<table class="table table-bordered" id="filelist_<?=$item_type?>">
    <thead>
        <tr>
        <th class="text-center">文件信息</th>
        <?php if($actions['edit']): ?>
        <th class="text-center" width="160">操作</th>
        <?php endif; ?>
    </tr>
    </thead>
    <tbody>
    <?php $items = $summary->attachements($item_type); ?>
    <?php if(count($items)==0): ?>
        <tr><td colspan="2" class="nofile">
            没有文件
        </td></tr>
    <?php endif; ?>
    <?php foreach($items as $file):?>
    <tr>
        <td>
        <div>
            <?=$file['file_name']?>
            <small class="help-block">
                类型:<?=$file['file_ext']?>&nbsp;&nbsp;
                文件大小:<?=round($file['file_size']/1024,2)?> KB&nbsp;&nbsp;
                备注:<span class="file_note"><?=$file['file_note']?:'未填写'?></span>
            </small>
        </div>
        </td>
        <?php if($actions['edit']): ?>
        <td class="text-right" style="vertical-align: middle">
            <div class="btn-group-xs">
                <?php if($config['allow_convert'] && in_array(strtolower($file['file_ext']), ['doc','docx'])): ?>
                <?php if($file['pdf_path']): ?>
                <a target="_blank" data-id="<?=$file['id']?>" href="<?=site_path('up_files/'.$file['pdf_path'])?>" class="btn btn-info">查看PDF</a>
                <?php else: ?>
                <a data-id="<?=$file['id']?>" href="javascript:void(0);" class="btn btn-warning pdfdownload">未生成pdf</a>
                <?php endif; ?>
                <a data-id="<?=$file['id']?>" href="javascript:void(0);" class="convertfile btn btn-primary">转成PDF</a>
                <?php endif; ?>
            </div>
            <div class="btn-group-xs text-center" style="width: 150px">
                <?=btn('link','下载',site_path('engine/summary/attachement/edit/downloadProjectAttachment/id/'.$file['id'].'/item_id/'.$summary->getSummaryId()))?>
                <?=btn('button','备注','button',null,'editNote($(this).closest(\'tr\'),\''.$file['id'].'\',\''.$summary->getSummaryId().'\')','btn-sm','btn-alt-info')?>
                <a data-id="<?=$file['id']?>" href="javascript:void(0);" class="deletefile btn btn-sm btn-alt-danger">删除</a>
            </div>
        </td>
        <?php endif; ?>
    </tr>
    <?php endforeach;?>
    </tbody>
</table>

<script language="javascript" type="text/javascript">
    $(function(){
        $('#myTab').find('li:eq(0)').find('a').click();
        $('#upload_<?=$item_type?>').webupload({
            server: baseurl+"engine/summary/attachement/edit/uploadProjectAttachment",
            pick: {
                id: '#upload_<?=$item_type?>',
                multiple:false,
                label: '选择文件'
            },
            formData:{item_type:'<?=$item_type?>',item_id:'<?=$summary->getSummaryId()?>'},
            fileNumLimit:30,
            fileSizeLimit:parseInt(<?=$config['file_size']?:20?>)*1024*1024,
            fileSingleSizeLimit:parseInt(<?=$config['file_size']?:20?>)*1024*1024,
            accept:{
                title: 'att',
                extensions: '<?=$config['file_ext']?implode(',', $config['file_ext']):'jpg'?>',
                mimeTypes: '.<?=$config['file_ext']?implode(',.', $config['file_ext']):'jpg'?>'
            }
        },
        {
        accept:function( file, response ) {
            var data = $.parseJSON(response._raw);
            if ( !data.status ) {
                showMsg(data.message,{icon:2});
                return false;
            }
        },
        progress:function( file, percentage ) {
            $('#upload_<?=$item_type?>').find('.webuploader-pick').text('文件上传中...');
        },
        complete:function( file ) {
            $('#upload_<?=$item_type?>').find('.webuploader-pick').text('选择文件');
        },
        queued:function( file ) {
            $('#upload_<?=$item_type?>').find('.webuploader-pick').text('准备上传');
        },
        error:function(type) {
            $('#upload_<?=$item_type?>').find('.webuploader-pick').text('选择文件');
            if (type == "Q_TYPE_DENIED") {
                showMsg("请上传 <?=implode('、', $config['file_ext'])?> 格式的文件");
            } else if (type == "Q_EXCEED_SIZE_LIMIT") {
                showMsg("文件大小不能超过<?=$config['file_size']?>M");
            }else {
                showMsg("上传出错！请检查后重新上传！错误代码 "+type);
            }
          },
          success:function(file, response){
            var data = $.parseJSON(response._raw);
            if ( !data.status ) {
                showMsg(data.message,{icon:2});
                return false;
            }

            data = data.data;

            appendToTable<?=$item_type?>(data);

            <?php if($config['allow_imagecropper']): ?>
            var file_name = data[0].file_name;
            var file_ext=file_name.substring(file_name.lastIndexOf("."),file_name.length);
            // if(file_ext=='.jpg' || file_ext=='.png'){
            //     imageCut('<?=site_path('up_files/')?>'+'/'+data[0].path);
            // }
            <?php endif; ?>
          }
        });
		
		$('.qrbtn').each(function(){
			$(this).mouseenter(function(){
			  $(this).parent().find('img').show()
			}).mouseleave(function(){
				$(this).parent().find('img').hide()
			});
		})
    });

    $('#filelist_<?=$item_type?>').on('click','.deletefile',function(){
      deleteAttachment(this,'filelist_<?=$item_type?>','<?=$summary->getSummaryId()?>');
    });

    $('#filelist_<?=$item_type?>').on('click','.convertfile',function(){
      convertAttachment(this,'<?=$summary->getSummaryId()?>',function(obj,json){
        showMsg(json.message);
        $(obj).closest('tr').find('.pdfdownload').attr('href','<?=site_path('up_files/')?>'+'/'+json.data).attr('target',"_blank" ).removeClass('btn-warning').addClass('btn-info').text('查看PDF');
      });
    });
</script>

<script language="javascript" type="text/javascript">
function appendToTable<?=$item_type?>(data){
    var id = data[0].id;
    var file_name = data[0].file_name;
    var index1=file_name.lastIndexOf(".");
    var index2=file_name.length;
    var file_ext=file_name.substring(index1,index2);//后缀名
    var file_savepath = data[0].path;
    var file_size = Math.ceil(data[0].size/1024,2);
    var upload_time = data[0].time;
    var isWord = file_ext=='.doc' || file_ext=='.docx';

    var tr = `
    <tr>
    <td>
    `+file_name+`
    <small class="help-block">
        类型:`+file_ext+`&nbsp;&nbsp;
        文件大小:`+file_size+` KB&nbsp;&nbsp;
        备注:<span class="file_note">未填写</span>
    </small>
    </td>
    <td class="text-right">
    <div class="btn-group-xs">`;
    
    if(isWord)
        tr += `
        <?php if($config['allow_convert']): ?>
        <a data-id="`+id+`" href="javascript:void(0);" class="btn btn-warning pdfdownload">未生成pdf</a>
        <a data-id="`+id+`" href="javascript:void(0);" class="convertfile btn btn-primary">转成PDF</a>
        <?php endif; ?>
        `;

    tr += `
    </div>
    <div class="btn-group-xs">
    <a data-id="`+id+`" href="<?=site_path('engine/summary/attachement/edit/downloadProjectAttachment/item_id/'.$summary->getSummaryId())?>/id/`+id+`" class="downloadfile btn btn-sm btn-alt-primary">下载</a>
    <button type="button" class="btn btn-sm btn-alt-info" onclick="editNote($(this).closest('tr'),`+id+`,'<?=$summary->getSummaryId()?>')">备注</button>
    <a href="javascript:void(0);" data-id="`+id+`" class="deletefile btn btn-sm btn-alt-danger">删除</a>
    </div>
    </td>
    </tr>`;

    $('#filelist_<?=$item_type?>').find('tbody').prepend(tr);
    $('#filelist_<?=$item_type?>').find('.nofile').closest('tr').remove();
}

</script>
<div class="clearfix"></div>
</div>
<div class="space-10"></div>