<?php if($configs['action']['show']): ?>
    <div class="clearfix"></div>
    <?php $config = $configs['attachement'];?>

    <?php if(count($config['necessary'])>0): ?>
        <?php foreach($config['necessary'] as $item_type):?>
            <div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">
                <?=$config['alias'][$item_type]?:$widgetConfigs['necessary'][$item_type]?>
                <?php if($config['is_must'][$item_type]): ?>
                    <label class="text-danger">必有附件</label>
                <?php endif; ?>
            </div>
            <div style="border:2px dotted #DDD;padding:10px;margin:10px 0;border-radius: 5px;">
            <table class="table table-bordered" >
                <thead>
                    <tr>
                    <th class="text-center">文件信息</th>
                    <th class="text-center">操作</th>
                </tr>
                </thead>
                <tbody>
                <?php $items = $summary->attachements($item_type); ?>
                <?php if(count($items)==0): ?>
                    <tr><td colspan="2" class="nofile">
                        没有文件
                    </td></tr>
                <?php endif; ?>
                <?php foreach($items as $file):?>
                <tr>
                    <td>
                    <div>
                        <?=$file['file_name']?>
                        <small class="help-block">
                            类型：<?=$file['file_ext']?>&nbsp;&nbsp;
                            文件大小：<?=round($file['file_size']/1024,2)?> KB&nbsp;&nbsp;
                            上传时间：<?=$file['updated_at']?>
                        </small>
                        <small class="help-block">备注:<span class="file_note"><?=$file['file_note']?:'未填写'?></span></small>
                    </div>
                    </td>
                    <td align="center" style="vertical-align: middle">
                        <?=Button::setTarget('_blank')->setUrl(site_path('up_files/'.$file['file_path']))->link('查看')?>
                    </td>
                </tr>
                <?php endforeach;?>
                </tbody>
            </table>
            </div>
            <div class="clearfix"></div>
            <div class="space-10"></div>
        <?php endforeach; ?>
    <?php else: ?>
        <div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">
            <?=$config['subject']?>
        </div>
        <div style="border:2px dotted #DDD;padding:10px;margin:10px 0;border-radius: 5px;">
            <table class="table table-bordered" >
                <thead>
                    <tr>
                    <th class="text-center">文件信息</th>
                    <th class="text-center">操作</th>
                </tr>
                </thead>
                <tbody>
                <?php $items = $summary->attachements($widget_name); ?>
                <?php if(count($items)==0): ?>
                    <tr><td colspan="2" class="nofile">
                        没有文件
                    </td></tr>
                <?php endif; ?>
                <?php foreach($items as $file):?>
                <tr>
                    <td>
                    <div>
                        <?=$file['file_name']?>
                        <small class="help-block">
                            类型:<?=$file['file_ext']?>&nbsp;&nbsp;
                            文件大小:<?=round($file['file_size']/1024,2)?> KB&nbsp;&nbsp;
                            备注:<span class="file_note"><?=$file['file_note']?:'未填写'?></span>
                        </small>
                    </div>
                    </td>
                    <td align="center" style="vertical-align: middle">
                        <?=Button::setTarget('_blank')->setUrl(img_path('up_files/'.$file['file_path']))->link('查看')?>
                        <!--
                        <a data-id="<?=$file['id']?>" href="<?=site_path('engine/summary/attachement/edit/downloadProjectAttachment/id/'.$file['id'].'/item_id/'.$summary->getSummaryId())?>" class="downloadfile btn btn-info">下载</a>
                        -->
                    </td>
                </tr>
                <?php endforeach;?>
                </tbody>
            </table>
            </div>
            <div class="clearfix"></div>
            <div class="space-10"></div>
    <?php endif; ?>

    <div class="clearfix"></div>
<?php else: ?>
<div data-widget-id="attachement_<?=$widget_name?>" style="width:100%;"><p align="center"><i class="fa fa-spinner fa-spin"></i>内容载入中... </p></div>

<script type="text/javascript" src="<?=site_path('js/common.js')?>"></script>
<script type="text/javascript">
var url = '<?=site_url("engine/summary/attachement/edit/edit/worker/".$configs['action']['worker'])?>';
ajaxData(url,{widget:'<?=$widget_name?>',id:'<?=$summary->getSummaryId()?>',actions:<?=json_encode($configs['action'])?>},'html',function(){
    $('[data-widget-id="attachement_<?=$widget_name?>"]').html('<p align="center"><i class="fa fa-spinner fa-spin"></i>内容载入中... </p>');
},function(html){
    $('[data-widget-id="attachement_<?=$widget_name?>"]').html(html);
},function(){
    
});
</script>
<div class="clearfix"></div>
<?php endif; ?>