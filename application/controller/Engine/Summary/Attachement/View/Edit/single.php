<?php $items = $summary->attachements($item_type,$config['single_model']); ?>

<div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">
    <?=$config['subject']?>
</div>

<div style="border:2px dotted #DDD;padding:10px;margin:10px 0;border-radius: 5px;">

<?php if($actions['edit']): ?>
    
<div class="text-left">
<div class="col-xs-8 text-danger">
步骤：<br/>
1、下载模板 (离线填写)<br/>
2、上传模板<br/>
3、转成PDF<br/>
* 如需替换内容，请删除后重新上传
</div>
<div class="col-xs-4 text-right no-margin no-padding">
<?php if ($config['template']): ?>
    <a href="<?=site_path('up_files/'.$config['template'])?>" class="btn btn-success" ><i class="fa fa-cloud-download"></i> 下载模板</a>
<?php else: ?>
    <button class="btn btn-warning">未找到模板</button>
<?php endif ?>
</div>
</div>
<div class="clearfix"></div>
<hr/>

<div id="haddle_filelist_<?=$item_type?>" class="text-info <?php if(count($items)>0): ?>hidden <?php endif; ?>">
    文件大小不能超过：<b><?php if(!$config['file_size']) echo '20';else echo round($config['file_size'],2);?>M</b>,
        上传格式为：<b><?=$config['file_ext']?implode(',',$config['file_ext']):'jpg,png,doc,docx,pdf'?></b>
    <div class="pull-right">
        <div id="upload_<?=$item_type?>" class="picker">选择文件</div>
    </div>
    <hr/>
</div>

<div class="space-10"></div>
<?php endif; ?>

<div id="filelist_<?=$item_type?>">

<?php if(count($items)==0): ?>
<div class="text-center nofile">
<p>
    <i class="fa fa-sticky-note-o fa-5x grey"></i>
    <small class="help-block">没有上传文件</small>
</p>
</div>
<?php else: ?>
<?php $file = $items[0];?>
<div class="text-center contentarea">
    <p align="left">
        文件名：<?=$file['file_name']?>
    </p>
    <p align="left">
        备注：<span class="file_note"><?=$file['file_note']?:'未填写'?></span>
    </p>
    <p >
        <a data-id="<?=$file['id']?>" href="<?=site_path('engine/summary/attachement/edit/downloadProjectAttachment/id/'.$file['id'].'/item_id/'.$summary->getSummaryId())?>" class="downloadfile" style="display: inline-block;">

            <?php if(in_array($file['file_ext'],['doc','docx'])): ?>
            <i class="fa fa-file-word fa-5x"></i>
            <?php endif; ?>
            <?php if(in_array($file['file_ext'],['pdf'])): ?>
            <i class="fa fa-file-pdf fa-5x text-danger"></i>
            <?php endif; ?>
            <small class="help-block">点击下载</small>
        </a>

        <?php if($config['allow_convert'] && in_array(strtolower($file['file_ext']), ['doc','docx'])): ?>
        <?php if($file['pdf_path']): ?>
        <a target="_blank" data-id="<?=$file['id']?>" href="<?=site_path('up_files/'.$file['pdf_path'])?>" style="display: inline-block;margin-left: 60px;">

            <i class="fa fa-file-pdf fa-5x text-danger"></i>

            <small class="help-block">查看PDF</small>
        </a>
        <?php else: ?>
        <a data-id="<?=$file['id']?>" href="javascript:void(0);" class="grey pdfdownload" onclick="convertAttachmentSingle(this,'attachement_<?=$item_type?>','<?=$summary->getSummaryId()?>',function(obj,json){
            showMsg(json.message);$(obj).attr('href','<?=site_path('up_files/')?>'+'/'+json.data).attr('target','_blank' ).removeClass('grey').removeAttr('onclick').find('small').text('查看PDF');$(obj).find('.fa').addClass('text-danger');
        })" style="display: inline-block;margin-left: 60px;">

            <i class="fa fa-file-pdf fa-5x"></i>

            <small class="help-block">转成PDF</small>
        </a>
        <?php endif; ?>
        <?php endif; ?>
    </p>
    <?php if($actions['edit']): ?>
    <p>
        <div class=" btn-sm">
        <button type="button" class="btn btn-info" onclick="editNote($(this).closest('#filelist_<?=$item_type?>'),'<?=$file['id']?>','<?=$summary->getSummaryId()?>')">编辑备注</button>
        <a data-id="<?=$file['id']?>" href="javascript:void(0);" class="btn btn-danger" onclick="deleteAttachmentSingle(this,'filelist_<?=$item_type?>','<?=$summary->getSummaryId()?>')">删除</a>
        </div>
    </p>
    <?php endif; ?>
</div>
<?php endif; ?>
</div>

<script language="javascript" type="text/javascript">
    $(function(){
        $('#myTab').find('li:eq(0)').find('a').click();
        $('#upload_<?=$item_type?>').webupload({
            server: baseurl+"engine/summary/attachement/edit/uploadProjectAttachment",
            pick: {
                id: '#upload_<?=$item_type?>',
                multiple:false,
                label: '点击选择文件'
            },
            formData:{item_type:'<?=$item_type?>',item_id:'<?=$summary->getSummaryId()?>'},
            fileNumLimit:30,
            fileSizeLimit:parseInt(<?=$config['file_size']?:20?>)*1024*1024,
            fileSingleSizeLimit:parseInt(<?=$config['file_size']?:20?>)*1024*1024,
            accept:{
                title: 'att',
                extensions: '<?=$config['file_ext']?implode(',', $config['file_ext']):'jpg'?>',
                mimeTypes: '.<?=$config['file_ext']?implode(',.', $config['file_ext']):'jpg'?>'
            }
        },
        {
        accept:function( file, response ) {
            var data = $.parseJSON(response._raw);
            if ( !data.status ) {
                showMsg(data.message,{icon:2});
                return false;
            }
        },
        progress:function( file, percentage ) {
            $('#upload_<?=$item_type?>').find('.webuploader-pick').text('文件上传中...');
        },
        complete:function( file ) {
            $('#upload_<?=$item_type?>').find('.webuploader-pick').text('点击选择文件');
        },
        queued:function( file ) {
            $('#upload_<?=$item_type?>').find('.webuploader-pick').text('准备上传');
        },
        error:function(type) {
            $('#upload_<?=$item_type?>').find('.webuploader-pick').text('点击选择文件');
            if (type == "Q_TYPE_DENIED") {
                showMsg("请上传 <?=implode('、', $config['file_ext'])?> 格式的文件");
            } else if (type == "Q_EXCEED_SIZE_LIMIT") {
                showMsg("文件大小不能超过<?=$config['file_size']?>M");
            }else {
                showMsg("上传出错！请检查后重新上传！错误代码 "+type);
            }
          },
          success:function(file, response){
            var data = $.parseJSON(response._raw);
            if ( !data.status ) {
                showMsg(data.message,{icon:2});
                return false;
            }

            data = data.data;

            replaceContent<?=$item_type?>(data,'filelist_<?=$item_type?>');

            <?php if($config['allow_imagecropper']): ?>
            var file_name = data[0].file_name;
            var file_ext=file_name.substring(file_name.lastIndexOf("."),file_name.length);
            if(file_ext=='.jpg' || file_ext=='.png'){
                imageCut('<?=site_path('up_files/')?>'+'/'+data[0].path);
            }
            <?php endif; ?>
          }
        });
    });

    $('#filelist_<?=$item_type?>').on('click','.deletefile',function(){
      deleteAttachment<?=$item_type?>(this,'filelist_<?=$item_type?>');
    });

    $('#filelist_<?=$item_type?>').on('click','.convertfile',function(){
      convertAttachment<?=$item_type?>(this);
    });
</script>
<div class="space"></div>

<script language="javascript" type="text/javascript">
function replaceContent<?=$item_type?>(data,containerId){
    var id = data[0].id;
    var file_name = data[0].file_name;
    var index1=file_name.lastIndexOf(".");
    var index2=file_name.length;
    var file_ext=file_name.substring(index1,index2);//后缀名
    var file_savepath = data[0].path;
    var file_size = Math.ceil(data[0].size/1024,2);
    var upload_time = data[0].time;
    var isWord = file_ext=='.doc' || file_ext=='.docx';
    var isPdf = file_ext=='.pdf';

    var html = `
    <p align="left">
        文件名：`+file_name+`
    </p>
    <p align="left">
        备注：<span class="file_note">未填写</span>
    </p>
    <p align="center">
        <a data-id="`+id+`" href="<?=site_path('engine/summary/attachement/edit/downloadProjectAttachment/item_id/'.$summary->getSummaryId())?>/id/`+id+`" class="downloadfile" style="display: inline-block;">
    `;

    if(isWord){
        html += `<i class="fa fa-file-word fa-5x"></i>`;
    }

    if(isPdf){
        html += `<i class="fa fa-file-pdf fa-5x text-danger"></i>`;
    }
    
    html += `
            <small class="help-block">点击下载</small>
        </a>
    `;
    if(isWord){
        html += `
            <?php if($config['allow_convert']): ?>
            <a data-id="`+id+`" href="javascript:void(0);" class="grey" onclick="convertAttachmentSingle(this,'attachement_<?=$item_type?>','<?=$summary->getSummaryId()?>',function(obj,json){
            showMsg(json.message);$(obj).attr('href','<?=site_path('up_files/')?>'+'/'+json.data).attr('target','_blank' ).removeClass('grey').removeAttr('onclick').find('small').text('查看PDF');$(obj).find('.fa').addClass('text-danger');
        })" style="display: inline-block;margin-left: 60px;">

                <i class="fa fa-file-pdf fa-5x"></i>

                <small class="help-block">转成PDF</small>
            </a>
            <?php endif; ?>
        `;
    }
    html += `
    </p>
    <p>
        <div class=" btn-sm text-center">
        <button type="button" class="btn btn-info" onclick="editNote($('#`+containerId+`'),'`+id+`','<?=$summary->getSummaryId()?>')">编辑备注</button>
        <a data-id="`+id+`" href="javascript:void(0);" class="btn btn-danger" onclick="deleteAttachmentSingle(this,'`+containerId+`','<?=$summary->getSummaryId()?>')">删除</a>
        </div>
    </p>
    `;

    $('#'+containerId).html(html);
    $('#haddle_'+containerId).addClass('hidden');

    if(isWord){
        showConfirm('检测到您上传的文件为 WORD ，是否立即转成 PDF ？','',['是','否'],function(index){
            $('#'+containerId).find('.grey[data-id]').click();
            layer.close(index);
        },function(index){
            
            layer.close(index);
        });
    }
}
</script>
<div class="clearfix"></div>
</div>