<style>
    .handle {
        cursor: move !important;
        font-weight: 900;
        line-height: 35px;
    }
    .drag-hover{
        background-color:#f9f5d1 !important;
        border:1px dashed #919191;
        margin-bottom:15px
    }
</style>
<script type="text/javascript" src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script type="text/javascript" src="<?=site_url('assets/js/Sortable.min.js')?>"></script>
<div class="block block-rounded">
    <div class="page-body">
        <div class="block-content tab-content">
            <div class="tab-pane active" id="tab1" role="tabpanel">
                <div class="clearfix"></div>
                <?php
                    if(!$is_show):
                ?>
                <div class="alert alert-warning alert-dismissable" role="alert">
                    <h3 class="alert-heading font-size-h6 my-2">
                        附件上传说明
                    </h3>
                    <p>1.上传文件格式：jpg、png、pdf<br>
                        2.单个文件体积不超过20MB<br>
                        3.上传后请在文件名一栏填写文件名</p>
                </div>
                <div class="form-group">
                    <div id="uploader" class="wu-example dropzone">
                        <div class="queueList">
                            <div id="dndArea" class="placeholder">
                                <div id="upload-<?=$item_type?>"></div>
                                <?=Button::setIcon('fa fa-upload')->button('点击上传文件')?>
                                <p>或者将文件拖到此处，上传文件格式为：jpg、png、pdf，体积不超过20MB</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="clearfix"></div>
                <?php endif;?>
                <div class="header no-margin-top">已上传文件列表</div>

                <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="item_id" value="<?=$project->getProjectId()?>">
                    <input type="hidden" name="table" value="filemanager">
                    <input type="hidden" name="upload_size" value="20480000">
                    <table class="table table-sm">
                        <thead>
                        <tr>
                            <?php
                            if(!$is_show):
                            ?>
                            <th class="text-center" style="width: 10%">排序</th>
                            <?php endif;?>
                            <th class="text-center" style="width: 10%">序号</th>
                            <th>文件名</th>
                            <th class="text-center" style="width: 10%">操作</th>
                        </tr>
                        </thead>
                        <tbody id="file-list-<?=$item_type?>" class="file-list-group">
                        <?php if($project->getAttachment($item_type)->getTotal()>0):
                            $attachments = $project->getAttachment($item_type);
                            while($file = $attachments->getObject()):
                                ?>
                                <tr id="file<?=$file->getId()?>">
                                    <?php
                                    if(!$is_show):
                                    ?>
                                    <td class="text-center">
                                        <i class="fa fa-arrows-alt handle"></i>
                                    </td>
                                    <?php endif;?>
                                    <td class="text-center">
                                        <input type="hidden" name="attachment_id[]" value="<?=$file->getId()?>">
                                        <?php
                                        if(!$is_show):
                                        ?>
                                        <input class="form-control" style="text-align: center" type="number" name="no[<?=$file->getId()?>]" min="1" value="<?=$attachments->getIndex()?>">
                                        <?php else:?>
                                        <?=$attachments->getIndex()?>
                                        <?php endif;?>
                                    </td>
                                    <td style="width: 300px">
                                        <?php
                                        if(!$is_show):
                                        ?>
                                        <input class="form-control" type="text" name="filenote[<?=$file->getId()?>]" value="<?=$file->getFileNote()?:$file->getFileName()?>">
                                        <?php else:?>
                                            <?=$file->getFileNote()?:$file->getFileName()?>
                                        <?php endif;?>
                                    </td>
                                    <td class="text-center">
                                        <a href="<?=$file->getPreviewUrl()?>" class="btn btn-alt-primary btn-sm" target="_blank">查看</a>
                                        <?php
                                        if(!$is_show):
                                        ?>
                                        <a href="javascript:delete_file('<?=$file->getId()?>','')" onClick="return confirm('确定要删除该文件吗？')" class="btn btn-alt-danger btn-sm">删除</a>
                                        <?php endif;?>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                        <?php endif;?>
                        </tbody>
                        <tr>
                    </table>

                    <div class="clearfix"></div>

                    <?php
                    if(!$is_show):
                    ?>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <div style="width:100px;margin:0 auto;text-align:center">
                            <a href="javascript:void(0);" onclick="form1.submit();" class="btn btn-primary btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存资料</a>
                        </div>
                    </div>
                    <?php endif;?>
                </form>
                <div class="clearfix"></div>
            </div>
        </div>
    </div>
    <div class="page-footer">
        <!--右下角浮动-->

    </div>
</div>
<script>
    var baseurl = $('#baseUrl').val();
    var fileSizeLimit = '2000';   //单位：MB
    var fileSingleSizeLimit = '20';   //单位：MB
    var fileNumLimit = 100;   //允许上传的文件数量
    $(function(){
        var uploadFileNum = '<?=$project->getAttachment()->getTotal()?>';   //已上传的文件数量
        $('#upload-<?=$item_type?>').webupload({
            formData:{item_id:'<?=$project->getProjectId()?>',item_type:'<?=$item_type?>'},
            accept:{
                title: 'PDF',
                extensions: 'pdf,jpg,png',
                mimeTypes: 'pdf,jpg,png'
            },
            dnd: '#uploader .queueList',
            paste: document.body,
            uploadFileNum:uploadFileNum,
            fileNumLimit:fileNumLimit,
            fileSizeLimit:fileSizeLimit*1024*1024,
            fileSingleSizeLimit:fileSingleSizeLimit*1024*1024,
        },{
            success:function(file, response){
                this.options.uploadFileNum++;
                var data = eval(response._raw);
                var id = data[0].id;
                var file_name = data[0].file_name;
                var index1=file_name.lastIndexOf(".");
                var index2=file_name.length;
                var file_ext=file_name.substring(index1,index2);//后缀名
                var file_savepath = data[0].path;
                var file_size = _sizeFormat(data[0].size);
                var upload_time = data[0].time;
                var fid = file.id;
                var filenote = file_name.replace(file_ext, '');
                var fileno = $("#file-list-<?=$item_type?> tr").length;

                var trhtml = '<tr id="file'+id+'">\n' +
                    '    <td class="text-center">\n' +
                    '    <i class="fa fa-arrows-alt handle"></i>\n' +
                    '    </td>\n' +
                    '    <td class="text-center"><input type="hidden" name="attachment_id[]" value="'+id+'"><input class="form-control" style="text-align: center" type="number" min=1 name="no['+id+']" value="'+(fileno+1)+'" /></td>\n' +
                    '    <td class="text-center"><input class="form-control" type="text" name="filenote['+id+']" value="'+filenote+'" /></td>\n' +
                    '    <td class="text-center">\n' +
                    '\t<a href="'+baseurl+'up_files/'+file_savepath+'" class="btn btn-alt-primary btn-sm" target="_blank">查看</a>\n' +
                    '\t<a href="javascript:delete_file('+id+',\''+fid+'\')" class="btn btn-alt-danger btn-sm" onClick="return confirm(\'确定要删除该文件吗？\')">删除</a>\n' +
                    '    </td>\n' +
                    '</tr>';
                $(".ant-table-placeholder").remove();
                $("#file-list-<?=$item_type?>").append(trhtml);
            }
        });
    });

    function delete_file(id,fid) {
        var url = "<?=site_url('common/file_delete')?>";
        $.getJSON(url,{fid:id,id:'<?=$project->getProjectId()?>'},function (data) {
            if(data.code==1){
                showSuccess('删除成功！');
                $("#file"+id).remove();
                $("#attachment_"+id).remove();
                if(fid) $('#PDF_'+fid).remove();
            }else{
                showError(data.msg);
            }
        });
    }

    var filetable = document.getElementById('file-list-<?=$item_type?>');
    var sortable = new Sortable(filetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $(".file-list-group tr").each(function (){
                $(this).find("input[name^=no]").val(++index);
            })
        },

    });
</script>