<style type="text/css">
.widget-config { border:solid 1px #efefef; padding:5px;}
.widget-config dt { border-bottom:dashed 2px #efefef;}
.widget-config dd { float:left; padding:3px 5px;}
</style>
<div class="row">
    <div class="col-lg-12">
        <h2>附件上传配置</h2>
        <dl class="widget-config">
          <div class="form-group row">
            <label class="col-md-2 text-right">描述</label>
            <div class="col-md-9">
            <textarea name="configs[attachement][subject]" class="form-control" rows="4"><?=$configs['attachement']['subject']?></textarea>
            <small class="help-block"></small>
            </div>
            <div class="clearfix"></div>
          </div>

          <div class="form-group row">
            <label class="col-md-2 text-right">上传大小</label>
            <div class="col-md-2">
            <div class="input-group">
              <input name="configs[attachement][file_size]" type="number" id="file_size" value="<?=$configs['attachement']['file_size']?:20?>" class="form-control"/>
              <span class="input-group-addon">M</span>
            </div>
            <small class="help-block"></small>
            </div>
            <div class="clearfix"></div>
          </div>

          <div class="form-group row">
            <label class="col-md-2 text-right">允许后缀</label>
            <div class="col-md-9">
              <?=get_checkbox(['docx','pdf','doc','jpg','png','xlsx'],'configs[attachement][file_ext]',$configs['attachement']['file_ext']?:'20','',true,2)?>
              <small class="help-block"></small>
            </div>
            <div class="clearfix"></div>
          </div>

          <div class="form-group row">
            <label class="col-md-2 text-right">允许转换</label>
            <div class="col-md-4">
              <?=get_radio([1=>'是',0=>'否'],'configs[attachement][allow_convert]',$configs['attachement']['allow_convert']?:0,'',false)?>
              <small class="help-block">是否允许将上传的 <span class="text-danger">WORD</span> 转成 PDF</small>
            </div>
            <div class="clearfix"></div>
          </div>

          <div class="form-group row">
            <label class="col-md-2 text-right">允许裁剪</label>
            <div class="col-md-4">
              <?=get_radio([1=>'是',0=>'否'],'configs[attachement][allow_imagecropper]',$configs['attachement']['allow_imagecropper']?:0,'',false)?>
              <small class="help-block">是否允许对上传的 <span class="text-danger">图片</span> 进行裁剪</small>
            </div>
            <div class="clearfix"></div>
          </div>

          <div class="form-group row">
            <label class="col-md-2 text-right">模式</label>
            <div class="col-md-4">
              <?=get_radio([0=>'多文件'],'configs[attachement][single_model]',$configs['attachement']['single_model']?:0,'',false)?>
              <small class="help-block">附件上传模式</small>
            </div>
            <div class="clearfix"></div>
          </div>

          <div class="form-group row">
            <label class="col-md-2 text-right">部件模式</label>
            <div class="col-md-9">
                <?=get_radio(array('worker'=>'申报书引擎','tasker'=>'任务书引擎','summaryer'=>'年度考核引擎','completer'=>'验收报告引擎'),'configs[attachement][engine]',($configs['attachement']['engine']?:'worker'),'',false)?>
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group row">
            <label class="col-md-12 text-right">附件清单<small class="help-block text-danger">各附件配置统一继承以上设置</small></label>
            <div class="col-md-12 col-xs-12">
              <ul class="list-unstyled">
                <li class="col-lg-8 text-center float-left">名称</li>
                <li class="col-lg-4 text-center float-right">是否必传</li>
                <?php foreach($widgetConfigs['necessary'] as $mark=>$subject): ?>
                  <li class="col-md-12 col-xs-12">
                    <div class="col-md-8 col-xs-8  float-left">
                    <?=get_checkbox([$mark=>$configs['attachement']['alias'][$mark]?:$subject],'configs[attachement][necessary]',$configs['attachement']['necessary']?:'20','',false,12)?>
                    </div>
                    <div class="col-md-1 col-xs-1  float-left">
                      <i class="fa fa-edit" onclick="editSubject(this)"></i>
                      <input type="hidden" name="configs[attachement][alias][<?=$mark?>]" value="<?=$configs['attachement']['alias'][$mark]?>">
                    </div>
                    <div class="col-md-3 col-xs-3  float-right">
                    <?=get_radio([1=>'是',0=>'否'],'configs[attachement][is_must]['.$mark.']',$configs['attachement']['is_must'][$mark]?:0,'',false)?>
                    </div>
                </li>
                <?php endforeach; ?>
              </ul>
              <small class="help-block"></small>
            </div>
            <div class="clearfix"></div>
          </div>

          <p style="clear:both;"></p>
        </dl>
    </div>
</div>
<script type="text/javascript">
function editSubject(obj){
    var subject = $(obj).closest('div').prev().find('label');
    console.log(subject.text());
    showPrompt('修改别名',subject.text(),2,function(value, index, elem){
        subject.text(value);
        $(obj).closest('div').find('input').val(value);
        layer.close(index);
    });
}
</script>