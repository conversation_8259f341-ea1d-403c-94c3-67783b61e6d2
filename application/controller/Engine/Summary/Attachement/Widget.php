<?php
namespace App\Controller\Engine\Summary\Attachement;
use Sofast\Core\Sf;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use App\Contract\iWidget;

class Widget implements iWidget
{   
    private $summary = NULL;
    /**
     * 应用层配置
     */
    private $configs = array();
    /**
     * 模块层配置
     */
    private $widgetConfigs = array();
    private $view = NULL;
	private $widget_name = 'attachement';
    
	function __construct($widget_name,$configs = NULL)
	{
		$this->widget_name = $widget_name;
		$this->widgetConfigs = Configs::getConfigs();
		if($configs !== NULL){
			$this->configs = $configs;
		}else $this->configs = Configs::getDefault();
		$this->view = new Template(realpath(dirname(__FILE__)).'/View/');
		return $this;
	}
	
	public function setProject($summary)
	{
		$this->summary = $summary;
		return $this;
	}

    public function getWidgetName()
    {
        return $this->widget_name;
    }
	
	public function setConfig($configs=array())
	{
		if(count($configs)) $this->configs = $configs;
		return $this;
	}
	
	public function getConfig()
	{
		return $this->configs;
	}
	
	public function output($tpl='')
	{
		$this->view->set("summary",$this->summary);
		$this->view->set("configs",$this->getConfig());
		$this->view->set("widget_name",$this->widget_name);
		$this->view->set("widgetConfigs",$this->widgetConfigs);
		if($tpl) return $this->view->getContent("Edit/output");
		else{
			$htmlStr = '';
			if(count($this->configs['attachement'])) $htmlStr .= $this->view->getContent("Edit/output");
			return $htmlStr;
		}
	}

	public function lists()
	{
		if(!$this->summary) return array();
		$attachments = $this->summary->attachments('project_*');
		if($attachments->getTotal() < 1) return array();

		$data = array();
		$necessary = $this->configs['attachement']['necessary'];
		$must = array_filter($this->configs['attachement']['is_must']);
		//是否为企业
		$is_business = $this->summary->getCorporation(true)->isBusiness(true);
		foreach($necessary as $_necessary){
			//if(input::session("userlevel") == 1){print_r();exit;}
			$data[$_necessary]['subject'] = $this->configs['attachement']['alias'][$_necessary]?$this->configs['attachement']['alias'][$_necessary]:$this->widgetConfigs['necessary'][$_necessary];
			if($_necessary == 'project_zczmcl' && !$is_business){
				$data[$_necessary]['is_must'] = false;
			}else{
				$data[$_necessary]['is_must'] = $must[$_necessary]?true:false;
			}
			$data[$_necessary]['data'] 	  = array();
		}

		while($attachment = $attachments->getObject()){
			if(!in_array($attachment->getItemType(), $this->configs['attachement']['necessary']))
				continue;
			$_temp = array();
			$_temp['file_id'] = $attachment->getId();
			$_temp['file_name'] = $attachment->getFileName();
			$_temp['file_path'] = $attachment->getFilePath();
			$_temp['file_note'] = $attachment->getFileNote();
			$_temp['file_size'] = $attachment->getFileSize();
			$_temp['updated_at'] = $attachment->getUpdatedAt();

			$data[$attachment->getItemType()]['data'][] = $_temp;
		}

		return $data;
	}
	/**
	 * 模块配置
	 */
	public function manager()
	{
		$this->view->set("configs",$this->configs);
		$this->view->set("widgetConfigs",$this->widgetConfigs);
		return $this->view->getContent("Manager");
	}
	
	/**
	 * 完整性检验
	 * 返回数组
	 */
	function validate(){
		$result = [];
		$must = array_filter($this->configs['attachement']['is_must']);

		foreach($must as $item_type=>$value){
			//过滤掉没有勾选的附件
			if(!in_array($item_type, $this->configs['attachement']['necessary']))
				continue;

			//时有时无的必传单独判断
//			if(in_array($item_type,['project_cxyhzxy','project_zxz_nssbb','project_zxz_yfsjb'])) continue;
			
//			if($item_type=='project_zczmcl'){
//				//自筹能力证明材料
//				$company = $this->summary->getCorporation(true);
//				if($company->isBusiness()){
//					$files = $this->summary->attachements($item_type);
//					if(count($files)==0) $result[] = '必有附件 【'.($this->configs['attachement']['alias']['project_zczmcl']?:$this->widgetConfigs['necessary']['project_zczmcl']).'】 未上传';
//				}
//				continue;
//			}
			$count = $this->summary->getAttachementCount($item_type);
			if($count==0) $result[] = '【'.($this->configs['attachement']['alias'][$item_type]?:$this->widgetConfigs['necessary'][$item_type]).'】 未上传';
			
		}

		//合作协议
//		$cooperations = $this->summary->getCooperations();
//		if($cooperations->getTotal() >= 2){
//			if(in_array('project_hzxy', $this->configs['attachement']['necessary'])){
//				$files = $this->summary->attachements('project_hzxy');
//				if(count($files)==0) $result[] = '必有附件 【'.($this->configs['attachement']['alias']['project_hzxy']?:$this->widgetConfigs['necessary']['project_hzxy']).'】 未上传';
//			}else if(in_array('project_cxyhzxy', $this->configs['attachement']['necessary'])){
//				$files = $this->summary->attachements('project_cxyhzxy');
//				if(count($files)==0) $result[] = '必有附件 【'.($this->configs['attachement']['alias']['project_cxyhzxy']?:$this->widgetConfigs['necessary']['project_cxyhzxy']).'】 未上传';
//			}
//		}

		//产学研合作协议
//		$yield_study = $this->summary->getBaseinfo()->getYieldStudy();
//		if($yield_study == '是' && in_array('project_cxyhzxy', $this->configs['attachement']['necessary'])){
//			$files = $this->summary->attachements('project_cxyhzxy');
//			if(count($files)==0) $result[] = '必有附件 【'.($this->configs['attachement']['alias']['project_cxyhzxy']?:$this->widgetConfigs['necessary']['project_cxyhzxy']).'】 未上传';
//		}

		//查账
		// $company = $this->summary->getCorporation();
		// $cls = sf::getModel("CompanyLibrarys")->selectAll("code='".$company->getCode()."' AND type=3");
		// if($cls->getTotal() >= 1 && in_array('project_zxz_nssbb', $this->configs['attachement']['necessary'])){
		// 	$files = $this->summary->attachements('project_zxz_nssbb');
		// 	if(count($files)<3) $result[] = '必有附件 【'.($this->configs['attachement']['alias']['project_zxz_nssbb']?:$this->widgetConfigs['necessary']['project_zxz_nssbb']).'】 数量不少于3个';
		// }

		// //核定
		// $company = $this->summary->getCorporation();
		// $cls = sf::getModel("CompanyLibrarys")->selectAll("code='".$company->getCode()."' AND type=4");
		// if($cls->getTotal() >= 1 && in_array('project_zxz_yfsjb', $this->configs['attachement']['necessary'])){
		// 	$files = $this->summary->attachements('project_zxz_yfsjb');
		// 	if(count($files)==0) $result[] = '必有附件 【'.($this->configs['attachement']['alias']['project_zxz_yfsjb']?:$this->widgetConfigs['necessary']['project_zxz_yfsjb']).'】 未上传';
		// }
		
		/*if(in_array('project_zxz_nssbb', $this->configs['attachement']['necessary']) || in_array('project_zxz_yfsjb', $this->configs['attachement']['necessary'])){
			$company = $this->summary->getCorporation(true);
			$libs = (array)$company->getLibTypes();
			
			if(in_array(4,$libs)){//如果核定征收
				$files = $this->summary->attachements('project_zxz_yfsjb');
				if(count($files) < 1) $result[] = '必有附件 【'.($this->configs['attachement']['alias']['project_zxz_yfsjb']?:$this->widgetConfigs['necessary']['project_zxz_yfsjb']).'】 还未上传';
			}else{//查账
				$files = $this->summary->attachements('project_zxz_nssbb');
				if(count($files) < 3) $result[] = '必有附件 【'.($this->configs['attachement']['alias']['project_zxz_nssbb']?:$this->widgetConfigs['necessary']['project_zxz_nssbb']).'】 数量不少于3个（请按要求拆分上传）';
			}
		}*/

		return $result;
	}

    function getFileListHtml()
    {
        return '';
    }
	
	function __call($tpl,$args)
	{
		return $this->output($tpl);
	}
	
	function __toString()
	{
		return $this->output();	
	}
}
?>