<?php
namespace App\Controller\Engine\Summary\Content;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use App\Facades\DCS;
use App\Facades\Phpword;

class Edit extends BaseController
{	
    public $summary = null;
    private $configs = [];
	private $type = 'apply';

    function load(){
        $widget_name = input::mix("widget") ? input::mix("widget") : 'attachement';
		
        $this->summary = sf::getModel("Summarys")->selectBySummaryId(input::mix("item_id")?:input::mix("id"));
		//if($this->summary->isNew()) $this->page_debug("项目不存在！",getFromUrl());
		// if($this->summary->getUserId() != input::session("roleuserid")) $this->page_debug("你没有权限执行该操作！",getFromUrl());
		
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->view->set("summary",$this->summary);
        $this->view->set("item_type",$widget_name);
		switch(trim(input::mix("engine")))
		{
			case 'tasker':
				$this->type = 'task';
			break;
			case 'stager':
				$this->type = 'stage';
			break;
			case 'completer':
				$this->type = 'complete';
			break;
			default:
				$this->type = 'apply';
			break;	
		}
        $this->configs = $this->summary->getWidgetConfigs($widget_name,$this->type);
        $this->view->set("configs",$this->configs);
    }

    function index(){
        $this->edit();
    }
	/**
	 * 附件列表
	 */
	function edit(){
        $this->view->apply('inc_body','Edit/edit');
        $this->view->display('page_blank');
	}

    function editContent(){
        if(input::getInput('post')){
            $item_type = Input::getInput('post.item_type').'_text';
            $file = sf::getModel('SummaryContents')->selectBySummaryId($this->summary->getSummaryId(),$item_type);
            $file->setSummaryId($this->summary->getSummaryId());
            $file->setContent(Input::getInput('post.content'));
            $file->setWidgetName($item_type);
            $file->save();

            sf::getModel("Historys")->addHistory(Input::getInput('session.roleuserid'),'编辑正文内容，附件表：SummaryContents,id：'.Input::getInput('mix.id'),'summarys');
            
            $this->page_debug("内容已保存");
            // exit(json_encode(['status'=>true,'message'=>'已保存']));
        }

        $this->view->apply('inc_body','Edit/edit_content');
        $this->view->display('page_blank');
    }
	
	/**
	 * 删除附件
	 */
	function delete()
	{
		$item = sf::getModel("SummaryContents",input::mix("id"));
        
		if($item->getUserId() != input::session("roleuserid")) $this->page_debug("你没有权限删除该附件！",getFromUrl());
		if($item->delete()) $this->page_debug("附件删除成功！",getFromUrl());
		else $this->page_debug("附件删除失败！",getFromUrl());
	}
	
	/**
	 * 附件排序
	 */
	function doSort()
	{
		if(is_array(input::post("sorts"))){
			$sorts = input::post("sorts");
			asort($sorts);//对数据排序
			for($i=0,$n=count($sorts);$i<$n;$i++){
				$item = sf::getModel("SummaryContents",$sorts[$i]);
				if($item->isNew()) continue;
				if($item->getUserId() != input::session("userid")) continue;
				$item->setSort($i);
				$item->save();
				$i++;
			}
		}
		$this->page_debug("附件排序保存成功！",getFromUrl());
	}

    function deleteProjectAttachment(){

//        if($this->summary->getUserId() != input::session("roleuserid"))
//            exit(json_encode(['status'=>false,'message'=>'没有权限']));

        $file = sf::getModel('SummaryContents',Input::getInput('post.id'));

        if($file->isNew()){
            exit(json_encode(['status'=>false,'message'=>'附件不存在']));
        }

        if($file->getSummaryId() != Input::getInput('post.item_id')){
            exit(json_encode(['status'=>false,'message'=>'没有权限']));
        }

        $file->delete();

        sf::getModel("Historys")->addHistory(Input::getInput('session.roleuserid'),'删除附件，附件表：SummaryContents,id：'.Input::getInput('mix.id'),'summarys');
            
        exit(json_encode(['status'=>true,'message'=>'已删除']));
    }

    function editProjectAttachmentNote(){
//        if($this->summary->getUserId() != input::session("roleuserid"))
//            exit(json_encode(['status'=>false,'message'=>'没有权限']));

        $file = sf::getModel('SummaryContents',Input::getInput('post.id'));

        if($file->isNew()){
            exit(json_encode(['status'=>false,'message'=>'附件不存在']));
        }

        if($file->getSummaryId() != Input::getInput('post.item_id')){
            exit(json_encode(['status'=>false,'message'=>'没有权限']));
        }

        $file->setFileNote(Input::getInput('post.content'));

        $file->save();

        sf::getModel("Historys")->addHistory(Input::getInput('session.roleuserid'),'编辑附件备注，附件表：SummaryContents,id：'.Input::getInput('mix.id'),'summarys');
            
        exit(json_encode(['status'=>true,'message'=>'已保存']));
    }

    function downloadProjectAttachment(){

        $file = sf::getModel('SummaryContents',Input::getInput('mix.id'));

        if($file->isNew()){
            $this->page_debug('附件不存在');
        }

        if(($file->getSummaryId() != Input::getInput('mix.item_id')) && !in_array(input::session('userlevel'), [1])){
            $this->page_debug('没有权限');
        }

        //sf::getModel("Historys")->addHistory(Input::getInput('session.roleuserid'),'下载附件，附件表：SummaryContents,id：'.Input::getInput('mix.id'),'projects');
		if($file->getServerIp()) $template = "http://".$file->getServerIp().'/up_files/'.$file->getFilePath();
        else $template = site_path('up_files/'.$file->getFilePath());
        //$template = WEBROOT.'/up_files/'.$file->getFilePath();
        $info = pathinfo($template);

        header("Content-type:application/octet-stream");
        header("Accept-Ranges:bytes");
        header("Accept-Length:".filesize($template));
        header('Content-Disposition: attachment; filename='.$info['basename']);
        header('Expires: 0');
        //主要是这个判断
        if (isset($_SERVER['HTTP_USER_AGENT']) && ((strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE') !== false)))
        {
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Pragma: public');
        }else
        {
            header('Pragma: no-cache');
        }

        ob_clean();
        flush();

        exit(file_get_contents($template));
    }

    function downloadTpl(){
        $configs = $this->configs;
        $template = site_path('up_files/'.$configs['attachement']['template']);
        $info = pathinfo($template);

        header("Content-type:application/octet-stream");
        header("Accept-Ranges:bytes");
        header("Accept-Length:".filesize($template));
        header('Content-Disposition: attachment; filename='.$info['basename']);
        header('Expires: 0');
        //主要是这个判断
        if (isset($_SERVER['HTTP_USER_AGENT']) && ((strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE') !== false)))
        {
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Pragma: public');
        }else
        {
            header('Pragma: no-cache');
        }

        ob_clean();
        flush();

        exit(file_get_contents($template));
    }
	
	function downloadPdf(){

        $file = sf::getModel('SummaryContents',Input::getInput('mix.id'));

        if($file->isNew()){
            $this->page_debug('附件不存在');
        }

        if(($file->getSummaryId() != Input::getInput('mix.item_id')) && !in_array(input::session('userlevel'), [1])){
            $this->page_debug('没有权限');
        }

        //sf::getModel("Historys")->addHistory(Input::getInput('session.roleuserid'),'下载附件，附件表：SummaryContents,id：'.Input::getInput('mix.id'),'projects');
		if($file->getServerIp()) $template = "http://".$file->getServerIp().'/up_files/'.$file->getPdfPath();
        else $template = site_path('up_files/'.$file->getPdfPath());
        //$template = WEBROOT.'/up_files/'.$file->getFilePath();
        $info = pathinfo($template);

        header("Content-type:application/octet-stream");
        header("Accept-Ranges:bytes");
        header("Accept-Length:".filesize($template));
        header('Content-Disposition: attachment; filename='.$info['basename']);
        header('Expires: 0');
        //主要是这个判断
        if (isset($_SERVER['HTTP_USER_AGENT']) && ((strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE') !== false)))
        {
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Pragma: public');
        }else
        {
            header('Pragma: no-cache');
        }

        ob_clean();
        flush();

        exit(file_get_contents($template));
    }

    function convertProjectAttachmentToPdf(){
        $file = sf::getModel('SummaryContents',Input::getInput('mix.id'));

        if($file->isNew()){
            exit(json_encode(['status'=>false,'message'=>'附件不存在']));
        }

        if(($file->getSummaryId() != Input::getInput('mix.item_id')) && !in_array(input::session('userlevel'), [1])){
            exit(json_encode(['status'=>false,'message'=>'没有权限']));
        }

        $docFile = WEBROOT.'/up_files/'.$file->getFilePath();

        //if(!is_file($docFile))
        //    exit(json_encode(['status'=>false,'message'=>'文件不存在，请重新上传']));

        $filePath = $file->getFilePath();
        $info = pathinfo($filePath);

        if(!in_array($info['extension'],['doc','docx'])){
            exit(json_encode(['status'=>false,'message'=>'文件格式错误，仅支持doc或docx']));
        }

        $pdfPath = $info['dirname'].'/'.$info['filename'].'.pdf';
        $pdfSaveName = $info['filename'];

        $sourceFile = site_path('up_files/'.$file->getFilePath());
        //if($file->getServerIp()) $sourceFile = "http://".$file->getServerIp().'/up_files/'.$file->getFilePath();
        //else $sourceFile = site_path('up_files/'.$file->getFilePath());

        $msg = DCS::setDownloadUrl($sourceFile)->toPdf()->save($pdfSaveName,WEBROOT.'/up_files/'.$info['dirname']);

        if($msg['errorMsg']){
            exit(json_encode(['status'=>false,'message'=>'文件转换失败：'.implode('、', $msg['errorMsg'])]));
        }else{
            $file->setPdfPath($pdfPath);
            $file->save();

            exit(json_encode(['status'=>true,'message'=>'文件已转换','data'=>$pdfPath]));
        }
        
    }

    function replacePdf(){
        $file = sf::getModel('SummaryContents',Input::getInput('mix.id'));
        $pdfPath = Input::getInput('mix.path');

        if($file->isNew()){
            exit(json_encode(['status'=>false,'message'=>'附件不存在']));
        }

        if(($file->getSummaryId() != Input::getInput('mix.item_id')) && !in_array(input::session('userlevel'), [1])){
            exit(json_encode(['status'=>false,'message'=>'没有权限']));
        }

        $info = pathinfo($pdfPath);

        if(!in_array($info['extension'],['pdf'])){
            exit(json_encode(['status'=>false,'message'=>'文件格式错误，仅支持pdf']));
        }

        $file->setPdfPath($pdfPath);
        $file->save();

        exit(json_encode(['status'=>true,'message'=>'文件已替换','data'=>$pdfPath]));
    }

    function uploadProjectAttachment(){
        if($_FILES){
            /**
             * 未登录则不能上传
             */
            if(!$_SESSION['id'])
                exit(json_encode(['status'=>false,'message'=>'页面已过期'],JSON_UNESCAPED_UNICODE));

            $upload_type = input::getInput("post.upload_type") ? explode(",",input::getInput("post.upload_type")) : config::get("upload_type",array('jpg','bmp','pdf','png','gif','bmp','rar','doc','xls','zip','docx'));
            $upload_size = input::getInput("post.upload_size") ? input::getInput("post.upload_size") : config::get("upload_size","20971520");

            $upload = sf::getLib("upload","file",config::get("upload_path","./up_files/"),$upload_size,$upload_type);

            if($upload->upload()){
                $this->summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput("post.item_id"));

                $result = $upload->getSaveFileInfo();
                foreach($result as $index=>$files){
                    //对上传的文件进行检查
                    $msg = $this->checkContent($files);
                    if($msg) exit(json_encode(['status'=>false,'message'=>$msg],JSON_UNESCAPED_UNICODE));

                    $content = sf::getModel('SummaryContents');
                    $content->setSummaryId($this->summary->getSummaryId());
                    $content->setWidgetName(input::getInput("post.item_type")?input::getInput("post.item_type"):'content');
                    $content->setFilePath($files['path']);
                    $content->setFileName($files['name']);
                    $content->setFileExt($files['type']);
                    $content->setServerIp(trim($_SERVER['SERVER_ADDR']));
                    // $content->setContent($this->getContent($files['path']));
                    $content->save();

                    $result[$index]['file_name'] = $files['name'];
                    $result[$index]['file_savename'] = $files['savename'];
                    $result[$index]['time'] = date("Y-m-d H:i:s");
                    $result[$index]['size'] = $files['size'];
                    $result[$index]['path'] = $files['path'];
                    $result[$index]['id'] = $content->getId();

                    // $msg = $this->checkPart(WEBROOT.'/up_files/'.$this->summary->worker()->getConfigs($content->getWidgetName())['attachement']['template'],WEBROOT.'/up_files/'.$files['path']);
                    // if($msg) $result[$index]['message'] = $msg;
                }
            }else exit(json_encode(['status'=>false,'message'=>$upload->getError()],JSON_UNESCAPED_UNICODE));
            exit(json_encode(['status'=>true,'message'=>'内容已上传','data'=>$result],JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * <AUTHOR>
     * @DateTime  2019-08-27
     * @copyright 获取正文内容
     * @license   [license]
     * @version   [version]
     * @return    [type]      [description]
     */
    function getContent($file_path=''){
        $content = '';

        // $file_path = sf::getModel('SummaryContents',161)->getFilePath();
        $targetFile = WEBROOT.'/up_files/'.$file_path;

        if(!is_file($targetFile)) return $content;

        $target = Phpword::setTemplate($targetFile);
        $targetContent = $target->getDocumentPart('main');
        $content = strip_tags($targetContent);
        return $content;
    }

    /**
     * <AUTHOR>
     * @DateTime  2019-08-13
     * @copyright 校验正文是否上传错误
     * @license   [license]
     * @version   [version]
     * @return    [type]      [description]
     */
    private function checkPart($templateFile='',$targetFile=''){
        $msg = '';

        if(!is_file($templateFile) || !is_file($targetFile)) return $msg;
        
        $template = Phpword::setTemplate($templateFile);
        $templateContent = $template->getDocumentPart('main');
        preg_match('|<w:t>(.*)</w:t>|isU',$templateContent,$templateTitle);

        $target = Phpword::setTemplate($targetFile);
        $targetContent = $target->getDocumentPart('main');
        preg_match('|<w:t>(.*)</w:t>|isU',$targetContent,$targetTitle);
        
        if(strip_tags($templateTitle[0]) != strip_tags($targetTitle[0]))
            $msg = '系统检测到您上传的文件与当前模板可能不匹配，如果没有问题请忽略此消息';
        return $msg;
    }

    /**
     * <AUTHOR>
     * @DateTime  2019-08-13
     * @copyright pdf检查
     * @license   [license]
     * @version   [version]
     * @param     array       $data [description]
     * @return    [type]            [description]
     */
    private function checkContent($data=[]){
        $msg = '';
        if(strtolower($data['file_ext'])=='pdf'){
            if(strpos(file_get_contents(WEBROOT.'/up_files/'.$data['file_path']),'startxref')===false){
                $msg = 'PDF无法被系统识别!';
            }
        }
        return $msg;
    }
	
}
?>