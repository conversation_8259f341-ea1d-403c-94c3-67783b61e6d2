<style type="text/css">
.widget-config { border:solid 1px #efefef; padding:5px;}
.widget-config dt { border-bottom:dashed 2px #efefef;}
.widget-config dd { float:left; padding:3px 5px;}
</style>
<h2>附件上传配置</h2>
<dl class="widget-config">
    <div class="form-group row">
    <label class="col-md-2 text-right">描述</label>
    <div class="col-md-9">
    <textarea name="configs[attachement][subject]" class="form-control" rows="4"><?=$configs['attachement']['subject']?></textarea>
    <small class="help-block">可作为标题，查看模式自动隐藏</small>
    </div>
    <div class="clearfix"></div>
  </div>
    <div class="form-group row">
    <label class="col-md-2 text-right">步骤</label>
    <div class="col-md-9">
    <textarea name="configs[attachement][step]" class="form-control" rows="4"><?=$configs['attachement']['step']?></textarea>
    </div>
    <div class="clearfix"></div>
  </div>

  <div class="form-group row">
    <label class="col-md-2 text-right">上传大小</label>
    <div class="col-md-2">
    <div class="input-group">
      <input name="configs[attachement][file_size]" type="number" id="file_size" value="<?=$configs['attachement']['file_size']?:20?>" class="form-control"/>
      <span class="input-group-addon">M</span>
    </div>
    <small class="help-block"></small>
    </div>
    <div class="clearfix"></div>
  </div>

  <div class="form-group row">
    <label class="col-md-2 text-right">允许后缀</label>
    <div class="col-md-9">
      <?=get_checkbox(['docx','pdf','doc','jpg','png'],'configs[attachement][file_ext]',$configs['attachement']['file_ext']?:['docx'],'',true,2)?>
      <small class="help-block"></small>
    </div>
    <div class="clearfix"></div>
  </div>

  <div class="form-group row">
    <label class="col-md-2 text-right">允许转换</label>
    <div class="col-md-4">
      <?=get_radio([1=>'是',0=>'否'],'configs[attachement][allow_convert]',$configs['attachement']['allow_convert']?:1,'',false)?>
      <small class="help-block">是否允许将上传的 <span class="text-danger">WORD</span> 转成 PDF</small>
    </div>
    <div class="clearfix"></div>
  </div>

  <div class="form-group row">
    <label class="col-md-2 text-right">允许裁剪</label>
    <div class="col-md-4">
      <?=get_radio([1=>'是',0=>'否'],'configs[attachement][allow_imagecropper]',$configs['attachement']['allow_imagecropper']?:0,'',false)?>
      <small class="help-block">是否允许对上传的 <span class="text-danger">图片</span> 进行裁剪</small>
    </div>
    <div class="clearfix"></div>
  </div>

  <div class="form-group row">
    <label class="col-md-2 text-right">模式</label>
    <div class="col-md-4">
      <?=get_radio([1=>'单文件'],'configs[attachement][single_model]',$configs['attachement']['single_model']?:1,'',false)?>
      <small class="help-block">附件上传模式</small>
    </div>
    <div class="clearfix"></div>
  </div>

  <div class="form-group row">
    <label class="col-md-2 text-right">下载模板</label>
    <div class="col-md-5">
    <div class="input-group">
      <input type="text" id="template" name="configs[attachement][template]" value="<?=$configs['attachement']['template']?>" class="form-control"/>
      <span class="input-group-addon" onclick="return showWindow('上传','<?=site_url("common/webupload")?>',{area:['350px','250px'],maxmin:false,end:function(){var uploaddata = getCache('uploaddata');$('#template').val(uploaddata[0].path);}})">上传</span>
    </div>
    <small class="help-block"></small>
    </div>
    <div class="clearfix"></div>
  </div>

  <div class="form-group row">
    <label class="col-md-2 text-right">是否显示标题</label>
    <div class="col-md-4">
      <?=get_radio([1=>'是',0=>'否'],'configs[attachement][show_subject]',$configs['attachement']['show_subject']?:0,'',false)?>
      <small class="help-block"></small>
    </div>
    <div class="clearfix"></div>
  </div>
  <div class="form-group row">
    <label class="col-md-2 text-right">是否允许编辑器</label>
    <div class="col-md-4">
      <?=get_radio([1=>'是',0=>'否'],'configs[attachement][editor]',$configs['attachement']['editor']?:0,'',false)?>
      <small class="help-block"></small>
    </div>
    <div class="clearfix"></div>
  </div>

   <div class="form-group row">
    <label class="col-md-2 text-right">是否禁止编辑</label>
    <div class="col-md-4">
      <?=get_radio([1=>'是',0=>'否'],'configs[attachement][disable]',$configs['attachement']['disable']?:0,'',false)?>
      <small class="help-block"></small>
    </div>
    <div class="clearfix"></div>
  </div>

  <div class="form-group row">
    <label class="col-md-2 text-right">使用编辑器说明</label>
    <div class="col-md-9">
    <textarea name="configs[attachement][editor_subject]" class="form-control" rows="4"><?=$configs['attachement']['editor_subject']?></textarea>
    <small class="help-block"></small>
    </div>
    <div class="clearfix"></div>
  </div>
  <div class="form-group row">
    <label class="col-md-2 text-right">显示模板</label>
    <div class="col-md-9">
    <input type="text" id="template" name="configs[attachement][output_template]" value="<?=$configs['attachement']['output_template']?>" class="form-control"/>
    <small class="help-block"></small>
    </div>
    <div class="clearfix"></div>
  </div>
  
  <p style="clear:both;"></p>
</dl>
<div class="widget-config">
    <h4>执行器</h4>
    <div><?=get_radio(array('worker'=>'申报书引擎','tasker'=>'任务书引擎','summaryer'=>'年度考核引擎','completer'=>'验收报告引擎'),'configs[engine]',($configs['engine']?:'worker'),'',false)?></div>
</div>

<script type="text/javascript" src="<?=site_path('js/jquery.cookie.min.js')?>"></script>