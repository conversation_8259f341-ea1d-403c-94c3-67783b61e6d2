<div class="clearfix"></div>
<script type="text/javascript" src="<?=site_path('js/common.js')?>"></script>
<script type="text/javascript" src="<?=site_path('js/cropper/jquery.imageCropper.js')?>"></script>
<script type="text/javascript" src="<?=site_path('js/jquery.webupload.js')?>" role="webupload"></script>
<?php $config = $configs['attachement'];?>
<?php $item = $summary->contents($item_type); ?>
<?php $item_text = $summary->contents($item_type.'_text'); ?>
<div class="block">
    <div class="block-content">
        <div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">
            <?=$config['subject']?>
        </div>
        <div style="border:2px dotted #DDD;padding:10px;margin:10px 0;border-radius: 5px;">

        <?php if ($config['editor']): ?>
        <div class="text-left">
        <div class="col-xs-10 text-info">
        <?=$config['editor_subject']?>
        </div>
        <div class="col-xs-2 text-right no-margin no-padding">
        <button type="button" class="btn btn-info" onclick="return showWindow('编辑内容','<?=site_url('engine/summary/content/edit/editContent/id/'.$summary->getSummaryId().'/widget/'.$item_type)?>')">打开编辑器</button>
        </div>

        <?php if($item_text['content']): ?>
        <div class="clearfix"></div>
        <hr/>
        <div style="overflow: auto;"><?=$item_text['content']?></div>
        <?php endif; ?>
        </div>
        <div class="clearfix"></div>
        <hr/>
        <?php endif; ?>

        <div class="text-left">
        <div class="col-xs-8 text-danger">
        步骤：<br/>
        <?php
        if($config['step']):
            echo showText($config['step']);
        else:
            ?>
            1、下载正文模板<br/>
            2、用office软件打开并填写，填写完毕后转换为pdf格式<br/>
            3、上传<br/>
        <?php
        endif;
        ?>
        </div>
        <div class="col-xs-4 text-right no-margin no-padding">
        <?php if ($config['template']): ?>
            <?=btn("link","下载模板",site_url("up_files/".$config['template']),"download")?>
        <?php else: ?>
            <button type="button" class="btn btn-warning btn-sm">未找到模板</button>
        <?php endif ?>
        </div>
        </div>
        <div class="clearfix"></div>
        <hr/>

        <div class="alert alert-danger hidden" id="message_filelist_<?=$item_type?>"></div>

        <div id="haddle_filelist_<?=$item_type?>" class="text-info <?php if($item['file_path']): ?>hidden <?php endif; ?>">
            文件大小不能超过：<b><?php if(!$config['file_size']) echo '20';else echo round($config['file_size'],2);?>M</b>,
                上传格式为：<b><?=$config['file_ext']?implode(',',$config['file_ext']):'jpg,png,doc,docx,pdf'?></b>
            <div class="pull-right">
                <div id="upload_<?=$item_type?>" class="picker">选择文件</div>
            </div>
            <hr/>
        </div>

        <div class="space-10"></div>

        <div id="filelist_<?=$item_type?>">

        <?php if(!$item['file_path']): ?>
        <div class="text-center nofile">
        <p>
            <i class="fa fa-sticky-note-o fa-5x grey"></i>
            <small class="help-block mt-2">没有上传文件</small>
        </p>
        </div>
        <?php else: ?>
        <div class="text-center contentarea">
            <p align="left">
                文件名：<?=$item['file_name']?>
            </p>
            <p >
                <a data-id="<?=$item['id']?>" href="<?=site_path('engine/summary/content/edit/downloadProjectAttachment/id/'.$item['id'].'/item_id/'.$summary->getSummaryId())?>" class="downloadfile" style="display: inline-block;width: 4rem;">

                    <?php if(in_array($item['file_ext'],['doc','docx'])): ?>
                        <i class="fa fa-file-word fa-5x"></i>
                    <?php endif; ?>
                    <?php if(in_array($item['file_ext'],['pdf'])): ?>
                        <i class="fa fa-file-pdf fa-5x text-danger"></i>
                    <?php endif; ?>
                    <small class="help-block mt-2">点击下载</small>
                </a>

                <?php if($config['allow_convert'] && in_array(strtolower($item['file_ext']), ['doc','docx'])): ?>
                <?php if($item['pdf_path']): ?>
                <a id="preview<?=$item['id']?>" target="_blank" data-id="<?=$item['id']?>" href="<?=site_path('up_files/'.$item['pdf_path'])?>" style="display: inline-block;margin-left: 60px;">

                    <i class="fa fa-file-pdf fa-5x text-danger"></i>

                    <small class="help-block mt-2">查看PDF</small>
                </a>
                <?php else: ?>
                <a id="pdf<?=$item['id']?>" data-id="<?=$item['id']?>" href="javascript:void(0);" class="grey pdfdownload" onclick="convertAttachmentSingle(this,'attachement_<?=$item_type?>','<?=$summary->getSummaryId()?>',function(obj,json){
                    showMsg(json.message);$(obj).attr('href','<?=site_path('up_files/')?>'+'/'+json.data).attr('target','_blank' ).removeClass('grey').removeAttr('onclick').find('small').text('查看PDF');$(obj).find('.fa').addClass('text-danger');
                })" style="display: inline-block;margin-left: 60px;width: 4rem;">
                    <i class="fa fa-file-pdf fa-5x"></i>
                    <small class="help-block mt-2">转成PDF</small>
                </a>
                <?php endif; ?>
                <?php endif; ?>
            </p>
            <p>
                <div class="alert alert-warning text-left d-none">
                    <h3>替换须知</h3>
                   （1）如果系统生成的PDF出现格式变形，内容丢失，可自行上传转换好的PDF文件；<br/>
                   （2）自行上传的PDF文件内容必须与WORD文档保持一致,如因此带来的后果需自行承担；<br/>
                   （3）自行上传的PDF文件需使用office2007及更高版本导出生成（转换的计算机最好不要装有其他PDF转化或查看软件），PDF文件不能进行加密;<br/>
                   （4）填写完成后，<strong>务必预览申报书PDF完整版</strong>，检查上传的PDF文件是否符合要求。<strong>如上传的PDF不符合要求，将无法生成完整版的PDF申报书，因此带来的后果需自行承担</strong>。
        <div class=" btn-sm">
                <a data-id="<?=$item['id']?>" href="javascript:void(0);" class="btn btn-sm btn-warning" onclick="return showWindow('上传','<?=site_url("common/webupload")?>',{area:['350px','250px'],maxmin:false,end:function(){var uploaddata = getCache('uploaddata');
                ajaxData('<?=site_url("engine/summary/content/edit/replacePdf")?>',{id:'<?=$item['id']?>',path:uploaddata[0].path,item_id:'<?=$summary->getSummaryId()?>'},'json',function(){},function(json){showMsg(json.message);if(json.status) $('#preview<?=$item['id']?>').attr('href','<?=site_path('up_files/')?>'+'/'+uploaddata[0].path+'?v='+Math.random());},function(){});}})">替换PDF</a>
                </div>
                </div>
            </p>

            <p>
                <div class=" btn-sm">
                <a data-id="<?=$item['id']?>" href="javascript:void(0);" class="btn btn-sm btn-alt-info d-none" onclick="convertAttachmentSingle(this,'attachement_<?=$item_type?>','<?=$summary->getSummaryId()?>',function(obj,json){
                    showMsg(json.message);$('#preview<?=$item['id']?>').attr('href','<?=site_path('up_files/')?>'+'/'+json.data+'?v='+Math.random()).attr('target','_blank' ).removeClass('grey').removeAttr('onclick').find('small').text('查看PDF');$('#preview<?=$item['id']?>').find('.fa').addClass('text-danger');
                })">重新转换</a>
                <a href="<?=site_path('up_files/'.$item['file_path'])?>" class="btn btn-sm btn-alt-primary" target="_blank">查看</a>
                <a data-id="<?=$item['id']?>" href="javascript:void(0);" class="btn btn-sm btn-alt-danger" onclick="deleteAttachmentSingle(this,'filelist_<?=$item_type?>','<?=$summary->getSummaryId()?>')">删除</a>
                </div>
            </p>
        </div>
        <?php endif; ?>
        </div>

        <script language="javascript" type="text/javascript">
            $(function(){
                $('#upload_<?=$item_type?>').webupload({
                    server: baseurl+"engine/summary/content/edit/uploadProjectAttachment",
                    pick: {
                        id: '#upload_<?=$item_type?>',
                        multiple:false,
                        label: '选择文件'
                    },
                    formData:{item_type:'<?=$item_type?>',item_id:'<?=$summary->getSummaryId()?>'},
                    fileNumLimit:30,
                    fileSizeLimit:parseInt(<?=$config['file_size']?:20?>)*1024*1024,
                    fileSingleSizeLimit:parseInt(<?=$config['file_size']?:20?>)*1024*1024,
                    accept:{
                        title: 'att',
                        extensions: '<?=$config['file_ext']?implode(',', $config['file_ext']):'jpg'?>',
                        mimeTypes: '.<?=$config['file_ext']?implode(',.', $config['file_ext']):'jpg'?>'
                    }
                },
                {
                accept:function( file, response ) {
                    var data = $.parseJSON(response._raw);
                    if ( !data.status ) {
                        showMsg(data.message,{icon:2});
                        return false;
                    }
                },
                progress:function( file, percentage ) {
                    $('#upload_<?=$item_type?>').find('.webuploader-pick').text('文件上传中...');
                },
                complete:function( file ) {
                    $('#upload_<?=$item_type?>').find('.webuploader-pick').text('选择文件');
                },
                queued:function( file ) {
                    $('#upload_<?=$item_type?>').find('.webuploader-pick').text('准备上传');
                },
                error:function(type) {
                    $('#upload_<?=$item_type?>').find('.webuploader-pick').text('选择文件');
                    if (type == "Q_TYPE_DENIED") {
                        showMsg("请上传 <?=implode('、', $config['file_ext'])?> 格式的文件");
                    } else if (type == "Q_EXCEED_SIZE_LIMIT") {
                        showMsg("文件大小不能超过<?=$config['file_size']?>M");
                    }else {
                        showMsg("上传出错！请检查后重新上传！错误代码 "+type);
                    }
                  },
                  success:function(file, response){
                    var data = $.parseJSON(response._raw);

                    if ( !data.status ) {
                        showMsg(data.message,{icon:2});
                        return false;
                    }

                    data = data.data;

                    replaceContent<?=$item_type?>(data,'filelist_<?=$item_type?>');
                  }
                });
            });

        </script>
        <div class="space"></div>

        <script language="javascript" type="text/javascript">
        function replaceContent<?=$item_type?>(data,containerId){
            var id = data[0].id;
            var file_name = data[0].file_name;
            var index1=file_name.lastIndexOf(".");
            var index2=file_name.length;
            var file_ext=file_name.substring(index1,index2);//后缀名
            var file_savepath = data[0].path;
            var file_size = Math.ceil(data[0].size/1024,2);
            var upload_time = data[0].time;
            var isWord = file_ext=='.doc' || file_ext=='.docx';
            var isPdf = file_ext=='.pdf';

            var html = `
            <p align="left">
                文件名：`+file_name+`
            </p>
            <p align="center">
                <a data-id="`+id+`" href="<?=site_path('engine/summary/content/edit/downloadProjectAttachment/item_id/'.$summary->getSummaryId())?>/id/`+id+`" class="downloadfile" style="display: inline-block;">
            `;

            if(isWord){
                html += `<i class="fa fa-file-word fa-5x"></i>`;
            }

            if(isPdf){
                html += `<i class="fa fa-file-pdf fa-5x text-danger"></i>`;
            }

            html += `
                    <small class="help-block mt-2">点击下载</small>
                </a>
            `;
            if(isWord){
                html += `
                    <?php if($config['allow_convert']): ?>
                    <a data-id="`+id+`" href="javascript:void(0);" class="grey" onclick="convertAttachmentSingle(this,'attachement_<?=$item_type?>','<?=$summary->getSummaryId()?>',function(obj,json){
                    showMsg(json.message);$(obj).attr('href','<?=site_path('up_files/')?>'+'/'+json.data).attr('target','_blank' ).removeClass('grey').removeAttr('onclick').find('small').text('查看PDF');$(obj).find('.fa').addClass('text-danger');
                })" style="display: inline-block;margin-left: 60px;">

                        <i class="fa fa-file-pdf fa-5x"></i>

                        <small class="help-block mt-2">转成PDF</small>
                    </a>
                    <?php endif; ?>
                `;
            }
            html += `
            </p>
            <p>
                <div class=" btn-sm text-center">
                <a data-id="`+id+`" href="javascript:void(0);" class="btn btn-danger" onclick="deleteAttachmentSingle(this,'`+containerId+`','<?=$summary->getSummaryId()?>')">删除</a>
                </div>
            </p>
            `;

            $('#'+containerId).html(html);
            $('#haddle_'+containerId).addClass('hidden');

            if(data[0]['message']){
                $('#message_filelist_<?=$item_type?>').toggleClass('hidden').html(data[0]['message']);
            }else{
                if(isWord){
                    showConfirm('检测到您上传的文件为 WORD ，是否立即转成 PDF ？','',['是','否'],function(index){
                        $('#'+containerId).find('.grey[data-id]').click();
                        layer.close(index);
                    },function(index){

                        layer.close(index);
                    });
                }
                if(isPdf){
                    //转换pdf版本
                    $( '#'+containerId ).find('small.help-block').html('<i class="fa fa-fw fa-spinner fa-spin text-primary"></i> 正在转换格式，请稍候...');
                    checkPdfVer(file_savepath,containerId);
                }

                <?php if($config['allow_imagecropper']): ?>
                var file_name = data[0].file_name;
                var file_ext=file_name.substring(file_name.lastIndexOf("."),file_name.length);
                if(file_ext=='.jpg' || file_ext=='.png'){
                    imageCut('<?=site_path('up_files/')?>'+'/'+data[0].path);
                }
                <?php endif; ?>
            }
        }

        function checkPdfVer(path,containerId) {
            var ext = path.substring(path.length-3);
            if(ext!='pdf') return;
            var url = '<?=site_url('tools/pdf/convert')?>';
            $.post(url,{path:path},function (data) {
                if(data.code==1){
                    showAlert(data.error);
                    $( '#'+containerId ).find('small.help-block').html('转化格式失败');
                }else{
                    $( '#'+containerId ).find('small.help-block').html('上传成功');
                }
            },'json');
        }
        </script>
        <div class="clearfix"></div>
        </div>
    </div>
</div>