<?php
namespace App\Controller\Engine\Summary\Content;
class Configs
{   
    private static $default = [];
    private static $configs = [
        'attachement'=>['name'=>'文件上传',
            'value'=>true,
        ],
    ];
    
    public static function getConfigs($key='')
    {
        if($key) return self::$configs[$key];
        else return self::$configs;
    }
    
    public static function getDefault($_configs=[],$default=[])
    {
        if(count($_configs) == 0) $_configs = self::getConfigs();
        foreach($_configs as $_key => $_val){
            $data = [];
            if(count($_val['childs'])) $default[$_key] = self::getDefault($_val['childs']);
            else $default[$_key] = $_val['value'];
        }
        return $default; 
    }
    
    public static function setConfigs(){}
    
}
?>