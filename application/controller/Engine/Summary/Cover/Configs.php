<?php
namespace App\Controller\Engine\Summary\Cover;
class Configs
{	
	private static $configs = array("zxmc"=>array('name'=>'专项名称','type'=>'checkbox','value'=>false),
									"xmmc"=>array('name'=>'项目名称','type'=>'checkbox','value'=>true),
									"syslb"=>array('name'=>'实验室类别','type'=>'checkbox','value'=>true),
									"szgj"=>array('name'=>'厅市（州）共建','type'=>'checkbox','value'=>true),
									"sbnd"=>array('name'=>'申报年度','type'=>'checkbox','value'=>true),
									"lxnd"=>array('name'=>'立项年度','type'=>'checkbox','value'=>true),
									"sysdw"=>array('name'=>'实验室定位','type'=>'checkbox','value'=>false),
									"xmcpmc"=>array('name'=>'项目（产品）名称','type'=>'checkbox','value'=>true),
									"ssly"=>array('name'=>'所属领域','type'=>'checkbox','value'=>false),
									"subxmmc"=>array('name'=>'显示副标题','type'=>'checkbox','value'=>false),
									"ptmc"=>array('name'=>'平台名称','type'=>'checkbox','value'=>false),
									"ptlb"=>array('name'=>'平台类别','type'=>'checkbox','value'=>false),
									"yjly"=>array('name'=>'研究领域','type'=>'checkbox','value'=>true),
									"research_area"=>array('name'=>'研究领域（六大类）','type'=>'checkbox','value'=>true),
									"xmlb"=>array('name'=>'项目类别','type'=>'checkbox','value'=>true),
									"jsly"=>array('name'=>'技术领域','type'=>'checkbox','value'=>false),
									"hyly"=>array('name'=>'行业领域','type'=>'checkbox','value'=>false),
                                    "frdb"=>array('name'=>'单位法定代表人','type'=>'checkbox','value'=>false),
                                    "leader"=>array('name'=>'中心主任（学科带头人）','type'=>'checkbox','value'=>true),
									"xmfzr"=>array('name'=>'项目负责人','type'=>'checkbox','value'=>true),
									"sxzj"=>array('name'=>'项目首席专家','type'=>'checkbox','value'=>false),
									"ktfzr"=>array('name'=>'课题负责人','type'=>'checkbox','value'=>false),
									"sbdw"=>array('name'=>'申报单位','type'=>'checkbox','value'=>true),
									"sbdw2"=>array('name'=>'申报单位(无盖章)','type'=>'checkbox','value'=>true),
									"dwlxr"=>array('name'=>'单位联系人','type'=>'checkbox','value'=>false),
									"dwlxrdh"=>array('name'=>'联系人电话','type'=>'checkbox','value'=>false),
									"szqy"=>array('name'=>'所在区域','type'=>'checkbox','value'=>false),
									"ssqy"=>array('name'=>'实施区域','type'=>'checkbox','value'=>false),
									"tjdw"=>array('name'=>'主管部门','type'=>'checkbox','value'=>true),
									"syszr"=>array('name'=>'实验室主任','type'=>'checkbox','value'=>true),
									"txdz"=>array('name'=>'通讯地址','type'=>'checkbox','value'=>true),
									"lxr"=>array('name'=>'联系人','type'=>'checkbox','value'=>true),
									"lxdh"=>array('name'=>'联系电话','type'=>'checkbox','value'=>true),
									"qzsj"=>array('name'=>'起止时间','type'=>'checkbox','value'=>true),
									"bsrq"=>array('name'=>'报送日期','type'=>'checkbox','value'=>false),
									"hzgb"=>array('name'=>'合作国别/地区','type'=>'checkbox','value'=>false),
									"gwhzdw"=>array('name'=>'国（境）外合作单位','type'=>'checkbox','value'=>false),
									"cpmc"=>array('name'=>'产品名称及型号','type'=>'checkbox','value'=>false),
									"sqjf"=>array('name'=>'申请经费','type'=>'checkbox','value'=>false),
									"hzdw"=>array('name'=>'合作单位','type'=>'checkbox','value'=>false),
									"zdls"=>array('name'=>'指导老师','type'=>'checkbox','value'=>false),
									"tdmc"=>array('name'=>'团队名称','type'=>'checkbox','value'=>false),
									"tddtr"=>array('name'=>'团队带头人','type'=>'checkbox','value'=>false),
									"yjxm"=>array('name'=>'研究项目','type'=>'checkbox','value'=>false),
									"jsnx"=>array('name'=>'建设年限','type'=>'checkbox','value'=>false),
					);
	
	public static function getConfigs($key='')
	{
		if($key) return self::$configs[$key];
		else return self::$configs;
	}
	
	/**
	 * 取得默认配置
	 */
	public static function getDefault()
	{
		$data = array();
		$_configs = self::getConfigs();
		foreach($_configs as $_key => $_val){
			$data[$_key] = $_val['value'];
		}
		return $data; 
	}
	
}
?>