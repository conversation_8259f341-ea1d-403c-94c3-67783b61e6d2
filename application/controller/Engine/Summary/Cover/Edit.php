<?php
namespace App\Controller\Engine\Summary\Cover;
use App\Controller\BaseController;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class edit extends BaseController
{	
	private $summary = NULL;
	private $configs = array();
    private $type = 'apply';

	function load()
	{
		$this->summary = sf::getModel("Summarys")->selectBySummaryId(input::mix("id"));
		if($this->summary->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->type =  (input::mix("worker") == 'tasker') ? 'task' : 'apply';
		$this->view = new Template(realpath(dirname(__FILE__)).'/View/');
		$this->configs = $this->summary->getWidgetConfigs('cover',$this->type);
		$this->view->set("summary",$this->summary);
		$this->view->set("configs",$this->configs);
	}
	

	function index()
	{
		if(input::post())
		{
			$this->checkCsrf();
            if(!input::post('subject')) $this->error('请填写项目名称');
            $data = input::post('data');
            $this->summary->setSubject(input::post('subject'));
            $this->summary->setPrincipalName(input::post('syszr'));
            $this->summary->setLinkman($data['lxr']);
            $this->summary->setLinkmanMobile($data['lxdh']);
            $this->summary->setDatas($data);
            if(input::post('build_year')) $this->summary->setBuildYear(input::post('build_year'));
            //研究领域
            if(input::post("subject_id")){
                $_subject = sf::getModel("Subjects")->selectByCode(input::post("subject_id"),1);
                $this->summary->setSubjectId($_subject->getCode());
                $this->summary->setSubjectName($_subject->getSubject());
            }
            if(input::post("subject_ids")){
                $subjectId = array_pop (input::post("subject_ids"));
                $this->summary->setSubjectIds(input::post("subject_ids"));
                $this->summary->setSubjectId($subjectId);
                $_subject = sf::getModel("Subjects")->selectByCode($subjectId,1);
                $this->summary->setSubjectName($_subject->getSubject());
            }
            $this->summary->setUpdatedAt(date('Y-m-d H:i:s'));
            $this->summary->save();
            $this->closeWindow();
		}
		
		if(input::session("userlevel") == 2){//兼职单位
			$declarer = sf::getModel("Declarers")->selectByUserId(input::session("roleuserid"));
			if(!$multiples = $declarer->multiples(true)) $multiples = false;

            $declarers = sf::getModel("Declarers")->selectAll("corporation_id = '".$declarer->getCorporationId()."'");
			$this->view->set("multiples",$multiples);
			$this->view->set("declarers",$declarers);
		}
		$this->view->set("summary",$this->summary);
		$this->view->apply("inc_body","Edit");
		$this->view->display("page_blank");
	}
	
}
?>