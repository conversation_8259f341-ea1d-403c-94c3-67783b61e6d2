<style type="text/css">
.widget-config {
	border:solid 1px #efefef;
	padding:5px;
}
.widget-config dt {
	border-bottom:dashed 2px #efefef;
}
.widget-config dd {
	float:left;
	padding:3px 5px;
}
</style>
<h2>封面显示配置</h2>
<dl class="widget-config">
  <?php  foreach($widgetConfigs as $_key => $_val):?>
  <dd>
    <label>
      <input type="checkbox" name="configs[<?=$_key?>]" value="1"<?php if($configs[$_key]):?> checked="checked"<?php endif;?> />
      <input type="text" name="configs[langs][<?=$_key?>]" value="<?=$configs['langs'][$_key]?:$_val['name']?>" size="36" />
    </label>
  </dd>
  <?php  endforeach;?>
  <p style="clear:both;"></p>
</dl>
<div class="widget-config">
    <h4>修改权限</h4>
    <div><?=get_radio(array('readonly'=>'只读','write'=>'可修改'),'configs[writable]',($configs['writable']?:'readonly'),'',false)?></div>
</div>
<div class="widget-config">
    <h4>执行器</h4>
    <div><?=get_radio(array('worker'=>'申报书引擎','tasker'=>'任务书引擎','summaryer'=>'年度考核','completer'=>'验收书引擎'),'configs[engine]',($configs['engine']?:'summaryer'),'',false)?></div>
</div>