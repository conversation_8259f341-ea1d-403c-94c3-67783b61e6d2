<div class="main">
  <div class="btn-group btn-group-sm" role="group">
    <?=btn("back")?>
    <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-alt-primary"><i class="fa fa-save"></i> 保存资料</a>
    <p style="clear:both;"></p>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <caption>
        编辑封面
        </caption>
        <?php if($configs['xmmc']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['xmmc']?:'项目名称'?></th>
          <td width="85%"><input readonly name="subject" type="text" id="subject" value="<?=$summary->getSubject()?>" style="99%" size="36" class="required form-control"  /></td>
        </tr>
        <?php endif;?>
        <?php if($configs['syslb']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['syslb']?:'实验室类别'?></th>
          <td width="85%">
              <select name="data[type]" id="type" class="form-control">
                  <?=getSelectFromArray(['企业类','学科类'],$summary->getData('type')->getData())?>
              </select>
          </td>
        </tr>
        <?php endif;?>
        <?php if($configs['szgj']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['szgj']?:'厅市（州）共建'?></th>
          <td width="85%">
              <select name="data[built_together]" id="built_together" class="form-control">
                  <option value="">请选择</option>
                  <?=getSelectFromArray(['厅市（州）共建（学科类）','厅市（州）共建（企业类）','否'],$summary->getData('built_together')->getData())?>
              </select>
          </td>
        </tr>
        <?php endif;?>
      <?php if($configs['sysdw']):?>
          <tr>
              <th width="15%" align="right"><?=$configs['langs']['sysdw']?:'实验室定位'?></th>
              <td width="85%"><?=get_radio(['基础研究','应用基础研究','前沿技术研究'],'data[sysdw]',$summary->getData('sysdw'))?></td>
          </tr>
      <?php endif;?>
        <?php if($configs['xmcpmc']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['xmcpmc']?:'项目（产品）名称'?></th>
          <td width="85%"><input name="subject" type="text" id="subject" value="<?=$summary->getSubject()?>" style="99%" size="36" class="required" /></td>
        </tr>
        <?php endif;?>
        <?php if($configs['cpmc']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['cpmc']?:'产品名称及型号'?></th>
          <td width="85%"><input name="subject" type="text" id="subject" value="<?=$summary->getSubject()?>" style="99%" size="36" class="required" /></td>
        </tr>
        <?php endif;?>
        <?php if($configs['ssly']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['ssly']?:'所属领域'?></th>
          <td width="85%"><?=get_radio(['集成电路与新型显示','新一代网络技术','大数据','软件与信息服务','航空与燃机','智能装备','轨道交通','新能源与智能汽车','医疗健康','新材料','清洁能源','绿色化工','节能环保','新一代人工智能','农产品精深加工','现代农业种业','现代农业装备','现代农业冷链物流'],'ssly',$summary->product()->getSsly())?></td>
        </tr>
        <?php endif;?>
        <?php if($configs['tdmc']):?>
          <tr>
              <th width="15%" align="right"><?=$configs['langs']['tdmc']?:'团队名称'?></th>
              <td width="85%"><input name="subject" type="text" id="subject" value="<?=$summary->getSubject()?>" style="99%" size="36" class="required" /></td>
          </tr>
        <?php endif;?>
        <?php if($configs['ptmc']):?>
          <tr>
              <th width="15%" align="right"><?=$configs['langs']['ptmc']?:'平台名称'?></th>
              <td width="85%"><input name="subject" type="text" id="subject" value="<?=$summary->getSubject()?>" style="99%" size="36" class="required" /></td>
          </tr>
        <?php endif;?>
        <?php if($configs['ssqy']):?>
          <tr>
              <th width="15%" align="right"><?=$configs['langs']['ssqy']?:'实施区域'?></th>
              <td width="85%"><input type="text" name="implementation_area" class="myarea" value="<?=$summary->getBaseinfo()->getData('implementation_area')?:'510000'?>" ajaxUrl="<?=site_url("ajax/area")?>" fixProvince="true" /></td>
          </tr>
        <?php endif;?>
        <?php if($configs['yjly']):?>
        <tr>
          <th width="15%" align="right">研究领域</th>
          <td width="85%">
              <div class="cxselect" data-selects="subject_id1,subject_id2,subject_id3" data-url="<?= site_path('json/subjects.json') ?>" data-json-value="v" style="float: left">
                  <select class="form-control w-auto custom-control-inline subject_id1" data-value="<?=$summary->getSubjectIds(1)?>" name="subject_ids[subject_id1]"></select>
                  <select class="form-control w-auto custom-control-inline subject_id2" name="subject_ids[subject_id2]" data-first-title="请选择" data-value="<?=$summary->getSubjectIds(2)?>"></select>
                  <select class="form-control w-auto custom-control-inline subject_id3" name="subject_ids[subject_id3]" data-first-title="请选择" data-value="<?=$summary->getSubjectIds(3)?>"></select>
              </div>
              <div class="clearfix"></div>
              <p>备注：请务必根据实际情况选择到最后一级。</p>
          </td>
        </tr>
        <?php endif;?>
        <?php if($configs['research_area']):?>
        <tr>
          <th width="15%" align="right">研究领域</th>
          <td width="85%">
              <select name="data[research_area]" id="research_area" class="form-control">
                  <option value="">请选择</option>
                  <?=getSelectFromArray(['医学综合领域','工程与材料综合领域','数理化与地球科学综合领域','信息综合领域','生物综合领域'],$summary->getData('research_area'))?>
              </select>
          </td>
        </tr>
        <?php endif;?>
        <?php if($configs['hyly']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['hyly']?:'行业领域'?></th>
          <td width="85%">
              <div class="cxselect" data-selects="industry_id1,industry_id2,industry_id3,industry_id4" data-url="<?= site_path('json/industrys.json') ?>" data-json-value="v" style="float: left">
                  <select class="form-control w-auto custom-control-inline industry_id1" data-value="<?=$summary->getIndustryIds(1)?>" name="industry[industry_id1]"></select>
                  <select class="form-control w-auto custom-control-inline industry_id2" name="industry[industry_id2]" data-first-title="请选择" data-value="<?=$summary->getIndustryIds(2)?>"></select>
                  <select class="form-control w-auto custom-control-inline industry_id3" name="industry[industry_id3]" data-first-title="请选择" data-value="<?=$summary->getIndustryIds(3)?>"></select>
                  <select class="form-control w-auto custom-control-inline industry_id4" name="industry[industry_id4]" data-first-title="请选择" data-value="<?=$summary->getIndustryIds(4)?>"></select>
              </div>
          </td>
        </tr>
        <?php endif;?>
          <?php if($configs['syszr']):?>
              <tr>
                  <th width="15%" align="right"><?=$configs['langs']['syszr']?:'实验室主任'?></th>
                  <td width="85%"><input  name="syszr" type="text" id="syszr" value="<?=$summary->getPrincipalName()?>" style="99%" size="36" class="required form-control"  /></td>
              </tr>
          <?php endif;?>
      <?php if($configs['xmlb']):?>
          <tr>
              <th width="15%" align="right">项目类别</th>
              <td width="85%">
                  <select name="project_type" id="project_type" class="form-control">
                      <option value="">=请选择=</option>
                      <?=getProjectTypes($summary->getProjectType())?>
                  </select>
              </td>
          </tr>
          <tr id="plan_id">
              <th width="15%" align="right">所属计划</th>
              <td width="85%">
                  <select name="plan_id"  class="form-control">
                      <option value="">=请选择=</option>
                      <?=getPlanOption($summary->getPlanId())?>
                  </select>
              </td>
          </tr>
      <?php endif;?>
        <?php if($multiples):?>
        <tr>
          <th align="right"><?=$configs['langs']['sbdw']?:'申报单位'?></th>
          <td><select name="company_id" id="company_id" class="form-control">
              <?php while($company = $multiples->getObject()):?>
              <option value="<?=$company->getUserId()?>"<?php if($company->getUserId() == $summary->getCorporationId()):?> selected="selected"<?php endif;?>>
              <?=$company->getSubject()?>
              </option>
              <?php endwhile;?>
            </select>
        </tr>
        <?php endif;?>
          <?php if($configs['txdz']):?>
              <tr>
                  <th align="right"><?=$configs['langs']['txdz']?:'通讯地址'?></th>
                  <td><input name="data[txdz]" type="text" value="<?=$summary->getData('txdz')?>"  class="form-control"  /></td>
              </tr>
          <?php endif;?>
          <?php if($configs['lxr']):?>
              <tr>
                  <th align="right"><?=$configs['langs']['lxr']?:'联系人'?></th>
                  <td><input name="data[lxr]" type="text" value="<?=$summary->getData('lxr')?>"  class="form-control"  /></td>
              </tr>
          <?php endif;?>
          <?php if($configs['lxdh']):?>
              <tr>
                  <th align="right"><?=$configs['langs']['lxdh']?:'联系电话'?></th>
                  <td><input name="data[lxdh]" type="text" value="<?=$summary->getData('lxdh')?>"  class="form-control"  /></td>
              </tr>
          <?php endif;?>
        <?php if($configs['jsnx']):
            $firstYear = $summary->getRadicateYear();
            $secondYear = $summary->getRadicateYear()+1;
            $thirdYear = $summary->getRadicateYear()+2;
            $fourthYear = $summary->getRadicateYear()+3;
            $years = [
                $firstYear.'-'.$firstYear,
                $firstYear.'-'.$secondYear,
                $firstYear.'-'.$thirdYear
            ];
            if($summary->getLevel()=='国家级'){
                $years = [
                    $firstYear.'-'.$fourthYear
                ];
            }
        ?>
        <tr>
          <th align="right"><?=$configs['langs']['jsnx']?:'建设年限'?></th>
          <td><select name="build_year" id="build_year" class="form-control">
                  <option value="">请选择</option>
              <?=getSelectFromArray($years,$summary->getBuildYear())?>
            </select>
        </tr>
        <?php endif;?>
        <?php if($configs['qzsj']):?>
        <tr>
          <th align="right"><?=$configs['langs']['qzsj']?:'起止时间'?></th>
          <td><input name="start_at" type="text" id="start_at" class="form-control w-auto custom-control-inline" value="<?=$summary->getStartAt()?>" data-com="date" data-icon="none" data-format="yyyy-mm-dd"/>
            到
            <input name="end_at" type="text" id="end_at" class="form-control w-auto custom-control-inline" value="<?=$summary->getEndAt()?>" data-com="date" data-icon="none" data-format="yyyy-mm-dd"/>
          </td>
        </tr>
        <?php endif;?>
        <tr>
          <td colspan="2" align="center">
              <?=Button::setType('submit')->setClass('btn-alt-primary btn-loading')->setDatas(['loading-text' => '保存中...'])->setIcon('save')->button('保存资料')?>
            <input name="id" type="hidden" id="id" value="<?=$summary->getSummaryId()?>" /></td>
        </tr>
      </table>
    </form>
  </div>
</div>

<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
    function setPlanSelect(){
        if($("#project_type").val() === '3783' || $("#project_type").val()==='3784' || $("#project_type").val()==='3785'){
            $("#plan_id").removeClass("hidden");
        }else{
            $("#plan_id").addClass("hidden");
        }
    }
    $(document).ready(function(){
        setPlanSelect();
        $("#project_type").change(function () {
            setPlanSelect();
        })
    });
</script>