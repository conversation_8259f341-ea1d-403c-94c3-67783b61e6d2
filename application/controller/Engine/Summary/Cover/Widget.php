<?php
namespace App\Controller\Engine\Summary\Cover;
use Sofast\Core\Sf;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use App\Contract\iWidget;

class Widget implements iWidget
{	
	private $summary = NULL;
	/**
	 * 应用层配置
	 */
	private $configs = array();
	/**
	 * 模块层配置
	 */
	private $widgetConfigs = array();
	/**
	 * 模板对象
	 */
	private $view = NULL;
	
	private $widget_name = 'cover';
    
	function __construct($widget_name,$configs = NULL)
	{
		$this->widget_name = $widget_name;
		$this->widgetConfigs = Configs::getConfigs();
		if($configs !== NULL){
			$this->configs = $configs;
		}else $this->configs = Configs::getDefault();

		$this->view = new Template(realpath(dirname(__FILE__)).'/View/');
		$this->view->set('widget_name',$this->widget_name);
		return $this;
	}

	public function getWidgetName()
    {
        return $this->widget_name;
    }
	
	public function setProject($summary)
	{
		$this->summary = $summary;
		return $this;
	}

	
	public function setConfig($configs=array())
	{
		if(count($configs)) $this->configs = $configs;
		return $this;
	}
	
	public function getConfig()
	{
		return $this->configs;
	}
	
	public function output($tpl='')
	{
        $configs = $this->getConfig();
		$this->view->set("summary",$this->summary);
		$this->view->set("configs",$configs);
		
		return $this->view->getContent("Output");
	}
	
	/**
	 * 模块配置
	 */
	public function manager()
	{
		$this->view->set("configs",$this->configs);
		$this->view->set("widgetConfigs",$this->widgetConfigs);
		return $this->view->getContent("Manager");
	}
	
	/**
	 * 完整性检验
	 * 返回数组
	 */
	function validate()
	{
		$message = array();
		if(!$this->summary->getSubject()) $message[] = "项目名称还未填写";
        $configs = $this->configs;
        if($configs['jsnx'] && !$this->summary->getBuildYear()){
            $message[] = "任务书封面【建设年限】还未填写";
        }
        if($configs['jsnx'] && $this->summary->getLevel()=='国家级'){
            $firstYear = $this->summary->getRadicateYear();
            $thirdYear = $this->summary->getRadicateYear()+2;
            $buildYear = $firstYear.'-'.$thirdYear;
            if($this->summary->getBuildYear()!=$buildYear){
//                $message[] = "标书封面【建设年限】应该为：".$buildYear;
            }
        }
//		if(!$this->summary->getStartAt()) $message[] = "项目开始时间还未填写";
//		if(!$this->summary->getEndAt()) $message[] = "项目结束时间还未填写";

		return $message;	
	}

    function getFileListHtml()
    {
        return '';
    }
	
	function __call($tpl,$args)
	{
		return $this->output($tpl);
	}
	
	function __toString()
	{
		return $this->output();	
	}
}
?>