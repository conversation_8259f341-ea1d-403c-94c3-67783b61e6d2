<?php
namespace App\controller\Engine;
use App\Controller\BaseController;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use App\Facades\PDF;
use App\Lib\Attachment;

class Summaryer extends BaseController
{
    private $configs = array();
    private $modulars = array();
    private $worker = NULL;
    private $view = NULL;
    private $summary = NULL;
    private $declare_year = 2024;   //填报年度

    function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->declare_year = date('Y')-1;
    }


    function index()
    {
        $this->edit();
    }


    function edit()
    {
        $this->summary = sf::getModel("Summarys")->selectBySummaryId(input::getMix('id'));
        if($this->summary->isNew()) $this->page_debug("年度考核报告不存在！",getFromUrl());
        if($this->summary->getPlatformId() != input::session("roleuserid")) $this->page_debug("你没有权限执行该操作！",getFromUrl());
        $this->worker = $this->summary->worker('summary');
        if($this->worker->isNew()) $this->page_debug("参数错误！",getFromUrl());
        //不在编辑状态不能编辑
        if(!in_array($this->summary->getStatement(),array('-1','0','1','3','6','12'))) $this->page_debug("当前状态你没有权限执行该操作！",getFromUrl());
        //标记为编辑模式
        $this->summary->setWidgetConfigs('action',array('edit'=>true,'worker'=>'summaryer'));
        $this->view->set("edit",'yes');
        $this->view->set("summary",$this->summary);
        $this->view->set("configs",$this->worker->getConfigs());
        $this->view->display("Worker/".$this->worker->template());
    }

    function show()
    {
        $this->summary = sf::getModel("Summarys")->selectBySummaryId(input::getMix('id'));
        if($this->summary->isNew()) $this->page_debug("年度考核报告不存在！",getFromUrl());
        if($this->summary->getWorkerId()==0) $this->page_debug("该项目年度考核报告无具体内容信息！",getFromUrl());
        $this->worker = $this->summary->worker('summary');
        $this->summary->setWidgetConfigs('action',array('show'=>true,'worker'=>'summaryer'));
        $this->view->set("summary",$this->summary);
        $this->view->set("configs",$this->worker->getConfigs());
        $this->view->display("Worker/".$this->worker->template());
    }

    function download()
    {
        $this->output();
    }

    /**
     * <AUTHOR>
     * @DateTime  2019-08-11
     * @copyright 重载模块
     * @license   [license]
     * @version   [version]
     * @return    [type]      [description]
     */
    function reloadWidget()
    {
        $this->summary = sf::getModel("Summarys")->selectBySummaryId(input::mix("id"));
        if($this->summary->isNew()) exit('年度考核报告不存在！');
        $this->summary->setWidgetConfigs('action',array('edit'=>'yes','worker'=>'Summaryer'));
        exit($this->summary->widgets(input::mix("widget"),'summary'));
    }

    /**
     * 申报书盖章等页面打印稿生成
     * @return [type] [description]
     */
    function printer()
    {
        @ini_set('memory_limit', '200M');//设置最大使用内存为200M
        $this->summary = sf::getModel("Summarys")->selectBySummaryId(input::mix("id"));
        if($this->summary->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->summary->setWidgetConfigs('action',array('print'=>'yes','worker'=>'Summaryer'));
        $this->worker = $this->summary->worker('summary');
        $this->view->set("print",'yes');
        $this->view->set("summary",$this->summary);
        $this->view->set("configs",$this->summary->getWidgetConfigs());
        $htmlStr = $this->view->getContent("Worker/".$this->worker->template());
        $hash = substr(md5(strip_tags($htmlStr)),0,20);
        //替换一下
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        //$this->summary->setShortUrl($hash,'attachement');
        //提取打印内容
        $type = input::getMix('type');
        $pattern = '/\<widget type\="printer"\>(.*?)\<\/widget\>/s';
        if($type)  $pattern = '/\<widget type\="printer"\ name="'.$type.'">(.*?)\<\/widget\>/s';
        preg_match_all($pattern,$htmlStr,$matches);
        if($matches[1]){
            $htmlStr = implode('<pagebreak></pagebreak>',$matches[1]);
            $htmlStr = '<link rel="stylesheet" href="'.site_path("css/template.css").'">'.$htmlStr;
        }else{
            $htmlStr = stristr($htmlStr,'<widget type="printer">');
            $htmlStr = stristr($htmlStr,'</widget>',true);
            $htmlStr = str_replace('<widget type="printer">','<link rel="stylesheet" href="'.site_path("css/template.css").'">',$htmlStr);
        }

        $pdf =  PDF::reload('zh-CN')->setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="50%" style="border: 0px solid #fff;">年度考核报告</td><td width="50%" style="text-align: right;border: 0px solid #fff;">'.$this->summary->getSubject().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;">- {PAGENO} -</td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setTitle($this->summary->getSubject())
            ->setSubject($this->summary->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->summary->getUserName())
            ->setContent($htmlStr)
            ->hasCover(false)
            ->setWaterMark('年度考核报告',0.1)
            ->show();
    }

    /**
     * 生成月度报告
     */
    function output()
    {
        @ini_set('memory_limit', '200M');//设置最大使用内存为200M
        $this->summary = sf::getModel("Summarys")->selectBySummaryId(input::mix("id"));
        if($this->summary->isNew()) $this->page_debug("年度考核报告不存在！",getFromUrl());
        //已经生成的直接下载
        if($this->summary->enablePrint()){
            if($file_name = $this->summary->getConfigs("file.apply")){//如果已经生成文件，直接打开下载
                $refresh = input::mix("refresh") ? true : false;
                if(!$refresh){//不刷新就直接跳转
                    addHistory($this->summary->getSummaryId(),'下载年度考核报告','summary',1);
                    @header("Location:".site_path('up_files/'.$file_name));exit();
                    exit;
                }
            }
        }

        $this->summary->setWidgetConfigs('action',array('download'=>'yes','worker'=>'summaryer'));

        $this->worker = $this->summary->worker('summary');
        $this->view->set("download",'yes');
        $this->view->set("summary",$this->summary);
        $this->view->set("configs",$this->summary->getWidgetConfigs());
        $htmlStr = $this->view->getContent("Worker/".$this->worker->template());
        $hash = substr(md5(strip_tags($htmlStr)),0,20);
        //替换一下
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        //拆分内容
        $title = '年度考核报告';
        $htmlArray = explode('{{file:',$htmlStr);

        $pdf =  PDF::reload('zh-CN')->setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="50%" style="border: 0px solid #fff;">'.$title.'</td><td width="50%" style="text-align: right;border: 0px solid #fff;">'.$this->summary->getSubject().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;">- {PAGENO} -</td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setTitle($this->summary->getSubject())
            ->setSubject($this->summary->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->summary->getCompanyName())
            ->hasCover(false);
        //设置输出内容
//        if($this->summary->getUserRole()==3){
//            //医院只有正文
//            $content = $htmlArray[1];
//            if(substr($content,0,4) == 'http') $file = strstr($content,'}}',true);
//            else $file = config::get("upload_path").strstr($content,'}}',true);
//            $pdf->setContent($file,'file');
//        }else{
            for($i=0,$n=count($htmlArray);$i<$n;$i++){
                if($i > 0){//分析插入文件
                    //如果是WORD文档将直接传输URL地址
                    if(substr($htmlArray[$i],0,4) == 'http') $file = strstr($htmlArray[$i],'}}',true);
                    else $file = config::get("upload_path").strstr($htmlArray[$i],'}}',true);
                    $pdf->setContent($file,'file');

                    $content = substr(strstr($htmlArray[$i],'}}'),2);
                    $pdf->setContent($content);
                }else {
                    $pdf->setContent($htmlArray[$i]);
                }
            }
//        }


        //保存的文件名
        $savename = time().mt_rand(1000,9999).'.pdf';
        $file_path = date("Y").'/'.date("m").'/'.$savename;
        $dir_path = WEBROOT."/up_files/".dirname($file_path);
        if (!file_exists($dir_path)) { //检查目录是否存在
            if (!@mkdir($dir_path, 0755, true)) {
                exit('不能创建目录: ' . \dirname($file_path));
            }
        }
        if($this->summary->enablePrint('apply')){
            //水印
            $pdf->setWaterMark($title,0.1);
            $pdf->save(WEBROOT."/up_files/".$file_path);

            //记录到项目配置
            $this->summary->setConfigs("file.apply",$file_path);
            @header("Location:".site_path('up_files/'.$file_path));exit();
            exit();
        }else{
            //水印
            $pdf->setWaterMark($title,0.1);

            $pdf->show();
            exit();
        }
    }




}
