<?php
namespace App\Controller\Engine\Cover;
use App\Controller\BaseController;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class edit extends BaseController
{	
	private $project = NULL;
	private $configs = array();
    private $type = 'apply';

	function load()
	{
		$this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
		if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->type =  (input::mix("worker") == 'tasker') ? 'task' : 'apply';
		$this->view = new Template(realpath(dirname(__FILE__)).'/View/');
		$this->configs = $this->project->getWidgetConfigs('cover',$this->type);
		$this->view->set("project",$this->project);
		$this->view->set("configs",$this->configs);
	}
	

	function index()
	{
		if(input::post())
		{
			$this->checkCsrf();
            if(!input::post('subject')) $this->error('请填写项目名称');
            $this->project->setSubject(input::post('subject'));
			$cooperatation = input::post('cooperatation');
            $this->project->setCooperatation($cooperatation['cooperatation_name'],$cooperatation['cooperatation_property']);
            $this->project->setDatas(input::post('data'));
            if(input::post('build_year')) $this->project->setBuildYear(input::post('build_year'));
            //研究领域
            if(input::post("subject_id")){
                $_subject = sf::getModel("Subjects")->selectByCode(input::post("subject_id"),1);
                $this->project->setSubjectId($_subject->getCode());
                $this->project->setSubjectName($_subject->getSubject());
            }
            if(input::post("subject_ids")){
                $subjectId = array_pop (input::post("subject_ids"));
                $this->project->setSubjectIds(input::post("subject_ids"));
                $this->project->setSubjectId($subjectId);
                $_subject = sf::getModel("Subjects")->selectByCode($subjectId,1);
                $this->project->setSubjectName($_subject->getSubject());
            }
            if($this->project->isModify() && strstr($this->project->getSubject(),'专家意见修改版')===false){
                $this->project->setSubject($this->project->getSubject().'（专家意见修改版）');
            }
            $this->project->setUpdatedAt(date('Y-m-d H:i:s'));
            $this->project->save();
            $this->closeWindow();
		}
		
		if(input::session("userlevel") == 2){//兼职单位
			$declarer = sf::getModel("Declarers")->selectByUserId(input::session("roleuserid"));
			if(!$multiples = $declarer->multiples(true)) $multiples = false;

            $declarers = sf::getModel("Declarers")->selectAll("corporation_id = '".$declarer->getCorporationId()."'");
			$this->view->set("multiples",$multiples);
			$this->view->set("declarers",$declarers);
		}
		$this->view->set("project",$this->project);
		$this->view->apply("inc_body","Edit");
		$this->view->display("page_blank");
	}
	
}
?>