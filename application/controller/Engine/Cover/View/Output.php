<div data-widget-id="<?=$widget_name?>">
    <?php
    $reloadWidgets = [];
    if($project->getWidget('basic')!==false) $reloadWidgets[] = 'basic';
    ?>
  <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" style="overflow:wrap; border:none;">
    <?php if($configs['action']['edit'] && $configs['writable']=='write'):?>
    <caption>
    <span style="float:right;">
    <?=Button::setName('编辑')->setUrl(site_url("engine/cover/edit/index/worker/".$configs['action']['worker']."/widget/".$widget_name."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setReloadWidget($reloadWidgets)->setWidgetUrl(site_url("engine/".$configs['action']['worker']."/reloadWidget"))->setIcon('edit')->widget()?>
    </span>
    </caption>
    <?php endif;?>
      <?php if($configs['xmmc']):?>
          <tr>
              <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['xmmc']?:'项目名称'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getSubject()?><?php if($configs['subxmmc']):?><?=$project->getSubtitle()?><?php endif;?>
        </span></td>
          </tr>
      <?php endif;?>
      <?php if($configs['gczxssly']):?>
          <tr>
              <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['gczxssly']?:'工程中心所属领域'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
                <?=$project->getData('gczxssly')?>
        </span></td>
          </tr>
      <?php endif;?>
      <?php if($configs['ptlb']):?>
          <tr>
              <td width="25%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['ptlb']?:'平台类别'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getGuide()->getSubject()?>
        </span></td>
          </tr>
      <?php endif;?>
    <?php if($configs['sysdw']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['sysdw']?:'实验室定位'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11"><?=getCheckedStr(['基础研究','应用基础研究','前沿技术研究'],$project->getData('sysdw'))?></span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['zxmc']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['zxmc']?:'专项名称'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=array_shift(explode('>',$project->getGuideName()))?>
        </span></td>
    </tr>
    <?php endif;?>
      <?php if($configs['sbdw']):?>
          <tr>
              <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['sbdw']?:'申报单位'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getCorporationName()?>
        </span>&nbsp;<span style="float:right">（盖章）</span></td>
          </tr>
      <?php endif;?>
      <?php if($configs['sbdw2']):?>
          <tr>
              <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['sbdw2']?:'申报单位'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getCorporationName()?>
        </span>&nbsp;</td>
          </tr>
      <?php endif;?>
      <?php if ($configs['hzdw']): ?>
            <tr>
              <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['hzdw']?:'联合共建单位'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom">
                <span class="STYLE11">
                  <?= $project->getCooperationStr() ?>
                </span>
              </td>
            </tr>
        <?php endif; ?>
        <?php if($configs['leader']):?>
            <tr>
              <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['leader']?:'中心主任（学科带头人）'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom">
                <span class="STYLE11">
                  <?=$project->getData('leader')?>
                </span>&nbsp;
              </td>
            </tr>
      <?php endif;?>
      <?php if($configs['sbnd']):?>
          <tr>
              <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['sbnd']?:'申报年度'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getDeclareYear()?>
        </span>&nbsp;</td>
          </tr>
      <?php endif;?>
      <?php if($configs['lxnd']):?>
          <tr>
              <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['lxnd']?:'立项年度'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getDeclareYear()?>
        </span>&nbsp;</td>
          </tr>
      <?php endif;?>

    <?php if($configs['xmcpmc']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['xmcpmc']?:'项目（产品）名称'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getSubject()?><?php if($configs['subxmmc']):?><?=$project->getSubtitle()?><?php endif;?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['cpmc']):?>
    <tr>
      <td width="25%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['cpmc']?:'产品名称及型号'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getSubject()?><?php if($configs['subxmmc']):?><?=$project->getSubtitle()?><?php endif;?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['ssly']):?>
        <tr>
           <td width="25%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['ssly']?:'所属领域'?>：</span></th>
          <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11"><?=$project->product(true)->getSsly()?></span></td>
        </tr>
        <?php endif;?>
    <?php if($configs['tdmc']):?>
    <tr>
      <td width="25%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['tdmc']?:'团队名称'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getSubject()?><?php if($configs['subxmmc']):?><?=$project->getSubtitle()?><?php endif;?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['ptmc']):?>
    <tr>
      <td width="25%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['ptmc']?:'平台名称'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getSubject()?><?php if($configs['subxmmc']):?><?=$project->getSubtitle()?><?php endif;?>
        </span></td>
    </tr>
    <?php endif;?>

    <?php if($configs['tddtr']):?>
    <tr>
      <td width="25%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['tddtr']?:'团队带头人'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getUserName()?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['yjxm']):?>
    <tr>
      <td width="25%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['yjxm']?:'研究项目'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getBaseinfo()->getData('research_program')?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['yjly']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['yjly']?:'学科方向'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getSubjectPath(3)?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['xmlb']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['xmlb']?:'项目类别'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getProjectTypeSubject()?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jsly']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['jsly']?:'技术领域'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getSkillDomain()?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if(false && $configs['hyly']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['hyly']?:'行业领域'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=trim(substr($project->getGuideName(),strrpos($project->getGuideName(),'>') + 1))?>
        </span></td>
    </tr>
    <?php endif;?>
      <?php if($configs['frdb']):?>
          <tr>
              <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['frdb']?:'单位法定代表人'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getBaseinfo('apply')->getPrincipal()?:$project->getCorporation()->getPrincipal()?>
        </span>&nbsp;</td>
          </tr>
      <?php endif;?>
      <?php if($configs['xmfzr']):?>
          <tr>
              <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['xmfzr']?:'项目负责人'?>：</span></td>
              <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getUserName()?>
        </span>&nbsp;</td>
          </tr>
      <?php endif;?>
    <?php if($configs['sqjf']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['sqjf']?:'申请经费'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getDeclareMoney()?>万元
        </span>&nbsp;</td>
    </tr>
    <?php endif;?>
    <?php if($configs['dwlxr']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['dwlxr']?:'单位联系人'?>：</span></td>
        <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><?=$project->getLinkmanName()?$project->getLinkmanName().' '.$project->getLinkmanMobile() : $project->getCorporation(true)->getLinkman().' '.$project->getCorporation()->getMobile()?></td>
    </tr>
    <?php endif;?>

    <?php if($configs['sxzj']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['sxzj']?:'项目首席专家'?>：</span></td>
      <td class="def" align="right" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><strong>（签字）</strong></td>
    </tr>
    <?php endif;?>
    <?php if($configs['ktfzr']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['ktfzr']?:'课题负责人'?>：</span></td>
      <td class="def" align="right" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><strong>（签字）</strong></td>
    </tr>
    <?php endif;?>
    <?php if($configs['zdls']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['zdls']?:'指导老师'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><?=$project->researcher()->getInstructor('xm')?></td>
    </tr>
    <?php endif;?>

    <?php if($configs['dwlxrdh']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['dwlxrdh']?:'联系人电话'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><?=$project->getCorporation()->getMobile()?></td>
    </tr>
    <?php endif;?>
    <?php if($configs['szqy']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['szqy']?:'所在区域'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getCorporation(true)->getArea()?>
        </span>&nbsp;</td>
    </tr>
    <?php endif;?>
    <?php if($configs['ssqy']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['ssqy']?:'实施区域'?>：</span></td>
      <td class="def" align="left" width="77%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getBaseinfo()->getData('implementation_area_subject')?>
        </span>&nbsp;</td>
    </tr>
    <?php endif;?>
    <?php if($configs['tjdw']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['tjdw']?:'推荐单位'?>：</span></td>
      <td width="77%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?=$project->getDepartmentName()?>
        </span>&nbsp;<span style="float:right">（盖章）</span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['lxr']):?>
    <tr>
      <td width="25%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['lxr']?:'联系人'?>：</span></td>
      <td width="75%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?= $project->getData('lxr') ?>
        </span>&nbsp;</td>
    </tr>
    <?php endif;?>
    <?php if($configs['lxdh']):?>
    <tr>
      <td width="25%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['lxdh']?:'联系电话'?>：</span></td>
      <td width="75%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?= $project->getData('lxdh') ?>
        </span>&nbsp;</td>
    </tr>
    <?php endif;?>
    <?php if($configs['txdz']):?>
    <tr>
      <td width="25%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['txdz']?:'通讯地址'?>：</span></td>
      <td width="75%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?= $project->getData('txdz') ?>
        </span>&nbsp;</td>
    </tr>
    <?php endif;?>
    <?php if($configs['hzgb']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['hzgb']?:'合作国别/地区'?>：</span></td>
      <td width="77%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?=$project->getBaseinfo()->getData('cooperation_nation')?><?=$project->getBaseinfo()->getData('cooperation_area')?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['gwhzdw']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['gwhzdw']?:'国（境）外合作单位'?>：</span></td>
      <td width="77%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?=$project->getBaseinfo()->getData('foreign_company_name')?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['qzsj']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['qzsj']?:'起止时间'?>：</span></td>
      <td width="77%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?=$project->getStartAt()?>
        至
        <?=$project->getEndAt()?>
        </span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['bsrq']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['bsrq']?:'报送时间'?>：</span></td>
      <td width="77%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?=$project->getDeclareAt("Y 年 m 月 d 日")?></span></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jsnx']):?>
    <tr>
      <td width="23%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8"><?=$configs['langs']['jsnx']?:'建设年限'?>：</span></td>
      <td width="77%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?=$project->getBuildYear()?></span></td>
    </tr>
    <?php endif;?>
  </table>
</div>
