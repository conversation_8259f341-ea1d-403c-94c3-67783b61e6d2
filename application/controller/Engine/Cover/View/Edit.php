<link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
<script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>
<div class="main">
  <div class="btn-group btn-group-sm" role="group">
    <?=btn("back")?>
    <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-alt-primary"><i class="fa fa-save"></i> 保存资料</a>
    <p style="clear:both;"></p>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <caption>
        编辑封面
        </caption>
        <?php if($configs['xmmc']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['xmmc']?:'项目名称'?></th>
          <td width="85%"><input name="subject" type="text" id="subject" value="<?=$project->getSubject()?>" style="99%" size="36" class="required form-control"  /></td>
        </tr>
        <?php endif;?>
        <?php if($configs['gczxssly']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['gczxssly']?:'工程中心所属领域'?></th>
          <td width="85%">
            <select name="data[gczxssly]" id="gczxssly" class="form-control">
                <option value="">=请选择=</option>
                <?=getSelectFromArray([
                  '电子信息','装备制造','食品饮料','先进材料',
                  '能源化工','数字经济','现代农业','生态环境',
                  '生物医药'],$project->getData('gczxssly'))?>
            </select>
          </td>
        </tr>
        <?php endif;?>
      <?php if($configs['sysdw']):?>
          <tr>
              <th width="15%" align="right"><?=$configs['langs']['sysdw']?:'实验室定位'?></th>
              <td width="85%"><?=get_radio(['基础研究','应用基础研究','前沿技术研究'],'data[sysdw]',$project->getData('sysdw'))?></td>
          </tr>
      <?php endif;?>
        <?php if($configs['xmcpmc']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['xmcpmc']?:'项目（产品）名称'?></th>
          <td width="85%"><input name="subject" type="text" id="subject" value="<?=$project->getSubject()?>" style="99%" size="36" class="required" /></td>
        </tr>
        <?php endif;?>
        <?php if($configs['cpmc']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['cpmc']?:'产品名称及型号'?></th>
          <td width="85%"><input name="subject" type="text" id="subject" value="<?=$project->getSubject()?>" style="99%" size="36" class="required" /></td>
        </tr>
        <?php endif;?>
        <?php if($configs['ssly']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['ssly']?:'所属领域'?></th>
          <td width="85%"><?=get_radio(['集成电路与新型显示','新一代网络技术','大数据','软件与信息服务','航空与燃机','智能装备','轨道交通','新能源与智能汽车','医疗健康','新材料','清洁能源','绿色化工','节能环保','新一代人工智能','农产品精深加工','现代农业种业','现代农业装备','现代农业冷链物流'],'ssly',$project->product()->getSsly())?></td>
        </tr>
        <?php endif;?>
        <?php if($configs['tdmc']):?>
          <tr>
              <th width="15%" align="right"><?=$configs['langs']['tdmc']?:'团队名称'?></th>
              <td width="85%"><input name="subject" type="text" id="subject" value="<?=$project->getSubject()?>" style="99%" size="36" class="required" /></td>
          </tr>
        <?php endif;?>
        <?php if($configs['ptmc']):?>
          <tr>
              <th width="15%" align="right"><?=$configs['langs']['ptmc']?:'平台名称'?></th>
              <td width="85%"><input name="subject" type="text" id="subject" value="<?=$project->getSubject()?>" style="99%" size="36" class="required" /></td>
          </tr>
        <?php endif;?>
        <?php if($configs['ssqy']):?>
          <tr>
              <th width="15%" align="right"><?=$configs['langs']['ssqy']?:'实施区域'?></th>
              <td width="85%"><input type="text" name="implementation_area" class="myarea" value="<?=$project->getBaseinfo()->getData('implementation_area')?:'510000'?>" ajaxUrl="<?=site_url("ajax/area")?>" fixProvince="true" /></td>
          </tr>
        <?php endif;?>
        <?php if($configs['yjly']):?>
        <tr>
          <th width="15%" align="right">研究领域</th>
          <td width="85%">
              <div class="cxselect" data-selects="subject_id1,subject_id2,subject_id3" data-url="<?= site_path('json/subjects.json') ?>" data-json-value="v" style="float: left">
                  <select class="form-control w-auto custom-control-inline subject_id1" data-value="<?=$project->getSubjectIds(1)?>" name="subject_ids[subject_id1]"></select>
                  <select class="form-control w-auto custom-control-inline subject_id2" name="subject_ids[subject_id2]" data-first-title="请选择" data-value="<?=$project->getSubjectIds(2)?>"></select>
                  <select class="form-control w-auto custom-control-inline subject_id3" name="subject_ids[subject_id3]" data-first-title="请选择" data-value="<?=$project->getSubjectIds(3)?>"></select>
              </div>
              <div class="clearfix"></div>
              <p>备注：请务必根据实际情况选择到最后一级。</p>
          </td>
        </tr>
        <?php endif;?>
        <?php if($configs['hyly']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['hyly']?:'行业领域'?></th>
          <td width="85%">
              <div class="cxselect" data-selects="industry_id1,industry_id2,industry_id3,industry_id4" data-url="<?= site_path('json/industrys.json') ?>" data-json-value="v" style="float: left">
                  <select class="form-control w-auto custom-control-inline industry_id1" data-value="<?=$project->getIndustryIds(1)?>" name="industry[industry_id1]"></select>
                  <select class="form-control w-auto custom-control-inline industry_id2" name="industry[industry_id2]" data-first-title="请选择" data-value="<?=$project->getIndustryIds(2)?>"></select>
                  <select class="form-control w-auto custom-control-inline industry_id3" name="industry[industry_id3]" data-first-title="请选择" data-value="<?=$project->getIndustryIds(3)?>"></select>
                  <select class="form-control w-auto custom-control-inline industry_id4" name="industry[industry_id4]" data-first-title="请选择" data-value="<?=$project->getIndustryIds(4)?>"></select>
              </div>
          </td>
        </tr>
        <?php endif;?>
      <?php if($configs['xmlb']):?>
          <tr>
              <th width="15%" align="right">项目类别</th>
              <td width="85%">
                  <select name="project_type" id="project_type" class="form-control">
                      <option value="">=请选择=</option>
                      <?=getProjectTypes($project->getProjectType())?>
                  </select>
              </td>
          </tr>
          <tr id="plan_id">
              <th width="15%" align="right">所属计划</th>
              <td width="85%">
                  <select name="plan_id"  class="form-control">
                      <option value="">=请选择=</option>
                      <?=getPlanOption($project->getPlanId())?>
                  </select>
              </td>
          </tr>
      <?php endif;?>
        <?php if($multiples):?>
        <tr>
          <th align="right"><?=$configs['langs']['sbdw']?:'申报单位'?></th>
          <td><select name="company_id" id="company_id" class="form-control">
              <?php while($company = $multiples->getObject()):?>
              <option value="<?=$company->getUserId()?>"<?php if($company->getUserId() == $project->getCorporationId()):?> selected="selected"<?php endif;?>>
              <?=$company->getSubject()?>
              </option>
              <?php endwhile;?>
            </select>
        </tr>
        <?php endif;?>
        <?php if($configs['hzdw']):?>
        <tr>
          <th width="15%" align="right"><?=$configs['langs']['hzdw']?:'合作单位'?></th>
          <td width="85%">
            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <table class="table table-bordered">
                            <tbody>
                            <tr>
                                <td align="center">
                                    单位名称
                                </td>
                                <td align="center">
                                    单位性质
                                </td>
                                <td width="100" align="center">
                                    操作
                                </td>
                            </tr>
                            <?php
                            $others = $project->getCooperatationArray();
                            if(empty($others)){
                                $others[0] = [];
                            }
                            $i=0;
                            foreach($others as $k=>$other):
                                $i++;
                                ?>
                                <tr data-row="<?=$k?>">
                                    <td>
                                        <input class="form-control text-left" type="text" name="cooperatation[cooperatation_name][]" value="<?=$other['subject']?>">
                                    </td>
                                    <td>
                                        <select class="form-control" name="cooperatation[cooperatation_property][]" >
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(get_select_data('property'),$other['property'])?>
                                        </select>
                                    </td>
                                    <td width="100" style="text-align: center">
                                        <?php
                                        if($i==count($others)):
                                            ?>
                                            <button type="button" class="btn btn-primary add-row"><span class="fa fa-plus" aria-hidden="true"></span></button>
                                        <?php
                                        else:
                                            ?>
                                            <button type="button" class="btn btn-danger remove-row"><span class="fa fa-minus" aria-hidden="true"></span></button>
                                        <?php
                                        endif;
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach;?>
                            </tbody>
                        </table>
                    </div>
                    <div class="clearfix"></div>
                </div>
          </td>
        </tr>
        <?php endif;?>
        <?php if($configs['leader']):?>
            <tr>
                <th align="right"><?=$configs['langs']['leader']?:'中心负责人'?></th>
                <td><input name="data[leader]" type="text" value="<?=$project->getData('leader')?>"  class="form-control"  /></td>
            </tr>
        <?php endif;?>
          <?php if($configs['lxr']):?>
              <tr>
                  <th align="right"><?=$configs['langs']['lxr']?:'联系人'?></th>
                  <td><input name="data[lxr]" type="text" value="<?=$project->getData('lxr')?>"  class="form-control"  /></td>
              </tr>
          <?php endif;?>
          <?php if($configs['lxdh']):?>
              <tr>
                  <th align="right"><?=$configs['langs']['lxdh']?:'联系电话'?></th>
                  <td><input name="data[lxdh]" type="text" value="<?=$project->getData('lxdh')?>"  class="form-control"  /></td>
              </tr>
          <?php endif;?>
          <?php if($configs['txdz']):?>
              <tr>
                  <th align="right"><?=$configs['langs']['txdz']?:'通讯地址'?></th>
                  <td><input name="data[txdz]" type="text" value="<?=$project->getData('txdz')?>"  class="form-control"  /></td>
              </tr>
          <?php endif;?>
        <?php if($configs['jsnx']):
            $firstYear = $project->getRadicateYear();
            $secondYear = $project->getRadicateYear()+1;
            $thirdYear = $project->getRadicateYear()+2;
            $fourthYear = $project->getRadicateYear()+3;
            $years = [
                $firstYear.'-'.$firstYear,
                $firstYear.'-'.$secondYear,
                $firstYear.'-'.$thirdYear
            ];
            if($project->getLevel()=='国家级'){
                $years = [
                    $firstYear.'-'.$fourthYear
                ];
            }
        ?>
        <tr>
          <th align="right"><?=$configs['langs']['jsnx']?:'建设年限'?></th>
          <td><select name="build_year" id="build_year" class="form-control">
                  <option value="">请选择</option>
              <?=getSelectFromArray($years,$project->getBuildYear())?>
            </select>
        </tr>
        <?php endif;?>
        <?php if($configs['qzsj']):?>
        <tr>
          <th align="right"><?=$configs['langs']['qzsj']?:'起止时间'?></th>
          <td><input name="start_at" type="text" id="start_at" class="form-control w-auto custom-control-inline" value="<?=$project->getStartAt()?>" data-com="date" data-icon="none" data-format="yyyy-mm-dd"/>
            到
            <input name="end_at" type="text" id="end_at" class="form-control w-auto custom-control-inline" value="<?=$project->getEndAt()?>" data-com="date" data-icon="none" data-format="yyyy-mm-dd"/>
          </td>
        </tr>
        <?php endif;?>
        <tr>
          <td colspan="2" align="center">
              <?=Button::setType('submit')->setClass('btn-alt-primary btn-loading')->setDatas(['loading-text' => '保存中...'])->setIcon('save')->button('保存资料')?>
            <input name="id" type="hidden" id="id" value="<?=$project->getProjectId()?>" /></td>
        </tr>
      </table>
    </form>
  </div>
</div>

<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
    function setPlanSelect(){
        if($("#project_type").val() === '3783' || $("#project_type").val()==='3784' || $("#project_type").val()==='3785'){
            $("#plan_id").removeClass("hidden");
        }else{
            $("#plan_id").addClass("hidden");
        }
    }
    $(document).ready(function(){
        setPlanSelect();
        $("#project_type").change(function () {
            setPlanSelect();
        })
    });


    $(document).on('click','.add-row',function(){
        //克隆行
        var rows = $(this).parents('#activeForm').find('other_corporations');
        var tr = $(this).parents('tr').clone();
        var line = rows.length;
        var row = tr.data('row');
        //替换行id
        var reg = '/\\['+row+'\\]/ig';
        var new_tr = tr.html().replace(eval(reg), '['+line+']');
        new_tr = "<tr data-row='"+line+"'>"+new_tr+"</tr>";
        //追加至表格
        $(this).parents('tbody').append(new_tr);
        //按钮变为减号
        $(this).removeClass('add-row').removeClass('btn-primary').addClass('remove-row').addClass('btn-danger');
        $(this).find('span').removeClass('fa-plus').addClass('fa-minus');
    });

    $(document).on('click','.remove-row',function(){
        $(this).parent().parent().remove();
    });

    $('.cxselect').cxSelect();
</script>