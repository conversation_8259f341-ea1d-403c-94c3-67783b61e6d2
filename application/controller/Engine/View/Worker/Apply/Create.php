<?php
$worker = $project->worker();
$configs = $worker->isNew() ? array() : $worker->getConfigs('cover');
$langs = $configs['langs']?:[];
?>
<div class="content">
    <div class="block block-rounded">
        <div class="block-content">
            <div class="col-xs-12 col-sm-12 col-md-12">
              <div class="btn-group btn-group-sm" role="group">
                <?=btn("back")?>
                <a href="javascript:$('#validateForm').submit();" class="btn btn-alt-primary"><i class="ace-icon fa fa-save"></i> 保存资料</a>
                <p style="clear:both;"></p>
              </div>
              <div class="box">
                <form id="validateForm" name="validateForm" method="post" action="">
                  <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover">
                    <caption>
                    新增平台申请书
                    </caption>
                      <tbody>
                    <tr>
                      <th width="15%" align="right"><?=$langs['xmmc']?:'平台名称'?></th>
                      <td width="85%">
                          <input name="subject" type="text" id="subject" value="<?=$project->getSubject()?>" style="99%" size="60" class="form-control"<?php if($guide->getProjectName()):?> readonly="readonly"<?php endif;?> required />
                      </td>
                    </tr>
                    <tr>
                      <th width="15%" align="right">平台类别</th>
                      <td width="85%"><?=$guide->getSubject()?></td>
                    </tr>
                    <?php if($multiples):?>
                    <tr>
                      <th align="right"><?=$langs['sbdw']?:'依托单位'?></th>
                      <td><select name="company_id" id="company_id" class="form-control" required>
                          <?php while($company = $multiples->getObject()):?>
                          <option value="<?=$company->getUserId()?>"<?php if($company->getUserId() == $project->getCorporationId()):?> selected="selected"<?php endif;?>>
                          <?=$company->getSubject()?>
                          </option>
                          <?php endwhile;?>
                        </select>
                    </tr>
                    <?php endif;?>
                      </tbody>
                      <tfoot>
                    <tr>
                      <td colspan="2" align="center">
                          <?=Button::setType('submit')->setSize('btn-xs')->setClass('btn-alt-primary btn-loading')->setDatas(['loading-text' => '创建中...'])->setIcon('add')->button('创建申报书')?>
                        <input name="id" type="hidden" id="id" value="<?=$project->getProjectId()?>" />
                        <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" /></td>
                    </tr>
                      </tfoot>
                  </table>
                </form>
              </div>
            </div>
        </div>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>