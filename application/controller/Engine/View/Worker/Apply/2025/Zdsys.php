<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title><?=$project->getSubject()?>--申报书</title>
    <meta name="robots" content="noindex, nofollow">
    <link rel="stylesheet" href="<?=site_path("css/template.css")?>?v=2">
    <?php if($download != 'yes'):?>
        <link rel="icon" type="image/png" sizes="192x192" href="<?=site_path('assets/media/favicons/favicon-192x192.png')?>">
        <link rel="apple-touch-icon" sizes="180x180" href="<?=site_path('assets/media/favicons/apple-touch-icon-180x180.png')?>">
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap">
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap">
        <link rel="stylesheet" id="css-main" href="<?=site_path('assets/css/compress.min.css')?>?v=<?=config::get('css.main_version')?>">
        <script src="<?=site_path('assets/js/compress.core.min.js')?>"></script>
        <script src="<?=site_path('assets/js/compress.app.min.js')?>"></script>
        <style>
            .project_review{width: 800px;margin: 100px auto;padding: 0;background: none}
            .project_review .main{margin: 80px auto;padding:60px !important;box-shadow:0 .5rem 2rem #d4dcec;-webkit-transform:translateY(-2px);transform:translateY(-2px);opacity:1}
            .content .block{
                margin: 0;padding: 0
            }
            .content .block .block-content{
                margin-top: 0
            }
            .alert p{
                font-size: .875rem!important;
                margin-bottom: 0.25rem;
            }
            .table td, .table th {
                border-top: 1px solid #000;
            }
        </style>
    <?php endif;?>
    <style>
        .title1{
            font-size:20px; font-weight:bold;font-family:'黑体';margin: 10px 0;
        }
        .title2{
            font-size:18px; font-weight:bold;font-family:'黑体';margin: 10px 0;
        }
        .title3{
            font-size:16px; font-weight:bold;font-family:'黑体';margin: 10px 0;
        }
    </style>
</head>
<body>
<script>
    if (document.addEventListener) {
        window.addEventListener('pageshow', function (event) {
            if (event.persisted || window.performance &&
                window.performance.navigation.type == 2) {
                $("#preloader").hide();
            }
        },false);
    }
</script>
<div id="preloader" style="display: none;">
    <div id="ctn-preloader" class="ctn-preloader">
        <div class="round_spinner">
            <div class="spinner"></div>
            <div class="text">
                <img src="<?=site_url('images/pdf.png')?>" alt="">
                <h4>
                    <span>文档生成中...</span>
                </h4>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="baseUrl" value="<?=site_url('/')?>" />
<div id="page-container" class="page-header-dark main-content-boxed">
    <?php if($download != 'yes'):?>
        <?php include(__DIR__."/../Navbar.php");?>
    <?php endif;?>
    <!-- Main Container -->
    <main id="main-container">
        <!-- Page Content -->
        <div class="content project_review" style="">
            <?php if(\Sofast\Core\router::getMethod()=='read' && \Sofast\Core\input::getMix('type')!='download'):?>
                <div class="alert alert-danger alert-dismissable" role="alert">
                    <h3 class="alert-heading font-size-h3 my-2 text-center">
                        注意
                    </h3>
                    <p class="text-center" style="font-size: 18px !important;">当前为申报书空白模板预览页面，填报需要登录项目负责人账号</p>
                </div>
            <?php endif;?>
            <div class="block block-rounded">
                <div class="block-content main">
                    <bookmark content="封面" data-pid="0"></bookmark>
                    <widget type="printer">
                        <table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def11" style="border:none;">
                            <tr>
                                <td height="5" align="right" width="17%" class="def11" ></td>
                                <td class="def11" align="left" width="21%" style="margin-bottom:0px;margin-top:20px" valign="bottom"></td>
                                <td valign="bottom" class="def11" align="right" width="15%"></td>
                                <td valign="bottom" class="def11" align="left" width="14%"></td>
                                <td valign="bottom" class="def11" align="left" width="15%"><span class="STYLE8">申报编号：</span></td>
                                <td class="def2" align="left" width="22%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><?=$project->getAcceptId()?></td>
                            </tr>
                        </table>
                        <p>&nbsp;</p>
                        <p>&nbsp;</p>
                        <p align="center"><span class="STYLE4"><strong>四川省重点实验室建设申请报告</strong></span></p>
                        <p align="center"><br></p>
                        <p align="center"><br></p>
                        <p align="center"><br></p>
                        <?=$project->widgets('cover')?>
                        <p>&nbsp;</p>
                        <p>&nbsp;</p>
                        <p align="center"><span class="STYLE5">四川省科学技术厅</span></p>
                        <p align="center">&nbsp;</p>
                    </widget>
                    <pagebreak resetpagenum="1"/>
                    <table width="680" align="center" cellpadding="5" cellspacing="0" class="table notes" border="1" style="overflow:wrap">
                        <tr>
                            <td height="40" align="center" ><strong>填写说明</strong></td>
                        </tr>
                        <tr>
                            <td height="800" align="left" valign="top" >
                                <p>&nbsp;</p>
                                <p>1、凡组建四川省重点实验室的单位填写此申报书。</p>
                                <p>2.申报书所列内容须据实填写，表达应明确、完整、严谨、扼要（外文名词要同时用中文表达），保证材料真实性。</p>
                                <p>3.申报书中涉及国家秘密的内容，请按照国家有关保密规定，进行脱密处理后填写。</p>
                                <p>4.申报书所列内容是签订任务书的重要依据。</p>
                                <p>5.若填写内容较多，可另加附页。申报书一律用A4纸正反打印，胶装成册，签章后一式六份报送科技厅。</p>
                                <p>6.所有申报材料恕不退还，请注意存留 。</p>
                            </td>
                        </tr>
                    </table>
                    <pagebreak />
                    <bookmark content="第一部分：基本信息表" data-level="1" data-id="1" data-pid="0"></bookmark>
                    <div class="title1 text-center" style="margin: 0">第一部分：基本信息表</div>
                    <bookmark content="一、实验室信息简表" data-level="2" data-id="11" data-pid="1"></bookmark>
                    <div class="title2" style="margin: 0">一、实验室信息简表</div>
                    <bookmark content="1.基本情况" data-level="2" data-id="101" data-pid="11"></bookmark>
                    <?=$project->widgets('basic')?>
                    <bookmark content="2.近五年承担科研项目数量" data-level="2" data-id="102" data-pid="11"></bookmark>
                    <?=$project->widgets('project')?>
                    <bookmark content="3.近五年研究成果" data-level="2" data-id="103" data-pid="11"></bookmark>
                    <?=$project->widgets('fruit')?>
                    <bookmark content="4.现有学术队伍" data-level="2" data-id="104" data-pid="11"></bookmark>
                    <?=$project->widgets('member')?>
                    <?=$project->widgets('team')?>
                    <bookmark content="5.学术交流" data-level="2" data-id="105" data-pid="11"></bookmark>
                    <?=$project->widgets('meet')?>
                    <pagebreak />
                    <bookmark content="二、依托单位信息简表" data-level="2" data-id="12" data-pid="11"></bookmark>
                    <div class="title2" style="margin: 0">二、依托单位信息简表</div>
                    <?=$project->widgets('company')?>
                    <?php
                    if($download != 'yes'):
                        ?>
                        <pagebreak />
                        <bookmark content="第二部分 实验室组建方案" data-level="1" data-id="2" data-pid="0"></bookmark>
                        <div class="title1" style="margin: 0">第二部分 实验室组建方案</div>
                    <?php endif;?>
                    <?=$project->widgets('content')?>
                    <?php if($download != 'yes'):?>
                        <pagebreak></pagebreak>
                        <bookmark content="相关附件" data-level="1" data-id="3" data-pid="0"></bookmark>
                        <?=$project->widgets('attachement')?>
                    <?php endif;?>
                    <?php if($download != 'yes'):?>
                        <pagebreak></pagebreak>
                        <p class="text-center text-gray">----------- END -----------</p>
                    <?php endif;?>
                    <div class="clearfix"></div>
                </div>
            </div>

        </div><!-- END Page Content -->
    </main><!-- END Main Container -->

</div><!-- END Page Container -->
<?php if($download != 'yes'):?>
    <?php include(__DIR__."/../PageNav.php");?>
    <script src="<?=site_path('js/layer/layer.js') ?>"></script>
    <script src="<?=site_path('js/func.js') ?>?v=<?=config::get('js.func_version')?>"></script>
    <script src="<?=site_path('js/common.js') ?>?v=<?=config::get('js.common_version')?>"></script>
    <script type="text/javascript">
        $(function(){
            $('pagebreak').before('<div class="pagebreak"></div>');
        });
    </script>
<?php endif;?>

</body>
</html>