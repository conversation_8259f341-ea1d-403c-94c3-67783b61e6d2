<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title><?=$project->getSubject()?>--申报书</title>
    <meta name="robots" content="noindex, nofollow">
    <link rel="stylesheet" href="<?=site_path("css/template.css")?>?v=2">
    <?php if($download != 'yes'):?>
        <link rel="icon" type="image/png" sizes="192x192" href="<?=site_path('assets/media/favicons/favicon-192x192.png')?>">
        <link rel="apple-touch-icon" sizes="180x180" href="<?=site_path('assets/media/favicons/apple-touch-icon-180x180.png')?>">
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap">
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap">
        <link rel="stylesheet" id="css-main" href="<?=site_path('assets/css/compress.min.css')?>?v=<?=config::get('css.main_version')?>">
        <script src="<?=site_path('assets/js/compress.core.min.js')?>"></script>
        <script src="<?=site_path('assets/js/compress.app.min.js')?>"></script>
        <style>
            .project_review{width: 800px;margin: 100px auto;padding: 0;background: none}
            .project_review .main{margin: 80px auto;padding:60px !important;box-shadow:0 .5rem 2rem #d4dcec;-webkit-transform:translateY(-2px);transform:translateY(-2px);opacity:1}
            .content .block{
                margin: 0;padding: 0
            }
            .content .block .block-content{
                margin-top: 0
            }
            .alert p{
                font-size: .875rem!important;
                margin-bottom: 0.25rem;
            }
            .table td, .table th {
                border-top: 1px solid #000;
            }
        </style>
    <?php endif;?>
    <style>
        .title1{
            font-size:20px; font-weight:bold;font-family:'黑体';margin: 10px 0;
        }
        .title2{
            font-size:18px; font-weight:bold;font-family:'黑体';margin: 10px 0;
        }
        .title3{
            font-size:16px; font-weight:bold;font-family:'黑体';margin: 10px 0;
        }
    </style>
</head>
<body>
<script>
    if (document.addEventListener) {
        window.addEventListener('pageshow', function (event) {
            if (event.persisted || window.performance &&
                window.performance.navigation.type == 2) {
                $("#preloader").hide();
            }
        },false);
    }
</script>
<div id="preloader" style="display: none;">
    <div id="ctn-preloader" class="ctn-preloader">
        <div class="round_spinner">
            <div class="spinner"></div>
            <div class="text">
                <img src="<?=site_url('images/pdf.png')?>" alt="">
                <h4>
                    <span>文档生成中...</span>
                </h4>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="baseUrl" value="<?=site_url('/')?>" />
<div id="page-container" class="page-header-dark main-content-boxed">
    <?php if($download != 'yes'):?>
        <?php include(__DIR__."/../Navbar.php");?>
    <?php endif;?>
    <!-- Main Container -->
    <main id="main-container">
        <!-- Page Content -->
        <div class="content project_review" style="">
            <?php if(\Sofast\Core\router::getMethod()=='read' && \Sofast\Core\input::getMix('type')!='download'):?>
                <div class="alert alert-danger alert-dismissable" role="alert">
                    <h3 class="alert-heading font-size-h3 my-2 text-center">
                        注意
                    </h3>
                    <p class="text-center" style="font-size: 18px !important;">当前为申报书空白模板预览页面，填报需要登录项目负责人账号</p>
                </div>
            <?php endif;?>
            <div class="block block-rounded">
                <div class="block-content main">
                    <bookmark content="封面" data-pid="0"></bookmark>
                    <widget type="printer">
                        <table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def11" style="border:none;">
                            <tr>
                                <td height="5" align="right" width="17%" class="def11" ></td>
                                <td class="def11" align="left" width="21%" style="margin-bottom:0px;margin-top:20px" valign="bottom"></td>
                                <td valign="bottom" class="def11" align="right" width="15%"></td>
                                <td valign="bottom" class="def11" align="left" width="14%"></td>
                                <td valign="bottom" class="def11" align="left" width="15%"><span class="STYLE8">申报编号：</span></td>
                                <td class="def2" align="left" width="22%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><?=$project->getAcceptId()?></td>
                            </tr>
                        </table>
                        <p>&nbsp;</p>
                        <p>&nbsp;</p>
                        <p align="center"><span class="STYLE4"><strong>四川省工程技术研究中心认定申请书</strong></span></p>
                        <p align="center"><br></p>
                        <p align="center"><br></p>
                        <p align="center"><br></p>
                        <?=$project->widgets('cover')?>
                        <p>&nbsp;</p>
                        <p>&nbsp;</p>
                        <p align="center"><span class="STYLE5">四川省科学技术厅</span></p>
                        <p align="center">&nbsp;</p>
                    </widget>
                    <pagebreak resetpagenum="1"/>
                    <table width="680" align="center" cellpadding="5" cellspacing="0" class="table notes" border="1" style="overflow:wrap">
                        <tr>
                            <td height="40" align="center" ><strong>说明</strong></td>
                        </tr>
                        <tr>
                            <td height="800" align="left" valign="top" >
                                <h5>一、内容及目的</h5>
                                <p>本认定申请书包括两部分内容：工程中心认定申请书及相关证明及附件材料。为四川省工程技术研究中心组建的基础材料，是四川省工程技术研究中心组建后验收的重要依据之一。</p>
                                <h5>二、填写要求</h5>
                                <p>1.指定专人填写，工程中心确认所填写内容准确无误后，在本申请书中承诺书上签字盖章，否则本申请无效。</p>
                                <p>2.填表用语简洁明了，数据翔实准确。</p>
                                <p>3.表内栏目不得空缺，如果某项栏目内容没有，请填无。</p>
                                <p>4.各种数据的统计截止日期为2022年12月31日。</p>
                                <p>5.各表格中的内容如果不够地方填写，可以扩充或加页。</p>
                                <p>6.按照“相关证明及附件材料清单”准备所需附件，如有缺漏，认定申请书中相关数据视为无效。</p>
                                <p>7.认定申请书中填写的数据需为工程中心依托单位真实数据。</p>
                                <h5>三、报送日期及材料须知</h5>
                                <p>认定申请书请于2023年10月31日18:00前填报完毕，并在“四川省工程技术研究中心管理平台”上报；待评审结果公示以后提交纸质材料一份，报送至高新处。</p>
                            </td>
                        </tr>
                    </table>
                    <pagebreak />
                        <div class="div_table">
                            <p class="div_table_title" style="text-align: center;font-size: 20px"><b>承      诺      书</b></p>
                            <p style="font-size:18px;line-height:35px; text-indent:2em;">经核实，本认定申请书中所填数据和情况描述准确无误，填报单位承诺对所填写的各种数据和情况描述的真实性负责。</p>

                            <p style="font-size:18px;line-height:35px; text-indent:2em;">&nbsp;</p>
                            <p style="font-size:18px;line-height:35px; text-indent:2em;">&nbsp;</p>
                            <p style="font-size:18px;line-height:30px; text-indent:2em;">&nbsp;</p>
                            <p style="font-size:18px;line-height:30px; text-indent:2em;">中心主任（签字）:</p>
                            <p style="font-size:18px;line-height:35px; text-indent:2em;text-align: right">日   期：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
                            <p style="font-size:18px;line-height:30px; text-indent:2em;">&nbsp;</p>
                            <p style="font-size:18px;line-height:30px; text-indent:2em;">中心依托单位负责人（签字）：</p>
                            <p style="font-size:18px;line-height:35px; text-indent:2em;text-align: right">日   期：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
                            <p style="font-size:18px;line-height:30px; text-indent:2em;">&nbsp;</p>
                            <p style="font-size:18px;line-height:30px; text-indent:2em;">中心依托单位（盖章）：</p>
                            <p style="font-size:18px;line-height:30px; text-indent:2em;">&nbsp;</p>
                        </div>
                    <pagebreak />
                    <?=$project->widgets('basic')?>
                    <?=$project->widgets('transform')?>
                    <?php if($download != 'yes'):?>
                        <pagebreak></pagebreak>
                        <bookmark content="相关附件" data-level="1" data-id="3" data-pid="0"></bookmark>
                        <?=$project->widgets('attachement')?>
                    <?php endif;?>
                    <?php if($download != 'yes'):?>
                        <pagebreak></pagebreak>
                        <p class="text-center text-gray">----------- END -----------</p>
                    <?php endif;?>
                    <div class="clearfix"></div>
                </div>
            </div>

        </div><!-- END Page Content -->
    </main><!-- END Main Container -->

</div><!-- END Page Container -->
<?php if($download != 'yes'):?>
    <?php include(__DIR__."/../PageNav.php");?>
    <script src="<?=site_path('js/layer/layer.js') ?>"></script>
    <script src="<?=site_path('js/func.js') ?>?v=<?=config::get('js.func_version')?>"></script>
    <script src="<?=site_path('js/common.js') ?>?v=<?=config::get('js.common_version')?>"></script>
    <script type="text/javascript">
        $(function(){
            $('pagebreak').before('<div class="pagebreak"></div>');
        });
    </script>
<?php endif;?>

</body>
</html>