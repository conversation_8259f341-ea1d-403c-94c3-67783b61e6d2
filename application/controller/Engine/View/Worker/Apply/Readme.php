<div class="content">
    <div class="block block-rounded">
        <div class="block-content">
            <div class="col-xs-12 col-sm-12 col-md-12">
              <div class="alert alert-danger" role="alert" style="margin-top:10px;display:none">
                <h2>温馨提示:</h2>
                <p style="text-indent:2em; font-size:18px;">为了提高您在线填写的速度和减少网络拥堵，请您先将您要申报的申报材料的WORD版下载到本地，在本地WORD里将申报书相关内容填写好后，再选一个网速度较快、网络畅通（建议是下班时间、晚上或周末）的环境，将您在WORD里填写的内容集中复制到网络中相应的栏目里。若有任何疑问和问题，请致电下方技术支持电话。</p>
                <p style="font-size:18px;">该指南需要填写的申报书为：
                  <?=$type->getSubject()?>。<a href="<?=site_path("up_files/".$type->getProjectFile())?>" class="btn btn-sm btn-info" style="padding:5px;"><i class="glyphicon glyphicon-download-alt"></i>点击下载离线提纲</a>
                </p>
              </div>
              <?php
              $content = $guide->getContent();
              if(empty($content)) $content = $guide->getParent()->getContent();
              if($content):?>
              <div class="alert alert-success" role="alert">
                <h2>申报说明</h2>
                <div>
                  <?=str_replace("color:rgb(255,0,0);","color:rgb(255,0,0);display:none;",$content)?>
                </div>
              </div>
              <?php endif;?>
                <form id="validateForm" name="validateForm" method="post" action="<?=site_url('engine/worker/create')?>">
                    <input name="guide_id" type="hidden" value="<?=$guide->getId()?>" />
                    <!--p style="font-size:18px;">填写申报书之前，请认真阅读以上信息！</p-->
                    <p style="font-size:18px;">
                        <?php if($type->getIsShow()):?>
                            <?=Button::setUrl(site_url("engine/worker/edit/guideid/".$guide->getGuideid()))->setEvent('btnLoading(this)')->setIcon('edit')->setSize('btn-lg')->link('开始填写')?>
                        <?php else:?>
                            <a href="#" class="btn btn-lg btn-danger">在线申报将陆续开放，请耐心等待... ...</a>
                        <?php endif;?>
                    </p>
                </form>
            </div>
        </div>
    </div>
</div>
<script>
    function btnLoading(me)
    {
        $(me).attr("disabled","disabled");
        $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1.3rem;height: 1.3rem"></span> 正在加载...');
        validateForm.submit();
    }
</script>
