<header id="page-header" style="position: fixed;top: 0;z-index: 999;">
    <!-- Header Content -->
    <div class="content-header">
        <!-- Left Section -->
        <div class="d-flex align-items-center">
            <a class="font-w600 text-dual tracking-wide" href="#"><?=$project->getCatSubject()?>申报书</a>
        </div>
        <!-- END Left Section -->
        <!-- Right Section -->
        <div>
            <div class="pull-right">
                <?php
                    if(in_array(input::session('userlevel'),[9,10])):
                ?>
                        <a href="<?=site_url("engine/worker/output/id/".$project->getProjectId())?>" onclick="loadPdf()" class="btn btn-sm btn-dual ml-2" role="button"><i class="fa fa-file-pdf"></i> 查看申报书完整版</a>
                <?php else:?>
                    <a href="<?=site_url("manager/index")?>" class="btn btn-sm btn-dual ml-2" role="button"><i class="fa fa-home"></i> 返回主页</a>
                    <?php if(!$project->isRead()):?>
                    <?php if(!$project->enablePrint('apply')):?>
                        <a href="<?=site_url("engine/worker/output/id/".$project->getProjectId())?>" onclick="loadPdf()" class="btn btn-sm btn-dual ml-2" role="button"><i class="fa fa-file-pdf"></i> 预览申报书PDF版</a>
                        <?php
                            if($project->worker()->getConfigs('show_print_stamp')==1):
                        ?>
                        <a href="<?=site_url("engine/worker/printer/id/".$project->getProjectId())?>" onclick="loadPdf()" class="btn btn-sm btn-dual ml-2" role="button"><i class="fa fa-print"></i> 打印审查意见</a>
                        <?php endif;?>
                    <?php else:?>
                        <a href="<?=site_url("engine/worker/output/id/".$project->getProjectId())?>" onclick="loadPdf()" class="btn btn-sm btn-dual ml-2" role="button"><i class="fa fa-file-pdf"></i> 下载PDF打印</a>
                        <a href="<?=site_url("engine/worker/output/refresh/yes/id/".$project->getProjectId())?>" onclick="loadPdf()" class="btn btn-sm btn-dual ml-2" role="button"><i class="si si-refresh"></i> 重新生成PDF</a>
                    <?php endif;?>
                        <?php
                            if(input::session('userlevel')== 3 && $project->getStatement()==2):
                        ?>
                        <?=Button::setName('推荐')->setUrl(site_url("unit/project/doSubmitOnlyOne/id/".$project->getProjectId()))->setClass('ml-2 btn-alt-success')->setIcon('check')->setWidth('800px')->setHeight('600px')->window()?>
                        <?=Button::setName('退回')->setUrl(site_url("unit/project/doback/id/".$project->getProjectId()))->setClass('ml-2 btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                        <?php endif;?>

                        <?php
                        if(input::session('userlevel')== 4 && $project->getStatement()==5):
                            ?>
                            <?=Button::setName('推荐')->setUrl(site_url("gather/project/doSubmitOnlyOne/id/".$project->getProjectId()))->setClass('ml-2 btn-alt-success')->setIcon('check')->setWidth('800px')->setHeight('600px')->window()?>
                            <?=Button::setName('退回')->setUrl(site_url("gather/project/doRejected/id/".$project->getProjectId()))->setClass('ml-2 btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                        <?php endif;?>

                        <?php
                        if(input::session('userlevel')== 6 && $project->getStatement()==9):
                            ?>
                            <?=Button::setName('预受理')->setUrl(site_url("office/accept/doPass/id/".$project->getProjectId()))->setClass('ml-2 btn-alt-success')->setIcon('fa fa-check-square')->setWidth('800px')->setHeight('600px')->window()?>

                            <?=Button::setName('预退回')->setUrl(site_url("office/accept/doNeedBack/id/".$project->getProjectId()))->setClass('btn-alt-danger')->setIcon('fa fa-times-circle')->setWidth('800px')->setHeight('600px')->window()?>
                            <?=Button::setName('受理')->setUrl(site_url("office/accept/doAcceptOnlyOne/id/".$project->getProjectId()))->setClass('ml-2 btn-alt-success')->setIcon('check')->setWidth('800px')->setHeight('600px')->window()?>
                            <?=Button::setName('退回')->setUrl(site_url("office/accept/doRejectedOnlyOne/id/".$project->getProjectId()))->setClass('ml-2 btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>

                        <?php endif;?>

                    <?php endif;?>
                    <?php if(false && $project->isRead()):?>
                            <a href="<?=site_url("engine/worker/read/type/download/code/".$project->getSubjectCode())?>" onclick="loadPdf()" class="btn btn-sm btn-dual ml-2" role="button"><i class="fa fa-file-pdf"></i> 下载申报书模板</a>
                    <?php endif;?>
                    <?php if(\Sofast\Core\router::getMethod()!='read' && $project->enableWrite()):?>
                        <a href="<?=site_url("user/project/doSubmit/id/".$project->getProjectId())?>" class="btn btn-sm btn-dual ml-2" role="button"><i class="fa fa-paper-plane"></i> 上报</a>
                    <?php endif;?>
                <?php endif;?>
            </div>
        </div><!-- END Right Section -->
    </div><!-- END Header Content -->
    <!-- Header Loader -->
    <!-- Please check out the Loaders page under Components category to see examples of showing/hiding it -->
    <div id="page-header-loader" class="overlay-header bg-white-90">
        <div class="content-header">
            <div class="w-100 text-center">
                <i class="fa fa-fw fa-2x fa-spinner fa-spin text-primary"></i>
            </div>
        </div>
    </div><!-- END Header Loader -->
</header>