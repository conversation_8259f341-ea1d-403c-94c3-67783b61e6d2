<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?=$project->getSubject()?></title>
    <link rel="stylesheet" id="css-main" href="<?=site_path('assets/css/compress.min.css')?>?v=<?=config::get('css.main_version')?>">
    <link rel="stylesheet" id="css-main" href="<?=site_path('assets/css/preview.css')?>?v=<?=config::get('css.main_version')?>">
    <style>
        i.fa{
            width: 24px;height: 24px;font-size: 19px;text-align: center;padding-top: 3px;
        }
        div#preloader{
            top: 67px;
        }
    </style>
</head>
<body>
<header id="header">
    <div class="logo">
        <a href="javascript:;" rel="home"><strong><?=$project->getSubject()?></strong></a>
    </div>
    <div class="navigate">
        <ul>
            <li><a href="<?=site_url('manager/index')?>" title="返回主页"><i class="fa fa-home"></i></a></li>
            <li><a class="download" href="<?=site_url('engine/worker/download/id/'.$project->getProjectId())?>"><i class="fa fa-download"></i> <span>下载</span></a></li>
            <li><a href="<?=site_url('engine/worker/show/id/'.$project->getProjectId())?>" target="_top"><i class="fa fa-times"></i></a></li>
        </ul>
    </div>
</header>

<div id="preview" style="background-color:#fff">
    <iframe onreadystatechange=stateChangeIE(this) onload=stateChangeFirefox(this) style="visibility: hidden;" id="preview-frame" class="preview-desktop" src="<?=site_url('engine/worker/output/id/'.$project->getProjectId())?>" frameborder="0"></iframe>
    <div id="preloader">
        <div id="ctn-preloader" class="ctn-preloader">
            <div class="round_spinner">
                <div class="spinner"></div>
                <div class="text">
                    <img src="<?=site_url('images/pdf.png')?>" alt="">
                    <h4>
                        <span>文档生成中...</span>
                    </h4>
                </div>
            </div>
        </div>
    </div>
</div>
</body>

<script>
    $('#output').qrcode({ width: 150, height: 150, text: window.location.href });
    function stateChangeIE(_frame) {
        if (_frame.readyState == "interactive") {
            var loader = document.getElementById("preloader");
            loader.innerHTML = "";
            loader.style.display = "none";
            _frame.style.visibility = "visible";
        }
    }
    function stateChangeFirefox(_frame) {
        var loader = document.getElementById("preloader");
        var f = document.getElementById("preview-frame");
        //f.height = "512";
        loader.innerHTML = "";
        loader.style.display = "none";
        _frame.style.visibility = "visible";
    }

</script>
</html>