<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                执行器部署
            </h3>
            <div class="block-options">
                <?=btn("back")?>
                <a href="<?=site_url("engine/manager/edit")?>" class="btn btn-sm btn-info" role="button"><i class="glyphicon glyphicon-plus"></i>新增</a>
            </div>

        </div>
        <div class="block-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="search">
              <form id="form1" name="form1" method="post" action="">
                <label>关键词
                  <input type="text" name="search" id="search" />
                </label>
                <label>
                  <input name="field" type="radio" id="radio" value="subject" checked="checked" />
                  名称
                </label>
                <select name="type">
               <?=getSelectFromArray(array('Apply'=>'申报书','Summary'=>'年度考核报告','Complete'=>'验收报告','Stage'=>'中期报告','Inspect'=>'绩效评价报告'),input::mix("type"),false)?>
                </select>
                  <select name="cat_id" id="cat_id">
                      <option value="">所属大类</option>
                      <?=getSelectFromArray(get_select_data('cat'),'',false)?>
                  </select>
            <input type="submit" name="button" id="button" value="提交" />
              </form>
            </div>
            <div class="box">
            <table cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
                <tr>
                  <th width="30"><input name="selectAll" id="selectAll" type="checkbox" /></th>
                  <th><?=getColumnStr('类型','type')?></th>
                  <th><?=getColumnStr('名称','subject')?></th>
                  <th><?=getColumnStr('专科代码','subject_code')?></th>
                  <th><?=getColumnStr('解析器','parser')?></th>
                  <th><?=getColumnStr('模板','templates')?></th>
                  <th><?=getColumnStr('大类','cat_id')?></th>
                  <th><?=getColumnStr('时间','updated_at')?></th>
                  <th width="180">操作</th>
                </tr>
                <?php while($worker = $pager->getObject()):?>
                <tr>
                  <td align="center"><input name="select_id[]"  type="checkbox" value="<?=$worker->getId()?>" /></td>
                  <td><?=$worker->getTypeName()?></td>
                  <td><?=$worker->getSubject()?></td>
                  <td><?=$worker->getSubjectCode()?></td>
                  <td><?=$worker->getParser()?></td>
                  <td><?=$worker->getTemplates()?></td>
                  <td><?=$worker->getCatSubject()?></td>
                  <td><?=$worker->getUpdatedAt("Y/m/d")?></td>
                  <td align="center"><?=btn("link","编辑",site_url("engine/manager/edit/id/".$worker->getId()),"edit")?> <?=btn("window","复制到",site_url("engine/manager/copyto/id/".$worker->getId()),"doit")?></td>
                </tr>
                <tr>
                  <td colspan="9" align="left" style=" color:#0099FF;">说明：<?=$worker->getNote()?></td>
                </tr>
                <?php endwhile; ?>
                <tr>
                  <td colspan="9" align="right"> <span class="pager_bar">
                    <?=$pager->fromto().$pager->navbar(10)?>
                    </span>
                   </td>
                </tr>
            </table>
            </div>
            </div>
        </div>
    </div>
        </div>
    </div>
</div>