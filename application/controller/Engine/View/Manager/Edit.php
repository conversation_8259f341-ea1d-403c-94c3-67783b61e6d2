<script type="text/javascript" src="<?=site_url('assets/js/Sortable.min.js')?>"></script>
<style>
    .handle {
        cursor: move !important;
        font-weight: 900;
        line-height: 35px;
    }
    .drag-hover{
        background-color:#f9f5d1 !important;
        border:1px dashed #919191;
        margin-bottom:15px
    }
</style>
<div class="content">
    <!-- Block Tabs -->
    <h2 class="content-heading">
        执行器编辑
    </h2>
    <div class="row">
        <div class="col-lg-12">
            <div class="col-xs-12 col-sm-12 col-md-12">
              <div class="btn-group btn-group-sm" role="group"><?=btn("back")?> <a href="javascript:$('#validateForm').submit();" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存</a> <p style="clear:both;"></p></div>
              <div class="box">
                <form id="validateForm" name="validateForm" method="post" action="">
                <table width="100%" cellpadding="3" cellspacing="1" class="table table-hover table-striped">
                  <caption>执行器基本信息</caption>
                    <tr>
                      <th align="right">名称</th>
                      <td width="35%"><input name="subject" type="text" id="subject" value="<?=$worker->getSubject()?>" class="form-control required" /></td>
                      <th width="15%">类型</th>
                      <td width="35%"><select class="form-control" name="type"><?=getSelectFromArray(array('Apply'=>'申报书','Summary'=>'年度考核报告','Complete'=>'验收报告','Stage'=>'中期报告','Inspect'=>'绩效评价报告','Other'=>'其他'),$worker->getType(),false)?></select></td>
                    </tr>
                    <tr>
                        <th>解析器</th>
                        <td>
                            <input class="form-control" name="parser" type="text" id="parser" value="<?=$worker->getParser()?:'worker'?>"  /></td>
                        <th align="right">离线提纲</th>
                        <td>
                            <input class="form-control w-auto custom-control-inline" name="outline" type="text" id="outline" value="<?=$worker->getOutline()?>" readonly="readonly" />
                            <input type="button" name="button2" class="btn btn-alt-dark" value="上传" onclick="return showWindow('上传','<?=site_url("common/webupload")?>',{area:['350px','250px'],maxmin:false,end:function(){var uploaddata = getCache('uploaddata');$('#outline').val(uploaddata[0].path);}})" />
                    </tr>
                    <tr>
                      <th align="right">使用模板</th>
                      <td  width="35%"><input class="form-control" name="templates" type="text" id="templates" value="<?=$worker->getTemplates()?>" style="text-transform:capitalize;" /></td>
                    <th width="15%">平台类别</th>
                    <td width="35%"><select class="form-control" name="cat_id" id="cat_id">
                            <option value="">请选择</option>
                            <?=getSelectFromArray(get_select_data('cat'),$worker->getCatId(),false)?>
                        </select></td>
                    </tr>
                    <tr>

                    </td>
                    </tr>
                    <tr>
                      <th align="right">填写说明</th>
                      <td colspan="3"><textarea class="form-control" name="note" cols="60" rows="5" style="width:99%;"><?=$worker->getNote()?></textarea></td>
                    </tr>
                </table>
                <table width="100%" cellpadding="3" cellspacing="1" class="table table-hover table-striped">
                    <caption>基本配置</caption>
                    <tr>
                        <th width="15%" align="right">显示打印审查意见按钮</th>
                        <td width="35%">
                            <select name="configs[show_print_stamp]" class="form-control">
                                <?=getSelectFromArray(array(2=>'否',1=>'是'),$worker->getConfigs('show_print_stamp'),false)?>
                            </select>
                        </td>
                        <th width="15%" align="right">&nbsp;</th>
                        <td width="35%">
                            &nbsp;
                        </td>
                    </tr>
                </table>
                <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
                  <caption>部件及配置 <?=btn("window","新增部件",site_url("engine/manager/addwidget/workerid/".$worker->getId()),'set')?>&nbsp;&nbsp;<?=btn("window","导入部件",site_url("engine/manager/import/id/".$worker->getId()),'doit')?></caption>
                    <thead>
                    <tr>
                      <th style="width: 80px">排序</th>
                      <th style="width: 90px">序号</th>
                      <th width="10%">引用标记</th>
                      <th width="10%">类名</th>
                      <th width="20%" align="right">部件名称</th>
                      <th >部件信息</th>
                      <th class="text-center" style="width: 200px">配置</th>
                    </tr>
                    </thead>
                    <tbody id="widget-list" class="widget-list-group">
                    <?php $widgets = $worker->widgets();?>
                    <?php while($widget = $widgets->getObject()):?>
                    <tr>
                        <td class="text-center">
                            <i class="fa fa-arrows-alt handle"></i>
                        </td>
                      <td><input class="form-control" type="number" name="orders[<?=$widget->getId()?>]" min="1" value="<?=$widget->getOrders()?>"></td>
                      <td><?=$widget->getWidgetName()?></td>
                      <td><?=$widget->getClassName()?></td>
                      <td><?=$widget->getSubject()?></td>
                      <td><?=$widget->getNote()?></td>
                      <td align="center">
                          <?=btn("window","编辑",site_url("engine/manager/addwidget/workerid/".$worker->getId()."/id/".$widget->getId()),'edit')?>
                          <?=btn("window","配置",site_url("engine/manager/config/id/".$widget->getId()),'set')?>
                           <?=Button::setName('删除')->setUrl(site_url("engine/manager/delwidget/workerid/".$worker->getId()."/id/".$widget->getId()))->setClass('btn-alt-danger')->delete()?>
                      </td>
                    </tr>
                    <?php endwhile;?>
                    </tbody>
                </table>
                <div class="ex_tools"><button type="submit" class="btn btn-alt-primary"><i class="fa fa-save mr-1"></i> 保存</button>
                        <input name="id" type="hidden" id="id" value="<?=$worker->getId()?>" />
                        <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" /></div>
                </form>
              </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="<?=site_path('js/jquery.cookie.min.js')?>"></script>
<script>
    var filetable = document.getElementById('widget-list');
    var sortable = new Sortable(filetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $(".widget-list-group tr").each(function (){
                $(this).find("input[name^=orders]").val(++index);
            })
        },

    });
</script>