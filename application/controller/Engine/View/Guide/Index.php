<div class="content">
    <div class="block block-rounded" >
        <div class="block-header block-header-default">
            <h3 class="block-title">
                平台申报
            </h3>
        </div>
        <div class="block-content">
            <table class="js-table-sections table table-hover table-vcenter">
                <thead>
                <tr>
                    <th>
                        序号
                    </th>
                    <th>
                        类别
                    </th>
                    <th style="width: 15%;">
                        状态
                    </th>
                    <th class="d-none d-sm-table-cell" style="width: 25%;">
                        申报周期
                    </th>
                    <th class="d-none d-sm-table-cell" style="width: 30%;">
                        主管处室
                    </th>
                    <th class="d-none d-sm-table-cell" style="width: 10%;">
                        操作
                    </th>
                </tr>
                </thead>
                <tbody>
                <?php $i=0;while($guide = $pager->getObject()):$i++;
                $url = $guide->getChildCount()>0 ? site_url('engine/guide/child_list/pid/'.$guide->getId()) : site_url('engine/worker/index/guideid/'.$guide->getGuideId());
                ?>
                <tr>
                    <td class="font-w600 text-center">
                        <?=$i?>
                    </td>
                    <td class="font-w600">
                        <div class="py-1">
                            <a href="<?=$url?>"><?=$guide->getSubject()?></a>
                        </div>
                    </td>
                    <td>
                        <?=$guide->getState()?>
                    </td>
                    <td class="d-none d-sm-table-cell">
                        <span class="text-muted"><?=$guide->getGuideInfo()?></span>
                    </td>
                    <td class="d-none d-sm-table-cell">
                        <span class="text-muted"><?=$guide->getOfficeName()?></span>
                    </td>
                    <td class="d-none d-sm-table-cell">
                        <?php
                            if(strtotime($guide->getEndAt()) < time()):
                        ?>
                            <button class="btn btn-alt-danger btn-sm" disabled>停止申报</button>
                        <?php elseif(strtotime($guide->getStartAt()) > time()):?>
                            <button class="btn btn-alt-warning btn-sm" disabled>还未开启</button>
                        <?php else:?>
                        <?=Button::setName('填报')->setUrl($url)->setClass('btn-alt-success')->link()?>
                        <?php endif;?>
                    </td>
                </tr>
                <?php endwhile; ?>
                <?php if($pager->getTotal()==0): ?>
                <tr>
                    <td colspan="5" align="center">
                        当前没有可以申报的项目
                    </td>
                </tr>
                <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div><!-- END Table Sections -->
</div><!-- END Page Content -->
<script>
    jQuery(function () { Dashmix.helpers(['table-tools-checkable', 'table-tools-sections']); });
</script>