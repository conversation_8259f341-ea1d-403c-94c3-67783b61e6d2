<?php

namespace App\Controller\Engine\Gczx\Basic;

class Configs
{
    private static $default = array();
    private static $configs = array(
        'basic' => array(
            'name' => '基本情况',
            'value' => true,
            'childs' => array(
                "allow_edit" => array('name' => '允许修改', 'value' => true),
                "subject" => array('name' => '中心名称', 'value' => true),
                "sbdw" => array('name' => '依托单位名称', 'value' => true),
                "corporation_property" => array('name' => '依托单位性质', 'value' => true),
                "corporation_staff_number" => array('name' => '依托单位职工总数', 'value' => true),
                "independent_legal_status" => array('name' => '工程中心是否有独立法人资格', 'value' => true),
                "gczxssly" => array('name' => '中心所在技术领域', 'value' => true),
                //中心主任信息
                "leader"=> array('name' => '中心主任姓名', 'value' => true),
                "leader_position"=> array('name' => '中心主任职务', 'value' => true),
                "leader_mobile"=> array('name' => '中心主任手机', 'value' => true),
                "leader_phone"=> array('name' => '中心主任座机', 'value' => true),
                //中心联系人信息
                "lxr"=> array('name' => '中心联系人姓名', 'value' => true),
                "lxr_position"=> array('name' => '中心联系人职务', 'value' => true),
                "lxr_mobile"=> array('name' => '中心联系人手机', 'value' => true),
                "lxr_phone"=> array('name' => '中心联系人座机', 'value' => true),

                "txdz"=> array('name' => '中心通讯地址', 'value' => true),

                "exception"=> array('name' => '是否发生重大安全、重大质量事故和严重环境违法、科研严重失信行为，是否被列入经营异常名录和严重违法失信名单', 'value' => true),
                //其他研发机构建设情况
                "city"=>array('name' => '其他研发机构建设情况-市工程技术研究中心', 'value' => true),
                "city_time"=>array('name' => '其他研发机构建设情况-市工程技术研究中心批复时间', 'value' => true),
                "lab"=>array('name' => '其他研发机构建设情况-省重点实验室', 'value' => true),
                "lab_time"=>array('name' => '其他研发机构建设情况-省重点实验室批复时间', 'value' => true),
                "province"=>array('name' => '其他研发机构建设情况-省级企业技术中心', 'value' => true),
                "province_time"=>array('name' => '其他研发机构建设情况-省级企业技术中心批复时间', 'value' => true),
                //联合共建单位
                'hzdw'=>array('name'=>'联合共建单位','value' => true),
            )
        )
    );

    public static function getConfigs($key = '')
    {
        if ($key) return self::$configs[$key];
        else return self::$configs;
    }

    public static function getDefault($_configs = array(), $default = array())
    {
        if (count($_configs) == 0) $_configs = self::getConfigs();
        foreach ($_configs as $_key => $_val) {
            $data = array();
            if (count($_val['childs'])) $default[$_key] = self::getDefault($_val['childs']);
            else $default[$_key] = $_val['value'];
        }
        return $default;
    }

    public static function setConfigs() {}
}
