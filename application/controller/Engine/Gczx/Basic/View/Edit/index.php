<link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
<script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>
<div class="block">
    <div class="block-header">
        <h3 class="block-title">
            基本情况表
        </h3>
    </div>
    <div class="block-content">
        <form class="form-horizontal" name="validateForm" id="activeForm" action="" method="post">
            <input type="hidden" name="id" value="<?=$project->getProjectId()?>">
            <?php if($configs['basic']['subject']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">中心名称</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="subject" value="<?=$project->getSubject()?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($multiples):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">依托单位名称</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <select name="company_id" id="company_id" class="form-control">
                            <?php while($company = $multiples->getObject()):?>
                            <option value="<?=$company->getUserId()?>"<?php if($company->getUserId() == $project->getCorporationId()):?> selected="selected"<?php endif;?>>
                            <?=$company->getSubject()?>
                            </option>
                            <?php endwhile;?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['corporation_property']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">依托单位性质</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <select class="form-control" name="data[corporation_property]" id="corporation_property" required>
                            <option value="">请选择</option>
                            <?=getSelectFromArray(['企业（含转制科研院所）','科研院所','高等学校','其他'],$project->getData('corporation_property'))?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['corporation_staff_number']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">依托单位职工总数</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[corporation_staff_number]" value="<?=$project->getData('corporation_staff_number')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['independent_legal_status']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">工程中心是否有独立法人资格</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <select class="form-control" name="data[independent_legal_status]" id="independent_legal_status" required>
                            <option value="">请选择</option>
                            <?=getSelectFromArray(['是','否'],$project->getData('independent_legal_status'))?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['gczxssly']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">工程中心是否有独立法人资格</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <select name="data[gczxssly]" id="gczxssly" class="form-control">
                            <option value="">=请选择=</option>
                            <?=getSelectFromArray([
                            '电子信息','装备制造','食品饮料','先进材料',
                            '能源化工','数字经济','现代农业','生态环境',
                            '生物医药'],$project->getData('gczxssly'))?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['leader']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">中心主任姓名</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[leader]" value="<?=$project->getData('leader')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['leader_position']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">中心主任职务</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[leader_position]" value="<?=$project->getData('leader_position')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['leader_mobile']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">中心主任手机</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[leader_mobile]" value="<?=$project->getData('leader_mobile')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['leader_phone']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">中心主任座机</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[leader_phone]" value="<?=$project->getData('leader_phone')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['lxr']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">联系人姓名</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[lxr]" value="<?=$project->getData('lxr')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['lxr_position']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">联系人职务</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[lxr_position]" value="<?=$project->getData('lxr_position')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['lxr_mobile']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">联系人手机</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[lxr_mobile]" value="<?=$project->getData('lxr_mobile')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['lxr_phone']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">联系人座机</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[lxr_phone]" value="<?=$project->getData('lxr_phone')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['txdz']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">通讯地址</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[txdz]" value="<?=$project->getData('txdz')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['exception']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">是否发生重大安全、重大质量事故和严重环境违法、科研严重失信行为，是否被列入经营异常名录和严重违法失信名单</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <select name="data[exception]" id="exception" class="form-control">
                            <option value="">=请选择=</option>
                            <?=getSelectFromArray(['是','否'],$project->getData('exception'))?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['city']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">其他研发机构建设情况-市工程技术研究中心</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <select name="data[city]" id="city" class="form-control">
                            <option value="">=请选择=</option>
                            <?=getSelectFromArray(['是','否'],$project->getData('city'))?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['city_time']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">其他研发机构建设情况-市工程技术研究中心批复时间</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" data-com="date" name="data[city_time]" value="<?=$project->getData('city_time')?>">
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['lab']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">其他研发机构建设情况-省重点实验室</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <select name="data[lab]" id="lab" class="form-control">
                            <option value="">=请选择=</option>
                            <?=getSelectFromArray(['是','否'],$project->getData('lab'))?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['lab_time']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">其他研发机构建设情况省重点实验室批复时间</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" data-com="date" name="data[lab_time]" value="<?=$project->getData('lab_time')?>">
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['province']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">其他研发机构建设情况-省级企业技术中心</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <select name="data[province]" id="province" class="form-control">
                            <option value="">=请选择=</option>
                            <?=getSelectFromArray(['是','否'],$project->getData('province'))?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['province_time']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">其他研发机构建设情况-省级企业技术中心批复时间</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" data-com="date" name="data[province_time]" value="<?=$project->getData('province_time')?>">
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['hzdw']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-3 text-right">联合共建单位</label>
                    <div class="col-9">
                        <table width="100%">
                            <tr>
                                <td width="100%">
                                    <table class="table table-bordered">
                                        <tbody>
                                        <tr>
                                            <td align="center">
                                                单位名称
                                            </td>
                                            <td align="center">
                                                单位性质
                                            </td>
                                            <td width="100" align="center">
                                                操作
                                            </td>
                                        </tr>
                                        <?php
                                        $others = $project->getCooperatationArray();
                                        if(empty($others)){
                                            $others[0] = [];
                                        }
                                        $i=0;
                                        foreach($others as $k=>$other):
                                            $i++;
                                            ?>
                                            <tr data-row="<?=$k?>">
                                                <td>
                                                    <input class="form-control text-left" type="text" name="cooperatation[cooperatation_name][]" value="<?=$other['subject']?>">
                                                </td>
                                                <td>
                                                    <select class="form-control" name="cooperatation[cooperatation_property][]" >
                                                        <option value="">请选择</option>
                                                        <?=getSelectFromArray(get_select_data('property'),$other['property'])?>
                                                    </select>
                                                </td>
                                                <td width="100" style="text-align: center">
                                                    <?php
                                                    if($i==count($others)):
                                                        ?>
                                                        <button type="button" class="btn btn-primary add-row"><span class="fa fa-plus" aria-hidden="true"></span></button>
                                                    <?php
                                                    else:
                                                        ?>
                                                        <button type="button" class="btn btn-danger remove-row"><span class="fa fa-minus" aria-hidden="true"></span></button>
                                                    <?php
                                                    endif;
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endforeach;?>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="clearfix"></div>
                </div>
                
            <?php endif;?>
            <div class="clearfix"></div>
            <div class="ex_tools" style="width:100px;margin:0 auto;margin-bottom: 30px">
                <?=Button::setType('submit')->setClass('btn-alt-primary btn-loading')->setDatas(['loading-text' => '保存中...'])->setIcon('save')->button('保存资料')?>
            </div>
        </form>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script type="text/javascript">
    $(document).on('click','.add-row',function(){
        //克隆行
        var rows = $(this).parents('#activeForm').find('other_corporations');
        var tr = $(this).parents('tr').clone();
        var line = rows.length;
        var row = tr.data('row');
        //替换行id
        var reg = '/\\['+row+'\\]/ig';
        var new_tr = tr.html().replace(eval(reg), '['+line+']');
        new_tr = "<tr data-row='"+line+"'>"+new_tr+"</tr>";
        //追加至表格
        $(this).parent().parent().parent().append(new_tr);
        //按钮变为减号
        $(this).removeClass('add-row').removeClass('btn-primary').addClass('remove-row').addClass('btn-danger');
        $(this).find('span').removeClass('fa-plus').addClass('fa-minus');
    });

    $(document).on('click','.remove-row',function(){
         $(this).parent().parent().remove();
    });

    $('.cxselect').cxSelect();


</script>

