<div data-widget-id="<?=$widget_name?>">
    <p align="center" style="font-size: 18px"><strong>一、依托单位、共建单位及工程中心基本情况</strong></p>
    <table width="680" align="center" cellpadding="5" cellspacing="0" class="table" border="0" style="overflow:wrap">
        <tbody>
            <tr>
                <td style="width: 33.3%;border: none;">表1-1</td>
                <td colspan="2" style="border: none;" class="text-center">
                    基本情况表 
                    <?php if ($configs['action']['edit']): ?>
                        <?=Button::setName('编辑')->setUrl(site_url("engine/Gczx/basic/edit/index/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setReloadWidget(['cover'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
                    <?php endif; ?>
                </td>
            </tr>
        </tbody>
    </table>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <?php if ($configs['basic']['subject']): ?>
            <tr>
                <td width="120" height="40" align="center"><b>中心名称</b></td>
                <td colspan="3"><?= $project->getSubject() ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['sbdw']): ?>
            <tr>
                <td width="120" height="40" align="center"><b>依托单位名称</b></td>
                <td colspan="3"><?=$project->getCorporationName() ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['corporation_property']): ?>
            <tr>
                <td height="40" align="center"><b>依托单位性质</b></td>
                <td colspan="3"><?= getCheckedStr(['企业（含转制科研院所）','科研院所','高等学校','其他'],$project->getData('corporation_property')) ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['corporation_staff_number']): ?>
            <tr>
                <td width="120" height="40" align="center"><b>依托单位职工总数</b></td>
                <td colspan="3"><?= $project->getData('corporation_staff_number') ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['independent_legal_status']): ?>
            <tr>
                <td colspan="2" height="40" align="center"><b>工程中心是否有独立法人资格</b></td>
                <td colspan="2"><?= getCheckedStr(['是','否'],$project->getData('independent_legal_status')) ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['gczxssly']): ?>
            <tr>
                <td width="120" height="40" align="center"><b>中心所在技术领域</b></td>
                <td colspan="3"><?= getCheckedStr(['电子信息','装备制造','食品饮料','先进材料',
                            '能源化工','数字经济','现代农业','生态环境',
                            '生物医药'],$project->getData('gczxssly')) ?></td>
            </tr>
        <?php endif; ?>
        <tr>
            <td height="40" align="center" colspan="4"><b>中心主任</b></td>
        </tr>
        <tr>
            <td width="120" height="40" align="center">姓名</td>
            <td height="40" align="center"><?= $project->getData('leader') ?></td>
            <td width="120" height="40" align="center">职务</td>
            <td height="40" align="center"><?= $project->getData('leader_position') ?></td>
        </tr>
        <tr>
            <td width="120" height="40" align="center">手机</td>
            <td height="40" align="center"><?= $project->getData('leader_mobile') ?></td>
            <td width="120" height="40" align="center">座机</td>
            <td height="40" align="center"><?= $project->getData('leader_phone') ?></td>
        </tr>
        <tr>
            <td height="40" align="center" colspan="4"><b>中心联系人</b></td>
        </tr>
        <tr>
            <td width="120" height="40" align="center">姓名</td>
            <td height="40" align="center"><?= $project->getData('lxr') ?></td>
            <td width="120" height="40" align="center">职务</td>
            <td height="40" align="center"><?= $project->getData('lxr_position') ?></td>
        </tr>
        <tr>
            <td width="120" height="40" align="center">手机</td>
            <td height="40" align="center"><?= $project->getData('lxr_mobile') ?></td>
            <td width="120" height="40" align="center">座机</td>
            <td height="40" align="center"><?= $project->getData('lxr_phone') ?></td>
        </tr>

        <?php if ($configs['basic']['gczxssly']): ?>
            <tr>
                <td width="120" height="40" align="center"><b>中心通讯地址</b></td>
                <td colspan="3"><?=$project->getData('txdz') ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['exception']): ?>
            <tr>
                <td width="240" height="40" align="center"><b>是否发生重大安全、重大质量事故和严重环境违法、科研严重失信行为，是否被列入经营异常名录和严重违法失信名单</b></td>
                <td colspan="3"><?= getCheckedStr(['是','否'],$project->getData('exception')) ?></td>
            </tr>
        <?php endif; ?>
    </table>

    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <tr>
            <td rowspan="4" align="center" style="vertical-align: middle !important;width: 150px;">
                <b>其它研发机构建设情况</b>
            </td>
            <td colspan="3">
                <p align="center">市工程技术研究中心</p>
            </td>
            <td colspan="4">
               <?=getCheckedStr(['是','否'],$project->getData('city')) ?>
               <?php if($project->getData('city')=='是'):?>
               <br>批复时间：<?=$project->getData('city_time')?>    
               <?php endif;?>    
            </td>
        </tr>
        <tr>
            <td colspan="3">
                <p align="center">省重点实验室</p>
            </td>
            <td colspan="4">
               <?=getCheckedStr(['是','否'],$project->getData('lab')) ?>
               <?php if($project->getData('lab')=='是'):?>
               <br>批复时间：<?=$project->getData('lab_time')?>    
               <?php endif;?>    
            </td>
        </tr>
        <tr> 
            <td colspan="3">
                <p align="center">省级企业技术中心</p>
            </td>
            <td colspan="4">
               <?=getCheckedStr(['是','否'],$project->getData('province')) ?>
               <?php if($project->getData('province')=='是'):?>
               <br>批复时间：<?=$project->getData('province_time')?>    
               <?php endif;?>    
            </td>
        </tr>
    </table>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
       <tbody>
            <tr>
                <td colspan="4" align="center" style="vertical-align: middle !important;width: 150px;">
                    <b>联合共建单位</b>
                </td>
            </tr>
            <tr>
                <td align="center">
                    序号
                </td>
                <td align="center">
                    单位名称
                </td>
                <td align="center">
                    单位性质
                </td>
            </tr>
            <?php
            $others = $project->getCooperatationArray();
            if(empty($others)){
                $others[0] = [];
            }
            foreach($others as $k=>$other):?>
                <tr >
                    <td><?=$k?></td>
                    <td>
                        <?=$other['subject']?>
                    </td>
                    <td>
                        <?=$other['property']?>
                    </td>
                </tr>
            <?php endforeach;?>
            </tbody>        
    </table>
</div>