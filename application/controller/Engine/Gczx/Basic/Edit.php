<?php
namespace App\Controller\Engine\Gczx\Basic;
use App\Controller\BaseController;
use Sofast\Core\lang;
use Sofast\Core\router;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Sf;

class edit extends BaseController
{
    private $project = NULL;
    private $configs = array();
    private $type = 'apply';

    function load()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        switch(trim(input::mix("engine")))
        {
            case 'tasker':
                $this->type = 'task';
                break;
            case 'completer':
                $this->type = 'complete';
                break;
            default:
                $this->type = 'apply';
                break;
        }
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->project->getWidgetConfigs('basic',$this->type));
    }

    function index()
    {
        if(input::post()){
            $this->project->setSubject(input::post('subject'));
            $cooperatation = input::post('cooperatation');
            $this->project->setCooperatation($cooperatation['cooperatation_name'],$cooperatation['cooperatation_property']);
            if(input::post("subject_ids")){
                $subjectId = array_pop (input::post("subject_ids"));
                $this->project->setSubjectIds(input::post("subject_ids"));
                $this->project->setSubjectId($subjectId);
                $_subject = sf::getModel("Subjects")->selectByCode($subjectId,1);
                $this->project->setSubjectName($_subject->getSubject());
            }
            $this->project->setDatas(input::post('data'));
            $this->project->setTexts(input::post('text'));
            $this->project->setUpdatedAt(date('Y-m-d H:i:s'));
            $this->project->save();
            $this->closeWindow();
        }
        if(input::session("userlevel") == 2){//兼职单位
			$declarer = sf::getModel("Declarers")->selectByUserId(input::session("roleuserid"));
			if(!$multiples = $declarer->multiples(true)) $multiples = false;

            $declarers = sf::getModel("Declarers")->selectAll("corporation_id = '".$declarer->getCorporationId()."'");
			$this->view->set("multiples",$multiples);
			$this->view->set("declarers",$declarers);
		}
        $this->view->set('project',$this->project);
        $this->view->set('type',$this->type);
        $this->view->apply("inc_body", "edit/index");
        $this->view->display("page_blank");
    }

    function update()
    {
        copyCompanyInfo2Project($this->project,$this->type);
        $this->success('更新成功！','javascript:parent.closeWindow();');
    }
}