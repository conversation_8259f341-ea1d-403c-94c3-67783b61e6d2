<?php
namespace App\Controller\Engine;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
use Sofast\Core\Config;
use Sofast\Support\Template;

class Guide extends BaseController
{	
	private $view = NULL;
	
	function load()
	{
		$this->view = new Template(realpath(dirname(__FILE__)).'/View/');
	}
	
	function index()
	{
	    $user = sf::getModel('Declarers')->selectByUserId(input::session('roleuserid'));
		$message = $years = array();
        if(config::get('current_declare_year')==date('Y')){
            $addWhere = "`year` = '".date('Y')."' and type = 'apply'";
        }else{
            $addWhere = "`year` IN ('".date('Y')."','".config::get('current_declare_year')."') and type = 'apply'";
        }

        //除了测试人员，其余只能申报在开启时间段的指南
		if (!isTester()) {
		    $addWhere .= " AND (unix_timestamp(start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') AND unix_timestamp(end_at) > unix_timestamp('".date("Y-m-d H:i:s")."')";
			$addWhere .= " OR id in (select parent_id from guides where id in (select guide_id from guide_exceptions where unix_timestamp(start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') AND unix_timestamp(end_at) > unix_timestamp('".date("Y-m-d H:i:s")."') AND company_name = '".$user->getCorporationName()."') group by parent_id))";
		}      


//		if($user->getSubjectCode()){
//            $addWhere.=" and mark = '".$user->getSubjectCode()."'";
//        }
		//统计显示年度
		$db = sf::getLib("Db");
		$query = $db->query("SELECT `year`,count(*) as num FROM guides WHERE  (user_level = 2 OR user_level = 0 ) and is_show = 1 and rgt - lft = 1 and $addWhere group by `year` ORDER BY year DESC");
		while($row = $db->fetch_array($query)){
			$years[$row['year']] = 	$row['num'];
		}
		//当前年度
		if(input::mix('year')) $year = input::mix('year');
		else{
			if(count($years)){
				$_years = array_keys($years);
				$year = $_years[0];
			}else $year = config::get("current_declare_year");
		}

		$this->view->set("pager",sf::getModel("Guides",0,$year)->selectAll("level = 2 and is_show = 1 and {$addWhere} and (user_level = '".input::session("userlevel")."' OR user_level = 0 ) ","ORDER BY `lft` ASC"));

		switch(input::session("userlevel"))
		{
			case 2:
				$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
				$message = $user->validate();
				if($user->getIsLock()) $message[] = "请完成账号实名认证";
			break;
			case 3:
				$user = sf::getModel("Corporations")->selectByUserId(input::getInput("session.roleuserid"));
//				$message = $user->validate();
//				if($user->getIsLock()) $message[] = "请完成单位账号实名认证";
			break;
			default:
			break;
		}
		$this->view->set("message",$message);
		$this->view->set("year",$year);
		$this->view->set("years",$years);
		$this->view->set("lastyear",config::get("current_declare_year"));
		$this->view->apply("inc_body","Guide/Index");
		$this->view->display("page_main");
	}

	function child_list()
	{
        $guidePid = (int)input::get('pid');
        $parentGuide = sf::getModel('Guides',$guidePid);
        if($parentGuide->isNew()){
            $this->error('没有找到该申报类型');
        }
	    $user = sf::getModel('Declarers')->selectByUserId(input::session('roleuserid'));
		$message = $years = array();
        $addWhere = " and type = 'apply'";
		if (!isTester()) {
		    // 如果不是测试账号，添加时间限制
		    $addWhere .= " AND (unix_timestamp(start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') AND unix_timestamp(end_at) > unix_timestamp('".date("Y-m-d H:i:s")."')";
			$addWhere .= " OR id in (select guide_id from guide_exceptions where unix_timestamp(start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') AND unix_timestamp(end_at) > unix_timestamp('".date("Y-m-d H:i:s")."') AND company_name = '".$user->getCorporationName()."'))";			
		}
		 

//		if($user->getSubjectCode()){
//            $addWhere.=" and mark = '".$user->getSubjectCode()."'";
//        }
		//统计显示年度
		$db = sf::getLib("Db");
		$query = $db->query("SELECT `year`,count(*) as num FROM guides WHERE  (user_level = 2 OR user_level = 0 ) and is_show = 1 and rgt - lft = 1 $addWhere group by `year` ORDER BY year DESC");
		while($row = $db->fetch_array($query)){
			$years[$row['year']] = 	$row['num'];
		}
		//当前年度
		if(input::mix('year')) $year = input::mix('year');
		else{
			if(count($years)){
				$_years = array_keys($years);
				$year = $_years[0];
			}else $year = config::get("current_declare_year");
		}

		$this->view->set("pager",sf::getModel("Guides",0,$year)->selectAll("parent_id = {$guidePid} and  is_show = 1 $addWhere and (user_level = '".input::session("userlevel")."' OR user_level = 0 ) ","ORDER BY `lft` ASC"));
		switch(input::session("userlevel"))
		{
			case 2:
				$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
				$message = $user->validate();
				if($user->getIsLock()) $message[] = "请完成账号实名认证";
			break;
			case 3:
				$user = sf::getModel("Corporations")->selectByUserId(input::getInput("session.roleuserid"));
//				$message = $user->validate();
//				if($user->getIsLock()) $message[] = "请完成单位账号实名认证";
			break;
			default:
			break;
		}
		$this->view->set("parentGuide",$parentGuide);
		$this->view->set("user",$user);
		$this->view->set("message",$message);
		$this->view->set("year",$year);
		$this->view->set("years",$years);
		$this->view->set("lastyear",config::get("current_declare_year"));
		$this->view->apply("inc_body","Guide/child_list");
		$this->view->display("page_main");
	}

	function read_list()
	{
		$message = $years = array();
        $addWhere = "  and parent_id = 0";
        if($_SESSION['login_from']){
            $this->page_debug('请登录项目负责人账号查看');
        }

//		if(!isTester()) $addWhere .= " AND unix_timestamp(start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') and unix_timestamp(end_at) > unix_timestamp('".date("Y-m-d H:i:s")."') ";
		//统计显示年度
		$db = sf::getLib("Db");
		$query = $db->query("SELECT `year`,count(*) as num FROM guides WHERE (user_level = 2 OR user_level = 0 ) and is_show = 1 and rgt - lft = 1 $addWhere group by `year` ORDER BY year DESC");

		while($row = $db->fetch_array($query)){
			$years[$row['year']] = 	$row['num'];
		}
		//当前年度
		if(input::mix('year')) $year = input::mix('year');
		else{
			if(count($years)){
				$_years = array_keys($years);
				$year = $_years[0];
			}else $year = config::get("current_declare_year");
		}

		$this->view->set("pager",sf::getModel("Guides",0,$year)->selectAll("level = 1 and is_show = 1 {$addWhere}","ORDER BY `lft` ASC"));

		$this->view->set("message",$message);
		$this->view->set("year",$year);
		$this->view->set("years",$years);
		$this->view->set("lastyear",config::get("current_declare_year"));
		$this->view->apply("inc_body","Guide/read_list");
		$this->view->display("page_main");
	}


    /**
     * 集团公司科研项目引导页面
     */
    function company()
    {
        $guides = sf::getModel("Guides")->selectAll("level = 2 and parent_id = 2 and (user_level = '".input::session("userlevel")."' OR user_level = 0 ) ","ORDER BY `lft` ASC");
        $this->view->set("guides",$guides);
        $this->view->apply("inc_body","Guide/Company");
        $this->view->display("page_main");
    }

	/**
	 * 获取子类
	 * @return [type] [description]
	 */
	function children(){
		$guide = sf::getModel("Guides")->selectByGuideId(input::mix('guideid'));
		$this->view->set("pager",$guide->getSiblings(input::session("userlevel"),true));//只列举有效指南
		exit($this->view->getContent("Guide/Children"));
	}

	/**
	 * 指南内容
	 * @return [type] [description]
	 */
	function content()
	{		
		$guide = sf::getModel("Guides")->selectByGuideId(input::mix('guideid'));
		$this->view->set('guide',$guide);
		$this->view->apply('inc_body','Guide/Content');
		$this->view->display('page_blank');
	}
	
}