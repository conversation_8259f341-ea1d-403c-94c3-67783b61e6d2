<?php
namespace App\Controller\Engine;
use App\Controller\BaseController;
use App\Facades\DCS;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use App\Facades\PDF;
use App\Lib\Attachment;

class Worker extends BaseController
{
    private $configs = array();
    private $modulars = array();
    private $worker = NULL;
    private $view = NULL;

    function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
    }

    /**
     * 展示填写说明内容
     * @return [type] [description]
     */
    function readme()
    {
        $guide = sf::getModel("Guides")->selectByGuideId(input::mix("guideid"));
        if($guide->isNew()) $this->page_debug("指南不存在！",getFromUrl());
        $type = $guide->types();
        if($type->isNew()) $this->page_debug("申报书还未准备好！",getFromUrl());
        if($msg = $this->check($guide)){
            $this->view->set("msg",$msg);
            $this->view->set("type",$type);
            $this->view->set("guide",$guide);
            $this->view->apply("inc_body","Worker/Apply/Error");
            $this->view->display("page_main");
        }
        $this->view->set("guide",$guide);
        $this->view->set("type",$type);
        $this->view->apply("inc_body","Worker/Apply/Readme");
        $this->view->display("page_main");
    }

    /**
     * 根据项目的实际情况分发到新建还是编辑
     * @return [type] [description]
     */
    function index()
    {
        if(input::mix("guideid") && input::mix('guideid')!='B6B9041D-804C-A1B8-4EEF-46E46D85E96C') {
            $guide = sf::getModel("Guides")->selectByGuideId(input::mix("guideid"));
            if($guide->isNew()) $this->page_debug("指南不存在！",getFromUrl());
            if(strtotime($guide->getEndAt()) < time()) $this->error("项目已经停止申报！",getFromUrl());
            //申报书类型
            $type = $guide->types();
            if($type->isNew()) $this->page_debug("申报书不存在！",getFromUrl());
            $_SESSION['worker']['type_id'] = $type->getId();
            $_SESSION['worker']['guide_id'] = $guide->getId();
            $_SESSION['worker']['guideid'] = $guide->getGuideid();
            $_SESSION['worker']['worker_id'] = $type->getWorkerId();
            $this->readme();
        }
        else $this->edit();
    }

    /**
     * 项目编辑页面
     * @return [type] [description]
     */
    function edit()
    {
        if(input::mix("id") && input::mix("id") != input::session("worker.project_id")){//从项目中取得参数
            $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
            if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
            $guide = $this->project->getGuide(true);
            if($guide->isNew()) $this->page_debug("指南丢失！",getFromUrl());
            //关闭时间不能编辑项目
            //申报时间验证
            if(!$this->project->time_validate()){
                $this->error("申报通道已关闭！",getFromUrl());
            }

            $type = $this->project->getType(true);
            if($type->isNew()) $this->page_debug("申报书丢失！",getFromUrl());

            $_SESSION['worker']['project_id'] = $this->project->getProjectId();
            $_SESSION['worker']['guide_id'] = $guide->getId();
            $_SESSION['worker']['guideid'] = $guide->getGuideid();
            $_SESSION['worker']['worker_id'] = $type->getWorkerId();
        }else if(input::mix("guideid") && input::session("worker.guide_id") != input::mix("guideid")){
            $guide = sf::getModel("Guides")->selectByGuideId(input::mix("guideid"));
            if($guide->isNew()) $this->page_debug("指南不存在！",getFromUrl());
            //申报时间验证
            if (strtotime($guide->getEndAt()) < time() && ! isTester()) {
                $this->page_debug("申报通道已关闭！", getFromUrl());
            }
            //申报书类型
            $type = $guide->types();
            if($type->isNew()) $this->page_debug("申报书不存在！",getFromUrl());
            $_SESSION['worker']['type_id'] = $type->getId();
            $_SESSION['worker']['guide_id'] = $guide->getId();
            $_SESSION['worker']['guideid'] = $guide->getGuideid();
            $_SESSION['worker']['worker_id'] = $type->getWorkerId();
            $projectPath = $type->getProjectPath();
            //查询未填写完成的项目
            $pager = sf::getModel("Projects")->selectAll("user_id='".input::session("roleuserid")."' and guide_id = '".$guide->getId()."' and statement in ('0','1','3','6')");
            if($pager->getTotal()) $this->jump(site_url($projectPath."/edit/id/".$pager->getObject()->getProjectId()));
            else $this->jump(site_url($projectPath."/create"));//创建项目
        }else{
            $this->project = sf::getModel("Projects")->selectByProjectId($_SESSION['worker']['project_id']);
            if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
            if($this->project->getUserId() != input::session("roleuserid")) $this->page_debug("你没有权限执行该操作！",getFromUrl());
            if($this->project->getUserId()!='963049C2-EB30-1A8B-22A3-2CD688C40449' && strtotime($this->project->getGuide(true)->getEndAt()) < time()) $this->page_debug("项目对应的指南已经停止申报！",getFromUrl());
        }

        $this->worker = sf::getModel("EngineWorkers",$_SESSION['worker']['worker_id']);
        if($this->worker->isNew()) $this->page_debug("参数错误！",getFromUrl());
        //不在编辑状态不能编辑
        if (!$this->project->enableWrite()) {
            $this->page_debug("当前状态你没有权限执行该操作！" . $this->project->getStatement(), getFromUrl());
        }
        //标记为编辑模式
        $this->project->setWidgetConfigs('action',array('edit'=>true,'worker'=>'worker'));
        $this->view->set("edit",'yes');
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->worker->getConfigs());
        $this->view->display("Worker/".$this->worker->template());
    }

    /**
     * 项目申报书查看页面
     * @return [type] [description]
     */
    function show()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->project->setWidgetConfigs('action',array('show'=>true,'worker'=>'worker'));
        $this->worker = $this->project->worker();

        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->worker->getConfigs());
        $this->view->display("Worker/".$this->worker->template());
    }

    /**
     * 生成项目申报书html存档
     */
    function makeHtml()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->project->setWidgetConfigs('action',array('show'=>true,'worker'=>'worker'));
        $this->worker = $this->project->worker();
        $saveDir = WEBROOT.'/Documents/'.$this->project->getDeclareYear().'/apply/'.$this->project->getProjectId();
        if(!is_dir($saveDir)) mkdir($saveDir,0777,true);
        $userlevel = input::session('userlevel');
        //生成管理员看的版本
        $_SESSION['userlevel'] = 6;
        $html = $this->getHtml();
        file_put_contents($saveDir.'/manager.html',$html);
        //生成单位看的版本
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        $_SESSION['userlevel'] = 3;
        $html = $this->getHtml();
        file_put_contents($saveDir.'/company.html',$html);
        //生成导出pdf的版本
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        $this->project->setWidgetConfigs('action',array('download'=>'yes','worker'=>'worker'));
        $this->view->set("download",'yes');
        $html = $this->getHtml('all');
        file_put_contents($saveDir.'/output.html',$html);
        $_SESSION['userlevel'] = $userlevel;
        echo 'ok';
    }

    function getHtml($area='content')
    {
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->worker->getConfigs());
        $htmlStr = $this->view->getContent("Worker/".$this->worker->template());
        if($area=='content'){
            $pattern = '/\<!-- Page Content -->(.*?)\<!-- END Page Content -->/s';
            preg_match_all($pattern,$htmlStr,$matches);
            return $matches[1][0];
        }
        return $htmlStr;
    }

    /**
     * 项目申报书空白模板查看
     * @return [type] [description]
     */
    function read()
    {
        $guide = sf::getModel('Guides',input::getMix('id'));
        if($guide->isNew()){
            $this->error('没有找到该类申报书');
        }
        $type = $guide->types();
        if($type->isNew()) $this->error("申报书不存在！",getFromUrl());
        $_SESSION['read']['worker_id'] = $type->getWorkerId();
        $_SESSION['read']['subject'] = $guide->getSubject();
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()){
            $this->project->setGuideId($guide->getId());
            $this->project->setLabel($guide->getMark());
            if($guide->getProjectStartAt()) $this->project->setStartAt($guide->getProjectStartAt());
            if($guide->getProjectEndAt()) $this->project->setEndAt($guide->getProjectEndAt());
            //设置默认项目名称
            if($guide->getProjectName()) $this->project->setSubject($guide->getProjectName());
            //项目类型
            $type = $guide->types();
            if($type->isNew()) $this->error("申报书不存在！",getFromUrl());
            $this->project->setTypeId($type->getId());
            $this->project->setTypeSubject($type->getSubject());
            $this->project->setWorkerId($type->getWorkerId());
            $this->project->setCatId($guide->getCatId()?:$type->getCatId());//设置大类
            $this->project->setLevel($this->project->getCatLevel());
            $this->project->setUserName(" ");
            $this->project->setCorporationName(" ");
        }

        $this->project->setStatement(999);      //预览模式
        if(input::getMix('type')=='download'){
            $this->downloadTpl();
        }
        $this->project->setWidgetConfigs('action',array('show'=>true,'worker'=>'worker'));
        $this->worker = $this->project->worker();

        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->worker->getConfigs());
        $this->view->display("Worker/".$this->worker->template());
    }


    /**
     * 评审界面测试
     * @return [type] [description]
     */
    function show2()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->project->setWidgetConfigs('action',array('show'=>true,'worker'=>'worker'));
        $this->worker = $this->project->worker();

        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->worker->getConfigs());
        $this->view->display("Worker/".$this->worker->template().'3');
    }

    /**
     * 申报书盖章等页面打印稿生成
     * @return [type] [description]
     */
    function printer()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) {
            if(input::session('userlevel')==3 && $_SESSION['read']['worker_id']){
                $company = sf::getModel('Corporations')->selectByUserId(input::session('roleuserid'));
                $this->project->setWorkerId($_SESSION['read']['worker_id']);
                $this->project->setSubject($_SESSION['read']['subject']);
                $this->project->setStatement(999);
                $this->project->setCorporationId($company->getUserId());
                $this->project->setCorporationName($company->getSubject());
                $this->project->setDepartmentId($company->getDepartmentId());
                $this->project->setDepartmentName($company->getDepartmentName());
            }else{
                $this->page_debug("项目不存在！",getFromUrl());
            }

        }
        $this->project->setWidgetConfigs('action',array('print'=>'yes','worker'=>'worker'));
        $this->worker = $this->project->worker();
        $this->view->set("print",'yes');
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->project->getWidgetConfigs());
        $htmlStr = $this->view->getContent("Worker/".$this->worker->template());
        $hash = substr(md5(strip_tags($htmlStr)),0,20);
        //替换一下
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        //$this->project->setShortUrl($hash,'attachement');
        //提取打印内容
        $type = input::getMix('type');
        $pattern = '/\<widget type\="printer"\>(.*?)\<\/widget\>/s';
        if($type)  $pattern = '/\<widget type\="printer"\ name="'.$type.'">(.*?)\<\/widget\>/s';
        preg_match_all($pattern,$htmlStr,$matches);
        if($matches[1]){
            $htmlStr = implode('<pagebreak></pagebreak>',$matches[1]);
            $htmlStr = '<link rel="stylesheet" href="'.site_path("css/template.css").'">'.$htmlStr;
        }else{
            $htmlStr = stristr($htmlStr,'<widget type="printer">');
            $htmlStr = stristr($htmlStr,'</widget>',true);
            $htmlStr = str_replace('<widget type="printer">','<link rel="stylesheet" href="'.site_path("css/template.css").'">',$htmlStr);
        }
        $title = $this->project->getCatSubject().'建设项目申报书正式版';
        if(in_array($this->project->getCatId(),[225,230,231])) $title=$this->project->getTopGuideSubject();
        $pdf =  PDF::setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="2%" style="border: 0px solid #fff;"></td><td width="90%" style="text-align: right;border: 0px solid #fff;">'.$this->project->getSubject().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;"></td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setTitle($this->project->getSubject())
            ->setSubject($this->project->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->project->getUserName())
            ->setContent($htmlStr)
            ->hasCover(true)
            ->setWaterMark($title,0.1)
            ->show();
    }

    /**
     * 生成申报书
     */
    function output()
    {
        @ini_set('memory_limit', '200M');//设置最大使用内存为200M
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        //已经生成的直接下载
        if($this->project->enablePrint('apply')){
            $file_name = $this->project->getConfigs("file.apply");
            if($this->project->isModify()) {
                $file_name = $this->project->getConfigs("file.modify");
            }
            if($file_name){//如果已经生成文件，直接打开下载
                $refresh = input::mix("refresh") ? true : false;
                if(!$refresh){//不刷新就直接跳转
                    addHistory($this->project->getProjectId(),'下载申报书','project',1);
                    $this->warterMark($file_name);
                    exit;
                }
            }
        }
        //生成申报书
        //2023-01-19注释掉了预览申报书时对申报书的检查，因为检查时会循环科研项目等表检查附件是否上传，会导致申报书里循环时，为空。
//        if(!isSuper(5) && $this->project->enablePrint('apply')){
//            if($msg = $this->project->validate('widget')){
//                $this->view->set("msg",$msg);
//                $this->view->apply("inc_body","Worker/Error");
//                $this->view->display("Page");
//                exit;
//            }
//        }
        $this->project->setWidgetConfigs('action',array('download'=>'yes','worker'=>'worker'));

        $this->worker = $this->project->worker();
        $this->view->set("download",'yes');
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->project->getWidgetConfigs());
        $htmlStr = $this->view->getContent("Worker/".$this->worker->template());
        $hash = substr(md5(strip_tags($htmlStr)),0,20);
        //替换一下
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        $this->project->setShortUrl($hash,'apply');
        //拆分内容
        $htmlArray = explode('{{file:',$htmlStr);
        $title = $this->project->getCatSubject().'建设项目申报书';
        if(in_array($this->project->getCatId(),[225,230,231])) $title=$this->project->getTopGuideSubject();
        $pdf =  PDF::setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="50%" style="border: 0px solid #fff;">'.$title.'</td><td width="50%" style="text-align: right;border: 0px solid #fff;">'.$this->project->getSubject().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;">- {PAGENO} -</td><td width="33%" style="text-align: right;border: 0px solid #fff;"><barcode code="'.site_url('m/p/h/'.$hash).'" type="QR" disableborder="0" size="0.6" error="M" /></td></tr></table>')
            ->setTitle($this->project->getSubject())
            ->setSubject($this->project->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->project->getUserName());
        //设置输出内容
        for($i=0,$n=count($htmlArray);$i<$n;$i++){
            if($i > 0){//分析插入文件
                //如果是WORD文档将直接传输URL地址
                if(substr($htmlArray[$i],0,4) == 'http') $file = strstr($htmlArray[$i],'}}',true);
                else $file = config::get("upload_path").strstr($htmlArray[$i],'}}',true);
                $pdf->setContent($file,'file');

                $content = substr(strstr($htmlArray[$i],'}}'),2);
                $pdf->setContent($content);
            }else $pdf->setContent($htmlArray[$i]);
        }
        //保存的文件名
        $savename = time().mt_rand(1000,9999).'.pdf';
        $file_path = date("Y").'/'.date("m").'/'.$savename;
        $dir_path = WEBROOT."/up_files/".dirname($file_path);
        if (!file_exists($dir_path)) { //检查目录是否存在
            if (!@mkdir($dir_path, 0755, true)) {
                exit('不能创建目录: ' . \dirname($file_path));
            }
        }
        if($this->project->enablePrint('apply')){
            //水印
            $pdf->setWaterMark($title,0.1);
            $pdf->save(WEBROOT."/up_files/".$file_path);
            //保存到附件表内
            $filemanager = sf::getModel("ProjectAttachments");
            $filemanager->setFileName($this->project->getSubject()."-申报书");
            $filemanager->setFileSavename($savename);
            $filemanager->setFilePath($file_path);
            $filemanager->setFileSize(filesize(WEBROOT."/up_files/".$file_path));
            $filemanager->setFileExt('pdf');
            $filemanager->setFileMinetype('application/pdf');
            $filemanager->setUserId($this->project->getUserId());
            $filemanager->setUserName($this->project->getUserName());
            $filemanager->setCreatedAt(date("Y-m-d H:i:s"));
            $filemanager->setItemId($this->project->getProjectId());
            $filemanager->setItemType('apply');
            if($this->project->isModify()) $filemanager->setItemType('modify');
            $filemanager->setFileNote("《".$this->project->getSubject().'》的申报书PDF存档');
//			$filemanager->setServerIp(trim($_SERVER['SERVER_ADDR']));
            $filemanager->save();
            //记录到项目配置
            if($this->project->isModify()) {
                $this->project->setConfigs('file.modify',$file_path);
            }else{
                $this->project->setConfigs("file.apply",$file_path);
            }
            addHistory($this->project->getProjectId(),'下载申报书','project',1);
            $this->warterMark($file_path);
            exit();
        }else{
            //水印
            $title = $this->project->getCatSubject().'建设项目申报书预览版';
            $pdf->setWaterMark($title,0.1);
            $pdf->show();
            exit();
        }
    }

    /**
     * 下载申报书
     * @return [type] [description]
     */
    function download()
    {
        $this->output();
    }

    /**
     * 下载申报书
     * @return [type] [description]
     */
    function downloadTpl()
    {
        @ini_set('memory_limit', '200M');//设置最大使用内存为200M
        $this->project->setWidgetConfigs('action',array('download'=>'yes','worker'=>'worker'));
        $this->worker = $this->project->worker();
        $this->view->set("download",'yes');
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->project->getWidgetConfigs());
        $htmlStr = $this->view->getContent("Worker/".$this->worker->template());
        $hash = substr(md5(strip_tags($htmlStr)),0,20);
        //替换一下
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        $this->project->setShortUrl($hash,'apply');
        //拆分内容
        $htmlArray = explode('{{file:',$htmlStr);
        $pdf =  PDF::setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="2%" style="border: 0px solid #fff;"></td><td width="90%" style="text-align: right;border: 0px solid #fff;">'.$this->project->getSubject().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;">- {PAGENO} -</td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setTitle($this->project->getSubject())
            ->setSubject($this->project->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->project->getUserName());
        //设置输出内容
        for($i=0,$n=count($htmlArray);$i<$n;$i++){
            if($i > 0){//分析插入文件
                //如果是WORD文档将直接传输URL地址
                if(substr($htmlArray[$i],0,4) == 'http') $file = strstr($htmlArray[$i],'}}',true);
                else $file = config::get("upload_path").strstr($htmlArray[$i],'}}',true);
                $pdf->setContent($file,'file');

                $content = substr(strstr($htmlArray[$i],'}}'),2);
                $pdf->setContent($content);
            }else $pdf->setContent($htmlArray[$i]);
        }
        //保存的文件名
        $savename = time().mt_rand(1000,9999).'.pdf';
        $file_path = date("Y").'/'.date("m").'/'.$savename;
        if (!file_exists(dirname($file_path))) { //检查目录是否存在
            if (!@mkdir(dirname($file_path), 0755, true)) {
                exit('不能创建目录: ' . \dirname($file_path));
            }
        }
        //水印
        $title = $this->project->getCatSubject().'建设项目申报书预览版';
        $pdf->setWaterMark($title,0.1);
        $pdf->show();
        exit();
    }

    /**
     * 项目申报书预览页面
     * @return [type] [description]
     */
    function preview()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->project->setWidgetConfigs('action',array('show'=>true,'worker'=>'worker'));
        $this->worker = $this->project->worker();

        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->worker->getConfigs());
        $this->view->display("Worker/Apply/preview");
    }

    function warterMark($pdfPath)
    {
        @header("Location:".site_path('up_files/'.$pdfPath));exit();
        if($this->project->getUserId()==input::session('roleuserid')) {
            @header("Location:".site_path('up_files/'.$pdfPath));exit();
        }
        $pdf =  PDF::setFooter(' ')
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->project->getUserName());
        $text = input::session('nickname').'  '.date('Y年m月d日H时i分s秒').'  '.input::session('nickname');
        $pdf->setWaterMarks($text,0.1);
        $pdf->setContent(config::get("upload_path").$pdfPath,'file');
        $pdf->show();
    }

    /**
     * 新增项目
     * @return [type] [description]
     */
    function create()
    {
        $guide = sf::getModel("Guides",$_SESSION['worker']['guide_id']);
        if($guide->isNew()) $this->error("指南丢失！",getFromUrl());
        //判断是否满足申报要求
        if($msg = $this->check($guide))
        {
            $this->view->set("msg",$msg);
            $this->view->apply("inc_body","Worker/Apply/Error");
            $this->view->display("Page");
            exit;
        }else{
            //新增项目
            $this->project = sf::getModel("Projects")->selectByProjectId('');
            if($guide->getProjectStartAt()) $this->project->setStartAt($guide->getProjectStartAt());
            if($guide->getProjectEndAt()) $this->project->setEndAt($guide->getProjectEndAt());
            //设置默认项目名称
            if($guide->getProjectName()) $this->project->setSubject($guide->getProjectName());
            $type = $guide->types();
            if($type->isNew()) $this->error("申报书不存在！",getFromUrl());
            $this->project->setTypeId($type->getId());
            $this->project->setTypeSubject($type->getSubject());
            $this->project->setWorkerId($type->getWorkerId());
            $this->project->setCatId($guide->getCatId()?:$type->getCatId());//设置大类
            $this->project->setLevel($this->project->getCatLevel());
            if(input::post())
            {
                $this->checkCsrf();//防止多次插入
                //查询正在填写中的项目
                $pager = sf::getModel("Projects")->selectAll("user_id='".input::session("roleuserid")."' and guide_id = '".$guide->getId()."' and statement in ('0','1','3','6','12')");
                if($pager->getTotal()) $this->jump(site_url("engine/worker/edit/id/".$pager->getObject()->getProjectId()));

                $this->project->setSubject(input::post('subject'));
                //设置默认项目名称
                if($guide->getProjectName()) $this->project->setSubject($guide->getProjectName());
                $this->project->setGuideId($guide->getId());
                $this->project->setSubjectCode($guide->getMark());
                //项目类型
                $type = $guide->types();
                if($type->isNew()) $this->error("申报书不存在！",getFromUrl());
                $this->project->setTypeId($type->getId());
                $this->project->setTypeSubject($type->getSubject());
                $this->project->setWorkerId($type->getWorkerId());
                $this->project->setCatId($guide->getCatId()?:$type->getCatId());//设置大类
                $this->project->setLevel($this->project->getCatLevel());
                if(input::session("userlevel") == 2){//申报人申报
                    if($guide->getUserLevel() != 2) $this->error("该类指南并未对项目负责人开放！请尝试使用申报单位管理员再次申报。",getFromUrl());
                    $declarer = sf::getModel("Declarers")->selectByUserId(input::session('roleuserid'));
                    $this->project->setUserId($declarer->getUserId());
                    $this->project->setUserName($declarer->getFullName());
                    $company = 	$declarer->getCorporation();
                    if($company->isNew()) $this->error("申报单位不存在！",getFromUrl());
                    //基本识别信息
                    $this->project->setCorporationId($company->getUserId());
                    $this->project->setCorporationName($company->getSubject());
                    $this->project->setDepartmentId($company->getDepartmentId());
                    $this->project->setDepartmentName($company->getDepartmentName());
                    if($company->getDepartmentId()=='BF7900E0-D81F-D9D6-8C83-4B5EA8AC9EFF'){
                        //测试项目
                        $this->project->setIsTest(1);
                    }
                    $researcher = $this->project->researcher('apply');
                    if($researcher->isNew()){
                        $researcher->setSubject($this->project->user()->getPersonname());
                        $researcher->setMobile($this->project->user()->getUserMobile());
                        $researcher->setSex($this->project->user()->getUserSex());
                        $researcher->setBirthday($this->project->user()->getUserBirthday());
                        $researcher->setPhone($this->project->user()->getUserMobile());
                        $researcher->setDegree($this->project->user()->getUserDegree());
                        $researcher->setTitle($this->project->user()->getUserHonor());
                        $researcher->setProfessional($this->project->user()->getUserWork());
                        $researcher->setEducation($this->project->user()->getEducation());
                        $researcher->save();
                    }
                }else if(input::session("userlevel") == 3){//申报单位申报
                    if($guide->getUserLevel() != 3) $this->page_debug("该类指南并未对申报单位开放！请尝试使用项目负责人再次申报。",getFromUrl());
                    $company = sf::getModel("Corporations")->selectByUserId(input::session("roleuserid"));
                    $this->project->setUserId($company->getUserId());
                    $this->project->setUserName('/');
                    if(!input::post("subject")){//基本业务费没有项目名称，将单位名称设置为项目名称
                        $this->project->setSubject($company->getSubject());
                    }
                    //基本识别信息
                    $this->project->setCorporationId($company->getUserId());
                    $this->project->setCorporationName($company->getSubject());
                    $this->project->setDepartmentId($company->getDepartmentId());
                    $this->project->setDepartmentName($company->getDepartmentName());
                }else if(input::session("userlevel") == 4){//推荐单位申报
                    if($guide->getUserLevel() != 4) $this->page_debug("该类指南并未对推荐单位开放！",getFromUrl());
                    $company = sf::getModel("Departments")->selectByUserId(input::session("roleuserid"));
                    $this->project->setUserId($company->getUserId());
                    $this->project->setUserName('/');
                    if(!input::post("subject")){//基本业务费没有项目名称，将单位名称设置为项目名称
                        $this->project->setSubject($company->getSubject());
                    }
                    //基本识别信息
                    $this->project->setCorporationId($company->getUserId());
                    $this->project->setCorporationName('/');
                    $this->project->setDepartmentId($company->getUserId());
                    $this->project->setDepartmentName($company->getSubject());
                }else $this->page_debug("你的角色不能申报该指南项目！请尝试使用项目负责人或申报单位管理员再次申报。",getFromUrl());
                //检查指南中是否对项目名称有要求
                $property = $guide->getProperty();
                if(in_array('title_with_company_name',$property)) $this->project->setSubject($company->getSubject());
                if(in_array('title_with_guide_name',$property)) $this->project->setSubject($guide->getSubject());
                //年度信息
                $this->project->setDeclareYear($guide->getYear()>=date('Y')?$guide->getYear():date('Y'));
                $this->project->setTypeCurrentGroup($guide->getCurrentGroup());
                //按照指南设置分流
                if($guide->getOfficeId()){
                    $this->project->setOfficeId($guide->getOfficeId());
                    $this->project->setOfficeSubject($guide->getOfficeName());
                }
                //其他内容
                $this->project->setStatement(1);
                $this->project->setCreatedAt(date('Y-m-d H:i:s'));
                $this->project->save();
                $this->project->setConfigs('path.apply',"engine/worker");
                $_SESSION['worker']['project_id'] = $this->project->getProjectId();
                $this->success("项目创建成功！请继续填写申报书其他内容。",site_url("engine/worker/edit/id/".$this->project->getProjectId()));
            }

            if(input::session("userlevel") == 2 && $guide->getUserLevel() == 2){//兼职单位
                $declarer = sf::getModel("Declarers")->selectByUserId(input::session("roleuserid"));
                if($declarer->isNew()) $this->page_debug("申报人不存在！。",getFromUrl());
                if(!$multiples = $declarer->multiples(true)) $multiples = false;
                $this->view->set("multiples",$multiples);

                $declarers = sf::getModel("Declarers")->selectAll("corporation_id = '".$declarer->getCorporationId()."'");
                $this->view->set("declarers",$declarers);
            }

            $this->view->set("project",$this->project);
            $this->view->set("guide",$guide);
            $this->view->apply("inc_body","Worker/Apply/Create");
            $this->view->display("page_main");
        }
    }

    /**
     * 新增项目
     * @return [type] [description]
     */
    function doCreate()
    {
        $guide = sf::getModel("Guides",$_SESSION['worker']['guide_id']);
        if($guide->isNew()) $this->page_debug("指南丢失！",getFromUrl());
        //判断是否满足申报要求
        if($msg = $this->check($guide))
        {
            $this->view->set("msg",$msg);
            $this->view->apply("inc_body","Worker/Apply/Error");
            $this->view->display("Page");
            exit;
        }else{
            //新增项目
            $this->project = sf::getModel("Projects")->selectByProjectId('');
            if($guide->getProjectStartAt()) $this->project->setStartAt($guide->getProjectStartAt());
            if($guide->getProjectEndAt()) $this->project->setEndAt($guide->getProjectEndAt());
            //设置默认项目名称
            if($guide->getProjectName()) $this->project->setSubject($guide->getProjectName());
            $this->project->setSubject();
            $this->project->setGuideId($guide->getId());
            //项目类型
            $type = $guide->types();
            if($type->isNew()) $this->page_debug("申报书不存在！",getFromUrl());
            $this->project->setTypeId($type->getId());
            $this->project->setTypeSubject($type->getSubject());
            $this->project->setWorkerId($type->getWorkerId());
            $this->project->setCatId($guide->getCatId()?:$type->getCatId());//设置大类
            $this->project->setLevel($this->project->getCatLevel());
            if(input::session("userlevel") == 2){//申报人申报
                if($guide->getUserLevel() != 2) $this->page_debug("该类项目并未对项目负责人开放！请尝试使用申报单位管理员再次申报。",getFromUrl());
                $declarer = sf::getModel("Declarers")->selectByUserId(input::session("roleuserid"));
                $this->project->setUserId($declarer->getUserId());
                $this->project->setUserName($declarer->getFullName());
                $company = 	$declarer->getCorporation();
                if($company->isNew()) $this->page_debug("申报单位不存在！",getFromUrl());
                //基本识别信息
                $this->project->setCorporationId($company->getUserId());
                $this->project->setCorporationName($company->getSubject());
                $this->project->setDepartmentId($company->getParentId());
                $this->project->setDepartmentName($company->getParentName());
            }else if(input::session("userlevel") == 3){//申报单位申报
                if($guide->getUserLevel() != 3) $this->page_debug("该类指南并未对申报单位开放！请尝试使用项目负责人再次申报。",getFromUrl());
                $company = sf::getModel("Corporations")->selectByUserId(input::session("roleuserid"));
                $this->project->setUserId($company->getUserId());
                $this->project->setUserName('/');
                if(!input::post("subject")){//基本业务费没有项目名称，将单位名称设置为项目名称
                    $this->project->setSubject($company->getSubject());
                }
                //基本识别信息
                $this->project->setCorporationId($company->getUserId());
                $this->project->setCorporationName($company->getSubject());
                $this->project->setDepartmentId($company->getDepartmentId());
                $this->project->setDepartmentName($company->getDepartmentName());
            }else if(input::session("userlevel") == 4){//推荐单位申报
                if($guide->getUserLevel() != 4) $this->page_debug("该类指南并未对推荐单位开放！",getFromUrl());
                $company = sf::getModel("Departments")->selectByUserId(input::session("roleuserid"));
                $this->project->setUserId($company->getUserId());
                $this->project->setUserName('/');
                if(!input::post("subject")){//基本业务费没有项目名称，将单位名称设置为项目名称
                    $this->project->setSubject($company->getSubject());
                }
                //基本识别信息
                $this->project->setCorporationId($company->getUserId());
                $this->project->setCorporationName('/');
                $this->project->setDepartmentId($company->getUserId());
                $this->project->setDepartmentName($company->getSubject());
            }else $this->page_debug("你的角色不能申报该指南项目！请尝试使用项目负责人或申报单位管理员再次申报。",getFromUrl());
            //检查指南中是否对项目名称有要求
            $property = $guide->getProperty();
            if(in_array('title_with_company_name',$property)) $this->project->setSubject($company->getSubject());
            if(in_array('title_with_guide_name',$property)) $this->project->setSubject($guide->getSubject());
            //年度信息
            $this->project->setDeclareYear($guide->getYear()>=date('Y')?$guide->getYear():date('Y'));
            $this->project->setTypeCurrentGroup($guide->getCurrentGroup());
            //按照指南设置分流
            if($guide->getOfficeId()){
                $this->project->setOfficeId($guide->getOfficeId());
                $this->project->setOfficeSubject($guide->getOfficeName());
            }
            //其他内容
            $this->project->setStatement(1);
            $this->project->save();
            $this->project->setConfigs('path.apply',"engine/worker");
            $_SESSION['worker']['project_id'] = $this->project->getProjectId();
            $this->success("项目创建成功！请继续填写申报书其他内容。",site_url("engine/worker/edit/id/".$this->project->getProjectId()));


            if(input::session("userlevel") == 2 && $guide->getUserLevel() == 2){//兼职单位
                $declarer = sf::getModel("Declarers")->selectByUserId(input::session("roleuserid"));
                if($declarer->isNew()) $this->page_debug("申报人不存在！。",getFromUrl());
                if(!$multiples = $declarer->multiples(true)) $multiples = false;
                $this->view->set("multiples",$multiples);

                $declarers = sf::getModel("Declarers")->selectAll("corporation_id = '".$declarer->getCorporationId()."'");
                $this->view->set("declarers",$declarers);
            }

            $this->view->set("project",$this->project);
            $this->view->set("guide",$guide);
            $this->view->apply("inc_body","Worker/Apply/Create");
            $this->view->display("page_main");
        }
    }

    /**
     * 检查申报主体是否符合指南要求
     * @param  guide $guide 申报指南对象
     * @return [type]        [description]
     */
    function check($guide)
    {
        $msg = $_msg = array();
        //验证指南
        if($_msg = $guide->validate()) $msg = array_merge_recursive($msg,$_msg);
        if($guide->getUserLevel() == 2){//验证负责人
            $declarer = sf::getModel("Declarers")->selectByUserId(input::session("roleuserid"));
            if($declarer->isNew()) $msg[] = "项目负责人错误";
            if($_msg = $declarer->validate()) $msg = array_merge_recursive($msg,$_msg);
            if($declarer->getIsLock()) $msg[] = '账号还没有通过实名认证';
            $company = $declarer->getCorporation();

            if($guide->getCatId()==174){
                //检查单位名单
//                $_company = sf::getModel('Companys')->selectByCode($company->getCode());
//                if($_company->isNew() || $_company->getStatement()>0){
//                    $msg[] = '贵单位不符合申报条件';
//                    return $msg;
//                }

                //检查国家重点学科
                $companyId = $declarer->getCorporationId();
                $nations = sf::getModel('ProjectNations')->selectAll("subject_code = '".$guide->getMark()."' and corporation_id = '{$companyId}'");
                if($nations->getTotal()>0){
                    $msg[] = '贵单位不符合申报条件';
                }
            }

            if(array_filter($guide->getAllowGrade()) && !in_array($declarer->getUserGrade(false),$guide->getAllowGrade())) $msg[] = "你的职称或学历不在指南要求的范围之内";

            if(!in_array('no_limit',$guide->getProperty())){
                $catIds = [$guide->getCatId()];
                if($declarer->hasProject('DECLARER',$guide->getYear(),'',$guide->getCurrentGroup(),$catIds)) $msg[] = "你该年度已经填报了一个项目不允许填写新的项目";
            }

            //有逾期未验收项目不也能申报
            if($declarer->hasProject('OVERDUE')) $message[] = "项目负责人还有逾期未结题项目，在完成结题手续之前不能申报新项目";

            //检查用户是否已经填写项目，不允许用户填写多个项目
            if($declarer->hasProject('UNDERWAY',$guide->getYear())) $msg[] = "你已有1个填写中的项目不能重复填写，如需重新填写请先删除不需要的项目";
            //检查是否有兼职单位
//            if(!$declarer->getIsMultiple()){
//                //验证单位
//                $company = $declarer->getCorporation(true);
//                if($company->isNew()) $msg[] = "所属单位错误";
//                if($company->getIsLock()) $msg[] = "贵单位还没有完成实名认证！";
//                if($guide->getAllowCompany()) {
//                    $_msg = $company->checkType($guide->getAllowCompany());
//                    if($_msg!==true){
//                        $msg[] = $_msg;
//                    }
//                }
//            }
        }else if($guide->getUserLevel() == 3){//单位申报
            $company = sf::getModel("Corporations")->selectByUserId(input::session("roleuserid"));
            if($company->isNew()) $msg[] = "对应的申报单位不存在！";
            if($_msg = $company->validate()) $msg = array_merge_recursive($msg,$_msg);
            if($company->getIsLock()) $msg[] = '单位还没有通过实名认证';
            if($guide->getAllowCompany() && !$company->checkType($guide->getAllowCompany())) $msg[] = "贵单位不在指南要求的范围之内";
            //检查单位处罚记录
            $badcredits = $company->selectBadcredit(5);
            while($badcredit = $badcredits->getObject()){
                if($badcredit->isNew()) continue;
                $msg[] = '贵单位在'.$badcredit->getCreatedAt("Y年m月d日").'留下处罚记录，过期时间为：'.$badcredit->getEndAt().'，原因是：'.$badcredit->getNote();
            }
        }

        return $msg;
    }

    /**
     * 刷新指定部件页面
     * @return string 部件的HTML内容
     */
    function reloadWidget(){
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->project->setWidgetConfigs('action',array('edit'=>'yes','worker'=>'worker'));
        exit($this->project->widgets(input::mix("widget")));
    }

    /**
     * 下载资金承诺书
     * @return void
     */
    function promise()
    {
        if(input::session('userlevel')==3){
            $this->error("请登录项目负责人账号下载！",getFromUrl());
        }
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $datas = [
            'company'=>$this->project->getCorporationName(),
            'subject'=>$this->project->getSubject(),
            'year'=>$this->project->getDeclareYear(),
        ];
        $savename = 'promise_'.time().mt_rand(1000,9999).'.docx';
        $savedir = WEBROOT."/up_files/".date("Y").'/'.date("m");
        $savepath = $savedir.'/'.$savename;
        if (!file_exists($savedir)) { //检查目录是否存在
            if (!mkdir($savedir, 0755, true) && !is_dir($savedir)) {
                exit('创建目录失败: ' . \dirname($savedir));
            }
        }
        //先生成word文档
        word_out($datas,WEBROOT.'/up_files/tpl/promise_'.$this->project->getCatId().'.docx','资金承诺书.docx',$savepath,false);

        //再生成pdf
        $wordUrl = site_path('/up_files/'.date("Y").'/'.date("m").'/'.$savename);
        $title = $this->project->getCatSubject().'建设项目资金承诺书';
        $pdf =  PDF::setTitle($this->project->getSubject())
            ->setSubject($this->project->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->project->getUserName());
        $pdf->setContent($wordUrl,'file');
        $pdf->setWaterMark($title,0.1);
        $pdf->show();
    }

    /**
     * 自动获取附件列表【试验阶段】
     * @return void
     */
    function attachementlist()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->project->setWidgetConfigs('action',array('print'=>'yes','worker'=>'worker'));
        $this->worker = $this->project->worker();
        $this->view->set("print",'yes');
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->project->getWidgetConfigs());
        $htmlStr = $this->view->getContent("Worker/".$this->worker->template());
        include_once(APPPATH.'/lib/SimpleHtmlDom.php');
        $html = str_get_html($htmlStr);
        $title1s = $html->find('.title1');
        $fileLists = [];
        foreach ($title1s as $title1){
            //章节标题
            $fileLists[] = $title1->text();
            if($title1->next_sibling()->attr['data-widget-id']){
                if(!$title1->next_sibling()->find('.btn-alt-success')) continue;
                foreach ($title1->next_sibling()->find('caption') as $caption){
                    //表格标题
                    $captionTitle = $caption->find('span')[0]->nodes[0]->text();
                    $fileLists[] = $captionTitle;
                    $downloadBtns = $caption->parent()->find('.btn-alt-success');
                    foreach ($downloadBtns as $downloadBtn){
                        //$downloadBtn->text() 按钮的标题：附件(1)  注意附件没有的情况
                        $links = $downloadBtn->onclick;
                        $links = explode("'",$links);
                        $link = $links[3];
                        $itemtypes = explode('item_type',$link);
                        $itemtypes = explode('/',$itemtypes[1]);
                        $itemtypes = array_filter($itemtypes);
                        $itemtype = array_shift($itemtypes);
                        if($itemtype){
                            $attachments = $this->project->getAttachment($itemtype);
                            while($attachment = $attachments->getObject()){
                                $fileLists[] = '<a href="'.$attachment->getPreviewUrl().'" target="_blank">'.($attachment->getFileNote()?:$attachment->getFileName()).'</a>';
                            }
                        }
                    }
                }
            }
            if($title1->next_sibling()->next_sibling()->attr['class']=='title2'){
                //二级标题
                $title2 = $title1->next_sibling()->next_sibling();
                $fileLists[] = $title2->text();
                if(!$title2->next_sibling()->find('.btn-alt-success')) continue;
                foreach ($title2->next_sibling()->find('caption') as $caption){
                    //表格标题
                    $captionTitle = $caption->find('span')[0]->nodes[0]->text();
                    $fileLists[] = $captionTitle;
                    $downloadBtns = $caption->parent()->find('.btn-alt-success');
                    foreach ($downloadBtns as $downloadBtn){
                        //$downloadBtn->text() 按钮的标题：附件(1)  注意附件没有的情况
                        $links = $downloadBtn->onclick;
                        $links = explode("'",$links);
                        $link = $links[3];
                        $itemtypes = explode('item_type',$link);
                        $itemtypes = explode('/',$itemtypes[1]);
                        $itemtypes = array_filter($itemtypes);
                        $itemtype = array_shift($itemtypes);
                        if($itemtype){
                            $attachments = $this->project->getAttachment($itemtype);
                            while($attachment = $attachments->getObject()){
                                $fileLists[] = '<a href="'.$attachment->getPreviewUrl().'" target="_blank">'.($attachment->getFileNote()?:$attachment->getFileName()).'</a>';
                            }
                        }
                    }
                }
            }


        }
        dd($fileLists);
//        foreach ($downloadBtns as $downloadBtn){
//            $table = $downloadBtn->parent()->parent()->parent()->parent();
//            if($table->tag=='table'){
//                dd($table->find('caption'),$table->find('caption span',0));
//            }
//        }
        dd(1);
        include_once(APPPATH.'/lib/phpQuery.php');
        include_once(APPPATH.'/lib/QueryList.php');

        $rules = array(
            'btns' => ['.btn-alt-success','text'],
        );
        $data = \QL\QueryList::Query($htmlStr,$rules)->data;
        dd($data);

        $doc = \phpQuery::newDocumentHTML($htmlStr);
        $btns = $doc->find('.btn-alt-success');
        $btns->each(function($element) {
            dd($element->parent());
        });
        dd(1);

        $hash = substr(md5(strip_tags($htmlStr)),0,20);
        $pattern = '/\<widget type\="printer"\>(.*?)\<\/widget\>/s';
        if($type)  $pattern = '/\<widget type\="printer"\ name="'.$type.'">(.*?)\<\/widget\>/s';
        preg_match_all($pattern,$htmlStr,$matches);
        if($matches[1]){
            $htmlStr = implode('<pagebreak></pagebreak>',$matches[1]);
            $htmlStr = '<link rel="stylesheet" href="'.site_path("css/template.css").'">'.$htmlStr;
        }else{
            $htmlStr = stristr($htmlStr,'<widget type="printer">');
            $htmlStr = stristr($htmlStr,'</widget>',true);
            $htmlStr = str_replace('<widget type="printer">','<link rel="stylesheet" href="'.site_path("css/template.css").'">',$htmlStr);
        }
    }

}
