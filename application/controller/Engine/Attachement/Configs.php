<?php
namespace App\Controller\Engine\Attachement;
class Configs
{   
    private static $default = [];
    private static $configs = [
        'attachement'=>['name'=>'文件上传',
            'value'=>true,
        ],
        'necessary'=>[
                'apply_stamp'=>'签字盖章页',
                'lab_rymd'=>'实验室人员名单（含固定人员和客座人员，分研究方向列出姓名、性别、出生年月、职称、专业、研究方向及单位等主要信息，研究、技术和管理人员分别排列，客座人员请备注）',
                'lab_xswyh'=>'拟任学术委员会名单（列出姓名、性别、出生年月、职称、单位、研究方向等主要信息）',
                'lab_sbqd'=>'实验室主要仪器设备清单（设备名称、规格型号、数量、原价值及合计等）',
                'lab_xmqd'=>'实验室近5年来承担的主要科研项目清单（名称、来源、编号、起止时间、经费、负责人、参加人员等）',
                'lab_hjqd'=>'实验室近5年来主要获奖清单（名称、等级、授予部门、主要获奖人员、获奖时间等）',
                'lab_cgqd'=>'实验室近5年来主要学术专著、论文、发明专利、成果评价等科研成果清单（题目、类型、时间、作者、水平等）',
                'lab_yxzm'=>'实验室运行证明',
                'lab_zccn'=>'实验室重复建设情况自查承诺的函',
                'lab_gxjl'=>'实验室近5年开放共享记录',
                'lab_gzzd'=>'实验室各项规章制度',
                'lab_hyjy'=>'实验室学术委员会会议纪要',
                'lab_hzxy'=>'合作协议书（联合两家及以上单位组建四川省重点实验室的提供）',
                'lab_zjcn'=>'市州政府资金承诺书（组建厅市共建四川省重点实验室的提供）',
                'attachement'=>'其他',
            ]
    ];
    
    public static function getConfigs($key='')
    {
        if($key) return self::$configs[$key];
        else return self::$configs;
    }
    
    public static function getDefault($_configs=[],$default=[])
    {
        if(count($_configs) == 0) $_configs = self::getConfigs();
        foreach($_configs as $_key => $_val){
            $data = [];
            if(count($_val['childs'])) $default[$_key] = self::getDefault($_val['childs']);
            else $default[$_key] = $_val['value'];
        }
        return $default; 
    }
    
    public static function setConfigs(){}
    
}
?>