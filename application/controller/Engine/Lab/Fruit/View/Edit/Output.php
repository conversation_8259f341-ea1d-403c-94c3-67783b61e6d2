<div data-widget-id="<?=$widget_name?>">
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <?php if ($configs['action']['edit']): ?>
        <caption>
            <span>近五年研究成果</span>
                <span style="float:right;">
            <?=Button::setName('编辑')->setUrl(site_url("engine/lab/fruit/edit/index/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
        </span>
        </caption>
        <?php endif; ?>
        <tr>
            <td height="40" align="center" colspan="7">获奖成果（项）</td>
        </tr>
        <tr>
            <td colspan="3" align="center">国家级</td>
            <td colspan="3" align="center">省部级</td>
            <td rowspan="2" valign="middle" align="center">其他奖项</td>
        </tr>
        <tr>
            <td align="center">一等</td>
            <td align="center">二等</td>
            <td align="center">三等</td>
            <td align="center">一等</td>
            <td align="center">二等</td>
            <td align="center">三等</td>
        </tr>
        <tr>
            <td class="text-center" height="33">
                <?=$project->getData('fruit_reward_nation_1')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_reward_nation_2')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_reward_nation_3')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_reward_province_1')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_reward_province_2')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_reward_province_3')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_reward_qt')->getData()?>
            </td>
        </tr>
    </table>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <tr>
            <td height="40" align="center" colspan="2">发明专利授权（项）</td>
        </tr>
        <tr>
            <td align="center">国内</td>
            <td align="center">国外</td>
        </tr>
        <tr>
            <td class="text-center" height="33">
                <?=$project->getData('fruit_patent_gn')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_patent_gw')->getData()?>
            </td>
        </tr>
    </table>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <tr>
            <td height="40" align="center" colspan="3">技术标准（项）</td>
        </tr>
        <tr>
            <td align="center" style="width: 33.3%">国际</td>
            <td align="center" style="width: 33.3%">国家</td>
            <td align="center">行业</td>
        </tr>
        <tr>
            <td class="text-center" height="33">
                <?=$project->getData('fruit_stardard_guoji')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_stardard_guojia')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_stardard_hy')->getData()?>
            </td>
        </tr>
    </table>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <tr>
            <td height="40" align="center" colspan="3">研究论文（篇）</td>
        </tr>
        <tr>
            <td align="center" style="width: 33.3%">国外</td>
            <td align="center" style="width: 33.3%">国内</td>
            <td align="center">SCI/EI/ISTP收录</td>
        </tr>
        <tr>
            <td class="text-center" height="33">
                <?=$project->getData('fruit_paper_gw')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_paper_gn')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_paper_sci')->getData()?>
            </td>
        </tr>
    </table>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <tr>
            <td height="40" align="center" colspan="3">出版学术专著与成果转化</td>
        </tr>
        <tr>
            <td align="center" style="width: 33.3%">出版学术专著（部）</td>
            <td align="center" style="width: 33.3%">成果转化（项）</td>
            <td align="center">成果转化收益（万元）</td>
        </tr>
        <tr>
            <td class="text-center" height="33">
                <?=$project->getData('fruit_book')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_transform_sl')->getData()?>
            </td>
            <td class="text-center">
                <?=$project->getData('fruit_transform_sy')->getData()?>
            </td>
        </tr>
    </table>
</div>