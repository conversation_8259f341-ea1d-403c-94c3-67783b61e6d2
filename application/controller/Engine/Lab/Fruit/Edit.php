<?php
namespace App\Controller\Engine\Lab\Fruit;
use App\Controller\BaseController;
use Sofast\Core\lang;
use Sofast\Core\router;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Sf;

class edit extends BaseController
{
    private $project = NULL;
    private $configs = array();
    private $type = 'apply';

    function load()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        switch(trim(input::mix("engine")))
        {
            case 'tasker':
                $this->type = 'task';
                break;
            case 'completer':
                $this->type = 'complete';
                break;
            default:
                $this->type = 'apply';
                break;
        }
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->project->getWidgetConfigs('fruit',$this->type));
    }

    function index()
    {
        if(input::post()){
            $this->project->setDatas(input::post('data'));
            $this->closeWindow();
        }
        $this->view->set('project',$this->project);
        $this->view->set('type',$this->type);
        $this->view->apply("inc_body", "edit/index");
        $this->view->display("page_blank");
    }

    function update()
    {
        copyCompanyInfo2Project($this->project,$this->type);
        $this->success('更新成功！','javascript:parent.closeWindow();');
    }
}