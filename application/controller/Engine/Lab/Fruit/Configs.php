<?php
namespace App\Controller\Engine\Lab\Fruit;
class Configs
{
    private static $default = array();
    private static $configs = array(
        'fruit'=>array('name'=>'近五年研究成果',
                          'value'=>true,
                          'childs'=>array(
                              "reward"=>array('name'=>'获奖成果','value'=>true),
                              "patent"=>array('name'=>'发明专利授权','value'=>true),
                              "standard"=>array('name'=>'技术标准','value'=>true),
                              "paper"=>array('name'=>'研究论文','value'=>true),
                              "book"=>array('name'=>'出版学术专著','value'=>true),
                              "transform"=>array('name'=>'成果转化','value'=>true),
                          ))
    );

    public static function getConfigs($key='')
    {
        if($key) return self::$configs[$key];
        else return self::$configs;
    }

    public static function getDefault($_configs=array(),$default=array())
    {
        if(count($_configs) == 0) $_configs = self::getConfigs();
        foreach($_configs as $_key => $_val){
            $data = array();
            if(count($_val['childs'])) $default[$_key] = self::getDefault($_val['childs']);
            else $default[$_key] = $_val['value'];
        }
        return $default;
    }

    public static function setConfigs(){}

}
?>