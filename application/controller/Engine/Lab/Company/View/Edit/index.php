<div class="block">
    <div class="block-header">
        <h3 class="block-title">
            第一依托单位基本信息
        </h3>
    </div>
    <div class="block-content">
        <form class="form-horizontal" name="validateForm" id="activeForm" action="" method="post">
            <input type="hidden" name="id" value="<?=$project->getProjectId()?>">
            <table class="table table-bordered">
                <tr>
                    <td align="center" width="120">第一依托单位名称</td>
                    <td colspan="3"><input readonly type="text" class="form-control" value="<?=$project->getCorporationName()?>"></td>
                </tr>
                <tr>
                    <td align="center">通讯地址</td>
                    <td colspan="3"><input name="data[txdz]" type="text" class="form-control" value="<?=$project->getData('txdz')?>"></td>
                </tr>
                <tr>
                    <td align="center" width="120">法人代表姓名</td>
                    <td><input name="data[company_frxm]" type="text" class="form-control" value="<?=$project->getData('company_frxm')?>"></td>
                    <td align="center" width="120">法人代表身份证</td>
                    <td><input name="data[company_frsfz]" type="text" class="form-control" value="<?=$project->getData('company_frsfz')?>"></td>
                </tr>
                <tr>
                    <td align="center" width="120">电话</td>
                    <td><input name="data[lxrzj]" type="text" class="form-control" value="<?=$project->getData('lxrzj')?>"></td>
                </tr>
                <tr>
                    <td align="center" width="120">联系人姓名</td>
                    <td><input name="data[lxr]" type="text" class="form-control" value="<?=$project->getData('lxr')?>"></td>
                    <td align="center" width="120">联系人身份证</td>
                    <td><input name="data[lxrsfz]" type="text" class="form-control" value="<?=$project->getData('lxrsfz')?>"></td>
                </tr>
                <tr>
                    <td align="center" width="120">联系人手机</td>
                    <td><input name="data[lxdh]" type="text" class="form-control" value="<?=$project->getData('lxdh')?>"></td>
                    <td align="center" width="120">E-mail</td>
                    <td><input name="data[email]" type="text" class="form-control" value="<?=$project->getData('email')?>"></td>
                </tr>
                </tbody>
            </table>
            <table class="table table-bordered">
                <tbody>
                <tr>
                    <td align="center">
                        相关博士学位点名称
                    </td>
                    <td width="100" align="center">
                        操作
                    </td>
                </tr>
                <?php
                $doctorals = $project->getData('doctoral');
                $doctoralArr = explode('$',$doctorals);
                $doctoralArr = array_filter($doctoralArr);
                if(empty($doctoralArr)){
                    $doctoralArr[0] = '';
                }
                $i=0;
                foreach($doctoralArr as $k=>$doctoral):
                    $i++;
                    ?>
                    <tr data-row="<?=$k?>">
                        <td>
                            <input class="form-control text-left" type="text" name="doctoral[]" value="<?=$doctoral?>">
                        </td>
                        <td width="100" style="text-align: center">
                            <?php
                            if($i==count($doctoralArr)):
                                ?>
                                <button type="button" class="btn btn-primary add-row"><span class="fa fa-plus" aria-hidden="true"></span></button>
                            <?php
                            else:
                                ?>
                                <button type="button" class="btn btn-danger remove-row"><span class="fa fa-minus" aria-hidden="true"></span></button>
                            <?php
                            endif;
                            ?>
                        </td>
                    </tr>
                <?php
                endforeach;
                ?>
                </tbody>
            </table>
            <table class="table table-bordered">
                <tbody>
                <tr>
                    <td align="center">
                        已建相关基地名称
                    </td>
                    <td align="center">
                        组建时间
                    </td>
                    <td align="center">
                        所属部门或级别
                    </td>
                    <td width="100" align="center">
                        操作
                    </td>
                </tr>
                <?php
                $platforms = $project->getText('platform');
                $platformArr = json_decode($platforms,true);
                if(empty($platformArr)){
                    $platformArr[0] = '';
                }
                $i=0;
                foreach($platformArr as $k=>$platform):
                    $i++;
                    ?>
                    <tr data-row="<?=$k?>">
                        <td>
                            <input class="form-control text-left" type="text" name="platform[subject][]" value="<?=$platform['subject']?>">
                        </td>
                        <td>
                            <input class="form-control text-left" type="date" name="platform[build_at][]" value="<?=$platform['build_at']?>">
                        </td>
                        <td>
                            <input class="form-control text-left" type="text" name="platform[rank][]" value="<?=$platform['rank']?>">
                        </td>
                        <td width="100" style="text-align: center">
                            <?php
                            if($i==count($platformArr)):
                                ?>
                                <button type="button" class="btn btn-primary add-row"><span class="fa fa-plus" aria-hidden="true"></span></button>
                            <?php
                            else:
                                ?>
                                <button type="button" class="btn btn-danger remove-row"><span class="fa fa-minus" aria-hidden="true"></span></button>
                            <?php
                            endif;
                            ?>
                        </td>
                    </tr>
                <?php
                endforeach;
                ?>
                </tbody>
            </table>
            <table class="table table-bordered">
                <tbody>
                <tr>
                    <td align="center">
                        第一依托单位简介（不超过300字）
                    </td>
                </tr>
                <tr>
                    <td>
                        <textarea class="form-control" name="text[company_brief1]" cols="30" rows="10"><?=$project->getText('company_brief1')?></textarea>
                    </td>
                </tr>
                <tr>
                    <td align="center">
                        其他依托单位简介（每个单位不超过300字）
                    </td>
                </tr>
                <tr>
                    <td>
                        <textarea class="form-control" name="text[company_brief2]" cols="30" rows="10"><?=$project->getText('company_brief2')?></textarea>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="clearfix"></div>
            <div class="ex_tools" style="width:100px;margin:0 auto;margin-bottom: 30px">
                <?=Button::setType('submit')->setClass('btn-alt-primary btn-loading')->setDatas(['loading-text' => '保存中...'])->setIcon('save')->button('保存资料')?>
            </div>
        </form>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?=site_url('js/math.js')?>"></script>
<script type="text/javascript">
    $(document).on('click','.add-row',function(){
        //克隆行
        var rows = $(this).parents('#activeForm').find('other_corporations');
        var tr = $(this).parents('tr').clone();
        var line = rows.length;
        var row = tr.data('row');
        //替换行id
        var reg = '/\\['+row+'\\]/ig';
        var new_tr = tr.html().replace(eval(reg), '['+line+']');
        new_tr = "<tr data-row='"+line+"'>"+new_tr+"</tr>";
        //追加至表格
        $(this).parents('tbody').append(new_tr);
        //按钮变为减号
        $(this).removeClass('add-row').removeClass('btn-primary').addClass('remove-row').addClass('btn-danger');
        $(this).find('span').removeClass('fa-plus').addClass('fa-minus');
    });

    $(document).on('click','.remove-row',function(){
        $(this).parents('tr').remove();
    });
</script>