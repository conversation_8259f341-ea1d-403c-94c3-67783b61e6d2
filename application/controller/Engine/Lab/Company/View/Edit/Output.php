<div data-widget-id="<?=$widget_name?>">
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <caption>
            <span>依托单位基本信息</span>
            <?php if ($configs['action']['edit']): ?>
                <span style="float:right;">
                <?=Button::setName('编辑')->setUrl(site_url("engine/lab/company/edit/index/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setReloadWidget(['cover'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
                </span>
            <?php endif;?>
        </caption>
        <tr>
            <td align="center" width="120">第一依托单位<br>名称</td>
            <td colspan="3"><?=$project->getCorporationName()?></td>
        </tr>
        <tr>
            <td align="center">通讯地址</td>
            <td colspan="3"><?=$project->getData('company_address')?></td>
        </tr>
        <tr>
            <td align="center" width="120">法人代表</td>
            <td><?=$project->getData('company_frxm')?></td>
            <td align="center" width="120">电话</td>
            <td><?=$project->getData('lxrzj')?></td>
        </tr>
    </table>
    <?php
    $doctorals = $project->getData('doctoral');
    $doctoralArr = explode('$',$doctorals);
    $doctoralArr = array_filter($doctoralArr);
    ?>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <caption>
            <span>相关博士学位点</span>
        </caption>
        <tr>
            <td align="center" width="60">序号</td>
            <td align="center">博士学位点名称</td>
        </tr>
        <?php
        $i=0;
        foreach ($doctoralArr as $doctoral):
        ?>
            <tr>
                <td align="center""><?=++$i?></td>
                <td align="center"><?=$doctoral?></td>
            </tr>
        <?php endforeach;?>
        <?php
        if(empty($doctoralArr)):
        ?>
        <tr>
            <td colspan="2" align="center">无</td>
        </tr>
        <?php endif;?>
    </table>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <caption>
            <span>已建相关基地</span>
        </caption>
        <tbody>
        <tr>
            <td align="center" width="60">
                序号
            </td>
            <td align="center">
                已建相关基地名称
            </td>
            <td align="center">
                组建时间
            </td>
            <td align="center">
                所属部门或级别
            </td>
        </tr>
        <?php
        $platforms = $project->getText('platform');
        $platformArr = json_decode($platforms,true);
        if(empty($platformArr)){
            $platformArr[0] = '';
        }
        $i=0;
        foreach($platformArr as $k=>$platform):
            $i++;
            ?>
            <tr>
                <td align="center">
                    <?=$i?>
                </td>
                <td><?=$platform['subject']?></td>
                <td align="center"><?=$platform['build_at']?></td>
                <td align="center"><?=$platform['rank']?></td>
            </tr>
        <?php
        endforeach;
        ?>
        </tbody>
    </table>
    <?php if ($configs['company']['brief1']): ?>
        <div class="div_table" style="width: 680px;margin: 0 auto">
            <p class="div_table_title"><strong>第一依托单位简介</strong></p>
            <p class="div_table_content"><?= showText($project->getText('company_brief1')) ?></p>
        </div>
    <?php endif; ?>
    <?php if ($configs['company']['brief2']): ?>
        <div class="div_table" style="width: 680px;margin: 0 auto">
            <p class="div_table_title"><strong>其他依托单位简介</strong></p>
            <p class="div_table_content"><?= showText($project->getText('company_brief2')) ?></p>
        </div>
    <?php endif; ?>
</div>