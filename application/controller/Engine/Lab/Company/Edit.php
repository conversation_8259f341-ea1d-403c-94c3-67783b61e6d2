<?php
namespace App\Controller\Engine\Lab\Company;
use App\Controller\BaseController;
use Sofast\Core\lang;
use Sofast\Core\router;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Sf;

class edit extends BaseController
{
    private $project = NULL;
    private $configs = array();
    private $type = 'apply';

    function load()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        switch(trim(input::mix("engine")))
        {
            case 'tasker':
                $this->type = 'task';
                break;
            case 'completer':
                $this->type = 'complete';
                break;
            default:
                $this->type = 'apply';
                break;
        }
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->project->getWidgetConfigs('company',$this->type));
    }

    function index()
    {
        if(input::post()){
            $datas = input::post('data');
            $datas['doctoral'] = '';
            if(input::post('doctoral')){
                $datas['doctoral'] = implode('$',input::post('doctoral'));
            }
            $texts = input::post('text');
            $texts['platform'] = '';
            $platformArr = [];
            if($platforms = input::post('platform')){
                foreach ($platforms['subject'] as $k=>$subject){
                    $platformArr[$k]['subject'] = $subject;
                    $platformArr[$k]['build_at'] = $platforms['build_at'][$k];
                    $platformArr[$k]['rank'] = $platforms['rank'][$k];
                }
                $platformArr = array_filter($platformArr);
                $texts['platform'] = $platformArr;
            }

            $this->project->setDatas($datas);
            $this->project->setTexts($texts);
            $this->closeWindow();
        }
        $this->view->set('project',$this->project);
        $this->view->set('type',$this->type);
        $this->view->apply("inc_body", "edit/index");
        $this->view->display("page_blank");
    }

    function update()
    {
        copyCompanyInfo2Project($this->project,$this->type);
        $this->success('更新成功！','javascript:parent.closeWindow();');
    }
}