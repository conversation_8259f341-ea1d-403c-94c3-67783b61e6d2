<?php
namespace App\Controller\Engine\Lab\Company;
class Configs
{
    private static $default = array();
    private static $configs = array(
        'company'=>array('name'=>'依托单位信息简表',
                          'value'=>true,
                          'childs'=>array(
                              "basic"=>array('name'=>'基本情况','value'=>true),
                              "docter"=>array('name'=>'相关博士学位点','value'=>true),
                              "base"=>array('name'=>'已建相关基地','value'=>true),
                              "brief1"=>array('name'=>'第一依托单位简介','value'=>true),
                              "brief2"=>array('name'=>'其他依托单位简介','value'=>true),
                          ))
    );

    public static function getConfigs($key='')
    {
        if($key) return self::$configs[$key];
        else return self::$configs;
    }

    public static function getDefault($_configs=array(),$default=array())
    {
        if(count($_configs) == 0) $_configs = self::getConfigs();
        foreach($_configs as $_key => $_val){
            $data = array();
            if(count($_val['childs'])) $default[$_key] = self::getDefault($_val['childs']);
            else $default[$_key] = $_val['value'];
        }
        return $default;
    }

    public static function setConfigs(){}

}
?>