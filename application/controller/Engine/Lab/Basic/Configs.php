<?php
namespace App\Controller\Engine\Lab\Basic;
class Configs
{
    private static $default = array();
    private static $configs = array(
        'basic'=>array('name'=>'基本情况',
                          'value'=>true,
                          'childs'=>array(
                              "allow_edit"=>array('name'=>'允许修改','value'=>true),
                              "subject"=>array('name'=>'拟建实验室名称','value'=>true),
                              "corporation_name"=>array('name'=>'第一依托单位','value'=>true),
                              "department_name"=>array('name'=>'主管部门','value'=>true),
                              "corporation_property"=>array('name'=>'单位性质','value'=>true),
                              "other_corporation"=>array('name'=>'其他依托单位','value'=>true),
                              "sysdw"=>array('name'=>'实验室定位','value'=>true),
                              "yjly"=>array('name'=>'研究领域','value'=>true),
                              "lab_name"=>array('name'=>'现有实验室名称','value'=>true),
                              "open_at"=>array('name'=>'现有实验室对外开放时间','value'=>true),
                              "open_year"=>array('name'=>'现有实验室对外开放年限','value'=>true),
                              "device_value"=>array('name'=>'现有仪器与设备价值','value'=>true),
                              "area"=>array('name'=>'现有实验室占地面积','value'=>true),
                              "fund"=>array('name'=>'近五年经费投入','value'=>true),
                              "research_content"=>array('name'=>'主要研究方向及内容','value'=>true),
                          ))
    );

    public static function getConfigs($key='')
    {
        if($key) return self::$configs[$key];
        else return self::$configs;
    }

    public static function getDefault($_configs=array(),$default=array())
    {
        if(count($_configs) == 0) $_configs = self::getConfigs();
        foreach($_configs as $_key => $_val){
            $data = array();
            if(count($_val['childs'])) $default[$_key] = self::getDefault($_val['childs']);
            else $default[$_key] = $_val['value'];
        }
        return $default;
    }

    public static function setConfigs(){}

}
?>