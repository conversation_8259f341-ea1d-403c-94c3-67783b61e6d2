<link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
<script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>
<div class="block">
    <div class="block-header">
        <h3 class="block-title">
            基本情况
        </h3>
    </div>
    <div class="block-content">
        <form class="form-horizontal" name="validateForm" id="activeForm" action="" method="post">
            <input type="hidden" name="id" value="<?=$project->getProjectId()?>">
            <?php if($configs['basic']['subject']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">拟建实验室名称</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="subject" value="<?=$project->getSubject()?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['corporation_name']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">第一依托单位</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" readonly value="<?=$project->getCorporationName()?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['department_name']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">主管部门</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" readonly value="<?=$project->getDepartmentName()?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['corporation_property']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">单位性质</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <select class="form-control" name="data[corporation_property]" id="corporation_property" required>
                            <option value="">请选择</option>
                            <?=getSelectFromArray(['科研院所','高等院校','企业','其他'],$project->getData('corporation_property'))?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['other_corporation']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">其他依托单位</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <table class="table table-bordered">
                            <tbody>
                            <tr>
                                <td align="center">
                                    单位名称
                                </td>
                                <td align="center">
                                    单位性质
                                </td>
                                <td width="100" align="center">
                                    操作
                                </td>
                            </tr>
                            <?php
                            $others = $project->getCooperatationArray();
                            if(empty($others)){
                                $others[0] = [];
                            }
                            $i=0;
                            foreach($others as $k=>$other):
                                $i++;
                                ?>
                                <tr data-row="<?=$k?>">
                                    <td>
                                        <input class="form-control text-left" type="text" name="cooperatation[cooperatation_name][]" value="<?=$other['subject']?>">
                                    </td>
                                    <td>
                                        <select class="form-control" name="cooperatation[cooperatation_property][]" >
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(get_select_data('property'),$other['property'])?>
                                        </select>
                                    </td>
                                    <td width="100" style="text-align: center">
                                        <?php
                                        if($i==count($others)):
                                            ?>
                                            <button type="button" class="btn btn-primary add-row"><span class="fa fa-plus" aria-hidden="true"></span></button>
                                        <?php
                                        else:
                                            ?>
                                            <button type="button" class="btn btn-danger remove-row"><span class="fa fa-minus" aria-hidden="true"></span></button>
                                        <?php
                                        endif;
                                        ?>
                                    </td>
                                </tr>
                            <?php
                            endforeach;
                            ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['sysdw']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">实验室定位</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <select class="form-control" name="data[sysdw]" id="sysdw" required>
                            <option value="">请选择</option>
                            <?=getSelectFromArray(['基础研究','应用基础研究','前沿技术研究'],$project->getData('sysdw'))?>
                        </select>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['yjly']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">研究领域</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <div class="cxselect" data-selects="subject_id1,subject_id2,subject_id3" data-url="<?= site_path('json/subjects.json') ?>" data-json-value="v" style="float: left">
                            <select class="form-control w-auto custom-control-inline subject_id1" data-value="<?=$project->getSubjectIds(1)?>" name="subject_ids[subject_id1]"></select>
                            <select class="form-control w-auto custom-control-inline subject_id2" name="subject_ids[subject_id2]" data-first-title="请选择" data-value="<?=$project->getSubjectIds(2)?>"></select>
                            <select class="form-control w-auto custom-control-inline subject_id3" name="subject_ids[subject_id3]" data-first-title="请选择" data-value="<?=$project->getSubjectIds(3)?>"></select>
                        </div>
                        <div class="clearfix"></div>
                        <p>备注：请务必根据实际情况选择到最后一级。</p>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['lab_name']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">现有实验室名称</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[lab_name]" value="<?=$project->getData('lab_name')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['lab_name']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">现有实验室对外开放时间</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[open_at]" value="<?=$project->getData('open_at')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['lab_name']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">现有实验室对外开放年限</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <input type="text" class="form-control" name="data[open_year]" value="<?=$project->getData('open_year')?>" required>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['device_value']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">现有仪器与设备价值</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <div class="input-group">
                            <input type="number" step="0.01" min="0" name="data[device_value]" class="form-control" value="<?=$project->getData('device_value')?>">
                            <div class="input-group-append">
                                <span class="input-group-text">万元</span>
                            </div>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['device_value']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">现有实验室占地面积</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <div class="input-group">
                            <input type="number" step="0.01" min="0" name="data[area]" class="form-control" value="<?=$project->getData('area')?>">
                            <div class="input-group-append">
                                <span class="input-group-text">m<sup>2</sup></span>
                            </div>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['device_value']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">近五年经费投入</label>
                    <div class="col-xs-12 col-sm-5 col-md-5">
                        <div class="input-group">
                            <input type="number" step="0.01" min="0" name="data[fund]" class="form-control" value="<?=$project->getData('fund')?>">
                            <div class="input-group-append">
                                <span class="input-group-text">万元</span>
                            </div>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <?php if($configs['basic']['research_content']):?>
                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">主要研究方向及内容</label>
                    <div class="col-xs-12 col-sm-9 col-md-9">
                        <textarea name="text[research_content]" cols="30" rows="10" class="form-control"><?=$project->getText('research_content')?></textarea>
                    </div>
                    <div class="clearfix"></div>
                </div>
            <?php endif;?>
            <div class="clearfix"></div>
            <div class="ex_tools" style="width:100px;margin:0 auto;margin-bottom: 30px">
                <?=Button::setType('submit')->setClass('btn-alt-primary btn-loading')->setDatas(['loading-text' => '保存中...'])->setIcon('save')->button('保存资料')?>
            </div>
        </form>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script type="text/javascript">
    $(document).on('click','.add-row',function(){
        //克隆行
        var rows = $(this).parents('#activeForm').find('other_corporations');
        var tr = $(this).parents('tr').clone();
        var line = rows.length;
        var row = tr.data('row');
        //替换行id
        var reg = '/\\['+row+'\\]/ig';
        var new_tr = tr.html().replace(eval(reg), '['+line+']');
        new_tr = "<tr data-row='"+line+"'>"+new_tr+"</tr>";
        //追加至表格
        $(this).parents('tbody').append(new_tr);
        //按钮变为减号
        $(this).removeClass('add-row').removeClass('btn-primary').addClass('remove-row').addClass('btn-danger');
        $(this).find('span').removeClass('fa-plus').addClass('fa-minus');
    });

    $(document).on('click','.remove-row',function(){
        $(this).parents('tr').remove();
    });

    $('.cxselect').cxSelect();


</script>

