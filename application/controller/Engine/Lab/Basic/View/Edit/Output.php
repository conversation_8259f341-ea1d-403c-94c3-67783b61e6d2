<div data-widget-id="<?=$widget_name?>">
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <?php if ($configs['action']['edit']): ?>
        <caption>
            <span>基本情况</span>
                <span style="float:right;">
            <?=Button::setName('编辑')->setUrl(site_url("engine/lab/basic/edit/index/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setReloadWidget(['cover'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
        </span>
        </caption>
        <?php endif; ?>
        <?php if ($configs['basic']['subject']): ?>
            <tr>
                <td width="120" height="40" align="center"><b>拟建实验室名称</b></td>
                <td colspan="3"><?= $project->getSubject() ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['corporation_name']): ?>
            <tr>
                <td width="120" height="40" align="center"><b>第一依托单位</b></td>
                <td><?= $project->getCorporationName() ?></td>
                <td width="120" height="40" align="center"><b>主管部门</b></td>
                <td><?= $project->getDepartmentName() ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['corporation_property']): ?>
            <tr>
                <td height="40" align="center"><b>单位性质</b></td>
                <td colspan="3"><?= getCheckedStr(['科研院所','高等院校','企业','其他'],$project->getData('corporation_property')) ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['other_corporation']): ?>
            <tr>
                <td height="40" align="center"><b>其他依托单位</b></td>
                <td colspan="3"><?= $project->getCooperationStr() ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['sysdw']): ?>
            <tr>
                <td height="40" align="center"><b>实验室定位</b></td>
                <td colspan="3"><?= getCheckedStr(['基础研究','应用基础研究','前沿技术研究'],$project->getData('sysdw')) ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['yjly']): ?>
            <tr>
                <td height="40" align="center"><b>研究领域</b></td>
                <td colspan="3"><?=$project->getSubjectPath(3)?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['lab_name']): ?>
            <tr>
                <td height="40" align="center"><b>现有实验室名称</b></td>
                <td colspan="3"><?=$project->getData('lab_name')?></td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['open_at']): ?>
            <tr>
                <td width="120" height="40" align="center"><b>现有实验室对外开放时间及年限</b></td>
                <td>开放时间：<u>&nbsp;<?= $project->getData('open_at') ?>&nbsp;</u>，年限：<u>&nbsp;<?= $project->getData('open_year') ?>&nbsp;</u></td>
                <td width="120" height="40" align="center"><b>现有仪器与设备价值</b></td>
                <td><u>&nbsp;<?= $project->getData('device_value') ?>&nbsp;</u>万元</td>
            </tr>
        <?php endif; ?>
        <?php if ($configs['basic']['area']): ?>
            <tr>
                <td width="120" height="40" align="center"><b>现有实验室占地面积</b></td>
                <td><u>&nbsp;<?= $project->getData('area') ?>&nbsp;</u>m<sup>2</sup></td>
                <td width="120" height="40" align="center"><b>近五年经费投入</b></td>
                <td><u>&nbsp;<?= $project->getData('fund') ?>&nbsp;</u>万元</td>
            </tr>
        <?php endif; ?>
    </table>
    <?php if ($configs['basic']['research_content']): ?>
      <div class="div_table" style="width: 680px;margin: 0 auto">
            <p class="div_table_title"><strong>主要研究方向及内容</strong>(限300字)</p>
            <p class="div_table_content"><?= showText($project->getText('research_content')) ?></p>
      </div>
    <?php endif; ?>
</div>