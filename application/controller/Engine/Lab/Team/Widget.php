<?php
namespace App\Controller\Engine\Lab\Team;
use Sofast\Core\Sf;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use App\Contract\iWidget;

class Widget implements iWidget
{
    private $project = NULL;
    /**
     * 应用层配置
     */
    private $configs = array();
    /**
     * 模块层配置
     */
    private $widgetConfigs = array();
    /**
     * 模板对象
     */
    private $view = NULL;

	private $widget_name = 'team';

    /**
     * 数据模式
     */
    private $type = 'Apply';
    
	function __construct($widget_name,$configs = NULL)
	{
        $this->widget_name = $widget_name;
        $this->widgetConfigs = Configs::getConfigs();
        if($configs !== NULL){
            $this->configs = $configs;
        }else $this->configs = Configs::getDefault();
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->view->set('widget_name',$this->widget_name);
        return $this;
    }

    public function setProject($project)
    {
        $this->project = $project;
        return $this;
    }

    public function getWidgetName()
    {
        return $this->widget_name;
    }

    public function setConfig($configs=array())
    {
        if(count($configs)) $this->configs = $configs;
        return $this;
    }

    public function getConfig()
    {
        return $this->configs;
    }

    public function output($tpl='Edit')
    {
        $configs = $this->getConfig();
        switch($configs['engine'])
        {
            case 'tasker':
                $type = 'task';
                break;
            case 'completer':
                $type = 'complete';
                break;
            default:
                $type = 'apply';
                break;
        }
        $this->view->set("project",$this->project);
        $this->view->set("configs",$configs);
        $this->view->set("type",$type);
        if($tpl) return $this->view->getContent($tpl."/Output");
        else{
            $htmlStr = '';
            foreach($this->configs as $_key => $_val){
                if(count($_val)) $htmlStr .= $this->view->getContent($_key."/Output");
            }
            return $htmlStr;
        }
    }

    /**
     * 模块配置
     */
    public function manager()
    {
        $this->view->set("configs",$this->configs);
        $this->view->set("widgetConfigs",$this->widgetConfigs);
        return $this->view->getContent("Manager");
    }
	
	/**
	 * 完整性检验
	 * 返回数组
	 */
    function validate()
    {
        $message = array();
        if($this->configs['baseinfo']['sdyy'] && empty($this->project->getBaseinfo('apply')->getSdyy())){
            $message[] = "【一、基本信息】还未填写是否是紧密型城市医疗集团试点城市内牵头医院";
        }
//        if($baseinfo = $this->project->getBaseinfo())
//        {
//            if(empty($this->project->getSubject())) $message[] = "还未填写项目名称";
//            if($this->getCatId()==15 && empty($this->project->getProjectType())) $message[] = "还未选择项目类别";
//        }
//        if($this->project->isInPlan() && !$this->project->getPlanId()){
//            $message[] = "计划内项目请选择所属计划";
//        }
//		//技术领域
//		if($this->configs['baseinfo']['skill_domain']){
//			if(strlen($this->project->getSkillId()) < 3) $message[] = "项目技术领域必须正确选填";
//			if(hasSon($this->project->getSkillId(),4)) $message[] = "项目技术领域必须选择到最后一级";
//		}
//		//学科方向
//		if($this->configs['baseinfo']['subject_code']){
//			if(strlen($this->project->getSubjectId()) < 3) $message[] = "研究领域必须正确选填";
//			if(hasSon($this->project->getSubjectId(),1)) $message[] = "研究领域必须选择到最后一级";
//		}
        return $message;
    }

    function getFileListHtml()
    {
        return '';
    }

    function __call($tpl,$args)
    {
        return $this->output($tpl);
    }

    function __toString()
    {
        return $this->output();
    }
}
?>