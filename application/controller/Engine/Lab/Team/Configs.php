<?php
namespace App\Controller\Engine\Lab\Team;
class Configs
{
    private static $default = array();
    private static $configs = array(
        'team'=>array('name'=>'学术队伍组成',
                          'value'=>true,
                          'childs'=>array(
                          ))
    );

    public static function getConfigs($key='')
    {
        if($key) return self::$configs[$key];
        else return self::$configs;
    }

    public static function getDefault($_configs=array(),$default=array())
    {
        if(count($_configs) == 0) $_configs = self::getConfigs();
        foreach($_configs as $_key => $_val){
            $data = array();
            if(count($_val['childs'])) $default[$_key] = self::getDefault($_val['childs']);
            else $default[$_key] = $_val['value'];
        }
        return $default;
    }

    public static function setConfigs(){}

}
?>