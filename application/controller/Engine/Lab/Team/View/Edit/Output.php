<div data-widget-id="<?=$widget_name?>">
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <?php if ($configs['action']['edit']): ?>
        <caption>
                <span style="float:right;">
            <?=Button::setName('编辑')->setUrl(site_url("engine/lab/team/edit/index/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
        </span>
        </caption>
        <?php endif; ?>
        <tr>
            <td colspan="2" align="center" style="width: 16%">类别（人）</td>
            <td align="center" style="width: 14%">合计</td>
            <td align="center" style="width: 14%">正高级职称</td>
            <td align="center" style="width: 14%">副高级职称</td>
            <td align="center" style="width: 14%">博士生导师</td>
            <td align="center" style="width: 14%">博士</td>
            <td align="center" style="width: 14%">硕士</td>
        </tr>
        <?php
        $total = $project->getData('fix_total')->getData()+$project->getData('flow_total')->getData();
        $totalSeniors = $project->getData('fix_seniors')->getData()+$project->getData('flow_seniors')->getData();
        $totalAdvanced = $project->getData('fix_advanced')->getData()+$project->getData('flow_advanced')->getData();
        $totalProfessor = $project->getData('fix_professor')->getData()+$project->getData('flow_professor')->getData();
        $totalDocter = $project->getData('fix_docter')->getData()+$project->getData('flow_docter')->getData();
        $totalMaster = $project->getData('fix_master')->getData()+$project->getData('flow_master')->getData();
        ?>
        <tr>
            <td colspan="2" align="center">固定人员</td>
            <td align="center" align="center">
                <?=$project->getData('fix_total')?>
            </td>
            <td align="center">
                <?=$project->getData('fix_seniors')?>
            </td>
            <td align="center">
                <?=$project->getData('fix_advanced')?>
            </td>
            <td align="center">
                <?=$project->getData('fix_professor')?>
            </td>
            <td align="center">
                <?=$project->getData('fix_docter')?>
            </td>
            <td align="center">
                <?=$project->getData('fix_master')?>
            </td>
        </tr>
        <tr>
            <td colspan="2" align="center" >客座人员</td>
            <td align="center" align="center">
                <?=$project->getData('flow_total')?>
            </td>
            <td align="center">
                <?=$project->getData('flow_seniors')?>
            </td>
            <td align="center">
                <?=$project->getData('flow_advanced')?>
            </td>
            <td align="center">
                <?=$project->getData('flow_professor')?>
            </td>
            <td align="center">
                <?=$project->getData('flow_docter')?>
            </td>
            <td align="center">
                <?=$project->getData('flow_master')?>
            </td>
        </tr>
        <tr>
            <td colspan="2" align="center">合计</td>
            <td align="center" align="center">
                <?=$total?></td>
            <td align="center" align="center">
                <?=$totalSeniors?></td>
            <td align="center" align="center">
                <?=$totalAdvanced?></td>
            <td align="center" align="center">
                <?=$totalProfessor?></td>
            <td align="center" align="center">
                <?=$totalDocter?></td>
            <td align="center" align="center">
                <?=$totalMaster?></td>
        </tr>
    </table>
</div>