<div class="block">
    <div class="block-header">
        <h3 class="block-title">
            学术队伍组成
        </h3>
    </div>
    <div class="block-content">
        <form class="form-horizontal" name="validateForm" id="activeForm" action="" method="post">
            <input type="hidden" name="id" value="<?=$project->getProjectId()?>">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <td colspan="2" align="center" style="width: 16%">类别（人）</td>
                    <td align="center" style="width: 14%">合计</td>
                    <td align="center" style="width: 14%">正高级职称</td>
                    <td align="center" style="width: 14%">副高级职称</td>
                    <td align="center" style="width: 14%">博士生导师</td>
                    <td align="center" style="width: 14%">博士</td>
                    <td align="center" style="width: 14%">硕士</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td colspan="2" align="center">固定人员</td>
                    <td align="center" align="center">
                        <input name="data[fix_total]" type="text" class="form-control int" value="<?=$project->getData('fix_total')?>">
                    </td>
                    <td align="center">
                        <input name="data[fix_seniors]" type="text" class="form-control int" value="<?=$project->getData('fix_seniors')?>">
                    </td>
                    <td align="center">
                        <input name="data[fix_advanced]" type="text" class="form-control int" value="<?=$project->getData('fix_advanced')?>">
                    </td>
                    <td align="center">
                        <input name="data[fix_professor]" type="text" class="form-control int" value="<?=$project->getData('fix_professor')?>">
                    </td>
                    <td align="center">
                        <input name="data[fix_docter]" type="text" class="form-control int" value="<?=$project->getData('fix_docter')?>">
                    </td>
                    <td align="center">
                        <input name="data[fix_master]" type="text" class="form-control int" value="<?=$project->getData('fix_master')?>">
                    </td>
                </tr>
                <tr>
                    <td colspan="2" align="center" >客座人员</td>
                    <td align="center" align="center">
                        <input name="data[flow_total]" type="text" class="form-control int" value="<?=$project->getData('flow_total')?>">
                    </td>
                    <td align="center">
                        <input name="data[flow_seniors]" type="text" class="form-control int" value="<?=$project->getData('flow_seniors')?>">
                    </td>
                    <td align="center">
                        <input name="data[flow_advanced]" type="text" class="form-control int" value="<?=$project->getData('flow_advanced')?>">
                    </td>
                    <td align="center">
                        <input name="data[flow_professor]" type="text" class="form-control int" value="<?=$project->getData('flow_professor')?>">
                    </td>
                    <td align="center">
                        <input name="data[flow_docter]" type="text" class="form-control int" value="<?=$project->getData('flow_docter')?>">
                    </td>
                    <td align="center">
                        <input name="data[flow_master]" type="text" class="form-control int" value="<?=$project->getData('flow_master')?>">
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="clearfix"></div>
            <div class="ex_tools" style="width:100px;margin:0 auto;margin-bottom: 30px">
                <?=Button::setType('submit')->setClass('btn-alt-primary btn-loading')->setDatas(['loading-text' => '保存中...'])->setIcon('save')->button('保存资料')?>
            </div>
        </form>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?=site_url('js/math.js')?>"></script>
