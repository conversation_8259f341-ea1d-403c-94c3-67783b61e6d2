<div data-widget-id="<?=$widget_name?>">
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <caption>
            <span>学术交流</span>
            <?php if ($configs['action']['edit']): ?>
                <span style="float:right;">
                <?=Button::setName('编辑')->setUrl(site_url("engine/lab/meet/edit/index/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
                </span>
            <?php endif; ?>
        </caption>
        <tr>
            <td align="center" style="width: 33.3%">近五年参加国际学术会议（次）</td>
            <td align="center" style="width: 33.3%">近五年参加全国学术会议（次）</td>
            <td align="center" style="width: 33.3%">近五年参加全省学术会议（次）</td>
        </tr>
        <tr>
            <td align="center" height="33"><?=$project->getData('meet_gjhy')?></td>
            <td align="center"><?=$project->getData('meet_qghy')?></td>
            <td align="center"><?=$project->getData('meet_qshy')?></td>
        </tr>
    </table>
</div>