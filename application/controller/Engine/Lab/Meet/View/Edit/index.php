<div class="block">
    <div class="block-header">
        <h3 class="block-title">
            学术交流
        </h3>
    </div>
    <div class="block-content">
        <form class="form-horizontal" name="validateForm" id="activeForm" action="" method="post">
            <input type="hidden" name="id" value="<?=$project->getProjectId()?>">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <td align="center" style="width: 33.3%">近五年参加国际学术会议（次）</td>
                    <td align="center" style="width: 33.3%">近五年参加全国学术会议（次）</td>
                    <td align="center" style="width: 33.3%">近五年参加全省学术会议（次）</td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td align="center" height="33"><input name="data[meet_gjhy]" type="text" class="form-control int" value="<?=$project->getData('meet_gjhy')?>"></td>
                    <td align="center" height="33"><input name="data[meet_qghy]" type="text" class="form-control int" value="<?=$project->getData('meet_qghy')?>"></td>
                    <td align="center" height="33"><input name="data[meet_qshy]" type="text" class="form-control int" value="<?=$project->getData('meet_qshy')?>"></td>
                </tr>
                </tbody>
            </table>
            <div class="clearfix"></div>
            <div class="ex_tools" style="width:100px;margin:0 auto;margin-bottom: 30px">
                <?=Button::setType('submit')->setClass('btn-alt-primary btn-loading')->setDatas(['loading-text' => '保存中...'])->setIcon('save')->button('保存资料')?>
            </div>
        </form>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?=site_url('js/math.js')?>"></script>
