<div data-widget-id="<?=$widget_name?>">
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <?php if ($configs['action']['edit']): ?>
        <caption>
            <span>近五年承担科研项目数量（项）</span>
                <span style="float:right;">
            <?=Button::setName('编辑')->setUrl(site_url("engine/lab/project/edit/index/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
        </span>
        </caption>
        <?php endif; ?>
        <tr>
            <td height="40" align="center">立项年度</td>
            <td height="40" align="center">国家级项目</td>
            <td height="40" align="center">省部级项目</td>
            <td height="40" align="center">厅市级项目</td>
            <td height="40" align="center">横向项目</td>
            <td height="40" align="center">其他项目</td>
        </tr>
        <?php
        $currentYear = $project->getDeclareYear();
        $nationTotal = $provinceTotal = $cityTotal = $hxTotal = $qtTotal = 0;
        for($i=1;$i<6;$i++):
            $currentYear--;
            $nationTotal+=(int)$project->getData('project_nation',$currentYear)->getData();
            $provinceTotal+=(int)$project->getData('project_province',$currentYear)->getData();
            $cityTotal+=(int)$project->getData('project_city',$currentYear)->getData();
            $hxTotal+=(int)$project->getData('project_hx',$currentYear)->getData();
            $qtTotal+=(int)$project->getData('project_qt',$currentYear)->getData();
            ?>
            <tr>
                <td class="text-center"><?=$currentYear?></td>
                <td class="text-center">
                    <?=$project->getData('project_nation',$currentYear)->getData()?>
                </td>
                <td class="text-center">
                    <?=$project->getData('project_province',$currentYear)->getData()?>
                </td>
                <td class="text-center">
                    <?=$project->getData('project_city',$currentYear)->getData()?>
                </td>
                <td class="text-center">
                    <?=$project->getData('project_hx',$currentYear)->getData()?>
                </td>
                <td class="text-center">
                    <?=$project->getData('project_qt',$currentYear)->getData()?>
                </td>
            </tr>
        <?php
        endfor;
        ?>
        <tr>
            <td class="text-center">合计</td>
            <td class="text-center">
                <?=$nationTotal?>
            </td>
            <td class="text-center">
                <?=$provinceTotal?>
            </td>
            <td class="text-center">
                <?=$cityTotal?>
            </td>
            <td class="text-center">
                <?=$hxTotal?>
            </td>
            <td class="text-center">
                <?=$qtTotal?>
            </td>
        </tr>
    </table>

</div>