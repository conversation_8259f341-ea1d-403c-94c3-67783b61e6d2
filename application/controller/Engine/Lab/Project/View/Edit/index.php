<div class="block">
    <div class="block-header">
        <h3 class="block-title">
            近五年承担科研项目数量（项）
        </h3>
    </div>
    <div class="block-content">
        <form class="form-horizontal" name="validateForm" id="activeForm" action="" method="post">
            <input type="hidden" name="id" value="<?=$project->getProjectId()?>">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th class="text-center">立项年度</th>
                    <th class="text-center">国家级项目</th>
                    <th class="text-center">省部级项目</th>
                    <th class="text-center">厅市级项目</th>
                    <th class="text-center">横向项目</th>
                    <th class="text-center">其他项目</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $currentYear = $project->getDeclareYear();
                $nationTotal = $provinceTotal = $cityTotal = $hxTotal = $qtTotal = 0;
                for($i=1;$i<6;$i++):
                    $currentYear--;
                    ?>
                    <tr>
                        <td class="text-center"><?=$currentYear?>
                            <input type="hidden" name="year[]" value="<?=$currentYear?>"></td>
                        <td class="text-center">
                            <div class="input-group">
                                <input name="<?=$currentYear?>[project_nation]" type="number" min="0" class="form-control" value="<?=$project->getData('project_nation',$currentYear)?>">
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="input-group">
                                <input name="<?=$currentYear?>[project_province]" type="number" min="0" class="form-control" value="<?=$project->getData('project_province',$currentYear)?>">
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="input-group">
                                <input name="<?=$currentYear?>[project_city]" type="number" min="0" class="form-control" value="<?=$project->getData('project_city',$currentYear)?>">
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="input-group">
                                <input name="<?=$currentYear?>[project_hx]" type="number" min="0" class="form-control" value="<?=$project->getData('project_hx',$currentYear)?>">
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="input-group">
                                <input name="<?=$currentYear?>[project_qt]" type="number" min="0" class="form-control" value="<?=$project->getData('project_qt',$currentYear)?>">
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php
                endfor;
                ?>
                </tbody>
            </table>
            <p>
                注：①项目类型：国家级一般指科技部、国家发展改革委、财政部、自然科学基金委、国家社科基金委下达的项目；省部级一般指科技厅、省发展改革委、财政厅下达的项目，以及除了科技部、国家发展改革委、财政部以外的国家其它部委下达的部级项目；其他一般指市级项目以及除了科技厅、省发展改革委、财政厅以外的其他省厅级、局级项目等；②数据报告期：近5年的数据，并与编写提纲附件数据一致。
            </p>
            <div class="clearfix"></div>
            <div class="ex_tools" style="width:100px;margin:0 auto;margin-bottom: 30px">
                <?=Button::setType('submit')->setClass('btn-alt-primary btn-loading')->setDatas(['loading-text' => '保存中...'])->setIcon('save')->button('保存资料')?>
            </div>
        </form>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
