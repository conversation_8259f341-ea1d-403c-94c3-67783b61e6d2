<?php
namespace App\Controller\Engine\Lab\Member;
class Configs
{
    private static $default = array();
    private static $configs = array(
        'member'=>array('name'=>'现有学术队伍',
            'value'=>true,
            'readme'=>true,
            'childs'=>array(
                "no"=>array('name'=>'序号','value'=>true),
                "user_type"=>array('name'=>'类别','value'=>false),
                "name"=>array('name'=>'姓名','value'=>true),
                "card_type"=>array('name'=>'证件类型','value'=>true),
                "certificate"=>array('name'=>'证件号','value'=>true),
                "sex"=>array('name'=>'性别','value'=>true),
                "age"=>array('name'=>'年龄','value'=>true),
                "birthday"=>array('name'=>'出生年月','value'=>true),
                "education"=>array('name'=>'学历','value'=>true),
                "degree"=>array('name'=>'学位','value'=>true),
                "title"=>array('name'=>'职称','value'=>true),
                "honor"=>array('name'=>'荣誉称号','value'=>true),
                "major"=>array('name'=>'从事专业','value'=>true),
                "mobile"=>array('name'=>'电话','value'=>true),
                "company_name"=>array('name'=>'所在单位','value'=>true),
                "duty"=>array('name'=>'职务','value'=>true),
                "works"=>array('name'=>'项目分工','value'=>true),
				"time"=>array('name'=>'投入本项目的工作时间','value'=>false),
                "sign"=>array('name'=>'签字','value'=>true),
            )),
        'hidden_caption'=>array('name'=>'隐藏标题',
            'value'=>false,
            'childs'=>array(

            )),
    );

    public static function getConfigs($key='')
    {
        if($key) return self::$configs[$key];
        else return self::$configs;
    }

    public static function getDefault($_configs=array(),$default=array())
    {
        if(count($_configs) == 0) $_configs = self::getConfigs();
        foreach($_configs as $_key => $_val){
            $data = array();
            if(count($_val['childs'])) $default[$_key] = self::getDefault($_val['childs']);
            else $default[$_key] = $_val['value'];
        }
        return $default;
    }

    public static function setConfigs(){}
	
}
?>