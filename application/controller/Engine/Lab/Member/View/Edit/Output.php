<div data-widget-id="<?=$widget_name?>">
    <?php if($configs['member']):?>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" style="font-size:14px;font-family:'黑体';overflow:wrap;" class="abc">
        <caption>
            现有学术队伍
        </caption>
    </table>
    <?=getReadMe($configs)?>
        <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" style="font-size:14px;font-family:'黑体';overflow:wrap;" class="abc">
            <caption>
                <?php if($configs['action']['edit']):?>
                    <span style="float:right;">
                        <!--
                        <?=Button::setName('导入')->setUrl(site_url("engine/excel/tpl/index/type/team/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('import')->setClass('btn-success')->widget()?>
                        -->
                        <?=Button::setName('添加')->setUrl(site_url("engine/lab/member/edit/team/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
                    </span>
                <?php endif;?>
            </caption>
        <tr>
            <td width="5%" align="center">序号</td>
            <td align="center">姓名</td>
            <td align="center">性别</td>
            <td align="center">年龄</td>
            <td align="center">学位</td>
            <td align="center">职称/<br />荣誉称号</td>
            <td align="center">专业</td>
            <td align="center">所在单位及职务</td>
            <td align="center">联系电话</td>
            <?php if($configs['action']['edit']):?>
            <td align="center" width="70px">操作</td>
            <?php endif;?>
        </tr>
        <tr>
            <td colspan="<?=!$configs['action']['download']?10:9?>" align="center">实验室负责人</td>
        </tr>
        <?php
        $members = $project->getMembers('apply','principal');
        $count = 0;
        $age = 0;
        while($member = $members->getObject()):$count++;$age+=$member->getAge()?>
            <tr>
                <td height="40" align="center"><?=$members->getIndex()?></td>
                <td align="center"><?=$member->getName()?></td>
                <td align="center"><?=$member->getSex()?></td>
                <td align="center"><?=$member->getAge()?></td>
                <td align="center"><?=$member->getDegree()?></td>
                <td align="center"><?=$member->getTitleType().($member->getHonor()?'/'.$member->getHonor():'')?></td>
                <td align="center"><?=$member->getMajor()?></td>
                <td align="center"><?=$member->getCompanyName()?> <?=$member->getDuty()?></td>
                <td align="center"><?=$member->getMobile()?></td>
                <?php if($configs['action']['edit']):?>
                <td align="center">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?php if($configs['action']['edit']):?>
                    <?=Button::setName('编辑')->setUrl(site_url("engine/lab/member/edit/team/engine/".$configs['engine']."/mid/".$member->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-primary')->widget()?>
                    <?=Button::setName('删除')->setUrl(site_url("engine/lab/member/edit/remove/engine/".$configs['engine']."/mid/".$member->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-danger')->widget()?>
                        <?php endif;?>
                    </div>
                </td>
                <?php endif;?>
            </tr>
        <?php endwhile;?>
        <?php
        if($members->getTotal()==0):
            ?>
            <tr>
                <td colspan="<?=!$configs['action']['download']?10:9?>" align="center">无</td>
            </tr>
        <?php endif;?>
        <tr>
            <td colspan="<?=!$configs['action']['download']?10:9?>" align="center">拟聘学术委员会主任</td>
        </tr>
        <?php
        $members = $project->getMembers('apply','chairman');
        $count = 0;
        $age = 0;
        while($member = $members->getObject()):$count++;$age+=$member->getAge()?>
            <tr>
                <td height="40" align="center"><?=$members->getIndex()?></td>
                <td align="center"><?=$member->getName()?></td>
                <td align="center"><?=$member->getSex()?></td>
                <td align="center"><?=$member->getAge()?></td>
                <td align="center"><?=$member->getDegree()?></td>
                <td align="center"><?=$member->getTitleType().($member->getHonor()?'/'.$member->getHonor():'')?></td>
                <td align="center"><?=$member->getMajor()?></td>
                <td align="center"><?=$member->getCompanyName()?> <?=$member->getDuty()?></td>
                <td align="center"><?=$member->getMobile()?></td>
                <?php if($configs['action']['edit']):?>
                <td align="center">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?php if($configs['action']['edit']):?>
                    <?=Button::setName('编辑')->setUrl(site_url("engine/lab/member/edit/team/engine/".$configs['engine']."/mid/".$member->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-primary')->widget()?>
                    <?=Button::setName('删除')->setUrl(site_url("engine/lab/member/edit/remove/engine/".$configs['engine']."/mid/".$member->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-danger')->widget()?>
                        <?php endif;?>
                    </div>
                </td>
                <?php endif;?>
            </tr>
        <?php endwhile;?>
        <?php
        if($members->getTotal()==0):
            ?>
            <tr>
                <td colspan="<?=!$configs['action']['download']?10:9?>" align="center">无</td>
            </tr>
        <?php endif;?>
        <tr>
            <td colspan="<?=!$configs['action']['download']?10:9?>" align="center">各研究方向学术带头人（研究方向一般不超过4个）</td>
        </tr>
        <?php
        $members = $project->getMembers('apply','foregoer');
        $count = 0;
        $age = 0;
        while($member = $members->getObject()):$count++;$age+=$member->getAge()?>
            <tr>
                <td height="40" align="center"><?=$members->getIndex()?></td>
                <td align="center"><?=$member->getName()?></td>
                <td align="center"><?=$member->getSex()?></td>
                <td align="center"><?=$member->getAge()?></td>
                <td align="center"><?=$member->getDegree()?></td>
                <td align="center"><?=$member->getTitleType().($member->getHonor()?'/'.$member->getHonor():'')?></td>
                <td align="center"><?=$member->getMajor()?></td>
                <td align="center"><?=$member->getCompanyName()?> <?=$member->getDuty()?></td>
                <td align="center"><?=$member->getMobile()?></td>
                <?php if($configs['action']['edit']):?>
                <td align="center">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?php if($configs['action']['edit']):?>
                    <?=Button::setName('编辑')->setUrl(site_url("engine/lab/member/edit/team/engine/".$configs['engine']."/mid/".$member->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-primary')->widget()?>
                    <?=Button::setName('删除')->setUrl(site_url("engine/lab/member/edit/remove/engine/".$configs['engine']."/mid/".$member->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-danger')->widget()?>
                        <?php endif;?>
                    </div>
                </td>
                <?php endif;?>
            </tr>
        <?php endwhile;?>
        <?php
        if($members->getTotal()==0):
            ?>
            <tr>
                <td colspan="<?=!$configs['action']['download']?10:9?>" align="center">无</td>
            </tr>
        <?php endif;?>
    </table>
    <?php endif;?>
  </div>