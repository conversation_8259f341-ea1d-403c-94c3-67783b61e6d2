<script>
    //选择职称级别
    $(document).on("change","#title_type",function(){
        var id= $(this).val();
        if(id==0){
            $('#title').html('<option value="0">==请选择==</option>');
            return;
        }
        var url = baseurl+'common/HonorAjax';
        ajaxData(url,{id:id},'html',function(){},function(data){
            $('#title').html(data);
        });
    });
</script>
<div class="block">
    <div class="block-header">
        <h3 class="block-title">
            现有学术队伍
        </h3>
    </div>
    <div class="block-content">
        <div class="page-body">
            <div class="tab-content">
                <div class="tab-pane active">
                    <div role="tabpanel" class="tab-pane active">
                        <form class="form-horizontal" name="form1" id="activeForm" action="" method="post">

                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">序号</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <input type="number" name="no" id="no" required class="form-control required" value="<?=$member->getNo()?>" min="1" />
                                    <p class="helper-block">数字越小，排名越靠前</p>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php
                            if($configs['member']['user_type']):
                                ?>
                                <?php
                                $memberTypes = [
                                    'principal'=>'实验室负责人',
                                    'chairman'=>'拟聘学术委员会主任',
                                    'foregoer'=>'各研究方向学术带头人'
                                ];
                                ?>
                                <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                    <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">人员类别</label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <select name="user_type" id="user_type" class="form-control" required>
                                            <option value="">==请选择==</option>
                                            <?=getSelectFromArray($memberTypes,$member->getUserType(),false)?>
                                        </select>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                            <?php endif;?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">姓名</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <input type="text" name="name" id="name" required class="form-control required" value="<?=$member->getName()?>" />
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php
                            if($configs['member']['card_type']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">证件类型</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <select name="card_type" id="card_type" class="form-control">
                                        <?=getSelectFromArray(array('身份证','港澳台居民居住证','护照','军官证','其他证件'),$member->getCardType())?>
                                    </select>
                                    <p class="help-block">除外籍人士原则上都应选择身份证。</p>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['certificate']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">证件号</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <input type="text" name="certificate" id="certificate" required class="form-control" value="<?=$member->getCertificate()?>" onblur="getInfo(this)"/>
                                    <p class="help-block">除外籍人士原则上都应输入身份证号码。</p>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['sex']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">性别</label>
                                <div class="col-xs-12 col-sm-3 col-md-3">
                                    <?=get_radio(['男','女'],'sex',$member->getSex())?>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['age']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">年龄</label>
                                <div class="col-xs-12 col-sm-3 col-md-3" >
                                    <input type="number" name="age" id="age" required class="form-control required" value="<?=$member->getAge()?>" min="1" />
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['education']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">学历</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <select name="education" id="education" class="form-control">
                                        <option value="">==请选择==</option>
                                        <?=getSelectFromArray(get_select_data('education'),$member->getEducation())?>
                                    </select>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['degree']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">学位</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <select name="degree" id="degree" class="form-control">
                                        <option value="">==请选择==</option>
                                        <?=getSelectFromArray(get_select_data('degree'),$member->getDegree())?>
                                    </select>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['title']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">职称</label>
                                <div class="col-xs-12 col-sm-9 col-md-9">
                                    <select name="title_type" id="title_type" required class="form-control w-auto float-left">
                                        <?=$data=['0'=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'初级','636'=>'其他']?>
                                        <?=getSelectFromArray($data,array_search($member->getTitleType(),$data)?:'0',false)?>
                                    </select>

                                <select name="title" id="title" required class="form-control w-auto">
                                    <?=getOptionByParent(array_search($member->getTitleType(),$data),$member->getTitle())?>
                                </select>

                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['honor']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">荣誉称号</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <input class="form-control" type="text" name="honor" id="honor" value="<?=$member->getHonor()?>">
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['major']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">从事专业</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <input class="form-control" type="text" name="major" id="major" value="<?=$member->getMajor()?>">
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['mobile']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">联系电话</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <input class="form-control" type="text" name="mobile" id="mobile" value="<?=$member->getMobile()?>">
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['company_name']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">所在单位</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <input class="form-control" type="text" name="company_name" id="company_name" value="<?=$member->getCompanyName()?>">
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <?php
                            if($configs['member']['duty']):
                            ?>
                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <label class="control-label col-xs-12 col-sm-3 col-md-3 text-right">职务</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <input class="form-control" type="text" name="duty" id="duty" value="<?=$member->getDuty()?>">
                                </div>
                                <div class="clearfix"></div>
                            </div>
                            <?php endif;?>
                            <div class="clearfix"></div>

                            <div class="form-group row col-xs-12 col-sm-12 col-md-12">
                                <div style="width:400px;margin: 0 auto">
                                    <input type="hidden" name="continue" id="continue" value="0">
                                    <?php
                                    if($member->isNew()):
                                        ?>
                                        <?=btn('button','提交并继续添加','button','save','','btn-sm','btn-success btn-continue')?>
                                    <?php
                                    endif;
                                    ?>
                                    <?=btn('button','保存资料','submit','save')?>
                                    <?=btn('button','关闭窗口','button','close','closeWindow()','btn-sm','btn-alt-danger')?>
                                </div>
                            </div>

                        </form>
                        <p style="clear:both"></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="page-footer">

        </div>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script>
    $(function () {
        $(".btn-continue").click(function () {
            $("#continue").val(1);
            form1.submit();
        });

        $("#personnel_type").change(function (){
            if($(this).val()=='技师（治疗师）'){
                $(".submajor").show();
                $("#submajor").prop('required',true);
            }else{
                $(".submajor").hide();
                $("#submajor").prop('required',false);
            }
        });
    });

    function getInfo(obj){
        if($('#card_type').val()!='身份证') return;
        var certificate = $(obj).val();
        if(certificate.length!=18) return;
        var sex = IdCard(certificate,2);
        var age = IdCard(certificate,3);
        $("input:radio[value='"+sex+"']").attr('checked','true');
        $('input[name*="age"]').val(age);
    }


    /*
       * 当type=1时获取出生日期,type=2时获取性别,type=3时获取年龄
       * */
    function IdCard(IdCard, type) {
        if (type === 1) {
            //获取出生日期
            let birthday = IdCard.substring(6, 10) + "-" + IdCard.substring(10, 12) + "-" + IdCard.substring(12, 14)
            return birthday
        }
        if (type === 2) {
            //获取性别
            if (parseInt(IdCard.substr(16, 1)) % 2 === 1) {
                return "男"
            } else {
                return "女"
            }
        }
        if (type === 3) {
            //获取年龄
            var ageDate = new Date()
            var month = ageDate.getMonth() + 1
            var day = ageDate.getDate()
            var age = ageDate.getFullYear() - IdCard.substring(6, 10) - 1
            if (IdCard.substring(10, 12) < month || IdCard.substring(10, 12) === month && IdCard.substring(12, 14) <= day) {
                age++
            }
            if (age <= 0) {
                age = 1
            }
            return age
        }
    }
</script>