<?php
namespace App\Controller\Engine\Lab\Member;
use App\Controller\BaseController;
use Sofast\Core\lang;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Sf;

class Edit extends BaseController
{
    private $project = NULL;
    private $configs = array();
	private $type = 'apply';

    function load()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
		$this->view->set("project",$this->project);
        switch(trim(input::mix("engine")))
        {
            case 'tasker':
                $this->type = 'task';
                break;
            case 'completer':
                $this->type = 'complete';
                break;
            default:
                $this->type = 'apply';
                break;
        }
        $this->view->set("configs",$this->project->getWidgetConfigs('member',$this->type));
    }

    public function team()
    {
        $member = $this->project->getMemberById(input::getInput('mix.mid'));
        if($member->isNew()){
            $member->setProjectId($this->project->getProjectId());
            $member->setType($this->type);
        }
        if(input::getInput('post')){
            if(empty(input::getInput('post.user_type'))) $this->page_debug('请选择人员类型！');
            $certificate = strtoupper(trim(input::post('certificate')));
            if(input::post('card_type') == '身份证' && !isIdcard($certificate)) $this->page_debug('身份证号码格式不正确！');
            if(empty(input::getInput('post.certificate'))) $this->page_debug('请填写证件号！');
            if(empty(input::getInput('post.name'))) $this->page_debug('请填写姓名！');
            if((int)input::getInput('post.no') <1) $this->page_debug('序号不能小于1！');

            $member->setNo((int)input::getInput('post.no'));
            $member->setType($this->type);
            $member->setCardType(input::getInput('post.card_type'));
            $member->setUserType(input::getInput('post.user_type'));
            $member->setCertificate($certificate);
            $sex = input::getInput('post.sex');
            $age = (int)input::getInput('post.age');
            if($member->getCardType()=='身份证'){
                $idcard = sf::getLib('Idcard');
                $sex = $idcard->getSex($certificate);
                $age = $idcard->getAge($certificate);
            }
            $member->setYear($this->project->getDeclareYear());
            $member->setName(trim(input::getInput('post.name')));
            $member->setSex($sex);
            $member->setAge($age);
            $member->setEducation(input::getInput('post.education'));
            $member->setDegree(input::getInput('post.degree'));
            if(input::getInput('post.title_type')){
                $work = sf::getModel('CategoryWorks',input::post('title_type'),'work');
                $member->setTitleType($work->getSubject());
            }
            $member->setTitle(input::getInput('post.title'));
            $member->setMajor(input::getInput('post.major'));
            $member->setHonor(input::getInput('post.honor'));
            $member->setCompanyName(input::getInput('post.company_name'));
            $member->setDuty(input::getInput('post.duty'));
            $member->setMobile(input::getInput('post.mobile'));
            $member->setSubmajor(input::getInput('post.submajor'));
            $member->setWorkAt(input::getInput('post.work_at'));
            $member->setUpdatedAt(date('Y-m-d H:I:s'));
            $member->save();
            if(input::getInput('post.continue')==1){
                $this->success(lang::get('Has been saved!'),getFromUrl());
            }else{
                $this->closeWindow();
            }
        }
        $this->view->set('project',$this->project);
        $this->view->set('member',$member);
        $this->view->apply("inc_body",'edit/team');
        $this->view->display('page_blank');
    }

    public function remove()
    {
        if($this->project->isNew()) $this->error(lang::get('The project is not found!'),getFromUrl());
        if($this->project->getUserId()!=input::session('roleuserid')) $this->page_debug('没有权限删除',getFromUrl());
        $member = $this->project->getMemberById(input::getInput('mix.mid'));
        if($member->isNew()) $this->error('没有找到该参与人员',getFromUrl());
        if($member->isDeclarer()) $this->error('禁止删除项目负责人',getFromUrl());
        $member->delete();
        $this->success(lang::get('Has been deleted!'),'javascript:parent.closeWindow();');
    }



}