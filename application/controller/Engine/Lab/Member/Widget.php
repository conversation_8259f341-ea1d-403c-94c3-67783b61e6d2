<?php
namespace App\Controller\Engine\Lab\Member;
use Sofast\Core\Sf;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use App\Contract\iWidget;

class Widget implements iWidget
{
    private $project = NULL;
    /**
     * 应用层配置
     */
    private $configs = array();
    /**
     * 模块层配置
     */
    private $widgetConfigs = array();
    /**
     * 模板对象
     */
    private $view = NULL;


    private $widget_name = 'member';
    
	function __construct($widget_name,$configs = NULL)
	{
		$this->widget_name = $widget_name;
        $this->widgetConfigs = Configs::getConfigs();
        if($configs !== NULL){
            $this->configs = $configs;
        }else $this->configs = Configs::getDefault();

        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
		$this->view->set('widget_name',$this->widget_name);
        return $this;
    }

    public function setProject($project)
    {
        $this->project = $project;
        return $this;
    }

    public function getWidgetName()
    {
        return $this->widget_name;
    }

    public function setConfig($configs=array())
    {
        if(count($configs)) $this->configs = $configs;
        return $this;
    }

    public function getConfig()
    {
        return $this->configs;
    }

    public function output($tpl='Edit')
    {
        $configs = $this->getConfig();
        switch($configs['engine'])
        {
            case 'tasker':
                $type = 'task';
                break;
            case 'completer':
                $type = 'complete';
                break;
            default:
                $type = 'apply';
                break;
        }
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->getConfig());
        $this->view->set("leader",$this->project->getLeaderMember());
        if($tpl) return $this->view->getContent($tpl."/Output");
        else{
            $htmlStr = '';
            foreach($this->configs as $_key => $_val){
                if(count($_val)) $htmlStr .= $this->view->getContent($_key."/Output");
            }
            return $htmlStr;
        }
    }

    /**
     * 模块配置
     */
    public function manager()
    {
        $this->view->set("configs",$this->configs);
        $this->view->set("widgetConfigs",$this->widgetConfigs);
        return $this->view->getContent("Manager");
    }
	
	/**
	 * 完整性检验
	 * 返回数组
	 */
	function validate()
	{
        $message = array();
        $widgetConfigs = $this->widgetConfigs;
        //检查必传附件
        if($this->configs['needfile']){
            $toptitle = '';
            if($this->configs['needfile']['root'] || $this->configs['langs']['needfile']['root']!='预留') $toptitle = $this->configs['langs']['needfile']['root'].'-';
            foreach ($this->configs['needfile'] as $item=>$datas){
                foreach ($datas as $childItem=>$data){
                    if($data==0 || $childItem=='self') continue;
                    $fileTag = $item==$this->widget_name ? 'apply_'.$item.'_'.$childItem : 'apply_'.$this->widget_name.'_'.$item.'_'.$childItem;
                    $secondtitle = '';
                    if($this->configs['needfile_show'][$item.'.self']) $secondtitle=$this->configs['langs']['needfile'][$item.'.self'].'-';
                    if($item==$this->widget_name){
                        $title = $secondtitle.$this->configs['langs']['needfile'][$item.'.'.$childItem];
                    }else{
                        $title = $this->configs['langs']['needfile'][$this->widget_name.'.self'].'-'.$secondtitle.$this->configs['langs']['needfile'][$item.'.'.$childItem];
                    }
                    if($childItem=='structure'){
                        //人员基本结构和梯队结构配置情况按行检查附件
                        $structures = $this->project->memberStructures();
                        if($structures->getTotal()==0) continue;
                        while($structure = $structures->getObject()){
                            $count = $this->project->getAttachementCount($fileTag.'_'.$structure->getId());
                            if($count==0){
                                $message[] = '【'.$toptitle.$title.'-'.$structure->getName().'】还未上传附件证明材料';
                            }
                        }
                    }
                    if($childItem=='senior'){
                        //亚专科学科带头人及骨干发展情况按行检查附件
                        $seniors = $this->project->memberSeniors();
                        if($seniors->getTotal()==0) continue;
                        while($senior = $seniors->getObject()){
                            $count = $this->project->getAttachementCount($fileTag.'_'.$senior->getId());
                            if($count==0){
                                $message[] = '【'.$toptitle.$title.'-'.$senior->getName().'】还未上传附件证明材料';
                            }
                        }
                    }
                    if(!in_array($childItem,['structure','senior'])){
                        $count = $this->project->getAttachementCount($fileTag.'%');
                        if($count==0) {
                            $message[] = '【'.$toptitle.$title.'】还未上传附件证明材料';
                        }
                    }
                }
            }
        }
        return $message;
	}

    function getFileListHtml()
    {
        $configs = $this->getConfig();
        if(!$configs['needfile']) return '';
        $this->view->set("project",$this->project);
        $this->view->set("configs",$this->configs);
        $this->view->set("widgetConfigs",$this->widgetConfigs);
        $this->view->set("widgetName",$this->widget_name);
        return $this->view->getContent("FileList");
    }

    function __call($tpl,$args)
    {
        return $this->output($tpl);
    }

    function __toString()
    {
        return $this->output();
    }
}
?>