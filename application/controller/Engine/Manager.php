<?php
namespace App\Controller\Engine;
use App\Controller\BaseController;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Router;
use Sofast\Core\Sf;
use App\Facades\PDF;
use App\Lib\Attachment;

class Manager extends BaseController
{
	private $configs = array();
	private $modulars = array();
	private $view = NULL;
	
	function load()
	{
		$this->view = new Template(realpath(dirname(__FILE__)).'/View/');
	}
	
	function index()
	{
		$addSql = '';
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql .= " ORDER BY ".$orderfield." ".$ordermode;

        $addWhere = '1 ';
		if(input::post("search")) $addWhere .=  "and `".input::post("field")."` LIKE '%".trim(input::post("search"))."%' ";
		if(input::post("type")) $addWhere .=  " and `type` = '".trim(input::post("type"))."' ";
		if(input::post("cat_id")) $addWhere .=  " and `cat_id` = '".trim(input::post("cat_id"))."' ";

		$this->view->set("pager",sf::getModel("EngineWorkers")->getPager($addWhere,$addSql,30));
		$this->view->apply("inc_body","Manager/Index");
		$this->view->display("page_main");
	}
	
	function edit()
	{
		$worker = sf::getModel("EngineWorkers",input::mix("id"));
		if(input::post())
		{
			$worker->setSubject(input::post("subject"));
			$worker->setSubjectCode(strtoupper(input::post("subject_code")));
			$worker->setType(input::post("type"));
			$worker->setCatId(input::post("cat_id"));
			$worker->setNote(input::post("note"));
			$worker->setTemplates(ucfirst(input::post("templates")));
			$worker->setOutline(input::post("outline"));
			$worker->setParser(input::post("parser"));
			$worker->setConfigs(input::post("configs"));
			$worker->save();
            if($orders = input::post('orders')){
                foreach ($orders as $wid=>$order){
                    $widget = sf::getModel('EngineWorkerWidgets',$wid);
                    $widget->setOrders($order);
                    $widget->save();
                }
            }
			$this->page_debug("保存成功！",site_url('engine/manager/edit/id/'.$worker->getId()));
		}
		
		$this->view->set("worker",$worker);
		$this->view->apply("inc_body","Manager/Edit");
		$this->view->display("page_main");
	}
	
	function config()
	{
		$widget = sf::getModel("EngineWorkerWidgets",input::mix("id"));
		if(input::post())
		{
			$widget->setConfigs(input::post("configs"));
			$widget->save();
			exit("<script>parent.location.reload();</script>");
		}
		
		$this->view->set("widget",$widget);
		$this->view->apply("inc_body","Manager/Config");
		$this->view->display("page_blank");
	}
	
	/**
	 * 向执行器增加部件
	 */
	function addwidget()
	{
		$worker = sf::getModel("EngineWorkers",input::mix("workerid"));
		$widget = sf::getModel("EngineWorkerWidgets",input::mix("id"));
		if(input::post())
		{ 
			//部件信息
			$_widget = sf::getModel("EngineWidgets",input::post("widget_id"));
			if($_widget->isNew()) $this->page_debug("部件不存在！",getFromUrl());
			
			$widget->setEngineWidgetId($_widget->getId());
			$widget->setClassName($_widget->getClassName());
			$widget->setEngineWorkerId(input::post("workerid"));
			$widget->setSubject(input::post("subject"));
			$widget->setWidgetName(input::post("widget_name")?input::post("widget_name"):$_widget->getWidgetName());
			$widget->setNote(input::post("note"));
			$widget->setOrders(input::post("orders"));
			//$widget->setConfigs($_widget->getConfigs());
			$widget->save();
			exit("<script>parent.location.reload();</script>");
		}
		
		$this->view->set("widgets",sf::getModel("EngineWidgets")->selectAll("","ORDER BY id ASC"));
		$this->view->set("worker",$worker);
		$this->view->set("widget",$widget);
		$this->view->apply("inc_body","Manager/AddWidget");
		$this->view->display("page_blank");
	}

	/**
	 * 删除部件
	 */
	function delwidget()
	{
		$worker = sf::getModel("EngineWorkers",input::mix("workerid"));
        if($worker->isNew()){
            $this->error('没有找到该执行器');
        }
		$widget = sf::getModel("EngineWorkerWidgets",input::mix("id"));
		if($widget->isNew()){
		    $this->error('没有找到该部件');
        }
        $widget->delete();
        $this->success('删除成功',site_url('engine/manager/edit/id/'.$worker->getId()));
	}
	
	/**
	 * 从其他执行器导入
	 *
	 */
	function import()
	{
		$worker = sf::getModel("EngineWorkers",input::mix("id"));
		if($worker->isNew()) $this->page_debug("解析器不存在！",getFromUrl());
		
		if($ids = input::post("ids")){
			$pager = sf::getModel("EngineWorkerWidgets")->selectAll("id in ('".implode("','",$ids)."') ");
			$db = sf::getLib('db');
			while($widget = $pager->getObject()){
			    $sql = "INSERT INTO engine_worker_widgets  (engine_worker_id,engine_widget_id,subject,widget_name,class_name,note,configs,orders) select ".$worker->getId()." as engine_worker_id,engine_widget_id,subject,widget_name,class_name,note,configs,orders from engine_worker_widgets where id = ".$widget->getId()." and not exists(select * from engine_worker_widgets where engine_worker_id = '".$worker->getId()."' and engine_widget_id = ".$widget->getEngineWidgetId().")";
                $db->exec($sql);
			}
            exit("<script>top.location.href=top.location.href+'/_save/yes';</script>");
		}
		$this->view->set("worker",$worker);
		$this->view->set("workers",$worker->selectAll("id <> '".$worker->getId()."' ","order by id desc"));
		$this->view->apply("inc_body","Manager/import");
		$this->view->display("page_blank");	
	}
	
	/**
	 * 复制到指定的执行器
	 *
	 */
	function copyto()
	{
		if($ids = input::post("ids")){
			$to_worker_id = input::post('to_worker_id');
			$pager = sf::getModel("EngineWorkerWidgets")->selectAll("id in ('".implode("','",$ids)."') ");
			while($widget = $pager->getObject()){
				$widget->setEngineWorkerId($to_worker_id);
				$widget->copy();
			}
			$this->page_debug("复制成功！",getFromUrl());	
		}
		$from_worker = sf::getModel("EngineWorkers",input::mix("id"));
		if($from_worker->isNew()) $this->page_debug("解析器不存在！",getFromUrl());
		$this->view->set("from_worker",$from_worker);
		$this->view->set("workers",$from_worker->selectAll("id <> '".$from_worker->getId()."' "));
		$this->view->apply("inc_body","Manager/copyto");
		$this->view->display("page_blank");	
	}
	
	/**
	 * 从其他执行器导入
	 *
	 */
	function ajax_widget_list()
	{
		$result = array('state'=>true,'data'=>array(),"msg"=>"");
		$worker_id = input::mix("worker_id");
		if($worker_id < 0){
			$result['state'] = false;
			$result['msg'] = "解析器不存在！";
		}else{
			$pager = sf::getModel("EngineWorkerWidgets")->selectAll("engine_worker_id = '".$worker_id."' ","ORDER BY id ASC");
			while($widget = $pager->getObject()){
				$option = array();
				$option['id'] = $widget->getId();
				$option['name'] = $widget->getSubject()."(".$widget->getWidgetName().")";	
				$result['data'][] = $option;	
			}
			$result['state'] = true;
		}
		@header("content:application/json;chartset=uft-8");
		exit(json_encode($result));
	}


    function readme()
    {
        $worker = sf::getModel("EngineWorkers",835);
        $widgets = $worker->widgets();
        $this->view->set("widgets",$widgets);
        $this->view->apply("inc_body","Manager/readme");
        $this->view->display("page");
    }


    function readme_config()
    {
        if(input::post()){
            $readmes = input::post('readme');
            foreach ($readmes as $id=>$readme){
                $widget = sf::getModel("EngineWorkerWidgets",$id);
                $configs = $widget->getConfigs();
                $isEdit = false;
                foreach ($readme as $k=>$v){
                    if($v!=$configs['readme'][$k]){
                        $configs['readme'][$k] = $v;
                        $isEdit = true;
                    }
                }
                if($isEdit){
                    $widget->setConfigs($configs);
                    $widget->save();
                }
            }
            $this->success('保存成功！');
        }
        $widgets = sf::getModel("EngineWorkerWidgets")->selectAll("engine_widget_id = '".input::getMix('id')."'","order by engine_worker_id asc");
        $this->view->set("widgets",$widgets);
        $this->view->apply("inc_body","Manager/readme_config");
        $this->view->display("page_blank");
    }
	
}
