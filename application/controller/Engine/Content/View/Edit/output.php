<div data-widget-id="<?=$widget_name?>" style="width:100%;">
<div class="clearfix"></div>
<?php $config = $configs['attachement'];?>
<?php $item = $project->contents($widget_name); ?>
<?php $item_text = $project->contents($widget_name.'_text'); ?>
<?php
    if($config['show_subject']):
?>
<div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">
    <?=$config['subject']?>
</div>
<?php endif;?>

<div style="border:2px dotted #DDD;padding:10px;margin:10px 0;border-radius: 5px;">
<?php if($configs['action']['edit'] && !$config['disable']): ?>
<div class="text-left">
<div class="col-xs-8 text-danger">
步骤：<br/>
<?php
    if($config['step']):
    echo showText($config['step']);
    else:
?>
1、下载正文模板<br/>
2、用office软件打开并填写，填写完毕后转换为pdf格式<br/>
3、上传<br/>
<?php
    endif;
?>
</div>
<div class="col-xs-4 text-right no-margin no-padding">
<?php if ($config['template']): ?>
    <?=Button::setName('下载模板')->setUrl(site_url("engine/content/edit/downloadTpl/engine/" . $configs['engine'] . "/widget/".$widget_name."/id/" . $project->getProjectId()))->setIcon('download')->link()?>

<?php else: ?>
    <button type="button" class="btn btn-warning btn-sm">未找到模板</button>
<?php endif ?>

<?php if($configs['action']['edit'] && !$config['disable']): ?>
    <?=Button::setName('上传')->setUrl(site_url("engine/content/edit/index/engine/" . $configs['engine'] . "/widget/".$widget_name."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
<?php endif; ?>
</div>
</div>
<div class="clearfix"></div>
<hr/>
<?php endif; ?>

<?php if ($config['editor']): ?>
<div class="text-left">
    <?=$config['editor_subject']?>
    <?php if($item_text['content']): ?>
    <div style="overflow: auto;"><?=$item_text['content']?></div>
    <hr/>
    <?php endif; ?>
</div>
<?php endif; ?>

<div id="filelist_<?=$widget_name?>">
<?php if(!$item['file_path']): ?>
<div class="text-center nofile">
<p>
    <i class="fa fa-sticky-note-o fa-5x grey"></i>
    <small class="help-block">没有上传文件</small>
</p>
</div>
<?php else: ?>

<div class="text-center contentarea">
    <p align="left">
        文件名：<?=$item['file_name']?>
    </p>
    <p >
        <a data-id="<?=$item['id']?>" href="<?=site_path('engine/content/edit/downloadProjectAttachment/id/'.$item['id'].'/item_id/'.$project->getProjectId())?>" class="downloadfile" style="display: inline-block;width: 4rem;">

            <?php if(in_array($item['file_ext'],['doc','docx'])): ?>
            <i class="fa fa-file-word fa-5x"></i>
            <?php endif; ?>
            <?php if(in_array($item['file_ext'],['pdf'])): ?>
            <i class="fa fa-file-pdf fa-5x text-danger"></i>
            <?php endif; ?>
            <small class="help-block mt-2">点击下载</small>
        </a>

        <?php if($config['allow_convert'] && in_array(strtolower($item['file_ext']), ['doc','docx'])): ?>
        <?php if($item['pdf_path']): ?>
        <a id="preview<?=$item['id']?>" target="_blank" data-id="<?=$item['id']?>" href="<?=site_path('up_files/'.$item['pdf_path'])?>" style="display: inline-block;width: 4rem;margin-left: 60px;">

            <i class="fa fa-file-pdf fa-5x text-danger"></i>

            <small class="help-block">查看PDF</small>
        </a>
        <?php else: ?>
        <a id="pdf<?=$item['id']?>" data-id="<?=$item['id']?>" href="javascript:void(0);" class="grey pdfdownload" onclick="convertAttachmentSingle(this,'attachement_<?=$widget_name?>','<?=$project->getProjectId()?>',function(obj,json){
            showMsg(json.message);$(obj).attr('href','<?=site_path('up_files/')?>'+'/'+json.data).attr('target','_blank' ).removeClass('grey').removeAttr('onclick').find('small').text('查看PDF');$(obj).find('.fa').addClass('text-danger');
        })" style="display: inline-block;width: 4rem;margin-left: 60px;">

            <i class="fa fa-file-pdf fa-5x"></i>

            <small class="help-block">转成PDF</small>
        </a>
        <?php endif; ?>
        <?php endif; ?>
    </p>
    
    <p>
        <div class=" btn-sm">
        <a data-id="<?=$item['id']?>" href="javascript:void(0);" class="btn btn-sm btn-alt-info d-none" onclick="convertAttachmentSingle(this,'attachement_<?=$widget_name?>','<?=$project->getProjectId()?>',function(obj,json){
            showMsg(json.message);$('#preview<?=$item['id']?>').attr('href','<?=site_path('up_files/')?>'+'/'+json.data+'?v='+Math.random()).attr('target','_blank' ).removeClass('grey').removeAttr('onclick').find('small').text('查看PDF');$('#preview<?=$item['id']?>').find('.fa').addClass('text-danger');
        })">重新转换</a>
        <a href="<?=site_path('up_files/'.$item['file_path'])?>" class="btn btn-sm btn-alt-primary" target="_blank">查看</a>
        <?php if($configs['action']['edit'] && !$config['disable']): ?>
        <a data-id="<?=$item['id']?>" href="javascript:void(0);" class="btn btn-sm btn-alt-danger" onclick="deleteAttachmentSingle(this,'filelist_<?=$widget_name?>','<?=$project->getProjectId()?>')">删除</a>
        <?php endif; ?>
        </div>
    </p>
    
</div>
<?php endif; ?>
</div>

<div class="clearfix"></div>
</div>
</div>