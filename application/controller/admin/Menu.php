<?php
namespace App\Controller\Admin;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
class menu extends BaseController
{	
	public $type = 'default';
	
	function load()
	{
		$this->type = input::getInput("get.type") ? input::getInput("get.type") : 'default';
		view::set("type",$this->type);
	}
	
	/**
	 * 数据列表
	 */
	function index()
	{
		$menu = sf::getModel("menus",0,$this->type);
		$addWhere = $addSql = '';
		//取得带翻页的数据集
		view::set("pager",$menu->getPager($addWhere ,$addSql ,30));
		view::apply("inc_body","admin/menu/index");
		view::display("page");
	}
	
	/**
	 * 数据编辑
	 */
	function edit()
	{
		$menu = sf::getModel("menus",input::getInput("mix.id"),$this->type);
		if(input::getInput("post.subject"))
		{
			input::getInput("post.subject") && $menu->setSubject(input::getInput("post.subject"));
			$menu->setParentId(input::getInput("post.parent_id") ? input::getInput("post.parent_id") : 0);
			input::getInput("post.orders") && $menu->setOrders(input::getInput("post.orders"));
			input::getInput("post.url") && $menu->setUrl(input::getInput("post.url"));
			input::getInput("post.target") && $menu->setTarget(input::getInput("post.target"));
			input::getInput("post.alt") && $menu->setAlt(input::getInput("post.alt"));
			$menu->setUserGroupIds(input::getInput("post.user_group_ids"));
			$menu->setType($this->type);
			$menu->setUpdatedAt(date("Y-m-d H:i:s"));
			$menu->save();
			$this->page_debug(lang::get("Has been saved!"),getFromUrl(site_url("home/left"),site_url("admin/menu/index/type/".$this->type)));
		}
		view::set("menu",$menu);
		view::set('pager',sf::getModel("UserGroups")->selectAll("","ORDER BY id ASC",0));
		view::set("pid",input::getInput("get.pid") ? input::getInput("get.pid") : 0);
		view::set("parent_data",$menu->selectAll('','',0));
		view::apply("inc_body","admin/menu/edit");
		view::display("page");
	}
	
	/**
	 * 删除数据
	 */
	function delete()
	{
		sf::getModel("menus",input::getInput("get.id"),$this->type)->remove();
		$this->page_debug(lang::get("Has been deleted!"),getFromUrl());
	}

    public function getMenus()
    {
        $this->userlevel = (int)input::getMix('userlevel');
        $this->userid = (int)input::getMix('userid');
        $this->manager = sf::getModel('Managers',$this->userid);
        $managerMenus = (array)$this->manager->getAuths('menu');
        $userMenu1s = sf::getModel('UserMenus')->selectAll("level = 1 and user_group_ids like '%{$this->userlevel}%'");
        $this->menus = [];
        $this->menuNo=0;
        $this->menuNo = (int)$this->menuNo;
        while($userMenu1 = $userMenu1s->getObject()){
            if(!$userMenu1->hasAuthByGroupId($this->userlevel)) continue;
            $this->menus[$this->menuNo]['id'] = $userMenu1->getId();
            $this->menus[$this->menuNo]['pId'] = $userMenu1->getParentId();
            $this->menus[$this->menuNo]['name'] = $userMenu1->getSubject();
            if($managerMenus && in_array($userMenu1->getId(),$managerMenus)) $this->menus[$this->menuNo]['checked'] = true;
            if($userMenu1->hasSon()) $this->menus[$this->menuNo]['open'] = true;
            if($userMenu1->hasSon()){
                $this->getChildMenus($userMenu1);
            }
            if(!$userMenu1->hasSon()) $this->menuNo++;
        }
        echo json_encode($this->menus,JSON_UNESCAPED_UNICODE);exit();
    }

    public function getChildMenus($userMenuObj)
    {
        $managerMenus = (array)$this->manager->getAuths('menu');
        $userMenus = sf::getModel('UserMenus')->selectAll("parent_id = '".$userMenuObj->getId()."' and level = ".($userMenuObj->getLevel()+1)." and user_group_ids like '%{$this->userlevel}%'");
        $this->menuNo++;
        while($userMenu = $userMenus->getObject()){
            if(!$userMenu->hasAuthByGroupId($this->userlevel)) continue;
            $this->menus[$this->menuNo]['id'] = $userMenu->getId();
            $this->menus[$this->menuNo]['pId'] = $userMenu->getParentId();
            $this->menus[$this->menuNo]['name'] = $userMenu->getSubject();
            $this->menus[$this->menuNo]['level'] = $userMenu->getLevel();
            if($userMenu->hasSon()) $this->menus[$this->menuNo]['open'] = true;
            if($managerMenus && in_array($userMenu->getId(),$managerMenus)) $this->menus[$this->menuNo]['checked'] = true;
            if($userMenu->hasSon() && $userMenu->getLevel()<3){
                $this->getChildMenus($userMenu);
            }
            if(!$userMenu->hasSon()) $this->menuNo++;
        }

    }
}
?>