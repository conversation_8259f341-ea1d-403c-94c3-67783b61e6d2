<?php
namespace App\controller\admin;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\View;
use Sofast\Core\Lang;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use App\Facades\PDF;
class Platform extends BaseController
{	
	/**
	 * 单位检索
	 */
	public function index()
	{
		$this->platform_gird("1","admin/platform/index");
	}

	public function quarter_list()
    {
        $pagers = sf::getModel('Quarters')->getPager("platform_id = '".input::getMix('userid')."'","order by id desc");
        view::set('pagers',$pagers);
        view::apply("inc_body","admin/platform/quarter_list");
        view::display("page_blank");
    }

	function edit()
	{
		$platform = sf::getModel("Platforms")->selectByPlatformId(input::getInput("mix.userid"));
		if(input::getInput("post"))
		{
//            if(input::post("industry_other")=='其他' && empty(input::post("industry_other"))) $this->page_debug("其他产业领域请注明！",getFromUrl());
//            if(substr(input::post("area_code"),-2) =='00') $this->page_debug("所在地区请选到第三级!",getFromUrl());
            $area = sf::getModel('Region')->selectByCode(input::post("area_code"));
//            if($area->isNew()) $this->page_debug("请选择正确的所在地区!",getFromUrl());
            $user = sf::getModel("Users")->selectByName(input::post('user_name'));
            if($user!==false) $this->error('账号已存在，不能重复注册！',getFromUrl());
            $platform->setPlatformType(input::post('platform_type'));
            $platform->setSubject(input::post('subject'));
            $platform->setAreaCode(input::post("area_code"));
            if(!$area->isNew())$platform->setArea($area->getRegionName());
            $industryOther = '';
            if(input::post("industry")=='其他') $industryOther = input::post("industry_other");
            $platform->setIndustry(input::post("industry"));
            $platform->setIndustryOther($industryOther);
            $platform->setCorporationName(input::post("corporation_name"));
            $platform->setCorporationProperty(input::post("corporation_property"));
            $platform->setCooperatation(input::post("cooperatation_name"),input::post("cooperatation_property"));
            $platform->setPrincipalName(input::post("principal_name"));
            $platform->setPrincipalMobile(input::post("principal_mobile"));
            $platform->setLinkmanName(input::post("linkman_name"));
            $platform->setLinkmanMobile(input::post("linkman_mobile"));
            $platform->setAddress(input::post("address"));
            $platform->setCreatedAt(date("Y-m-d H:i:s"));
            $platform->setUpdatedAt(date("Y-m-d H:i:s"));
            $platform->save();

            $user = sf::getModel("Users")->selectByUserId('');
            $user->setUserName(input::post('user_name'));
            $user->setUserUsername(input::post("subject"));
            $user->setUserPassword(input::post('user_name'));
            $user->setUserGroupId(5);
            $user->setUserMobile(input::post("linkman_mobile"));
            $user->setCreatedAt(date('Y-m-d H:i:s'));
            $user->setUpdatedAt(date('Y-m-d H:i:s'));
            $user->setUserIp(input::getIp());
            //关联角色
            $user->setRole(5,$platform->getPlatformId());
			sf::getModel("historys")->addHistory($platform->getPlatformId(),'新增平台','platform');
			$this->success('平台新增成功！初始密码为：'.$user->getUserName(),site_url('admin/platform/index'));
		}
		view::set("platform",$platform);
		view::apply("inc_body","admin/platform/edit");
		view::display("page");
	}


    /**
     * 删除
     */
    public function doDelete()
    {
        $user = sf::getModel("platforms")->selectByPlatformId(input::mix("platform_id"));
        if(input::getInput("post.content") && !$user->isNew()){
            $result = $user->delete();
            if($result){
                addHistory($user->getPlatformId(),'删除平台！其原因是：<br />'.input::getInput("post.content"),'platform');
                $this->refresh('已删除','success');
            }else{
                $this->refresh('删除失败：平台已填写报表','error');
            }
        }
        //原因表单
        $form = Form::load('admin/platform/doDelete')
            ->addItem(Form::Label('平台名称：'.$user->getSubject())
                ->setAttribute('class','col-md-12')
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'删除原因'])->setWidth(12)->setAttribute('class','no-margin'))
            ->addItem(Form::hidden('platform_id',$user->getPlatformId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }
}
?>