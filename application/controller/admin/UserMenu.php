<?php
namespace App\Controller\Admin;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
use App\Models\UserMenu as menuModel;
use App\Models\UserGroup;
class UserMenu extends BaseController
{	
	public $type = 'default';
	
	function load()
	{
		$this->type = input::getInput("get.type") ? input::getInput("get.type") : 'default';
		view::set("type",$this->type);
	}
	
	/**
	 * 数据列表
	 */
	function index()
	{
		$menu = sf::getModel("UserMenus",0,$this->type);
		$addWhere .= '1 ';

        input::getInput('mix.mid') && $addWhere .= 'AND (id = "'.input::getInput('mix.mid').'" OR parent_id="'.input::getInput('mix.mid').'") ';

        if(input::getInput('post'))
            input::getInput('post.keyword') && $addWhere .= 'AND subject like "%'.input::getInput('post.keyword').'%"';

		//取得带翻页的数据集
		view::set("pager",$menu->getPager($addWhere ,$addSql ,30));
		$addWhere = '1';
		if (input::getMix('mid')){
            $addWhere.= " and parent_id = '".input::getMix('mid')."'";
        }else{
            $addWhere.= " and level = 1";
        }
        $userMenus = sf::getModel('UserMenus')->selectAll($addWhere,"order by orders asc");
        view::set("userMenus",$userMenus);
		view::apply("inc_body","admin/usermenu/index");
		view::display("page_main");
	}
	
	/**
	 * 数据编辑
	 */
	function edit()
	{
		$menu = sf::getModel("UserMenus",input::getInput("mix.id"),$this->type);
		if(input::getInput("post.subject"))
		{
			input::getInput("post.subject") && $menu->setSubject(input::getInput("post.subject"));
			$menu->setParentId(input::getInput("post.parent_id") ? input::getInput("post.parent_id") : 0);
			input::getInput("post.orders") && $menu->setOrders(input::getInput("post.orders"));
			input::getInput("post.url") && $menu->setUrl(input::getInput("post.url"));
			input::getInput("post.target") && $menu->setTarget(input::getInput("post.target"));
			input::getInput("post.alt") && $menu->setAlt(input::getInput("post.alt"));
			input::getInput("post.icon") && $menu->setIcon(input::getInput("post.icon"));
			$menu->setUserGroupIds(input::getInput("post.user_group_ids"));
			$menu->setOfficeIds(input::getInput("post.office_ids"));
			$menu->setType($this->type);
			$menu->setUpdatedAt(date("Y-m-d H:i:s"));
			$menu->save();
            if(input::post('clear_cache')){
                clearMenuCache();
            }
			$this->page_debug(lang::get("Has been saved!"),getFromUrl(site_url("home/left"),site_url("admin/usermenu/index/type/".$this->type)));
		}
		view::set("menu",$menu);
		view::set('pager',sf::getModel("UserGroups")->selectAll("","ORDER BY id ASC",0));
		view::set("pid",input::getInput("get.pid") ? input::getInput("get.pid") : 0);
		view::set("parent_data",$menu->selectAll('','',0));
		view::apply("inc_body","admin/usermenu/edit");
		view::display("page_main");
	}
	
	/**
	 * 删除数据
	 */
	function delete()
	{
		sf::getModel("UserMenus",input::getInput("get.id"),$this->type)->remove();
		$this->page_debug(lang::get("Has been deleted!"),getFromUrl());
	}

	/**
	 * 按角色列出菜单
	 * @return [type] [description]
	 */
	function index_role(){
		view::set("menus",menuModel::where('parent_id',0)->where('type','default')->orderBy('orders')->get());
		view::set("groups",UserGroup::get());
		view::apply("inc_body","admin/usermenu/index_role");
		view::display("page_main");
	}

	/**
	 * 顶级菜单
	 * @return [type] [description]
	 */
	function rootmenu(){
		$group_id = Input::getInput('post.group_id');
		$menus = menuModel::where('parent_id',0)->where('type','default')->orderBy('orders')->get();

		$result=[];
		foreach($menus as $menu){
			//if(!$this->hasAuth($menu,$group_id)) continue;
			$result[]=$menu;
		}
		view::set("menus",$result);
		view::set("group_id",$group_id);
		exit(view::getContent("admin/usermenu/rootmenulist"));
	}

	/**
	 * 各顶级菜单对应的子菜单
	 * @return [type] [description]
	 */
	function menulist(){
		$parent_id = Input::getInput('post.id');
		$group_id = Input::getInput('post.group_id');

		$menus = menuModel::where('parent_id',$parent_id)->where('type','default')->orderBy('id')->get();
		foreach($menus as $menu){
			//if(!$this->hasAuth($menu,$group_id)) continue;

			if($this->isParentNode($menu))
				$menu['is_parent_node'] = true;
			$result[] = $menu;
			$childs = menuModel::where('parent_id',$menu->id)->where('type','default')->orderBy('id')->get();
			foreach($childs as $m){
				//if(!$this->hasAuth($m,$group_id)) continue;
				$result[]=$m;
			}
		}

		view::set("menu",menuModel::find($parent_id));
		view::set("menus",$result);
		view::set("group_id",$group_id);
		exit(view::getContent("admin/usermenu/menulist"));
	}

	/**
	 * 根据角色修改菜单
	 * @return [type] [description]
	 */
	function changeMenuWithRole(){
		$menu_id = Input::getInput('mix.id');
		$group_id = Input::getInput('mix.group_id');
		$type = Input::getInput('post.type');

		$menu = menuModel::find($menu_id);

		//添加权限
		if($type==1){
			//当前菜单权限
			$menu->addGroup($group_id);
			//上级菜单权限
			switch ($menu->level) {
				case 2:
					$parent = menuModel::find($menu->parent_id);
					$parent->addGroup($group_id);

					$childs = menuModel::where('parent_id',$menu->id)->get();
					foreach ($childs as $child) {
						$child->addGroup($group_id);
					}
					break;
				case 3:
					$parent = menuModel::find($menu->parent_id);
					$parent->addGroup($group_id);
					$root = menuModel::find($parent->parent_id);
					$root->addGroup($group_id);
					break;
				default:
					break;
			}
		}
		if($type==2){
			//删除权限
			$menu->removeGroup($group_id);
			//删除下级权限
			switch ($menu->level) {
				case 1:
					$childs = menuModel::where('parent_id',$menu->id)->get();
					foreach ($childs as $child) {
						$child->removeGroup($group_id);

						$cs = menuModel::where('parent_id',$child->id)->get();
						foreach ($cs as $c){
							$c->removeGroup($group_id);
						}
					}
					break;
				case 2:
					$childs = menuModel::where('parent_id',$menu->id)->get();
					foreach ($childs as $child) {
						$child->removeGroup($group_id);
					}
					break;
				default:
					break;
			}
		}
		
	}

	private function isParentNode(menuModel $menu){
		$count = menuModel::where('parent_id',$menu->id)->where('type','default')->count();
		if($count>0) return true;
		else return false;
	}

/**
 * 菜单查看权限同步到访问权限
 * @return [type] [description]
 */
	function syncToAuth(){
		$menu_id = Input::getInput('mix.id');
		$result = menuModel::find($menu_id)->syncToAuth();
		exit($result);
	}

    function clearcache()
    {
        clearMenuCache();
        $this->success('清空成功！',getFromUrl());
    }
}
?>