<?php
namespace App\Controller\Admin;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
use Sofast\Core\Config;
class super extends BaseController
{	
	function index()
	{
		$this->grid('admin/super/index');
	}

	function index2()
	{
        view::display('page_main');
	}
	
	function search()
	{
		if(input::getInput("post"))
		{
			$addWhere = ' 1 ';
			//高级搜索
			if(input::getInput("mix.year")) $addWhere .= " AND declare_year IN ('".implode("','",input::getInput("mix.year"))."')";
			if(input::getInput("mix.office")) $addWhere .= " AND office_id IN ('".implode("','",input::getInput("mix.office"))."')";
			if(input::getInput("mix.types")) $addWhere .= " AND type_id IN ('".implode("','",input::getInput("mix.types"))."')";
			if(input::getInput("mix.gather")) $addWhere .= " AND department_id IN ('".implode("','",input::getInput("mix.gather"))."')";
			if(input::getInput("mix.project_state")) $addWhere .= " AND statement IN ('".implode("','",input::getInput("mix.project_state"))."')";
			if(input::getInput("mix.task_state")) $addWhere .= " AND statement IN ('".implode("','",input::getInput("mix.task_state"))."')";
			if(input::getInput("mix.complate_state")) $addWhere .= " AND statement IN ('".implode("','",input::getInput("mix.complate_state"))."')";
			
			if(input::getInput("mix.lowscore")!='' || input::getInput("mix.highscore")!='')
			{
				if(input::getInput("mix.lowscore")=='') $lowscore = 0;
				else $lowscore = input::getInput("mix.lowscore");
				if(input::getInput("mix.highscore")=='') $highscore = 100;
				else $highscore = input::getInput("mix.highscore");
				$addWhere .= " AND score >'".$lowscore."' and score<='".$highscore."'";
			}
			else
			{
				if(input::getInput("mix.score"))
				{
					if(strstr(input::getInput("mix.score"),'-'))
					{
						$score = explode('-',input::getInput("mix.score"));
						$lowscore = $score[0];
						$highscore = $score[1];
					}
					else if(strstr(input::getInput("mix.score"),'<'))
					{
						$score = explode('<',input::getInput("mix.score"));
						$lowscore = 0;
						$highscore = $score[1];
					}
					$addWhere .= " AND score >'".$lowscore."' and score<='".$highscore."'";
				}
			}
			
			$_SESSION['sqlStr'] = base64_encode($addWhere);
			$_SESSION['orderStr'] = base64_encode($addSql);
			
			exit("<script>parent.location.reload();</script>");	
		}
		view::apply("inc_body",'admin/super/search');
		view::display("page");	
	}
	
	/**
	 * 移动项目到指定归口部门和单位
	 */
	public function doMove()
	{
		$msg = array();
		if(input::getInput("session.SafetyCode") != input::getInput("post.SafetyCode")) $this->page_debug(lang::get("The safety code is error!"),getFromUrl());

		if(input::getInput("post.select_id")) $ids = input::getInput("post.select_id");
		else $ids[] = input::getInput("get.id");
		
		for($i=0,$n=count($ids);$i<$n;$i++)
		{
			$project = sf::getModel("projects")->selectByProjectId($ids[$i]);
			if($project->isNew()) continue;
			
			if(input::getInput("post.department_id")){
				$_department_name = $project->getDepartmentName();
				$project->setDepartmentId(input::getInput("post.department_id"));
				$project->setDepartmentName(sf::getModel("departments")->selectByUserId(input::getInput("post.department_id"))->getSubject());
				$msg[] = '项目归口部门从“'.$_department_name.'”移动到“'.$project->getDepartmentName().'”！';
			}
			
			if(input::getInput("post.corporation_id")){
				$_corporation_name = $project->getCorporationName();
				$project->setCorporationId(input::getInput("post.corporation_id"));
				$project->setCorporationName(input::getInput("post.corporation_name"));
				$msg[] = '项目单位从“'.$_corporation_name.'”设置为"'.$project->getCorporationName().'"！';
			}
			
			if(input::getInput("post.statement")){
				$project->setStatement(input::getInput("post.statement"));
				$msg[] = '设置项目状态为"'.$project->getState().'"!';
			}
			
			if(input::getInput("post.state_complete_book")){
				$project->setStateForCompleteBook(input::getInput("post.state_complete_book"));
				$msg[] = '设置验收书状态为"'.$project->getStateForComplete().'"!';
			}
			
			if(input::getInput("post.state_plan_book")){
				$project->setStateForPlanBook(input::getInput("post.state_plan_book"));
				$msg[] = '设置任务书状态为"'.$project->getStateForTask().'"!';
			}
			
			if(input::getInput("post.state_budget_book")){
				$project->setStateForBudgetBook(input::getInput("post.state_budget_book"));
				$msg[] = '设置项目预算状态为"'.$project->getStateForBudget().'"!';
			}
			
			//开启预立项和经费预算
			if(input::getInput("post.is_open")){
				input::getInput("post.is_open") == 'is_task' && $project->setTaskOpen(1);
				input::getInput("post.is_open") == 'is_budget' && $project->setBudgetOpen(1);
				$msg[] = (input::getInput("post.is_open") == 'is_task') ? '开启预立项！' : '开启经费预算！';
			}
			
			if(input::getInput("post.radicate_year")){
				$project->setRadicateYear(input::getInput("post.radicate_year"));
				$msg[] = '设置立项年度为“'.input::getInput("post.radicate_year").'”！';	
			}
			
			$project->setUpdatedAt(date("Y-m-d H:i:s"));
			$project->save();
			sf::getModel("historys")->addHistory($project->getProjectId(),lang::get("The projects has been move by super!")."<br />".implode('<br />',$msg)."<br /><strong>其理由是：</strong>".input::getInput("post.note"),'projects',1);
		}
		$this->page_debug(lang::get("The projects has been move!"),getFromUrl());
	}
	
	/**
	 * 结束专家评分(统计所有专家的评审)
	 */
	function syndic()
	{
		// $addWhere = "statement = 18 and project_id IN (SELECT project_id FROM project_grade WHERE is_submit < 2)";
		// // $addSql = "GROUP BY `project_id` ";
		// view::set("pager",sf::getModel("Projects")->getPager($addWhere,$addSql,30));
		// view::apply("inc_body",'admin/super/syndic');
		// view::display("page");
		$this->grid('admin/super/syndic',"statement = 18 and project_id IN (SELECT project_id FROM project_grade WHERE is_submit < 2)");	
	}
	
	/**
	 * 结束专家评分(统计所有专家的评审)--组
	 */
	function syndic_group()
	{
		$year = input::getInput("mix.declare_year") ? input::getInput("mix.declare_year") : config::get('current_declare_year',date('Y'));
		$type_current_group = input::getInput("mix.type_current_group") ? input::getInput("mix.type_current_group") : config::get('type_current_group',1);
		$type = input::getInput("mix.type_id") ? input::getInput("mix.type_id") : '';
		$subject = input::getInput("mix.subject_id") ? input::getInput("mix.subject_id") : '';
		//取得分组编号前缀
		$group_str = substr($year,-2);
		if($type && $subject == ''){
			$group_str .= sf::getModel("types",$type)->getMarker().$type_current_group;
		}
		$form_vars = array('declare_year','type_current_group','type_id','subject_id');
		view::set("pager",sf::getModel("ProjectGroup")->getPager("`group_subject` LIKE '".$group_str."%' AND is_lock = 1",'',25,'','',$form_vars));
		view::apply("inc_body","admin/super/group_list");
		view::display("page");
	}
	
	/**
	 * 处理统计
	 */
	public function doSyndic()
	{		
		if(input::getInput("post.select_id")){
			if(input::getInput("session.SafetyCode") != input::getInput("post.SafetyCode")) $this->page_debug(lang::get("The safety code is error!"),getFromUrl());
			$ids = input::getInput("post.select_id");
			for($i=0,$n=count($ids);$i<$n;$i++)
			{
				$grade = sf::getModel("ProjectGrade");
				if($grade->syndic($ids[$i]))//成功就写历史记录
				sf::getModel("historys")->addHistory($ids[$i],lang::get("The project has been syndic by super!"));
			}
			$this->page_debug(lang::get("The project has been syndic!"),getFromUrl());
		}else{//如果是传近来的是组ID
			$over = true;
			$group = sf::getModel("ProjectGroup",input::getInput("get.id"));
			$ids = $group->getArrayWithProject();//读组内信息
			for($i=0,$n=count($ids);$i<$n;$i++)
			{
				$grade = sf::getModel("ProjectGrade");
				if($over = $grade->syndic($ids[$i]))//成功就写历史记录
				sf::getModel("historys")->addHistory($ids[$i],lang::get("The project has been syndic by super!"));
			}
			if($over){
				$group->setIsLock(2);
				$group->save();
			}
			$this->page_debug(lang::get("The project has been syndic!"),getFromUrl());
		}
	}
	
	/**
	 * 会评录入
	 */
	function input()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("post.id") ? input::getInput("post.id") : input::getInput("get.id"));
		if(input::getInput("post"))
		{
			$project->setScore(input::getInput("post.score"));
			$project->setAssessType('offline');
			$project->setStatement(20);
			$project->setConfigs(['path.assess'=>'assess/offline']);
			$project->save();
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
			sf::getModel("historys")->addHistory(input::getInput("post.id"),lang::get("Input the score by super!")."<br />".input::getInput("post.note"),'projects',1);
            exit("<script>parent.location.href='".site_url('office/assign/complete_list/_save/yes')."';</script>");
		}
		view::set("project",$project);
		view::set("itemType",'reviews');
		view::apply("inc_body",'admin/super/input');
		view::display("page_blank");	
	}


	/**
	 * 意见上传
	 */
	function idea()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("post.id") ? input::getInput("post.id") : input::getInput("get.id"));
		if(input::getInput("post"))
		{
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('ProjectAttachments',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
			sf::getModel("historys")->addHistory(input::getInput("post.id"),"上传评审意见",'projects',1);
            exit("<script>parent.location.href='".site_url('office/assign/complete_list/_save/yes')."';</script>");
		}
		view::set("project",$project);
		view::set("itemType",'idea');
		view::apply("inc_body",'admin/super/idea');
		view::display("page_blank");
	}

	function history()
	{
		$addWhere = $addSql = '';
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';

		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		//处理搜索
		input::getInput("post.search") && $addWhere .= "`".input::getInput("post.field")."` LIKE '%".trim(input::getInput("post.search"))."%' ";
		//取得带翻页的数据集
		view::set("pager",sf::getModel("historys")->getPager($addWhere ,$addSql ,30));
		view::apply("inc_body","admin/super/history");
		view::display("page");

	}
	/**
	*申报单位列表
	*/
	function corporation()
	{
		$addWhere = $addSql = '';
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql .= 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		$addWhere .= ' 1 ';
		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
		if(input::getInput("mix.is_lock") == 1) $addWhere .= " AND `is_lock` = 1 ";
		else if(input::getInput("mix.is_lock") == 2) $addWhere .= " AND `is_lock` = 0 ";
		$from_vars = array('search','field','is_lock');
		view::set("pager",sf::getModel("corporations")->getPager($addWhere,$addSql,30,'','',$from_vars));
		view::apply("inc_body","admin/super/corporation");
		view::display("page");
	}
	

	function show()
	{
		
		view::set("dept",sf::getModel("corporations")->selectByUserId(input::getInput("get.id") ? input::getInput("get.id") : input::getInput("post.id")));
		view::apply("inc_body","admin/corporation/show");
		view::display("page");	
	}
	
	function delete()
	{
		if(input::getInput("post.select_id")){
			$ids = implode("','",input::getInput("post.select_id"));
		}else $ids = input::getInput("get.id"); 
		
		sf::getModel("corporations")->remove($ids);
		sf::getModel("historys")->addHistory('system',sprintf(lang::get('The unit has been remove,which ID is "%s"!'),htmlspecialchars($ids,ENT_QUOTES)));
		$this->page_debug(lang::get("Has been deleted!"),getFromUrl());
	}
	
	/**
	 * 验证申报单位
	 */
	function validate()
	{
		if(input::getInput("post.select_id")){
			$ids = implode("','",input::getInput("post.select_id"));
		}else $ids = input::getInput("get.id"); 

		sf::getModel("corporations")->validate($ids);
		sf::getModel("historys")->addHistory('system',sprintf(lang::get('The state has been change,which ID is "%s"!'),htmlspecialchars($ids,ENT_QUOTES)));
		$this->page_debug(lang::get("Has been validated!"),getFromUrl());
	}
	/**
	 * 编辑归口部门
	 */
	function edit()
	{
		$corporation = sf::getModel("corporations")->selectByUserId(input::getInput("get.id") ? input::getInput("get.id") : input::getInput("post.id"));		
		if(input::getInput("post.department_id"))
		{
			input::getInput("post.department_id") && $corporation->setDepartmentId(input::getInput("post.department_id"));
			if($corporation->save())
				sf::getModel("historys")->addHistory("system",sprintf(lang::get('The department of "%s" corporation has been changed!'),$corporation->getSubject()));
			$this->page_debug(lang::get("Has been saved!"),site_url("admin/super/corporation"));
		}
		view::set("user",$corporation);
		view::apply("inc_body","admin/super/edit");
		view::display("page");
	}
	
	/**
	 * 舍去专家评审得分
	 */
	function rgrade()
	{
		$grade = sf::getModel("ProjectGrade",input::getInput("mix.id"));
		if($grade->isNew()) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		
		//设置为弃权
		$grade->setIsSubmit(3);
		$grade->save();
		
		//重新计算得分
		$db = sf::getLib("db");
		$row = $db->fetch_first("SELECT AVG(score) AS score FROM `project_grade` WHERE `project_id` = '".$grade->getProjectId()."' AND `is_submit` = 2 ");
		
		//保存新的得分
		$project = sf::getModel("Projects")->selectByProjectId($grade->getProjectId());
		if($project->isNew()) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		$project->setScore($row['score']);
		$project->save();
		
		$this->page_debug(lang::get("Has been submit!"),getFromUrl());
	}
	/**
	 * 退回专家重新评审
	 */
	function gradeback()
	{
        $grade = sf::getModel("ProjectGrade",input::getInput("mix.id"));
		if($grade->isNew()) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		//设置为待上报
		$grade->setIsSubmit(1);
		$grade->save();
		$this->page_debug(lang::get("Has been rejected!"),getFromUrl());
	}
	/**
	 * 退回专家重新评审——奖励
	 */
	function awardBack()
	{
		$grade = sf::getModel("ProjectAwardGrade",input::getInput("mix.id"));
		if($grade->isNew()) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		//设置为待上报
		$grade->setIsSubmit(1);
		$grade->save();
		$this->page_debug(lang::get("Has been rejected!"),getFromUrl());
	}
	
	/**
	 * 舍去专家评审得分——奖励
	 */
	function rAwardGrade()
	{
		$grade = sf::getModel("ProjectAwardGrade",input::getInput("mix.id"));
		if($grade->isNew()) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		
		//设置为弃权
		$grade->setIsSubmit(3);
		$grade->save();
		
		//重新计算得分
		$db = sf::getLib("db");
		$row = $db->fetch_first("SELECT AVG(score) AS score FROM `project_award_grade` WHERE `project_id` = '".$grade->getProjectId()."' AND `is_submit` = 2 ");
		
		//保存新的得分
		$project = sf::getModel("Awards")->selectByProjectId($grade->getProjectId());
		if($project->isNew()) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		$project->setScore($row['score']);
		$project->save();
		
		$this->page_debug(lang::get("Has been submit!"),getFromUrl());
	}
	/**
	 * 舍去专家评审得分——人才
	 */
	function rTalentGrade()
	{
		$grade = sf::getModel("ProjectTalentGrade",input::getInput("mix.id"));
		if($grade->isNew()) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		
		//设置为弃权
		$grade->setIsSubmit(3);
		$grade->save();
		
		//重新计算得分
		$db = sf::getLib("db");
		$row = $db->fetch_first("SELECT AVG(score) AS score FROM `project_talent_grade` WHERE `project_id` = '".$grade->getProjectId()."' AND `is_submit` = 2 ");
		
		//保存新的得分
		$project = sf::getModel("Declarers")->selectByUserId($grade->getProjectId());
		if($project->isNew()) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		$project->setScore($row['score']);
		$project->save();
		
		$this->page_debug(lang::get("Has been submit!"),getFromUrl());
	}

	/**
	 * 退回专家重新评审——人才
	 */
	function talentBack()
	{
		$grade = sf::getModel("ProjectTalentGrade",input::getInput("mix.id"));
		if($grade->isNew()) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		//设置为待上报
		$grade->setIsSubmit(1);
		$grade->save();
		$this->page_debug(lang::get("Has been rejected!"),getFromUrl());
	}
	/**
	 * 更换项目负责人
	 */
	function changeUserName()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("post.id"));
		if($project->isNew()) exit('项目不存在！');
		if(!input::getInput("post.userid")) exit('替换用户不存在！');
		
		$_username = $project->getUserName();
		$project->setUserName(input::getInput("post.username"));
		$project->setUserId(input::getInput("post.userid"));

		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("post.userid"));
		$project->setCorporationId($declarer->getCorporationId());
		$project->setCorporationName($declarer->getCorporationName());
		
		$corporation = sf::getModel("Corporations")->selectByUserId($declarer->getCorporationId());
		$department = sf::getModel("Departments")->selectByUserId($corporation->getDepartmentId());
		$project->setDepartmentId($corporation->getDepartmentId());
		$project->setDepartmentName($department->getSubject());

		$project->setUpdatedAt(date("Y-m-d H:i:s"));
		$project->save();
		sf::getModel("historys")->addHistory($project->getProjectId(),sprintf(lang::get('The person in charge is replaced by %s to %s!'),$_username,input::getInput("post.username")));
		exit('替换成功！');
	}

	/**
	 * 更换项目联系人
	 */
	function changeLinkman()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("post.id"));
		if($project->isNew()) exit('项目不存在！');
		if(!input::getInput("post.userid")) exit('替换用户不存在！');

		$_linkmanName = $project->getLinkmanName();
		$project->setLinkmanName(input::getInput("post.username"));
		$project->setLinkmanId(input::getInput("post.userid"));

		$project->setUpdatedAt(date("Y-m-d H:i:s"));
		$project->save();
		$msg = $_linkmanName ? '项目联系人由'.$_linkmanName.'变更为'.$project->getLinkmanName() : "项目联系人设置为：".$project->getLinkmanName();
		sf::getModel("historys")->addHistory($project->getProjectId(),$msg);
		exit('联系人设置成功！');
	}
	
	/**
	 * 编辑立项信息
	 */
	function changeRadicate()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));		
		if($project->isNew()) $this->page_debug("指定项目不存在！",getFromUrl());
		if(input::getInput("post.note"))
		{
			$msg = array();
			$_radicate_id = $project->getRadicateId();
			$_radicate_year = $project->getRadicateYear();
			$_radicate_money = $project->getRadicateMoney();
			
			if(input::getInput("post.radicate_id") != $_radicate_id){
				$msg[] = "立项编号由【".$_radicate_id."】变更为【".input::getInput("post.radicate_id")."】";
				$project->setRadicateId(input::getInput("post.radicate_id"));
			}			
			if(input::getInput("post.radicate_year") != $_radicate_year){
				$msg[] = "立项年度由【".$_radicate_year."】变更为【".input::getInput("post.radicate_year")."】";
				$project->setRadicateYear(input::getInput("post.radicate_year"));
			}		
			if(input::getInput("post.radicate_money") != $_radicate_money){
				$msg[] = "立项经费由【".$_radicate_money."】变更为【".input::getInput("post.radicate_money")."】";
				$project->setRadicateMoney(input::getInput("post.radicate_money"));
			}
			if($project->save())
				sf::getModel("historys")->addHistory($project->getProjectId(),implode(';',$msg).'！处理原因为：'.input::getInput("post.note"),'project',1);
			exit("<script>parent.location.reload();</script>");	
		}
		view::set("project",$project);
		view::apply("inc_body","admin/super/radicate");
		view::display("page_blank");
	}
	
	/**
	 * 超级编辑
	 */
	function superEdit()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));		
		if($project->isNew()) $this->page_debug("指定项目不存在！",getFromUrl());
		if(input::getInput("post.note"))
		{
			$msg = array();
			if(input::getInput("post.subject") != $project->getSubject()){
				$msg[] = "项目名称由【".$project->getSubject()."】变更为【".input::getInput("post.subject")."】";
				$project->setSubject(input::getInput("post.subject"));
			}
			if(input::getInput("post.declare_at") != $project->getDeclareAt()){
				$msg[] = "项目申报时间由【".$project->getDeclareAt()."】变更为【".input::getInput("post.declare_at")."】";
				$project->setDeclareAt(input::getInput("post.declare_at"));
			}
//			if(input::getInput("post.real_start_at") != $project->getRealStartAt()){
//				$msg[] = "项目任务书开始时间由【".$project->getRealStartAt()."】变更为【".input::getInput("post.real_start_at")."】";
//				$project->setRealStartAt(input::getInput("post.real_start_at"));
//			}
//			if(input::getInput("post.real_end_at") != $project->getRealEndAt()){
//				$msg[] = "项目任务书结束时间由【".$project->getRealEndAt()."】变更为【".input::getInput("post.real_end_at")."】";
//				$project->setRealEndAt(input::getInput("post.real_end_at"));
//			}
//			if(input::getInput("post.flow_type") != $project->getFlowType()){
//				$_flowtype = $project->getProjectTypeSubject();
//				$project->setFlowType(input::getInput("post.flow_type"));
//				$flowtype = $project->getProjectTypeSubject();
//				$msg[] = "项目类别由【".$_flowtype."】变更为【".$flowtype."】";
//			}
//			if(input::getInput("post.research_type") != $project->getResearchType()){
//				$_researchtype = $project->getResearchTyp();
//				$project->setResearchType(input::getInput("post.research_type"));
//				$researchtype = $project->getResearchTyp();
//				$msg[] = "项目类型由【".$_researchtype."】变更为【".$researchtype."】";
//			}
			if(input::getInput("post.statement") != $project->getStatement()){
				$project->setStatement(input::getInput("post.statement"));
				$msg[] = "项目状态变更";
			}
//			if(input::getInput("post.is_task")){
//				$project->setStatement(29);
//				$msg[] = "项目设置为已经立项状态";
//			}
//			if(input::getInput("post.is_complete")){
//				$msg[] = "项目设置为结题状态";
//				$project->setStatement(30);
//			}
//			if(input::getInput("post.is_subsidy")){
//				$msg[] = "项目设置为后补类型";
//				$project->setIsSubsidy(1);
//			}
//			if(input::getInput("post.is_shift")){
//				$msg[] = "项目设置为转移支付";
//				$project->setIsShift(2);
//			}
//			if(input::getInput("post.task_tpl")){
//				$_d = array();
//				$book = sf::getModel("BookMaps",input::getInput("post.task_tpl"));
//				$_d["path.task"] = $book->getPath();
//				$_d["model.task"] = $book->getModel();
//				$_d["maps.task"] = $book->getId();
//				$project->setConfigs($_d);
//				$msg[] = "更新项目任务书填写模板";
//			}
//			if(input::getInput("post.budget_tpl")){
//				$_d = array();
//				$book = sf::getModel("BookMaps",input::getInput("post.budget_tpl"));
//				$_d["path.budget"] = $book->getPath();
//				$_d["model.budget"] = $book->getModel();
//				$_d["maps.budget"] = $book->getId();
//				$project->setConfigs($_d);
//				$msg[] = "更新项目预算书填写模板";
//			}
//			if(input::getInput("post.complete_tpl")){
//				$_d = array();
//				$book = sf::getModel("BookMaps",input::getInput("post.complete_tpl"));
//				$_d["path.complete"] = $book->getPath();
//				$_d["model.complete"] = $book->getModel();
//				$_d["maps.complete"] = $book->getId();
//				$project->setConfigs($_d);
//				$msg[] = "更新项目验收书填写模板";
//			}
//			if(input::getInput("post.assess_tpl")){
//				$_d = array();
//				$book = sf::getModel("BookMaps",input::getInput("post.assess_tpl"));
//				$_d["path.assess"] = $book->getPath();
//				$_d["maps.assess"] = $book->getId();
//				$project->setConfigs($_d);
//				$msg[] = "更新项目评审模板";
//			}
			$project->setUpdatedAt(date("Y-m-d H:i:s"));
			if($project->save() && $msg)
				sf::getModel("historys")->addHistory($project->getProjectId(),implode(';',$msg).'！处理原因为：'.input::getInput("post.note"),'project',1);
			exit("<script>parent.location.reload();</script>");	
		}
		view::set("project",$project);
		view::apply("inc_body","admin/super/super_edit");
		view::display("page_blank");
	}
	
	/**
	 * 增加备注
	 */
	function doNote()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));		
		if($project->isNew()) $this->page_debug("指定项目不存在！",getFromUrl());
		if(input::getInput("post.note"))
		{
			sf::getModel("historys")->addHistory($project->getProjectId(),'项目备注：'.input::getInput("post.note"),'project',1);
			exit("<script>parent.location.reload();</script>");	
		}
		view::set("project",$project);
		view::apply("inc_body","admin/super/note");
		view::display("page_blank");
	}
	
	/**
	 * 处理评审中的错误
	 */
	function assess()
	{
		if(input::getInput("post.id"))
		{
			$grade = sf::getModel("ProjectGrade",input::getInput("post.id"));
			$grade->setIsSubmit(0);
			$grade->save();
			sf::getModel("historys")->addHistory($grade->getId(),'退回重新打分','ProjectGrade',1);	
		}
		
		$db = sf::getLib("db");
		$sql = "SELECT p.accept_id,p.subject,e.user_name,g.* FROM projects as p,project_grade as g,experts as e WHERE p.project_id = g.project_id AND g.expert_id = e.user_id ";
		if(input::getInput("mix.subject")) $sql .= " AND p.subject like '%".trim(input::getInput("mix.subject"))."%' ";
		else $sql .= " AND p.subject IS NULL ";
		if(input::getInput("mix.user_name")) $sql .= " AND e.user_name like '%".trim(input::getInput("mix.user_name"))."%' ";
		if(input::getInput("mix.is_submit")) $sql .= " AND g.is_submit = '".input::getInput("mix.is_submit")."' ";
		
		$sql .= " ORDER BY updated_at DESC limit 0,10";
		$query = $db->query($sql);
		$data = $db->result_array($query);
		print_r($data);exit;
		view::set("data",$data);
		view::apply("inc_body","admin/super/assess");
		view::display("page");
	}
	public function grid($tpl = 'office/search/index',$addWhere = '1',$showMax=16,$page='page_main',$modelName='projects')
	{
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';


		//处理搜索
		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
		
		if(input::getInput("mix.declare_year")){
			$addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
		}
		if(input::getInput("mix.type_current_group")){
			$addWhere .= " AND `type_current_group` = '".input::getInput("mix.type_current_group")."' ";
		}
		if(input::getInput("mix.department_id")){
			$addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
		}

		input::getInput("mix.level") && $addWhere .= " AND `level` = '".input::getInput("mix.level")."' ";
		input::getInput("mix.type_id") && $addWhere .= " AND `type_id` = '".input::getInput("mix.type_id")."' ";
		input::getInput("mix.district") && $addWhere .= " AND `district` = '".input::getInput("mix.district")."' ";
		input::getInput("mix.flow_type") && $addWhere .= " AND `flow_type` = '".input::getInput("mix.flow_type")."' ";
		input::getInput("mix.research_type") && $addWhere .= " AND `research_type` = '".input::getInput("mix.research_type")."' ";
		input::getInput("mix.office_id") && $addWhere .= " AND `office_id` = '".input::getInput("mix.office_id")."' ";
		input::getInput("mix.subject_id") && $addWhere .= " AND `subject_id` LIKE '".input::getInput("mix.subject_id")."%' ";
		input::getInput("mix.subject_code") && $addWhere .= " AND `subject_code` = '".input::getInput("mix.subject_code")."' ";
        if(input::getInput("mix.statement")==2029){
            $addWhere .= " AND `statement` IN (20,28,29,30) ";
        }elseif(input::getInput("mix.statement")==2930){
            $addWhere .= " AND `statement` IN (29,30) ";
        }elseif(input::getInput("mix.statement")){
            $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";
        }
        //按指南搜索
        if(input::getInput("mix.guide_id")){
            $guide = sf::getModel('Guides',input::getInput("mix.guide_id"));
            $guide_ids = $guide->getChildren();
            $addWhere .= " AND `guide_id` IN (".implode(',',$guide_ids).") ";
        }
		input::getInput("mix.type_subject") && $addWhere .= " AND `type_id` = '".input::getInput("mix.type_subject")."' ";
		input::getInput("mix.project_domain") && $addWhere .= " AND `project_domain` = '".input::getInput("mix.project_domain")."' ";
		//按照时间检索
		input::getInput("mix.declare_start") && $addWhere .= " AND unix_timestamp(`declare_at`) >= unix_timestamp('".input::getInput("mix.declare_start")."') ";
		input::getInput("mix.declare_end") && $addWhere .= " AND unix_timestamp(`declare_at`) <= unix_timestamp('".input::getInput("mix.declare_end")."') ";
		input::getInput("mix.update_start") && $addWhere .= " AND unix_timestamp(`updated_at`) >= unix_timestamp('".input::getInput("mix.update_start")."') ";
		input::getInput("mix.update_end") && $addWhere .= " AND unix_timestamp(`updated_at`) <= unix_timestamp('".input::getInput("mix.update_end")."') ";
		
		if(input::getInput("mix.is_shift")) $addWhere .= " AND `is_shift` = '".input::getInput("mix.is_shift")."' ";
		
		if(input::getInput("mix.is_wait")) $addWhere .= " AND `is_wait` = '".input::getInput("mix.is_wait")."' ";
		else $addWhere .= " AND `is_wait` < 3 ";
        if(input::getInput("mix.is_short")) $addWhere .= " AND `is_short` = '".(input::getInput("mix.is_short")-1)."' ";
        if(input::getInput("mix.is_minority")) $addWhere .= " AND `is_minority` = '".(input::getInput("mix.is_minority")-1)."' ";
        if(strlen(input::getMix('diagnosis_code'))==2){
            $addWhere .= " AND `diagnosis_code` like '".input::getInput("mix.diagnosis_code")."%' ";
        }
        if(strlen(input::getMix('diagnosis_code'))>2){
            $addWhere .= " AND `diagnosis_code` = '".input::getInput("mix.diagnosis_code")."' ";
        }
        if(input::getInput("mix.is_test")==1) $addWhere .= " AND `is_test` = '0' ";
		// dd($this->hash);
		//将搜索条件保存以备打印或者导出
		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		{
			//保存标记
			$_SESSION['hash'] = $this->hash;
			$_SESSION['projects']['baseSql'] = base64_encode($addWhere);
			//打印
			$_SESSION['projects']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['projects']['orderStr'] = base64_encode($addSql);
		}else{
			$_SESSION['queryOptions'] = '';
			$_SESSION['url_str'];
			$addWhere = base64_decode($_SESSION['projects']['sqlStr']);
		}
		//显示页面
		$form_vars = array('search','level','declare_year','radicate_year','type_current_group','type_id','cat_id','subject_id','department_id','statement','office_id','field','state_for_complete_book','state_for_plan_book','state_for_budget_book','is_wait','is_shift','department_id','district','is_short','is_minority','diagnosis_code','is_test','subject_code','guide_id','guide_id1','guide_id2','guide_id3');
		// if(input::session('username')=='lijx')
		view::set("pager",sf::getModel(ucfirst($modelName))->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::set('money',$money);
		view::apply("inc_body",$tpl);
		view::display($page);
	}

    public function file_delete()
    {
        $project = sf::getModel('Projects')->selectByProjectId(input::getMix("id"));
        if($project->isNew()) $this->error('没有找到该项目');
        $file = sf::getModel("Filemanager",input::getInput("mix.fid"));
        $data['code'] = 0;
        if($file->isNew()){
            $data['msg'] = '找不到该文档';
            echo json_encode($data);exit();
        }
        if($file->getItemId()!=$project->getProjectId()){
            $data['msg'] = '不能删除';
            echo json_encode($data);exit();
        }
        $result = $file->remove(input::getInput("mix.fid"),true);
        if($result===true){
            $data['code'] = 1;
        }else{
            $data['msg'] = '删除失败';
        }
        echo json_encode($data);exit();
    }

    public function project_output()
    {
        if(input::getInput('post')){
            //处理排序
            $addSql = 'ORDER BY district asc,department_id asc,corporation_id asc';
            if(!input::getInput("mix.declare_year")){
                exit('申报年度必选');
            }
            $addWhere = '1 ';
            //处理搜索
            input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
            input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
            input::getInput("mix.radicate_year") && $addWhere .= " AND `radicate_year` = '".input::getInput("mix.radicate_year")."' ";
            input::getInput("mix.subject_code") && $addWhere .= " AND `subject_code` = '".input::getInput("mix.subject_code")."' ";
            input::getInput("mix.cat_id") && $addWhere .= " AND `cat_id` = '".input::getInput("mix.cat_id")."' ";
            input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";

            if(input::getInput("mix.has_score")){
                if(input::getInput("mix.has_score")=='有'){
                    $addWhere .= " AND `score` > 0 ";
                }else{
                    $addWhere .= " AND `score` = 0 ";
                }
            }
            if(input::getInput("mix.statement")){
                $statement = input::getInput("mix.statement");
                if(strstr($statement,',')!==false){
                    $addWhere .= " AND `statement` IN ($statement)";
                }else{
                    $addWhere .= " AND `statement` = {$statement}";
                }
            }

            input::getInput("mix.state_for_plan_book") && $addWhere .= " AND `state_for_plan_book` = '".input::getInput("mix.state_for_plan_book")."' ";
            input::getInput("mix.state_for_complete_book") && $addWhere .= " AND `state_for_complete_book` = '".input::getInput("mix.state_for_complete_book")."' ";
//            $addWhere .= " and user_id IN (select user_id from projects where statement = 29 and cat_id = 471)";

            $projects = sf::getModel('Projects')->selectAll($addWhere,$addSql);
            $oData = [];
            $i=0;
            $filename = '项目导出';
            while ($project = $projects->getObject()) {
                $oData[$i][] = $i+1;
                $oData[$i][] = $project->getDistrict();
                $oData[$i][] = $project->getAcceptId();
                $oData[$i][] = $project->getSubject();
                $oData[$i][] = $project->getUserName();
                $oData[$i][] = $project->getCorporation(true)->getUnitId();
                $oData[$i][] = $project->getCorporation()->getCode();
                $oData[$i][] = $project->getCorporationName();
                $oData[$i][] = $project->getCorporation()->getProperty();
                $oData[$i][] = $project->getCorporation()->getType();
                $oData[$i][] = $project->getDepartmentName();
                $oData[$i][] = $project->getDeclareAt();
                $oData[$i][] = strip_tags($project->getState());
                $i++;
            }
            $filename = '省级临床重点专科('.$i.'项)';
            $head = ['序号','所属片区','申报编号','项目名称','项目负责人','机构ID','统一社会信用代码','申报单位','单位性质','医院类别','主管部门','填报日期','项目状态'];
            excel_out($head,$oData,$filename,$filename);exit();
        }
        view::apply("inc_body","admin/super/project_output");
        view::display("page");
    }

    public function radicate()
    {
        $start_row = input::getInput('get.start') ?: 2;
        $size = input::getInput('get.size') ?: 15000;
        if (!empty($_FILES)) {
            $filename = $_FILES['file']['name'];
            $tmp_name = $_FILES['file']['tmp_name'];
            $msg = $this->doRadicate($filename, $tmp_name, $start_row, $size);
            if ($msg === FALSE) {
                $this->page_debug("上传失败:" . $this->error);
            } else {
                $this->page_debug("上传成功!");
            }
        }
        view::apply("inc_body", "admin/super/radicate_import");
        view::display("page_blank");
    }

    private function doRadicate($filename, $tmp_name, $start_row = 2, $max_row = 5000)
    {
        //上传后的文件名
        $filename = $_FILES['file']['name'];
        $uploadfile = $_FILES['file']['tmp_name'];
        //move_uploaded_file() 函数将上传的文件移动到新位置。若成功，则返回 true，否则返回 false。
        $result = $_FILES['file']['tmp_name'];
        if ($result) //如果上传文件成功，就执行导入excel操作
        {
            $excelreader = sf::getLib('ExcelReader');
            $excelreader->setOutputEncoding('UTF-8'); //设置输出的编码为utf8
            $excelreader->read($uploadfile); //要读取的excel文件地址
            $arr = $excelreader->sheets[0];
            $statement = input::post('statement') ? (int)input::post('statement') : 20;
            $declare_year = input::post('declare_year') ? (int)input::post('declare_year') : config::get('current_declare_year');
            $radicate_year = input::post('radicate_year') ? (int)input::post('radicate_year') : config::get('current_declare_year');
            $catId = (int)input::post('cat_id');
            //先整体检查一遍
            foreach ($arr['cells'] as $key=>$value){
                if($key==1) continue;
                $addwhere = "`subject`= '{$value[2]}' and `corporation_name`= '{$value[3]}' and statement = '{$statement}' and declare_year = '{$declare_year}'";
                if($catId) $addwhere.=" and cat_id = '{$catId}'";
                $projects = sf::getModel('Projects')->selectAll($addwhere);
                if($projects->getTotal()==0){
                    $this->error = $value[2]."没找到：".$addwhere;
                    return false;
                }
                if($projects->getTotal()>1){
                    $this->error = $value[2]."大于一个";
                    return false;
                }
            }
            //开始立项操作
            foreach ($arr['cells'] as $key=>$value){
                if($key==1) continue;
                $addwhere = "`subject`= '{$value[2]}' and `corporation_name`= '{$value[3]}' and statement = '{$statement}' and declare_year = '{$declare_year}'";
                if($catId) $addwhere.=" and cat_id = '{$catId}'";
                $projects = sf::getModel('Projects')->selectAll($addwhere);
                if($projects->getTotal()==0){
                    $this->error = $value[2]."没找到";
                    return false;
                }
                if($projects->getTotal()>1){
                    $this->error = $value[2]."大于一个";
                    return false;
                }
                while ($project = $projects->getObject()){
                    if($value[4]) $radicateId = $value[4];
                    else $radicateId = $project->generateRadicateId();
                    $project->setRadicateId($radicateId);
                    $project->setRadicateYear($radicate_year);
                    $project->setRadicateAt(date('Y-m-d H:i:s'));
                    $project->setRadicateMoney($value[5]);
                    $project->setStatement(29);
                    $project->setNote(input::post('note'));
                    $project->setUpdatedAt(date("Y-m-d H:i:s"));
                    $project->save();
                    addHistory($project->getProjectId(),lang::get("The project has been radicate!"));
                }
            }
            return true;
        } else {
            $this->error = "上传失败";
            return false;
        }
    }
}