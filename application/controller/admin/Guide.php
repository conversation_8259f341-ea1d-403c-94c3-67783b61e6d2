<?php
namespace App\Controller\Admin;
use App\Controller\BaseController;
use Sofast\Support\View;
use App\Models\Guide as guideModel;
use App\Facades\Form;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Lang;
use App\Facades\Cache;

class Guide extends BaseController
{	
	private $dir = 'admin';
	private $year = '2020';
	
	function load()
	{
		$this->year = input::getInput("mix.year") ? input::getInput("mix.year") : config::get("current_declare_year",date("Y"));
		view::set("year",$this->year);
	}
	
	/**
	 * 数据列表
	 */
	function index()
	{
		$parentId = input::get("pid") ? input::get("pid") : 0;
		$category = sf::getModel("Guides",$parentId,$this->year);
		$addWhere = $addSql = '';
		
		$addWhere .= "`parent_id` = '".$parentId."' ";
		input::getInput("mix.search") && $addWhere .= "AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
		
		view::set("category",$category);
		//取得带翻页的数据集
		view::set("pager",$category->getPager($addWhere ,$addSql,100));
		view::apply("inc_body","admin/guide/index");
		view::display("page_main");
	}
	
	/**
	 * 构建树
	 */
	function rebuildTree()
	{
		sf::getModel("Guides",0,$this->year)->rebuildTree();
		exit("<script>parent.location.reload();</script>");
	}
	
	/**
	 * 数据编辑
	 */
	function edit()
	{
		$category = sf::getModel("Guides",input::getInput("mix.id"),$this->year);
        if(input::getInput("get.pid") && $category->isNew()){
            $category->setParentId(input::getInput("get.pid"));
            $parent = $category->getParent();
            $category->setType($parent->getType());
            $category->setCatId($parent->getCatId());
            $category->setOfficeId($parent->getOfficeId());
            $category->setYear($parent->getYear());
            $category->setReportStartAt($parent->getReportStartAt());
            $category->setReportEndAt($parent->getReportEndAt());
            $category->setCurrentGroup($parent->getCurrentGroup());
            $category->setOrders($parent->getNewOrders());
        }
		if(input::getInput("post.subject"))
		{
            if(input::getInput("post.parent_id")>0){
                if(!input::post("mark")){
                    $this->error('请填写专科标记');
                }
                if($category->checkMarkRepeat(input::post("mark"))===false){
                    $this->error('标记重复');
                }
            }

			$_data = array();
			if($category->isNew()) $category->setGuideid(sf::getLib("MyString")->getRandString());
			input::getInput("post.subject") && $category->setSubject(input::getInput("post.subject"));
			$category->setProjectName(input::post("project_name"));//固定项目名称
			$category->setParentId(input::getInput("post.parent_id") ? input::getInput("post.parent_id") : 0);
			input::getInput("post.orders") && $category->setOrders(input::getInput("post.orders"));
			input::getInput("post.mark") && $category->setMark(input::getInput("post.mark"));
			input::getInput("post.content") && $category->setContent(input::getInput("post.content"));
			$category->setType(input::post("type"));
			if(input::post("cat_id")){
				$category->setCatId(input::post("cat_id"));
                $_data['cat_id'] = input::post("cat_id");
			}
			if(input::getInput("post.year")){
				$category->setYear(input::getInput("post.year"));
				$_data['year'] = input::getInput("post.year");
			}
			if(input::post("user_level")){
				$category->setUserLevel(input::post("user_level"));
				$_data['user_level'] = input::post("user_level");
			}
			if(input::getInput("post.current_group")){
				$category->setCurrentGroup(input::getInput("post.current_group"));
				$_data['current_group'] = input::getInput("post.current_group");
			}
			if(input::getInput("post.start_at")){
				$category->setStartAt(input::getInput("post.start_at"));
				$_data['start_at'] = input::getInput("post.start_at");
			}
			if(input::getInput("post.end_at")){
				$category->setEndAt(input::getInput("post.end_at"));
				$_data['end_at'] = input::getInput("post.end_at");
			}
			if(input::getInput("post.report_start_at")){
				$category->setReportStartAt(input::getInput("post.report_start_at"));
				$_data['report_start_at'] = input::getInput("post.report_start_at");
			}
			if(input::getInput("post.report_end_at")){
				$category->setReportEndAt(input::getInput("post.report_end_at"));
				$_data['report_end_at'] = input::getInput("post.report_end_at");
			}
			if(input::getInput("post.gather_end_at")){
				$category->setGatherEndAt(input::getInput("post.gather_end_at"));
				$_data['gather_end_at'] = input::getInput("post.gather_end_at");
			}
            if(input::getInput("post.submit_at")){
                $category->setSubmitAt(input::getInput("post.submit_at"));
                $_data['submit_at'] = input::getInput("post.submit_at");
            }
			if(input::getInput("post.assess_id")){//评审指标
				$assess = sf::getModel("Assess",input::getInput("post.assess_id"));
				$category->setAssessId($assess->getId());
				$_data['assess_id'] = $assess->getId();
			}
			if(input::getInput("post.assess_rule_id")){//评审指标
				$rule = sf::getModel("Rules",input::getInput("post.assess_rule_id"));
				$category->setAssessRuleId($rule->getId());
				$category->setAssessPath($rule->getPath());
				$_data['assess_rule_id'] = $rule->getId();
				$_data['assess_path'] = $rule->getPath();
			}
			if(input::getInput("post.declare_map_id")){//申报书
				$type = sf::getModel("Types",input::getInput("post.declare_map_id"));
				$category->setDeclareMapId($type->getId());
				$category->setApplyPath($type->getProjectPath());
				$_data['declare_map_id'] = $type->getId();
				$_data['apply_path'] = $type->getProjectPath();
			}
			if(input::getInput("post.task_map_id")){//任务书
				$book = sf::getModel("EngineWorkers",input::getInput("post.task_map_id"));
				$category->setTaskMapId($book->getId());
				$category->setTaskPath($book->getPath());
				$_data['task_map_id'] = $book->getId();
				$_data['task_path'] = $book->getPath();
			}
			if(input::getInput("post.budget_map_id")){//预算书
				$book = sf::getModel("BookMaps",input::getInput("post.budget_map_id"));
				$category->setBudgetMapId($book->getId());
				$category->setBudgetPath($book->getPath());
				$_data['budget_map_id'] = $book->getId();
				$_data['budget_path'] = $book->getPath();
			}
			if(input::getInput("post.stage_map_id")){//中期报告
                $book = sf::getModel("EngineWorkers",input::getInput("post.stage_map_id"));
                $category->setStageMapId($book->getId());
                $category->setStagePath($book->getPath());
                $_data['stage_map_id'] = $book->getId();
                $_data['stage_path'] = $book->getPath();
			}
			if(input::getInput("post.complete_map_id")){//验收书
                $book = sf::getModel("EngineWorkers",input::getInput("post.complete_map_id"));
                $category->setCompleteMapId($book->getId());
                $category->setCompletePath($book->getPath());
				$_data['complete_map_id'] = $book->getId();
				$_data['complete_path'] = $book->getPath();
			}
			if(input::post("project_start_at") != ''){
				$category->setProjectStartAt(input::post("project_start_at"));
				$_data['project_start_at'] = input::post("project_start_at");
			}
			if(input::post("project_end_at") != ''){
				$category->setProjectEndAt(input::post("project_end_at"));
				$_data['project_end_at'] = input::post("project_end_at");
			}
			if(input::post("start_year") != ''){
				$category->setStartYear(input::post("start_year"));
				$_data['start_year'] = input::post("start_year");
			}
			if(input::post("end_year") != ''){
				$category->setEndYear(input::post("end_year"));
				$_data['end_year'] = input::post("end_year");
			}
			if(input::post("month_min") != ''){
				$category->setMonthMin(input::post("month_min"));
				$_data['month_min'] = input::post("month_min");
			}
			if(input::post("month_max") != ''){
				$category->setMonthMax(input::post("month_max"));
				$_data['month_max'] = input::post("month_max");
			}
			if(input::getInput("post.office_id")){//归属处室
				$category->setOfficeId(input::getInput("post.office_id"));
				$_data['office_id'] = input::getInput("post.office_id");
			}
			if(input::getInput("post.allow_grade")){//负责人设置
				$category->setAllowGrade(input::getInput("post.allow_grade"));
				$_data['allow_grade'] = implode('|',input::post("allow_grade"));
			}
			if(input::post()){//单位设置
				$category->setAllowCompany(input::post("allow_company"));
				if(input::post('allow_company_son')) $_data['allow_company'] = implode('|',input::post("allow_company"));
			}

            if(input::post()){//特殊性质
				$category->setProperty(input::post("property"));
				if(input::post('property_son')) $_data['property'] = implode('|',input::post("property"));
			}


            $category->setUpdatedAt(date("Y-m-d H:i:s"));
            $category->save();

            //是否修改父类
			if(input::getInput("post.set_parent")){
				$category->setPathAttribute($_data);
			}

            //是否修改子类
			if(input::getInput("post.set_son")){
				$category->setTreeAttribute($_data);	
			}
			addHistory($category->getId(),lang::get('The guide has been edit!'),'guides');
			$this->page_debug(lang::get("Has been saved!"),getFromUrl());
		}
		view::set("category",$category);
		view::set("pid",input::getInput("get.pid") ? input::getInput("get.pid") : 0);
		view::set("parent_data",$category->selectAll('','',0));
		view::apply("inc_body","admin/guide/edit");
		view::display("page_main");
	}
	
	/**
	 * 删除数据
	 */
	function delete()
	{
		sf::getModel("Guides",input::getInput("mix.id"),$this->year)->remove();
		sf::getModel("Historys")->addHistory(input::getInput("mix.id"),lang::get('The guide has been delete!'),'Guides');
		$this->page_debug(lang::get("Has been deleted!"),getFromUrl());
	}
	
	/**
	 * 申报书配置
	 */
	function config()
	{
		$category = sf::getModel("Guides",input::getInput("mix.id"),$this->year);
		//取得附件模块
		if($postDatas = input::post())
		{
            $configs = [];
            foreach ($postDatas as $key=>$data){
                if(!is_array($data)) continue;
                foreach ($data as $k=>$v){
                    $configs[$key][$k] = $v;
                }
            }
            if($configs){
                foreach ($configs as $key=>$config){
                    $category->setConfigs($key,$config);
                }
            }else{
                //清空configs
            }

			$category->save();
			$_data = array();
			//是否修改子类
			if(input::post("set_son")){
				$pager = $category->selectSonTree();
				while($obj = $pager->getObject()){
                    if($configs){
                        foreach ($configs as $key=>$config){
                            $obj->setConfigs($key,$config);
                        }
                    }else{
                        //清空configs
                    }
					$obj->save();
				}	
			}
            if($postDatas['companys']){

            }
			exit("<script>parent.location.reload();</script>");
		}
		view::set("category",$category);
		view::apply("inc_body","admin/guide/Config");
		view::display("page_blank");
	}
	
	/**
	 * 将指南下面的指南复制到指定的指南下面
	 */
	function docopy()
	{
		$from 	= sf::getModel("Guides",input::mix("from"),$this->year);
		if($from->isNew()) $this->page_debug("指南未找到！",getFromUrl());
		if(input::post()){
			$from->copyto(input::post("to"));
			exit("<script>parent.location.reload();</script>");
		}
		view::set("category",$from);
		view::set("parent_data",$from->selectAll('','',0));
		view::apply("inc_body","admin/guide/docopy");
		view::display("page_blank");
	}
	
}
?>