<?php
namespace App\Controller\Migrate;

use Sofast\Core\config;
use Sofast\Core\input;
use Sofast\Core\Log;
use Sofast\Core\sf;
use Sofast\Support\template;

class Zdsys
{
    public function __construct()
    {
        @ini_set('memory_limit', '500M');
        if (php_sapi_name() != 'cli') {
            echo 'No authority';
            exit();
        }
    }

    /**
     * 迁移单位
     * @return void
     */
    public function company()
    {
        $sql = "SELECT * FROM `zdsys_corporations`";
        $db = sf::getLib('Db');
        $query = $db->query($sql);
        $i=0;
        while ($row = $db->fetch_array($query)) {
            $corporation = sf::getModel('Corporations')->selectByCode($row['code']);
            if(!$corporation->isNew()) continue;
            $corporation->setUserId(getOpenId($row['code']));
            $corporation->setZdsysUserId($row['user_id']);
            $corporation->setSubject($row['subject']);
            $corporation->setDepartmentId($row['department_id']);
            $corporation->setProperty($row['property']);
            $corporation->setAddress($row['address']);
            $corporation->setPhone($row['phone']);
            $corporation->setPostalcode($row['postalcode']);
            $corporation->setLinkman($row['linkman']);
            $corporation->setLinkmanEmail($row['linkman_email']);
            $corporation->setMobile($row['mobile']);
            $corporation->setPrincipal($row['principal']);
            $corporation->setPrincipalCardid($row['principal_cardid']);
            $corporation->setHomepage($row['homepage']);
            $corporation->setManagerUserId($row['manager_user_id']);
            $corporation->setBankId($row['bank_id']);
            $corporation->setBankName($row['bank_name']);
            $corporation->setArea($row['area']);
            $corporation->setAreaCode($row['area_code']);
            $corporation->setIsHightech($row['is_hightech']);
            $corporation->setIsLock($row['is_lock']);
            $corporation->setCreatedAt($row['created_at']?:date('Y-m-d H:i:s'));
            $corporation->setUpdatedAt(date('Y-m-d H:i:s'));
            $corporation->save();
            $i++;
            echo 'migrate->'.$i."\r\n";
        }
        echo 'migrate finish->'.$i."\r\n";
    }

    /**
     * 迁移项目负责人
     * @return void
     */
    public function researcher()
    {
        $sql = "SELECT * FROM `zdsys_declarers`";
        $db = sf::getLib('Db');
        $query = $db->query($sql);
        $i=0;
        while ($row = $db->fetch_array($query)) {
            $declarer = sf::getModel('Declarers')->selectByIdcard($row['user_idcard']);
            if(!$declarer->isNew()) continue;
            $declarer->setUserId(getOpenId($row['user_idcard']));
            $declarer->setZdsysUserId($row['user_id']);
            $_company = $declarer->getZdsysCompany($row['corporation_id']);
            $declarer->setCorporationId($_company->getUserId());
            $declarer->setCorporationName($_company->getSubject());
            $declarer->setPersonname($row['personname']);
            $declarer->setUserSex($row['user_sex']);
            $declarer->setUserBirthday($row['user_birthday']);
            $declarer->setCardType($row['card_type']);
            $declarer->setUserIdcard($row['user_idcard']);
            $declarer->setUserOccupation($row['user_occupation']);
            $declarer->setSubjectName($row['subject_name']);
            $declarer->setUserWork($row['user_work']);
            $declarer->setSubjectId($row['subject_id']);
            $declarer->setPostalCode($row['postal_code']);
            $declarer->setUserMobile($row['user_mobile']);
            $declarer->setUserPhone($row['user_phone']);
            $declarer->setUserEmail($row['user_email']);
            $declarer->setSpecialty($row['specialty']);
            $declarer->setBranch($row['branch']);
            $declarer->setUserHonor($row['user_honor']);
            $declarer->setUserGraduate($row['user_graduate']);
            $declarer->setUserGraduateAt($row['user_graduate_at']);
            $declarer->setUserHomeAddress($row['user_home_address']);
            $declarer->setUserSummary($row['user_summary']);
            $declarer->setIsLock($row['is_lock']);
            $declarer->setCreatedAt($row['created_at']?:date('Y-m-d H:i:s'));
            $declarer->setUpdatedAt(date('Y-m-d H:i:s'));
            $declarer->save();
            $i++;
            echo 'migrate->'.$i."\r\n";
        }
        echo 'migrate finish->'.$i."\r\n";
    }

    /**
     * 迁移申报书
     * @return void
     */
    public function apply()
    {
        $sql = "SELECT * FROM `zdsys_projects`";
        $db = sf::getLib('Db');
        $query = $db->query($sql);
        $i=0;
        while ($row = $db->fetch_array($query)) {
            $project = sf::getModel('Projects')->selectByProjectId($row['project_id']);
            $project->setSubjectCode('zdsys');
            $project->setCatId(271);
            $project->setOfficeId(300);
            $project->setOfficeSubject('平台处');
            $project->setLevel('省级');
            $project->setSubject($row['subject']);
            $project->setUserId($row['user_id']);
            $project->setUserName($row['user_name']);
            $project->setCorporationId($row['corporation_id']);
            $project->setCorporationName($row['corporation_name']);
            $cooperatation['cooperatation_name'] = explode('、', $row['other_corporation']);
            $project->setCooperatation($cooperatation);
            $project->setDepartmentId($row['department_id']);
            $project->setDepartmentName($row['department_name']);
            $project->setDeclareYear($row['declare_year']);
            $project->setRadicateYear($row['radicate_year']);
            $project->setRadicateAt($row['radicate_at']);
            $project->setDeclareAt($row['declare_at']);
            $project->setSubjectId($row['gb_code']);
            $project->setSubjectName($row['gb_subject']);
            $project->setSubjectIds([$row['gb_code']]);
            $project->setStatement($row['statement']);
            $project->setIsMigrate(1);
            if($project->isNew()) $project->setCreatedAt(date('Y-m-d H:i:s'));
            $project->setUpdatedAt(date('Y-m-d H:i:s'));
            $project->save();
            $project->setConfigs("archive.apply","archive/apply");
            $i++;
            echo 'migrate->'.$i."\r\n";
        }
        echo 'migrate finish->'.$i."\r\n";
    }


}
