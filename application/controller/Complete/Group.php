<?php
namespace App\controller\Complete;
use App\Controller\BaseController;
use Sofast\Core\Log;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Lang;
use App\Models\BookMap;

class group extends BaseController
{
    function load()
    {
        parent::load();
        if(!input::session('auth')){
            $this->error('你没有权限查看',getFromUrl());
        }
    }

    function wait_list()
    {
        //$year = input::getInput("mix.declare_year") ? input::getInput("mix.declare_year") : config::get('current_declare_year',date('Y'));
        $year = input::getInput("mix.declare_year") ? input::getInput("mix.declare_year") : config::get('current_declare_year',date('Y'));
        $type_id = input::getInput("mix.type_id") ? input::getInput("mix.type_id") : '';
        $guide_id = input::getInput("mix.guide_id") ? input::getInput("mix.guide_id") : '';

        //取得分组编号前缀
        $group_name_part1 = substr($year,-2);
        $group_name_part1.='YS';
//        $group_name_part1.='GJZDZK';
//        $group_name_part1.='QYZX';
//        $group_name_part1.='PZH';

        $data['group_name_part1'] = $group_name_part1;

        //取得分组编号第二部分
        $group_num = sf::getModel("CompleteGroup")->selectAll("group_type = 'complete' and `group_subject` LIKE '".$group_name_part1."%'")->getTotal();
        $data['group_name_part2'] = str_pad(($group_num+1),3,"0",STR_PAD_LEFT);

        //取得已有的没有完成专家分配的分组
        $data['groups'] = sf::getModel("CompleteGroup")->selectAll("is_lock < 1 and office_id = ".input::getInput("session.office_id"),"ORDER BY updated_at DESC",300);//`group_subject` LIKE '".substr($year,-2)."%' AND

        $addWhere = "statement = 10 and user_role = 2";
//		$addWhere = "statement not in (13,18,20) ";

        input::getInput("mix.type_subject") && $addWhere.=" AND type_id = ".input::getInput("mix.type_subject");

        input::getInput("mix.department_id") && $addWhere.=" AND department_id = '".input::getInput("mix.department_id")."'";
        //显示项目列表
//        dd($addWhere);
        view::set($data);
        $this->grid('complete/group/wait_list',$addWhere,50,'page_main','completes');
    }

    /**
     * 设置项目分组
     */
    function doGroup()
    {
        if(!is_array(input::getInput("post.select_id"))) $this->page_debug(lang::get("Must select a project!"),getFromUrl());
        $group = sf::getModel("CompleteGroup");
        //如果使用以前的分组（追加项目给分组）
        if(input::getInput("post.group_id")){
            $group->selectByPk(input::getInput("post.group_id"));
            $group->addProject(input::getInput("post.select_id"));
            $group->setUpdatedAt(date("Y-m-d H:i:s"));
            $group->setIsLock(0);
            if($group->save()){
                sf::getModel("historys")->addHistory($group->getId(),sprintf(lang::get("Additional items to the group,which name is '%s'!"),$group->getGroupSubject()),'group');
                $this->success(lang::get("The group has been set up!"),getFromUrl());
            }
        }
        //新增加分组
        if(input::getInput("post.group_name_part2")){
            $subject = input::getInput("post.group_name_part1").input::getInput("post.group_name_part2");
            if($group->hasSubject($subject))
                $this->error(lang::get("The group has been use!"),getFromUrl());

            $_group = $group->create($subject);
            $_group->setGroupType('complete');
            $_group->setOfficeId(input::getInput("session.office_id"));
            $_group->setGroupYear(config::get('current_declare_year',date('Y')));
            $_group->setGroupNote(input::getInput("post.group_note"));
            $_group->addProject(input::getInput("post.select_id"));
            $_group->setUpdatedAt(date("Y-m-d H:i:s"));

            if($_group->save()){
                sf::getModel("historys")->addHistory($_group->getId(),sprintf(lang::get("Create a group,which name is '%s'!"),$_group->getGroupSubject()),'group');
                $this->success(lang::get("The group has been set up!"),getFromUrl());
            }
        }
    }

    /**
     * 修改分组备注
     */
    function doEdit()
    {
        $group = sf::getModel("CompleteGroup",input::getInput("mix.id"));
        if(input::getInput("post.group_note"))
        {
            $group->setGroupNote(input::getInput("post.group_note"));
            $group->setAssessMap(input::getInput("post.assess_map"));
            $group->setUpdatedAt(date("Y-m-d H:i:s"));
            $group->save();
            sf::getModel("historys")->addHistory($group->getId(),sprintf(lang::get("Change note for the group to '%s'!"),$group->getGroupNote()),'group');
            exit("<script>parent.location.reload();</script>");
        }
        view::set("group",$group);
        view::apply("inc_body","complete/group/edit");
        view::display("page_blank");
    }

    /**
     * 撤消分组
     */
    function doCancel()
    {
        $group = sf::getModel("CompleteGroup",input::getInput("mix.id"));
        if ($group->isNew()) $this->page_debug(lang::get("The group do not exist!"), getFromUrl());
        if($group->cancel()){
            sf::getModel("historys")->addHistory($group->getId(),sprintf(lang::get("Cancel the group,which name is '%s'!"),$group->getGroupSubject()),'group');
            $this->page_debug(lang::get("The group has been cancel!"),getFromUrl());
        }else $this->page_debug(lang::get("You can not delete the group!"),getFromUrl());
    }


    /**
     * 移除分组
     */
    function remove()
    {
        $group = sf::getModel("CompleteGroup",input::getInput("mix.group_id"));
        if($group->isNew() || $group->getIsLock() > 0 ) $this->page_debug(lang::get("You can not remove the project from the group!"),getFromUrl());

        if(input::getInput("post.select_id")) $ids = input::getInput("post.select_id");
        else $ids[] = input::getInput("get.id");

        for($i=0,$n=count($ids);$i<$n;$i++)
        {
            if(!$ids[$i]) continue;//项目ID为空直接跳过
            $group->deleteProject($ids[$i]);
            sf::getModel("historys")->addHistory($ids[$i],lang::get('The project has been remove from the group!'),'project',1);
        }
        $this->page_debug(lang::get("The project has been remove from the group!"),getFromUrl());
    }

    /**
     * 等待分配专家的组
     */
    function index()
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'group_subject';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'ASC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        $year = input::getInput("mix.declare_year") ? input::getInput("mix.declare_year") : config::get('current_declare_year',date('Y'));
        $addWhere = "group_type = 'complete' and is_lock = 0 AND group_year = '".$year."' AND office_id = '".input::getInput("session.office_id")."' ";
        input::getInput("mix.subject") && $addWhere .= " AND group_subject like '%".trim(input::getInput("mix.subject"))."%'";
        input::getInput("mix.note") && $addWhere .= " AND group_note like '%".trim(input::getInput("mix.note"))."%'";

        $_SESSION['CompleteGroup']['sqlStr'] = base64_encode($addWhere);
        $_SESSION['CompleteGroup']['orderStr'] = base64_encode($addSql);

        view::set("pager",sf::getModel("CompleteGroup")->getPager($addWhere,$addSql,20,'','',array('subject','note')));
        view::apply("inc_body","complete/group/index");
        view::display("page_main");
    }

    /**
     * 已经分配专家的组(评估中的组)
     */
    function assess_list()
    {
        $year = input::getInput("mix.declare_year") ? input::getInput("mix.declare_year") : config::get('current_declare_year',date('Y'));
        $addWhere = "group_type = 'complete' and is_lock = 1 AND group_year = '".$year."' AND office_id = '".input::getInput("session.office_id")."' ";
        input::getInput("mix.subject") && $addWhere .= " AND group_subject like '%".trim(input::getInput("mix.subject"))."%'";
        input::getInput("mix.note") && $addWhere .= " AND group_note like '%".trim(input::getInput("mix.note"))."%'";
        input::getInput("mix.project_type") && $addWhere .= " AND project_type = '".trim(input::getInput("mix.project_type"))."'";

        view::set("pager",sf::getModel("CompleteGroup")->getPager($addWhere,'ORDER BY updated_at DESC',20));
        view::apply("inc_body","complete/group/assess_list");
        view::display("page_main");
    }

    /**
     * 评审完毕的组（评估完毕的组）
     */
    function complete_list()
    {
        $year = input::getInput("mix.declare_year") ? input::getInput("mix.declare_year") : config::get('current_declare_year',date('Y'));
        $addWhere = "group_type = 'complete' and is_lock = 2  AND office_id = '".input::getInput("session.office_id")."' ";
        input::getInput("mix.subject") && $addWhere .= " AND group_subject like '%".trim(input::getInput("mix.subject"))."%'";
        input::getInput("mix.note") && $addWhere .= " AND group_note like '%".trim(input::getInput("mix.note"))."%'";
        input::getInput("mix.group_year") ? $addWhere.=" AND group_year = '".input::getInput("mix.group_year")."' " : $addWhere.=" AND group_year = '".$year."' ";

        view::set("pager",sf::getModel("CompleteGroup")->getPager($addWhere,'ORDER BY updated_at DESC',20,'','',array('declare_year')));
        view::apply("inc_body","complete/group/complete_list");
        view::display("page_main");
    }

    /**
     * 查看分组的情况，包括包括项目，评审专家
     */
    function show()
    {
        $group = sf::getModel("CompleteGroup",input::getInput("mix.id"));
        if($group->isNew()) $this->page_debug(lang::get("The group do not exist!"),getFromUrl());

        view::set("group",$group);
        //专家列表
        view::set("experts",sf::getModel("Experts")->selectAll("user_id IN(SELECT expert_id FROM group_expert WHERE group_id = '".$group->getId()."' AND type = 'complete') ",'ORDER BY id DESC'));
        //项目列表
        $addWhere = "`project_id` IN(SELECT project_id FROM group_complete WHERE group_id = '".$group->getId()."' AND type = 'complete') ";
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
        view::set("pager",sf::getModel("Completes")->getPager($addWhere,'ORDER BY project_id DESC',20,'','',array('id','project_id','search','field')));
        //导数据使用
        $_SESSION['projects']['sqlStr'] = base64_encode("`project_id` IN(SELECT project_id FROM group_complete WHERE group_id = '".$group->getId()."' AND type = 'complete') ");
        $_SESSION['projects']['orderStr'] = base64_encode('ORDER BY project_id DESC');
        $_SESSION['experts']['sqlStr'] = base64_encode("user_id IN(SELECT expert_id FROM group_expert WHERE group_id = '".$group->getId()."' AND type = 'complete') ");
        $_SESSION['experts']['orderStr'] = base64_encode('ORDER BY id DESC');

        view::apply("inc_body","complete/group/show");
        view::display("page_main");
    }

    /**
     * 合并导出分组的项目
     */
    function export()
    {
        if(input::getInput("post.group_id"))
        {
            $group_ids = implode(',',input::getInput("post.group_id"));
            $addWhere = "project_id IN (SELECT project_id FROM group_complete WHERE group_id in (".$group_ids.") AND type = 'project')";
            $_SESSION['projects']['sqlStr'] = base64_encode($addWhere);
            //有排序条件就保存新的排序条件
            $_SESSION['projects']['orderStr'] = base64_encode("ORDER BY score DESC");
            $this->jump(site_url("export/export/index/type/projects"));
            exit;
        }
        $year = input::getInput("mix.declare_year") ? input::getInput("mix.declare_year") : config::get('current_declare_year',date('Y'));
        $addWhere = " office_id = ".input::getInput('session.office_id')." and is_lock = 2 AND group_year = '".$year."' ";
        view::set('year',$year);
        view::set("pager",sf::getModel("CompleteGroup")->selectAll($addWhere,'ORDER BY created_at DESC'));
        view::apply("inc_body","complete/group/export");
        view::display("page");
    }

    /**
     * 合并分组
     */
    function merge()
    {
        $ids = input::getInput("post.select_id");
        if(is_array($ids) && count($ids) > 1)
        {
            $group_id = array_shift($ids);
            $group = sf::getModel("CompleteGroup",$group_id);
            if($group->isNew()) $this->page_debug('选择的分组不存在？请重新选择条件试试!',getFromUrl());
            $group->doMerge($ids);//合并分组
            sf::getModel("historys")->addHistory($group->getId(),'分组合并成功，新的分组编号是：'.$group->getGroupSubject(),'group',1);
            $this->page_debug('分组合并成功，新的分组编号是：'.$group->getGroupSubject(),getFromUrl());
        }else $this->page_debug('至少需要选择两个分组进行合并',getFromUrl());
    }

    /**
     * 在项目分组中，发送消息给专家
     */
    function sendMessage()
    {
        $group = sf::getModel("CompleteGroup",input::getInput("mix.id"));
        $experts = $group->selectExpert();
        while ($expert = $experts->getObject()) {
            $exp[$expert->getUserId()] = $expert->getUserName();
        }
        if($group->isNew()) $this->page_debug('分组信息获取错误！',getFromUrl());

        if(input::getInput("post.message"))
        {
            $type = input::getInput("post.type") ? input::getInput("post.type") : 1;
            $exp = input::getInput("post.exp");
            $experts = $group->selectExpert($type);
            $short = sf::getModel("ShortMessages");
            while($expert = $experts->getObject())
            {
                if(!in_array($expert->getUserId(),$exp)) continue;
                if($expert->isNew()) continue;
                if(!isMobile($expert->getUserMobile())) continue;

                $short->sendSms($expert->getUserMobile(),sprintf(input::getInput("post.message"),$expert->getUserName(),$expert->getUser(true)->getUserName()),$group->getId(),'group',date("Y-m-d H:i:s",time()+6));//10分钟过后发送。

            }
            exit("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"><script>alert('消息发送成功！');parent.location.reload();</script>");
        }

        view::set("group",$group);
        view::set("exp",$exp);
        view::apply("inc_body","complete/group/message");
        view::display("page_blank");
    }

    /**
     * 移除分组
     */
    function setAssessTpl()
    {
        if(input::getInput("post.select_id")) $ids = input::getInput("post.select_id");
        else $ids[] = input::getInput("get.id");

        $book = sf::getModel("BookMaps",input::getInput("post.assess_tpl"));
        if($book->isNew()) $this->page_debug("替换的模板不存在！",getFromUrl());

        $_d["path.assess"] = $book->getPath();
        $_d["maps.assess"] = $book->getId();

        for($i=0,$n=count($ids);$i<$n;$i++)
        {
            $project = sf::getModel("projects")->selectByProjectId($ids[$i]);
            if($project->isNew() || $project->getScore() > 0) continue;//直接跳过
            $project->setConfigs($_d);
        }

        $this->page_debug("评分模板设置完毕！",getFromUrl());
    }

    /**
     * 设置专家角色
     */
    function setRole()
    {
        $group = sf::getModel("CompleteGroup",input::getInput("mix.id"));
        if($group->isNew()) $this->page_debug(lang::get("The group do not exist!"),getFromUrl());

        if(input::getInput("post.role"))
        {
            $msg = $role = array();
            $role = is_array(input::getInput("post.role")) ? input::getInput("post.role") : array();
            foreach($role as $key => $val){
                $grade = sf::getModel("GroupExpert",$key);
                if($grade ->getRole() != $val) $msg[] = '设置评审专家"'.$grade -> getExpert(true) -> getUserName().'"的角色为:"'.$val.'"';
                else continue;
                $grade -> setRole($val);
                $grade -> save();
            }

            if(count($msg)) sf::getModel("historys")->addHistory($group->getId(),implode(';',$msg).'!','group',1);//记录信息
            exit("<script>parent.location.reload();</script>");
        }
        view::set("group",$group);
        view::set("pager",sf::getModel("GroupExpert")->selectAll("group_id = '".$group->getId()."' and type = 'complete'"));
        view::apply("inc_body","complete/group/role");
        view::display("page_blank");
    }
    /**
     * 按组导出项目评审汇总表
     */
    function outSummary()
    {

        $projects =  sf::getModel("Projects")->selectAll(" group_id = '".input::getInput('mix.id')."'");
        $group =  sf::getModel("CompleteGroup",input::getInput('mix.id'));
        view::set("projects",$projects);
        view::set("group",$group);

        // view::apply("inc_body","complete/group/out_summary");
        // view::display("page");

        $htmlStr = view::getContent("complete/group/out_summary");
        header("Content-type:application");
        header("Content-Disposition: attachment; filename=".$group->getGroupSubject().'.doc');
        exit($htmlStr);
    }

    public function output_score_excel()
    {
        $groupId = input::getInput('get.id');
        $group = sf::getModel("CompleteGroup", $groupId);
        if ($group->isNew()) $this->page_debug('没有找到该分组');
        if ($group->isComplete() === false) $this->page_debug('该组还未评审完毕');
        $expertCount = sf::getLib('db')->result_first("SELECT count(*) c FROM `group_expert` where group_id = '{$groupId}' and type = 'complete'");
        if($expertCount==0){
            $this->page_debug('该组还未分配专家');
        }
        $completes = sf::getModel("Completes")->selectAll("project_id IN (SELECT project_id FROM `group_complete` WHERE group_id = '" . $group->getId() . "' AND type = '" . $group->getGroupType() . "')", "ORDER BY score DESC");
        $head = ['排序', '分组', '项目名称', '负责人', '申报单位', '主管部门', '片区', '单位性质','客观指标','主观指标','系统评分','专家评分','总分'];
        for($i=1;$i<=$expertCount;$i++){
            $head[] = '专家'.$i;
        }
        $body = [];
        $i = 0;
        $expertNames = [];
        $experts = [];
        $groupExperts = sf::getModel('GroupExpert')->selectAll("group_id = '$groupId' and type = 'complete'","order by expert_id asc");
        while($groupExpert = $groupExperts->getObject()){
            $experts[$groupExpert->getExpertId()] = $groupExpert->getExpert(true)->getUserName();
            $expertNames[] = $groupExpert->getExpert()->getUserName();
        }
        while ($complete = $completes->getObject()) {

            $body[$i][] = $i + 1;
            $body[$i][] = $group->getGroupNote();
            $body[$i][] = $complete->getSubject();
            $body[$i][] = filterName($complete->getUserName());
            $body[$i][] = $complete->getCompanyName();
            $body[$i][] = $complete->getDepartmentName();
            $body[$i][] = $complete->getDistrict();
            $body[$i][] = $complete->getCompany(true)->getProperty();
            $body[$i][] = (string)$complete->getObjectiveScore(true);
            $body[$i][] = (string)$complete->getSubjectiveScore(true);
            $body[$i][] = (string)$complete->getSystemScore(true);
            $body[$i][] = (string)$complete->getExpertScore(true);
            $body[$i][] = (string)$complete->getScore(true);
            foreach ($experts as $expertId=>$expertName){
                $grades = sf::getModel('CompleteGrade')->selectAll("project_id = '".$complete->getProjectId()."' and expert_id =  '{$expertId}'","order by expert_id asc");
                if($grades->getTotal()>0){
                    $grade = $grades->getObject();
                    $body[$i][] = $grade->getExpertScore();
                }else{
                    $body[$i][] = '回避';
                }

            }
            $i++;
        }
        $startZjNo = 13;
        foreach ($expertNames as $expertName){
            $head[$startZjNo] = $expertName;
            $startZjNo++;
        }
        $width = ['5.14', '12', '12', '7.14', '25', '22','12', '20', '10', '10', '10', '10', '7.29'];
        $footer = "评审专家组长签字：\n\n评审专家签字：\n\n                                                                   年   月   日\n\n纪检监督：\n\n                                                                   年   月   日";

        excel_out_assess($head, $body, $group->getGroupNote() . '评审结果', $group->getGroupNote() . '评审结果', $width, $group->getGroupYear()."年四川省临床重点专科\n".$group->getGroupNote()."评审结果", '', $footer);
    }

    public function output_score_word()
    {
        $groupId = input::getInput('get.id');
        $group = sf::getModel("CompleteGroup", $groupId);
        if ($group->isNew()) $this->page_debug('没有找到该分组');
        if ($group->isComplete() === false) $this->page_debug('该组还未评审完毕');
        $projects = sf::getModel("Projects")->selectAll("project_id IN (SELECT project_id FROM `group_complete` WHERE group_id = '" . $group->getId() . "' AND type = '" . $group->getGroupType() . "')", "ORDER BY score DESC");
        $body = [];
        $i = 0;
        while ($project = $projects->getObject()) {
            $body[$i]['no'] = $i + 1;
            $body[$i]['group_note'] = $group->getGroupNote();
            $body[$i]['accept_id'] = $project->getAcceptId();
            $body[$i]['subject'] = $project->getSubject();
            $body[$i]['user_name'] = filterName($project->getUserName());
            $body[$i]['corporation_name'] = $project->getCorporationName();
            $body[$i]['objective_score'] = (string)$project->getObjectiveScore(true);
            $body[$i]['subjective_score'] = (string)$project->getSubjectiveScore(true);
            $body[$i]['score'] = (string)$project->getScore(true);
            $i++;
        }

        $datas = [
            'year' => $group->getGroupYear(),
            'group_note' => $group->getGroupNote(),
            'result' => $body
        ];

        word_out($datas, WEBROOT . '/up_files/tpl/assess.docx', $group->getGroupNote().'评审结果', '',true);
        echo 'ok';
    }

    /**
     * 发送消息给所有专家
     */
    function sendMessageAll()
    {
        if (input::getInput("post.message")) {
            $type = input::getInput("post.type") ? input::getInput("post.type") : 1;
            $tpl = sf::getModel('ShortMessagesTpls',$type);
            $tpl->setMessage(input::post("message"));
            $tpl->save();
            $ids = input::post('select_id');
            $short = sf::getModel("ShortMessages");
            $i=0;
            foreach ($ids as $id){
                $expert = sf::getModel('Experts')->selectByUserId($id);
                if ($expert->isNew()) {
                    Log::write('没有该专家:'.$id);
                    continue;
                }
                $taskId = $expert->getAssessGroupId();
                if($taskId===false){
                    Log::write('未找到该分组:'.$taskId);
                    continue;
                }
                $user = $expert->getUser(true);
                if (!isMobile($user->getUserMobile())) {
                    Log::write('专家手机号格式不正确:'.$expert->getUserName().'-'.$user->getUserMobile());
                    continue;
                }
                $pwd = $user->getUserPassword();
                $msg = input::post("message");
                if (strstr($msg, '[姓名]') !== false) {
                    $msg = str_replace('[姓名]',$expert->getUserName(),$msg);
                }
                if (strstr($msg, '[账号]') !== false) {
                    if(empty($user->getUserName())){
                        Log::write('账号为空，发送短信失败，姓名:'.$expert->getUserName());
                        continue;
                    }
                    $msg = str_replace('[账号]',$user->getUserName(),$msg);
                }
                if (strstr($msg, '[密码]') !== false) {
                    if (strlen($pwd) >= 36) {
                        //不是明文密码,随机生成一个
                        $pwd = 'abc'.mt_rand(100000,999999);
                        $user->setUserPasswordNoEncrypt($pwd);
                        $user->save();
                    }
                    $msg = str_replace('[密码]',$pwd,$msg);
                }
                if (strstr($msg, '[网址]') !== false) {
                    $url = site_url('sms/index/task_id/' . $taskId . '/mobile/' . $user->getUserMobile());
                    $dwz = makeShortUrl($url);
                    $msg = str_replace('[网址]', $dwz, $msg);
                }
                $short->sendSms($user->getUserMobile(), $msg, $taskId, 'group', date("Y-m-d H:i:s", time() + 120), $taskId);//2分钟过后发送。
                $i++;
            }
            exit("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"><script>alert('成功发送{$i}条短消息！失败：".(count($ids)-$i)."条');parent.location.reload();</script>");
        }
        view::apply("inc_body", "complete/group/message_all");
        view::display("page_blank");
    }

    public function expert_search()
    {
        if (input::post('user_name')) {
            $groupYear = intval(input::post('group_year'));
            $typeId = intval(input::post('type_id'));
            $addwhere = "`group_year` = '{$groupYear}' and group_type = 'project' ";
            if ($typeId) $addwhere .= " and `types_id` = '{$typeId}'";
            $projectGroups = sf::getModel('CompleteGroup')->selectAll($addwhere);
            $expertIds = [];
            $datas = [];
            $i = 0;
            while ($projectGroup = $projectGroups->getObject()) {
                $groupExperts = sf::getModel('GroupExpert')->selectAll("group_id = '" . $projectGroup->getId() . "'");
                while ($groupExpert = $groupExperts->getObject()) {
//                    if(in_array($projectGroup->getId().'_'.$groupExpert->getExpertId(),$expertIds)) continue;
//                    $expertIds[] = $projectGroup->getId().'_'.$groupExpert->getExpertId();
                    $expert = sf::getModel('Experts')->selectByUserId($groupExpert->getExpertId());
                    if ($expert->getUserName() == input::post('user_name')) {
                        $datas[$i]['group_id'] = $projectGroup->getId();
                        $datas[$i]['group_note'] = $projectGroup->getGroupNote();
                        $datas[$i]['group_subject'] = $projectGroup->getGroupSubject();
                        $i++;
                    }
                }
            }
            view::set("datas", $datas);
        }
        view::apply("inc_body", "complete/group/expert_search");
        view::display("page_blank");

    }

    public function output_expert_ruleitem()
    {
        $groupId = input::getInput('get.id');
        $group = sf::getModel("CompleteGroup", $groupId);
        if ($group->isNew()) $this->page_debug('没有找到该分组');
        $expertIds = $group->getArrayWithExpert();
        $expertCount = $group->getExpertCount();
        $sheetIndex=0;
        $objPHPExcel = new \PHPExcel();
        foreach ($expertIds as $expertId){
            $expert = sf::getModel('Experts')->selectByUserId($expertId);
            $grades = sf::getModel('ProjectGrade')->selectAll("group_id = {$groupId} and expert_id = '{$expertId}'","order by expert_score desc");
            $head = ['排序', '分组', '项目编号', '项目名称', '负责人', '申报单位', '主管部门', '片区', '单位性质','专家评分'];
            $body = [];
            $i=0;
            while($grade = $grades->getObject()){
                $project = $grade->getProject(true);
                $body[$i][] = $grades->getIndex();
                $body[$i][] = $group->getGroupNote();
                $body[$i][] = $project->getAcceptId();
                $body[$i][] = $project->getSubject();
                $body[$i][] = filterName($project->getUserName());
                $body[$i][] = $project->getCorporationName();
                $body[$i][] = $project->getDepartmentName();
                $body[$i][] = $project->getDistrict();
                $body[$i][] = $project->getCorporation(true)->getProperty();
                $body[$i][] = (string)$grade->getExpertScore();
                $marks = $grade->getMark();
                $startIndexNo = 10;
                foreach ($marks as $mark=>$score){
                    $ruleItem = sf::getModel('RuleItems')->selectByRuleIdAndMark($grade->getRuleId(),$mark);
                    if($ruleItem->getScoreType()!='expert') continue;
                    if($grades->getIndex()==1){
                        $head[$startIndexNo] = $ruleItem->getLastSubject();
                        $startIndexNo++;
                    }
                    $body[$i][] = $score;

                }
                $i++;
            }
            $createFile = false;
            if($sheetIndex==$expertCount-1) $createFile=true;
            $objPHPExcel = excel_out($head,$body,$expert->getUserName(),$group->getGroupNote().'专家打分明细',[],'','',[],$sheetIndex,$createFile,$objPHPExcel);
            $sheetIndex++;
        }

    }


    public function output_all_ruleitem()
    {
        $groupId = input::getInput('get.id');
        $group = sf::getModel("CompleteGroup", $groupId);
        if ($group->isNew()) $this->page_debug('没有找到该分组');
        $completes = sf::getModel('Completes')->selectAll("group_id = {$groupId}","order by score desc");
        $head = ['排序', '分组', '项目名称', '负责人', '申报单位', '主管部门', '片区', '单位性质','客观指标','主观指标','系统评分','专家评分','总分'];
        $body = [];
        $i=0;
        while($complete = $completes->getObject()){
            $body[$i][] = $completes->getIndex();
            $body[$i][] = $group->getGroupNote();
            $body[$i][] = $complete->getSubject();
            $body[$i][] = filterName($complete->getUserName());
            $body[$i][] = $complete->getCompanyName();
            $body[$i][] = $complete->getDepartmentName();
            $body[$i][] = $complete->getDistrict();
            $body[$i][] = $complete->getCompany(true)->getProperty();
            $body[$i][] = (string)$complete->getObjectiveScore(true);
            $body[$i][] = (string)$complete->getSubjectiveScore(true);
            $body[$i][] = (string)$complete->getSystemScore(true);
            $body[$i][] = (string)$complete->getExpertScore(true);
            $body[$i][] = (string)$complete->getScore(true);
            $rule = $complete->getRule();
            $ruleItems = $rule->selectItems();
            $startIndexNo = 13;
            while($ruleItem = $ruleItems->getObject()){
                if($completes->getIndex()==1){
                    $head[$startIndexNo] = $ruleItem->getLastSubject();
                    $startIndexNo++;
                }
                $completeScore = $complete->getScoreByIndexCode($ruleItem->getMark());
                $body[$i][] = ($completeScore->getIsVeto() && $completeScore->getScoreType()=='system') ? $completeScore->getDesc() : $completeScore->getScore();
            }
            $i++;
        }
        excel_out($head,$body,$group->getGroupNote().'所有指标得分明细',$group->getGroupNote().'所有指标得分明细');
    }

    public function output_password()
    {
        $groupId = input::getInput('get.id');
        $group = sf::getModel("CompleteGroup", $groupId);
        if ($group->isNew()) $this->page_debug('没有找到该分组');
        $groupExperts = sf::getModel('GroupExpert')->selectAll("group_id = '{$groupId}' and type like 'complete%'","order by expert_id asc");
        $i=0;
        while($groupExpert = $groupExperts->getObject()){
            $expert = $groupExpert->getExpert(true);
            $user = $expert->getUser(true);
            $experts[$i]['no'] = $i+1;
            $experts[$i]['real_name'] = filterName($expert->getUserName());
            $experts[$i]['work_unit'] = $expert->getWorkUnit();
            $experts[$i]['user_name'] = $user->getUserName();
            $pwd = $user->getUserPassword();
            if (strlen($pwd) == 36 and $pwd[32] == ':') {
                //不是明文密码,随机生成一个
                $pwd = 'abc'.mt_rand(100000,999999);
                $user->setPassword($pwd);
                $user->save();
            }
            $experts[$i]['password'] = $pwd;
            $i++;
        }

        $groupType = '四川省国家临床重点专科建设项目';
        $datas = [
            'year' => $group->getGroupYear(),
            'type' => $groupType,
            'group_note' => $group->getGroupNote(),
            'experts' => $experts,
        ];
        $tpl = 'expert_password.docx';
        word_out($datas, WEBROOT . '/up_files/'.$tpl, $group->getGroupNote().'专家账号信息', '',true);
        echo 'ok';
    }
}
?>