<?php
namespace App\Controller\Gather;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Lang;
class project extends BaseController
{	
	
	/**
	 * 等待审核的项目列表
	 */
	public function wait_list()
	{
		$this->grid('gather/project/wait_list',"department_id = '".input::getInput("session.roleuserid")."' and statement = 5");
	}
	
	/**
	 * 上报的项目列表
	 */
	public function submit_list()
	{
		$this->grid('gather/project/submit_list',"department_id = '".input::getInput("session.roleuserid")."' and statement >=9");
	}
	
	/**
	 * 待科技厅受理的项目
	 */
	public function accept_list()
	{
		$this->grid('gather/project/accept_list',"department_id = '".input::getInput("session.roleuserid")."' and statement = ".config::get("HAS_RADICATE"));
	}
	
	/**
	 * 扩权县项目查询
	 */
	public function county_list()
	{
		$this->grid('gather/project/index',"department_id = '".input::getInput("session.roleuserid")."' and statement = ".config::get("HAS_RADICATE"));
	}
	
	/**
	 * 驳回的项目列表
	 */
	public function rejected_list()
	{
		$this->grid('gather/project/rejected_list',"department_id = '".input::getInput("session.roleuserid")."' and statement = 6");
	}
	
	/**
	 * 已经立项的项目列表
	 */
	public function radicate_list()
	{
		$this->grid('gather/project/radicate_list',"department_id = '".input::getInput("session.roleuserid")."' and statement IN (29,30) ");
	}
	
	/**
	 * 已经结题的项目列表
	 */
	public function complete_list()
	{
		$this->grid('gather/project/index',"department_id = '".input::getInput("session.roleuserid")."' and statement = ".config::get("HAS_COMPLETE"));
	}
	
	/**
	 * 综合搜索
	 */
	public function index()
	{
		$this->grid('gather/project/index',"department_id = '".input::getInput("session.roleuserid")."'");
	}
	
	/**
	 *
	 */
	function alternate_list()
	{
		$this->grid('gather/project/index',"department_id = '".input::getInput("session.roleuserid")."' AND is_wait = 1 AND statement < 29");
	}

    /**
     * 上报项目
     */
    function doSubmit()
    {
        if(input::getInput("post.select_id")) $ids = input::getInput("post.select_id");
        else $ids[] = input::getInput("get.id");

        for($i=0,$n=count($ids);$i<$n;$i++){
            $project = sf::getModel("projects")->selectByProjectId($ids[$i]);
            if($project->isNew())
                $this->error(lang::get("Must select a project!"),getFromUrl());
            if($project->getDepartmentId() != input::getInput("session.roleuserid"))
                $this->error(lang::get("You do not have permission to do it!"),getFromUrl());

            //申报时间验证
            if(!$project->time_validate()) {
                $this->error('申报通道已关闭', 'javascript:parent.closeWindow();');
            }

            //检查申报单位是否限制申报个数
            if($project->getDepartment()->getIgnoreCountLimit()==0 && $project->getCorporation()->getIgnoreCountLimit()==0) {
                //检查专科限额
                $count = sf::getModel('Projects')->selectAll("department_id = '".input::getInput('session.roleuserid')."' and declare_year = '".$project->getDeclareYear()."' and cat_id = '".$project->getCatId()."' and subject_code = '".$project->getSubjectCode()."' and statement >=9 and statement!=12")->getTotal();
                if($count>=1){
                    $this->error('每个专科限推荐1个',getFromUrl());
                }

                //检查总数限额
                $maxCount = 2;
                //省重总推荐数≤10个
                if($project->getCatId()==174){
                    $maxCount = 10;
                }
                $count = sf::getModel('Projects')->selectAll("department_id = '".input::getInput('session.roleuserid')."' and declare_year = '".$project->getDeclareYear()."' and type_current_group = '".$project->getTypeCurrentGroup()."' and cat_id = '".$project->getCatId()."' and statement >=9 and statement!=12")->getTotal();
                if($count>=$maxCount){
                    $this->error('已超数量限制：限推荐'.$maxCount.'个',getFromUrl());
                }
            }

            $department = sf::getModel("departments")->selectByUserId(input::getInput("session.roleuserid"));
            //已经受理不能重复受理
            if($project->getStatement() == 9)
                continue;
            //检查项目是否可以上报
//            if($msg = $project->validate())
//                $this->page_debug(sprintf(lang::get("You can not operate, because the:%s!"),'<br />'.implode('<br />',$msg)),getFromUrl());

            //配额计数
            $department->setUseQuota($department->getUseQuota() + 1);
            $department->save();
            //增加受理记录
            $project->addAcceptHistorys();
            //修改项目信息
            $project->setIsReport(1);
            $project->setStatement(9);
            $project->save();
            sf::getModel("historys")->addHistory($project->getProjectId(),lang::get("Has been submit by gather!"));
        }
        $this->success(lang::get("Has been submit by gather!"),getFromUrl());
    }

    /**
     * 审核项目
     */
    function doSubmitOnlyOne()
    {
        $project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->isNew()){
            $this->error('找不到该项目！');
        }

        //申报时间验证
        if(!$project->time_validate()) {
            $this->error('申报通道已关闭', 'javascript:parent.closeWindow();');
        }

        //检查限项
        if($project->getDepartment()->getIgnoreCountLimit()==0 && $project->getCorporation()->getIgnoreCountLimit()==0) {
            $configs = $project->getGuide()->getConfigs('limit');
            if($configs['gather_type_submit']>0){
                //相同专业限制推荐数量
                $maxCount = (int)$configs['gather_type_submit'];
                $count = sf::getModel('Projects')->selectAll("department_id = '".input::getInput('session.roleuserid')."' and declare_year = '".$project->getDeclareYear()."' and type_current_group = '".$project->getTypeCurrentGroup()."' and cat_id = '".$project->getCatId()."' and subject_code = '".$project->getSubjectCode()."' and statement >=9 and statement!=12")->getTotal();
                if($count>=$maxCount){
                    $this->error('每个专业限推荐'.$maxCount.'个', 'javascript:parent.closeWindow();');
                }
            }
            if($configs['gather_unit_submit']>0){
                //推荐单位针对同一家医院推荐数量限制
                $maxCount = (int)$configs['gather_unit_submit'];
                $count = sf::getModel('Projects')->selectAll("department_id = '".input::getInput('session.roleuserid')."' and declare_year = '".$project->getDeclareYear()."' and type_current_group = '".$project->getTypeCurrentGroup()."' and cat_id = '".$project->getCatId()."' and corporation_id = '".$project->getCorporationId()."' and statement >=9 and statement!=12")->getTotal();
                if($count>=$maxCount){
                    $this->error('同一医院推荐专业不超过'.$maxCount.'个', 'javascript:parent.closeWindow();');
                }
            }
            if($configs['gather_submit']>0 || $configs['gather_specific_submit']){
                //检查总数限额
                $maxCount = (int)$configs['gather_submit']; //推荐单位统一限制的数量
                $gatherSpecificSubmit = $configs['gather_specific_submit'];//针对个别推荐单位限制的数量
                $gathers = explode('|',$gatherSpecificSubmit);
                $gatherMaxCount = [];
                foreach ($gathers as $gather){
                    $gatherArr = explode(':',$gather);
                    $gatherMaxCount[$gatherArr[0]] = (int)$gatherArr[1];
                }
                if($gatherMaxCount[$project->getDepartment()->getSubject()]>0){
                    $maxCount = $gatherMaxCount[$project->getDepartment()->getSubject()];
                }
                if($maxCount>0){
                    $count = sf::getModel('Projects')->selectAll("department_id = '".input::getInput('session.roleuserid')."' and declare_year = '".$project->getDeclareYear()."' and type_current_group = '".$project->getTypeCurrentGroup()."' and cat_id = '".$project->getCatId()."' and statement >=9 and statement!=12")->getTotal();
                    if($count>=$maxCount){
                        $this->error('已超数量限制：限推荐'.$maxCount.'个', 'javascript:parent.closeWindow();');
                    }
                }
            }
        }


        if(input::getInput("post.content")){

            if($project->getDepartmentId() != input::getInput("session.roleuserid"))
                $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
//            $department = sf::getModel("departments")->selectByUserId(input::getInput("session.roleuserid"));
//            //配额计数
//            $department->setUseQuota($department->getUseQuota() + 1);
//            $department->save();
//            //增加受理记录
//            $project->addAcceptHistorys();
            //修改项目信息
            $project->setIsReport(1);
            $project->setStatement(9);
            $project->save();
            sf::getModel("Historys")->addHistory($project->getProjectId(),'主管部门审核通过！<br/>'.input::getInput("post.content"));

            //通知管理员
            $project->sendMessageToManager($project->getCorporationName()."的《".$project->getSubject()."》项目已通过主管部门审核！");

            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/审核通过！';</script>");
        }
        view::set("project",$project);
        view::set("msg","同意申报！");
        view::apply("inc_body","gather/project/note_submit");
        view::display("page_blank");
    }


    /**
     * 驳回项目
     */
    function doRejected()
    {
        $project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->getDepartmentId() != input::getInput("session.roleuserid") || $project->getStatement() > 9)
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        if(input::getInput("post.content"))
        {
            //如果驳回上报的项目就减去配额以供其他项目使用
            if($project->getStatement() >= 5){
                $department = sf::getModel("departments")->selectByUserId(input::getInput("session.roleuserid"));
                $department->setUseQuota($department->getUseQuota() - 1);
                $department->save();
            }
            //删除受理记录
            sf::getModel('AcceptHistorys')->remove("`project_id` = '".$project->getProjectId()."'");
            if($project->getIsReport()==1) $project->setIsReport(0);
            $project->setStatement(config::get("GATHER_BACK"));
//            $project->doDeletePdf();
            if($project->save()){
                $msg =  lang::get("Has been Rejected by gather!");
                $project->sendMessage(sprintf("你的项目《%s》已被主管部门退回。退回原因：".input::getInput("post.content"),$project->getSubject()));
            }
            else $msg =  lang::get("Error has happened!");

            sf::getModel("historys")->addHistory($project->getProjectId(),$msg."退回原因：<br>".input::getInput("post.content"));
            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/退回成功！';</script>");
        }
        view::set("project",$project);
        view::apply("inc_body","gather/project/back");
        view::display("page_blank");

    }
	
}