<?php
namespace App\Controller\Gather;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
class corporation extends BaseController
{
	
	/**
	 * 单位检索
	 */
	public function index()
	{
		$this->unit_gird("department_id = '".input::getInput("session.roleuserid")."' ","gather/corporation/index");
	}
	
	
	/**
	 * 等待审核的单位
	 */
	public function wait_list()
	{
		$this->unit_gird("department_id = '".input::getInput("session.roleuserid")."' AND `is_lock` = 1","gather/corporation/wait_list");
	}
	
	/**
	 * 已经审核的单位
	 */
	public function submit_list()
	{
		$this->unit_gird("department_id = '".input::getInput("session.roleuserid")."' AND `is_lock` = 5","gather/corporation/submit_list");
	}
	
	/**
	 * 被退回的单位
	 */
	
	public function back_list()
	{
		$this->unit_gird("department_id = '".input::getInput("session.roleuserid")."' AND `is_lock` = 2","gather/corporation/back_list");
	}
	
	
	/**
	 * 实名认证的单位
	 */
	public function real_list()
	{
		$this->unit_gird("department_id = '".input::getInput("session.roleuserid")."' AND `is_lock` = 0 ","gather/corporation/real_list");
	}
	
	
	/**
	 * 注册中的账号
	 */
	public function reg_list()
	{
		$this->unit_gird("department_id = '".input::getInput("session.roleuserid")."' AND `is_lock` = 9 ","gather/corporation/reg_list");
	}
	
	/**
	 * 审核申报单位
	 */
	function doSubmit()
	{
		$user = sf::getModel("corporations")->selectByUserId(input::getInput("mix.userid"));
		if($user->isNew()) $this->page_debug('申报单位不存在！',getFromUrl());
		
		if($user->getIslock() == 4)//黑名单
		$this->page_debug('该企业被列入黑名单不能审核通过!',getFromUrl());	
		
		if(count($user->validate())){//内容不全
			$this->page_debug('该申报单位不能审核通过，其原因是：'.implode(';',$user->validate()).'。',getFromUrl());	
		}
        if($user->getLevel()=='三级甲等'){
            $user->setIslock(5);//待科技厅审核
        }else{
            $user->setIslock(0);//认证通过
            $user->setIsReport(1);//已审核标志
        }

		$user->save();
		sf::getModel("historys")->addHistory($user->getUserId(),'主管部门审核通过！','corporations');
		$this->page_debug('审核成功！',getFromUrl());
	}
	
	/**
	 * 退回
	 */
	function doBack()
	{
		$user = sf::getModel("corporations")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post.content") && !$user->isNew()){
			$user->setIslock(2);//归口部门退回
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'归口部门审核退回！其原因是：<br />'.input::getInput("post.content"),'corporations');
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$user);
		view::apply("inc_body","gather/corporation/note");
		view::display("page_blank");
	}
	
	/**
	 * 关联的账号
	 */
	function users()
	{
		$corporation = sf::getModel("corporations")->selectByUserId(input::getInput("mix.userid"));	
		view::set("corporation",$corporation);
		view::apply("inc_body","gather/corporation/users");
		view::display("page_blank");
	}
	
	/**
	 * 重置密码
	 */
	function setpasswd()
	{
		$user = sf::getModel("Users")->selectByUserId(input::getInput("mix.userid"));
		if($user->isNew()) $this->page_debug('账号信息不存在！',getFromUrl());
		$password = 'abcd'.rand(100000,999999);
		$user->setUserPassword($password);
		$user->setUpdatedAt(date("Y-m-d H:i:s"));
		$user->save();
		$user->sendMessage(sprintf(lang::get('MSG.D004'),$user->getUserUsername(),$user->getUserName(),$password),$user->getUserId(),'corporations');
		sf::getModel("historys")->addHistory($user->getUserId(),lang::get("The password has been change!"),'corporations');
		$this->page_debug(sprintf(lang::get("The new password is:%s!"),$password),getFromUrl());	
	}

}