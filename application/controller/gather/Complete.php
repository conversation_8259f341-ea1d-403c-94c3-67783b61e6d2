<?php
namespace App\controller\gather;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Complete extends BaseController
{	
	function create()
    {
        $this->jump(site_url('engine/completer/create'));
    }
	function write_list()
	{
		$this->grid('gather/complete/write_list',"user_id = '".input::getInput("session.roleuserid")."' AND `statement` IN (-1,0,1,6,12) ");
	}

	function wait_list()
	{
		$this->grid('gather/complete/wait_list',"department_id = '".input::getInput("session.roleuserid")."' AND `statement` = 5 ");
	}
	
	function submit_list()
	{
		$this->grid('gather/complete/submit_list',"user_id = '".input::getInput("session.roleuserid")."' AND `statement` IN (9,10) ");
	}

	function accept_list()
	{
		$this->grid('gather/complete/accept_list',"department_id = '".input::getInput("session.roleuserid")."' AND `statement` = 9 ");
	}
	
	function rejected_list()
	{
		$this->grid('gather/complete/rejected_list',"department_id = '".input::getInput("session.roleuserid")."' AND `statement` = 6 ");
	}
	
	
	function index()
	{
		$this->grid('gather/complete/index',"department_id = '".input::getInput("session.roleuserid")."'");
	}

    /**
     * 上报项目
     */
    function doSubmit()
    {
        $complete = sf::getModel("Completes")->selectByProjectId(input::getInput("mix.id"));
        if(input::session('roleuserid') != $complete->getUserId() || $complete->isNew())
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        $msg = $complete->validate();
        if(input::post("id"))
        {
            if(count($msg)) $this->page_debug("请按照提示修改完成后再次上报。",getFromUrl());
            $complete->setStatement(9);
            $complete->setDeclareAt(date('Y-m-d H:i:s'));
            $complete->save();
            $complete->setConfigs("file.apply",'');//清空生成的
            sf::getModel("Historys")->addHistory($complete->getProjectId(),'验收报告上报','complete');
            $this->success(lang::get("Has been submit!"),site_url("gather/complete/index"));
        }

        $data['msg'] = $msg;
        $data['complete'] = $complete;
        view::set($data);
        view::apply("inc_body","gather/complete/submit_do");
        view::display("page_main");
    }
	
	/**
	 * 审核中期
	 */
	function doAccept()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		if(!$ids[0])
			$this->page_debug('请至少选择一个项目！',getFromUrl());			
		for($i=0,$n=count((array)$ids);$i<$n;$i++){
			$complete = sf::getModel("Completes")->selectByProjectId($ids[$i]);
			if($complete->getDepartmentId() != input::getInput("session.roleuserid")) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
			$complete->setStatement(9);
			if($complete->save()) sf::getModel("historys")->addHistory($complete->getProjectId(),"主管部门已审核该验收报告",'complete');
		}
		$this->success("上报成功！",getFromUrl());
	}
	
	/**
	 * 退回任务书——单个
	 */
	function doRejectedOnlyOne()
	{	
		$complete = sf::getModel("Completes")->selectByProjectId(input::getInput("mix.id"));
		if(input::getInput("post.content"))
		{
			if($complete->getDepartmentId() != input::getInput("session.roleuserid"))
				$this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
			$complete->setStatement(6);
			$complete->save();
			sf::getModel("historys")->addHistory($complete->getProjectId(),"主管部门已退回该验收报告"."<br />".input::getInput("post.content"),'complete');
			exit("<script>parent.location.reload();</script>");	
		}
		view::set("complete",$complete);
		view::apply("inc_body","gather/complete/back");
		view::display("page_blank");
	}
	/**
	 * 驳回任务书——多个
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		if(!$ids[0])
			$this->page_debug('请至少选择一个项目！',getFromUrl());			
		for($i=0,$n=count($ids);$i<$n;$i++){
			$complete = sf::getModel("Completes")->selectByProjectId($ids[$i]);
			if($complete->getDepartmentId() != input::getInput("session.roleuserid"))
				$this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
			$complete->setStatement(6);
			$complete->save();
			sf::getModel("historys")->addHistory($complete->getProjectId(),"主管部门已退回该验收报告",'complete');
		}
		$this->page_debug('退回成功！',getFromUrl());
	}

    public function stat()
    {
        if(input::post()){
            $declareYear = input::post('declare_year');
            $departmentId = input::session('roleuserid');
            $url = 'process/index/index/type/complete/declare_year/'.$declareYear.'/department_id/'.$departmentId;
            $this->jump(site_url($url));
        }
        $declareYear = input::getMix('declare_year')?:getLastYear();
        view::set("declareYear",$declareYear);
        view::apply("inc_body","gather/complete/stat");
        view::display("page");
    }

    /**
     * 集中处理项目的搜索信息
     *
     */
    public function grid($tpl = 'gather/complete/wait_list',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

        if(input::getInput("mix.declare_year"))
            $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";

        //将搜索条件保存以备打印或者导出
        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['completes']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['completes']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['completes']['orderStr'] = base64_encode($addSql);
        }
//		dd($addWhere);
        //显示页面
        $form_vars = array('search','declare_year','declare_month','department_id','department_id','statement');

        view::set("pager",sf::getModel('Completes')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }
}