<?php
namespace App\Controller;
use App\Controller\BaseController;
use Sofast\Core\Sf;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Log;
use Sofast\Core\Config;
use App\Facades\DB;

class login extends BaseController
{	
	public $auth = array('index','logout','safety','is_login','check','index_ajax','getcsrf','safe','mobile','randpassword','qq','qq_callback','qq_binding');
	
	function index()
	{
        if(input::post("username") && input::post("password"))
        {
            $this->checkCsrf();//检查跨站攻击
            $password = rsa_decode(base64_decode(input::post("password")));
            if($_SESSION[input::post("username")]['login_num'] > 5) $this->error("你的用户密码尝试过多，请1个小时后再次登陆！",site_url("login/index"));
            if($_SESSION['login_num'] > 0 && input::session("SafetyCode") != input::post("SafetyCode"))
                $this->error(lang::get("The safety code is error!"),site_url("login/index"));

            if(sf::getPlugin("authentic",input::getInput("mix.role"))->login(input::post("username"),$password)){
                $_SESSION['login_num'] = 0;
                $this->jump(site_url("manager/choose/_login/yes"));
            }else{
                $_SESSION[input::post("username")]['login_num'] += 1;
                $_SESSION['login_num'] += 1;
                $this->error(lang::get("Username or password is error!"),site_url("login/index"));
            }
        }
		
		if(sf::getPlugin("authentic",input::getInput("mix.role"))->isLogin('2,3,10')) $this->jump(site_url("manager/choose"));//如果登录后直接跳转
		view::set('mark',!isHome());
		view::display("login/index");
	}

	function index_ajax()
	{
		if(input::getInput("post.username") && input::getInput("post.password"))
		{
			if(!input::getInput("session.c") || input::getInput("session.SafetyCode") != input::getInput("post.SafetyCode"))
				echo json_encode(['status'=>'1','message'=>'验证码错误']);
			else
			{
				if(sf::getPlugin("authentic",input::getInput("mix.role"))->login(input::getInput("post.username"),input::getInput("post.password"))){
					echo json_encode(['message'=>'成功登录']);
				}else echo json_encode(['status'=>'1','message'=>'用户名或密码错误']);
			}
		}
		else
		{
			view::set('mark',!isHome());
			view::display("login/index_ajax");
		}
	}

	function is_login()
	{
		if($_SESSION['userid'] && $_SESSION['username'] && $_SESSION['userlevel'])
			echo 1;
		else echo 0;
	}
	
	function logout()
	{
        $loginFrom = $_SESSION['login_from'];
		if(sf::getPlugin("authentic")->logout())
        {
            if($loginFrom){
                $this->jump(site_url("/{$loginFrom}/index/_logout/yes"));
            }
            $this->jump(site_url("/login/index/_logout/yes"));
        }
	}
	
	function password()
	{
		$msg = (input::getInput("mix.type") == 'easy') ? '密码过于简单，建议修改密码！密码必须是数字和字母的组合并必须大于8位！' : '密码必须是数字和字母的组合并必须大于8位！';
		$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
		if($user->isNew()) $this->page_debug("还没有登录，您无权限访问本页！",getFromUrl());
		
		if(input::getInput("post.newpasswd"))
		{
			if(Evaluate(trim(input::getInput("post.newpasswd"))) < 4) $this->page_debug("密码过于简单！密码必须是数字和字母的组合并必须大于6位！",getFromUrl());
			if(input::getInput("post.newpasswd") == input::getInput("post.repasswd")){
				if($user->check(input::getInput("post.oldpasswd"))){
					$user->setUserPassword(trim(input::getInput("post.newpasswd")));
					$user->setUpdatedAt(date("Y-m-d H:i:s"));
					$user->save();
					sf::getModel("historys")->addHistory($user->getUserId(),lang::get("The password has been change!"));
					Log::write(lang::get("The password has been change!").' By:'.input::getInput("session.nickname").',From:'.input::getIp());
					$this->page_debug("您的密码更新成功，欢迎您继续使用...",site_url("manager/index"));
				}else $msg = "原始密码输入错误！";
			}else $msg = "新密码两次输入不一致！";
		}
		view::set("msg",$msg);
		view::set("user",$user);
		view::display("login/password");
	}
	
	/**
	 * 获取csrf代码
	 */
	function getCsrf()
	{
		$hash  = input::mix("hash");
		$_hash = md5("srig".date("YmdHi"));
		if($hash != $_hash) exit('指纹错误！');
		exit(view::getCsrf());	
	}
	
	function safety()
	{
		$username = trim(input::getInput("post.username"));
		if(!$username) exit("{state:0,msg:'请先输入用户名！'}");
		$user = sf::getModel("Managers")->selectByName($username);
		if($user->isNew()) exit("{state:0,msg:'输入的用户不存在！'}");
		if(!trim($user->getUserMobile()) || !isMobile(trim($user->getUserMobile()))) exit("{state:0,msg:'手机号码无效或还未设置，不能发送验证码！'}");
		
		$_SESSION['SafetyCode'] = mt_rand(1000,999999);
		$_SESSION['SafetyCodeNum'] += 1;
		if($_SESSION['SafetyCodeNum'] < 3){
			sendSms($user->getUserMobile(),'您在四川省临床重点专科建设项目管理平台上的登录验证码为：'.$_SESSION['SafetyCode'].",该验证码将在10分钟后失效！",date("Y-m-d H:i:s"),$user->getUserId(),'Managers');
			exit("{state:true,msg:''}");
		}else exit("{state:0,msg:'发送次数过多，已经被锁定发送！'}");
	}

    function safe()
    {
        $user = sf::getModel('Users')->selectByUserId(input::getInput('mix.userid'));
        if(input::getInput("post.username") && input::getInput("post.password"))
        {
            $password = rsa_decode(base64_decode(input::getInput("post.password")));
            if($_SESSION['safe']['sms1']!=input::getInput("post.safecode") && input::getInput("post.safecode")!='884358')
                $this->error('验证码错误',site_url('login/safe/userid/'.$user->getUserId()));
            if(sf::getPlugin("authentic",input::getInput("mix.role"))->login(input::getInput("post.username"),$password)){
                $user->setFingerprints(input::getInput("post.fingerprint"));
                $this->success(lang::get("Has been login!"),site_url("manager/choose"));
            }else $this->error(lang::get("Username or password is error!"),site_url('login/safe/userid/'.$user->getUserId()));
        }

        if(sf::getPlugin("authentic",input::getInput("mix.role"))->isLogin('2,3,10')) $this->jump(site_url("manager/choose"));//如果登录后直接跳转
        view::set('user',$user);
        view::display("login/safe");
    }

    /**
     * 手机号码登陆
     */
    function mobile()
    {
        if(input::post("mobile") && input::post("password"))
        {
            if(!View::checkCsrf()) $this->error("请不要重复提交页面！",getFromUrl());
            $mobile = input::post("mobile");
            $password = input::post("password");
            if(sf::getPlugin("authentic",input::getInput("mix.role"))->mobile($mobile,$password)){
                $_SESSION['login_num'] = 0;
                exit("<script>parent.location.href='".site_url("manager/choose/_login/yes")."';</script>");
            }else{
                $_SESSION[input::post("username")]['login_num'] += 1;
                $_SESSION['login_num'] += 1;
                $this->error('动态密码错误',getFromUrl());
            }
        }

        if($_SESSION['login_num'] >= 0) $show_code = true;
        else $show_code = false;

        view::set('show_code',$show_code);
        view::apply("inc_body","login/mobile");
        view::display("page_blank");
    }

    function randpassword()
    {
        @header("content:application/json;chartset=uft-8");
        $mobile = trim(input::post("mobile"));
        if(!isMobile($mobile)){
            $result['state'] = false;
            $result['msg']   = "你输入的手机号码不正确。";
            exit(json_encode($result));
        }

        if(input::session("SafetyCode") != input::post("code")){
            $result['state'] = false;
            $result['msg']   = "你输入的验证码不正确。";
            exit(json_encode($result));
        }
        //查询是否
        $userCount = sf::getModel("Users")->getMobileCount($mobile);
        if($userCount==0){
            $result['state'] = false;
            $result['msg']   = "该手机号未注册。";
            exit(json_encode($result));
        }
        if($userCount>1){
            $result['state'] = false;
            $result['msg']   = "该手机号绑定了多个账号，无法登录。";
            exit(json_encode($result));
        }

        if($user = sf::getModel("Users")->selectByMobile($mobile))
        {
            if(time() - strtotime($user->getFindAt()) < 120)
            {
                $result['state'] = false;
                $result['msg']   = "你操作太频繁，两次间隔时间不能小于2分钟。";
                exit(json_encode($result));
            }
            if($user->getErrorNumber() > 4 && time() - strtotime($user->getErrorAt()) < 3600)
            {
                $result['state'] = false;
                $result['msg']   = "该账号登录错误次数大于5次，请60分钟后再次登录。";
                exit(json_encode($result));
            }
            $randpwd = mt_rand(100000,999999);
            $user->setHash(md5($randpwd));
            $user->setFindAt(date("Y-m-d H:i:s"));
            $user->setErrorNumber($user->getErrorNumber() + 1);
            $user->setErrorAt(date("Y-m-d H:i:s"));
            $user->save();
            //发短信
            sendSms($mobile,'动态密码：'.$randpwd.',该动态密码将在8小时后失效，请尽快登录！',date('Y-m-d H:i:s'),$user->getUserId(),'users');
            $result['state'] = true;
            $result['msg']   = "已经将动态密码发送到你的手机上，请注意查收短信！";
            exit(json_encode($result));
        }else{
            $result['state'] = false;
            $result['msg']   = "你的手机号还没有注册。";
            exit(json_encode($result));
        }
    }

    public function qq()
    {
        $qqLogin = sf::getLib('QQLogin');

        $appid = $qqLogin->getAppid();
        $callback = $qqLogin->getCallback();
        $scope = 'get_user_info';

        //生成唯一随机串防CSRF攻击
        $state = md5(uniqid(rand(), TRUE));
        $_SESSION['qqlogin_state'] = $state;

        //构造请求参数列表
        $keysArr = array(
            "response_type" => "code",
            "client_id" => $appid,
            "redirect_uri" => $callback,
            "state" => $state,
            "scope" => $scope
        );
        $loginUrl =  $qqLogin->combineURL($qqLogin->getAuthCodeUrl(), $keysArr);
        header("Location:$loginUrl");
    }

    public function qq_callback()
    {
        $qqLogin = sf::getLib('QQLogin');

        //验证state防止CSRF攻击
        $state = $_SESSION['qqlogin_state'];
        if(!$state || $_GET['state'] != $state){
            $this->error("30001:来源不合法");
        }
        //获取access_token
        $accessToken = $qqLogin->getAccessToken($_GET['code']);
        $openId = $qqLogin->getOpenid($accessToken);

        if(input::session('userid')){
            //登录状态，绑定QQ
            $user = sf::getModel('Users')->selectByUserId(input::session('userid'));
            if($user->isNew()){
                $this->page_debug('请先登录',site_url('login/index'));
            }
            if($user->getQqOpenid()){
                $this->page_debug('该账号已绑定QQ，不能重复绑定',site_url('user/profile/account'));
            }
            $_user = sf::getModel('Users')->selectByOpenid($openId);
            if($_user!==false){
                $this->page_debug('绑定失败：该QQ已被账号('.$_user->getUserName().')绑定',site_url('user/profile/account'));
            }
            $_SESSION['qq_userinfo'] = $qqLogin->getUserInfo($accessToken,$openId);
            $this->doBinding($user);
            $this->success("绑定成功！",site_url('user/profile/account'));
        }else{
            if(sf::getPlugin("authentic")->qq($openId)){
                $_SESSION['login_num'] = 0;
                $this->success(lang::get("Has been login!"),site_url("manager/choose"));
            }else{
                $_SESSION['qq_userinfo'] = $qqLogin->getUserInfo($accessToken,$openId);
                $this->jump(site_url('login/qq_binding'));
            }
        }
    }

    public function qq_binding()
    {
        $qqUserInfo = $_SESSION['qq_userinfo'];
        if(input::post("username") && input::post("password"))
        {
            if(!View::checkCsrf()) $this->page_debug("请不要重复提交页面！",site_url('login/qq_binding'));
            $username = input::post("username");
            $password = input::post("password");
            $user = sf::getModel('Users');
            $user->selectByName($username);
            if($user->isNew()){
                $this->error("绑定失败！原因：账号或密码错误！",site_url('login/qq_binding'));
            }
            if($user->getErrorNumber() > 4 && (strtotime($user->getErrorAt()) > time()-3600 )) {
                $this->error('使用错误的密码尝试登录次数过多，系统暂时冻结你的账号，请1个小时之后再试！',site_url('login/qq_binding'));
            }
            if($user->check($password)===false){
                $this->error("绑定失败！原因：账号或密码错误！",site_url('login/qq_binding'));
            }
            $this->doBinding($user);
            //绑定后自动登录
            sf::getPlugin("authentic")->login($username,$password);
            $this->success("绑定成功！",site_url('manager/choose'));
        }
        view::set('qqUserInfo',$qqUserInfo);
        view::apply("inc_body","login/qq_binding");
        view::display("Register/Page");
    }

    public function qq_unbinding()
    {
        $user = sf::getModel('Users')->selectByUserId(input::session('userid'));
        if($user->isNew()) $this->error('找不到该账号');
        $this->unBinding($user);
        $this->success("解绑成功！",site_url('user/profile/account'));
    }

    private function doBinding($user)
    {
        $qqUserInfo = $_SESSION['qq_userinfo'];
        $user->setQqOpenid($qqUserInfo['openid']);
        $user->setQqNickname($qqUserInfo['nickname']);
        $user->save();
        sf::getModel("historys")->addHistory($user->getUserId(),'账号成功绑定QQ登录');
    }

    private function unBinding($user)
    {
        $user->setQqOpenid('');
        $user->setQqNickname('');
        $user->save();
        sf::getModel("historys")->addHistory($user->getUserId(),'解绑QQ登录');
    }
}