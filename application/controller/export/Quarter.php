<?php
namespace App\controller\export;
use App\Controller\BaseController;
use Sofast\Core\lang;
use Sofast\Core\Log;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\config;
use Sofast\Core\Sf;
use App\Model\Exports;

class Quarter extends BaseController
{

    function index(){
        view::apply("inc_body","export/quarter/index");
        view::display("page");
    }

    function doExport()
    {
        $guide = sf::getModel('Guides',input::post('guideid'));
        if($guide->isNew()) $this->error('没有找到该类报表');
        $statement = intval(input::post('statement'));
        $departmentId = input::session('userlevel')==4 ? input::session('roleuserid') : input::post('department_id');
        $addwhere = "guide_id = '".$guide->getId()."'";
        if($departmentId){
            $addwhere .= " and department_id = '{$departmentId}'";
        }
        if($statement){
            if($statement==3){
                $addwhere.=" AND statement IN (3,12)";
            }else{
                $addwhere.=" AND statement = '{$statement}'";
            }
        }
        $quarters = sf::getModel('Quarters')->selectAll($addwhere,"order by id asc");
        $body = [];
        $i=0;
        while($quarter = $quarters->getObject()){
            $body[$i][] = $quarter->getSubject();
            $body[$i][] = $quarter->getCompany(true)->getCode();
            $body[$i][] = $quarter->getLinkman();
            $body[$i][] = $quarter->getMobile();
            $body[$i][] = $quarter->getD01();
            $body[$i][] = $quarter->getD011();
            $body[$i][] = $quarter->getE01();
            $body[$i][] = $quarter->getE011();
            $body[$i][] = $quarter->getE03();
            $body[$i][] = $quarter->getE031();
            $body[$i][] = $quarter->getE04();
            $body[$i][] = $quarter->getE041();
            $body[$i][] = $quarter->getE08();
            $body[$i][] = $quarter->getE081();
            $body[$i][] = strip_tags($quarter->getState(false));
            $i++;
        }

        if($guide->getFormId()==1){
            $this->doExportForm1($addwhere,$guide);
        }else{
            $this->doExportForm2($addwhere,$guide);
        }
    }

    function doExportForm1($addwhere,$guide)
    {
        $quarters = sf::getModel('Quarters')->selectAll($addwhere,"order by id asc");
        $body = [];
        $i=0;
        while($quarter = $quarters->getObject()){
            $body[$i][] = $quarter->getSubject();
            $body[$i][] = $quarter->getCompany(true)->getCode();
            $body[$i][] = $quarter->getLinkman();
            $body[$i][] = $quarter->getMobile();
            $body[$i][] = $quarter->getD01();
            $body[$i][] = $quarter->getD011();
            $body[$i][] = $quarter->getE01();
            $body[$i][] = $quarter->getE011();
            $body[$i][] = $quarter->getE03();
            $body[$i][] = $quarter->getE031();
            $body[$i][] = $quarter->getE04();
            $body[$i][] = $quarter->getE041();
            $body[$i][] = $quarter->getE08();
            $body[$i][] = $quarter->getE081();
            $body[$i][] = strip_tags($quarter->getState(false));
            $i++;
        }

        $head = [
            ['企业名称','统一社会信用代码或组织机构代码','填表人','联系电话','期末从业人员','','工业（建筑业）总产值（万元）','','营业收入','','其中：主营业务收入','','出口交货值','','状态'],
            ['企业名称','统一社会信用代码或组织机构代码','填表人','联系电话','D01（人）','','E01（万元）','','E03（万元）','','E04（万元）','','E08（万元）','','状态'],
            ['企业名称','统一社会信用代码或组织机构代码','填表人','联系电话','1-本月','上年同期','1-本月','上年同期','1-本月','上年同期','1-本月','上年同期','1-本月','上年同期','状态'],
        ];

        $mergeHead = [
            'A1'=>'A3',
            'B1'=>'B3',
            'C1'=>'C3',
            'D1'=>'D3',
            'E1'=>'F1',
            'E2'=>'F2',
            'G1'=>'H1',
            'G2'=>'H2',
            'I1'=>'J1',
            'I2'=>'J2',
            'K1'=>'L1',
            'K2'=>'L2',
            'M1'=>'N1',
            'M2'=>'N2',
            'O1'=>'O3',
        ];
        $columnStyle = [
            'A'=>[
                'width'=>30,
                'align'=>'left',
            ],
            'B'=>[
                'width'=>25,
            ],
            'D'=>[
                'width'=>15,
            ],
            'O'=>[
                'width'=>20,
            ],
        ];
        excel_out($head, $body, $guide->getSubject().'汇总表', $guide->getSubject().'汇总表',$mergeHead,$columnStyle);
    }

    function doExportForm2($addwhere,$guide)
    {
        $quarters = sf::getModel('Quarters')->selectAll($addwhere,"order by id asc");
        $body = [];
        $i=0;
        while($quarter = $quarters->getObject()){
            $body[$i][] = $quarter->getSubject();
            $body[$i][] = $quarter->getCompany(true)->getCode();
            $body[$i][] = $quarter->getLinkman();
            $body[$i][] = $quarter->getMobile();
            $body[$i][] = $quarter->getD01();
            $body[$i][] = $quarter->getD011();
            $body[$i][] = $quarter->getD11();
            $body[$i][] = $quarter->getD111();
            $body[$i][] = $quarter->getE03();
            $body[$i][] = $quarter->getE031();
            $body[$i][] = $quarter->getF01();
            $body[$i][] = $quarter->getF011();
//            $body[$i][] = $quarter->getE04();
//            $body[$i][] = $quarter->getE041();
//            $body[$i][] = $quarter->getE05();
//            $body[$i][] = $quarter->getE051();
//            $body[$i][] = $quarter->getE06();
//            $body[$i][] = $quarter->getE061();
//            $body[$i][] = $quarter->getE08();
//            $body[$i][] = $quarter->getE081();
            $body[$i][] = strip_tags($quarter->getState(false));
            $i++;
        }

//        $head = [
//            ['企业名称','统一社会信用代码或组织机构代码','填表人','联系电话','期末从业人员','','营业收入','','其中：主营业务收入','','利润总额','','应交增值税','','出口交货值','','状态'],
//            ['企业名称','统一社会信用代码或组织机构代码','填表人','联系电话','D01（人）','','E03（万元）','','E04（万元）','','E05（万元）','','E06（万元）','','E08（万元）','','状态'],
//            ['企业名称','统一社会信用代码或组织机构代码','填表人','联系电话','1-本月','上年同期','1-本月','上年同期','1-本月','上年同期','1-本月','上年同期','1-本月','上年同期','1-本月','上年同期','状态'],
//        ];
//        $mergeHead = [
//            'A1'=>'A3',
//            'B1'=>'B3',
//            'C1'=>'C3',
//            'D1'=>'D3',
//            'E1'=>'F1',
//            'E2'=>'F2',
//            'G1'=>'H1',
//            'G2'=>'H2',
//            'I1'=>'J1',
//            'I2'=>'J2',
//            'K1'=>'L1',
//            'K2'=>'L2',
//            'M1'=>'N1',
//            'M2'=>'N2',
//            'O1'=>'P1',
//            'O2'=>'P2',
//            'Q1'=>'Q3',
//        ];
//        $columnStyle = [
//            'A'=>[
//                'width'=>30,
//                'align'=>'left',
//            ],
//            'B'=>[
//                'width'=>25,
//            ],
//            'D'=>[
//                'width'=>15,
//            ],
//            'Q'=>[
//                'width'=>20,
//            ],
//        ];
        $head = [
            ['企业名称','统一社会信用代码','填表人','联系电话','期末从业人员','','#本科及以上从业人员','','营业收入','','研究开发经费支出','','状态'],
            ['企业名称','统一社会信用代码','填表人','联系电话','D01(人)','','D11(人)','','E03(万元)','','F01(万元)','','状态'],
            ['企业名称','统一社会信用代码','填表人','联系电话','1-本月','上年同期','1-本月','上年同期','1-本月','上年同期','1-本月','上年同期','状态'],
        ];
        //表头合并
        $mergeHead = [
            'A1'=>'A3',
            'B1'=>'B3',
            'C1'=>'C3',
            'D1'=>'D3',
            'M1'=>'M3',
            'E1'=>'F1',
            'G1'=>'H1',
            'I1'=>'J1',
            'K1'=>'L1',
//            'C2'=>'D2',
            'E2'=>'F2',
            'G2'=>'H2',
            'I2'=>'J2',
            'K2'=>'L2',
        ];

        //设置列样式
        $columnStyle=[
            'A'=>[
                'width'=>35,
                'align'=>'left',
            ],
            'B'=>[
                'width'=>20,
                'align'=>'left',
            ],
            'D'=>[
                'width'=>20,
                'align'=>'left',
            ]
        ];

        excel_out($head, $body, $guide->getSubject().'汇总表', $guide->getSubject().'汇总表',$mergeHead,$columnStyle);
    }


    public function pdf()
    {
        $quarter = sf::getModel('Quarters')->selectByItemId(input::getMix('id'));
        if ($quarter->isNew()) $this->page_debug('未找到该报表或还未保存');
        $quarter->makepdf();
//        $pdf = $quarter->getPdf();
//        if($pdf && $quarter->getStatement()==10){
//            $this->jump(site_path('up_files/'.$pdf));
//        }else{
//            $quarter->makepdf();
//        }
    }

    /**
     * 导出未填写的平台
     * @return void
     */
    public function nowrite()
    {
        $guideid = intval(input::getMix('guideid'));
        $guide = sf::getModel('Guides', $guideid);
        if ($guide->isNew()) $this->error("未找到该报表类型", getFromUrl());
        $head[0] = [$guide->getSubject().' - 未填写的平台'];
        $head[1] = ['调查表名', '平台名称', '平台类型', '第一依托单位', '联系人', '联系方式'];
        $i = 0;
        $body = [];
        $platforms = sf::getModel('Platforms')->selectAll("platform_id not in (select platform_id from quarters where guide_id = ".$guide->getId().")","ORDER BY platform_type asc,id asc");
        while($platform = $platforms->getObject()){
            $body[$i][] = $guide->getSubject();
            $body[$i][] = $platform->getSubject();
            $body[$i][] = $platform->getPlatformType();
            $body[$i][] = $platform->getCorporationName();
            $body[$i][] = $platform->getLinkmanName();
            $body[$i][] = $platform->getLinkmanMobile();
            $i++;
        }
        //表头合并
        $mergeHead = [
            'A1'=>chr((ord('A')+count($head[1])-1)).'1',
        ];

        //设置列样式
        $columnStyle=[
            'A'=>[
                'width'=>25,
            ],
            'B'=>[
                'width'=>40,
                'align'=>'left',
            ],
            'C'=>[
                'width'=>25,
            ],
            'D'=>[
                'width'=>40,
            ],
            'F'=>[
                'width'=>15,
            ]
        ];
        $filename = $guide->getSubject().' - 未填写的平台';

        excel_out($head,$body,'未填写的平台',$filename,$mergeHead,$columnStyle);
    }


    public function stat_type()
    {
        $guideid = intval(input::getMix('guideid'));
        $guide = sf::getModel('Guides', $guideid);
        if ($guide->isNew()) $this->error("未找到该报表类型", getFromUrl());
        $head[0] = [$guide->getSubject().' - 按平台类别统计'];
        $head[1] = ['指标类别', '指标名称', '单位'];
        $types = get_select_data('platform_type');
        foreach ($types as $type) {
            $head[1][] = $type;
        }
        $body = [];
        $i = 0;
        $topIndexs = getTopIndexs();
        while ($topIndex = $topIndexs->getObject()){
            $indent = '';
            $indexs = getIndexs($topIndex->getCode());
            $j = 0;
            while ($index = $indexs->getObject()) {
                $indent = $index->getIndent();
                if ($indent > 0) $j = 0;
                $indentStr = str_pad('',$indent*3,'　');
                $body[$i][] = $topIndex->getSubject();
                $body[$i][] = $indentStr.($j==0 ? $index->getQizhong() : '').$index->getSubject();
                $body[$i][] = $index->getUnit();
                foreach ($types as $type){
                    $body[$i][] = $guide->getQuaterDataByPlatformType($type,$index->getCode());
                }
                $i++;
            }
        }
        //表头合并
        $mergeHead = [
            'A1'=>chr((ord('A')+count($head[1])-1)).'1',
        ];

        //设置列样式
        $columnStyle=[
            'A'=>[
                'width'=>15,
            ],
            'B'=>[
                'width'=>27,
                'align'=>'left',
            ]
        ];
        $filename = $guide->getSubject().' - 按平台类别统计';

        excel_out($head,$body,'按平台类别统计',$filename,$mergeHead,$columnStyle);
    }
    public function stat_property()
    {
        $guideid = intval(input::getMix('guideid'));
        $guide = sf::getModel('Guides', $guideid);
        if ($guide->isNew()) $this->error("未找到该报表类型", getFromUrl());
        $head[0] = [$guide->getSubject().' - 按依托单位性质统计'];
        $head[1] = ['指标类别', '指标名称', '单位'];
        $propertys = get_select_data('property');
        foreach ($propertys as $property) {
            $head[1][] = $property;
        }
        $body = [];
        $i = 0;
        $topIndexs = getTopIndexs();
        while ($topIndex = $topIndexs->getObject()){
            $indent = '';
            $indexs = getIndexs($topIndex->getCode());
            $j = 0;
            while ($index = $indexs->getObject()) {
                $indent = $index->getIndent();
                if ($indent > 0) $j = 0;
                $indentStr = str_pad('',$indent*3,'　');
                $body[$i][] = $topIndex->getSubject();
                $body[$i][] = $indentStr.($j==0 ? $index->getQizhong() : '').$index->getSubject();
                $body[$i][] = $index->getUnit();
                foreach ($propertys as $property){
                    $body[$i][] = $guide->getQuaterDataByProperty($property,$index->getCode());
                }
                $i++;
            }
        }
        //表头合并
        $mergeHead = [
            'A1'=>chr((ord('A')+count($head[1])-1)).'1',
        ];

        //设置列样式
        $columnStyle=[
            'A'=>[
                'width'=>15,
            ],
            'B'=>[
                'width'=>27,
                'align'=>'left',
            ]
        ];
        $filename = $guide->getSubject().' - 按依托单位性质统计';

        excel_out($head,$body,'按依托单位性质统计',$filename,$mergeHead,$columnStyle);
    }
    public function stat_industry()
    {
        $guideid = intval(input::getMix('guideid'));
        $guide = sf::getModel('Guides', $guideid);
        if ($guide->isNew()) $this->error("未找到该报表类型", getFromUrl());
        $head[0] = [$guide->getSubject().' - 按产业领域统计'];
        $head[1] = ['指标类别', '指标名称', '单位'];
        $industrys = get_select_data('industry');
        foreach ($industrys as $industry) {
            $head[1][] = $industry;
        }
        $body = [];
        $i = 0;
        $topIndexs = getTopIndexs();
        while ($topIndex = $topIndexs->getObject()){
            $indent = '';
            $indexs = getIndexs($topIndex->getCode());
            $j = 0;
            while ($index = $indexs->getObject()) {
                $indent = $index->getIndent();
                if ($indent > 0) $j = 0;
                $indentStr = str_pad('',$indent*3,'　');
                $body[$i][] = $topIndex->getSubject();
                $body[$i][] = $indentStr.($j==0 ? $index->getQizhong() : '').$index->getSubject();
                $body[$i][] = $index->getUnit();
                foreach ($industrys as $industry){
                    $body[$i][] = $guide->getQuaterDataByIndustry($industry,$index->getCode());
                }
                $i++;
            }
        }
        //表头合并
        $mergeHead = [
            'A1'=>chr((ord('A')+count($head[1])-1)).'1',
        ];

        //设置列样式
        $columnStyle=[
            'A'=>[
                'width'=>15,
            ],
            'B'=>[
                'width'=>27,
                'align'=>'left',
            ]
        ];
        $filename = $guide->getSubject().' - 按产业领域统计';

        excel_out($head,$body,'按产业领域统计',$filename,$mergeHead,$columnStyle);
    }


}
?>