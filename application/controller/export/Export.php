<?php
namespace App\Controller\Export;
use App\Controller\BaseController;
use App\Model\Exports;
use Sofast\Core\lang;
use Sofast\Core\Log;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
@ini_set('memory_limit', '200M');//设置最大使用内存为200M
class export extends BaseController
{	
	private $type = 'projects';
	
	function load(){
		$this->table = input::getInput("mix.table")?input::getInput("mix.table"):'projects';	
	}
	
	function index(){
		view::set("tags",sf::getModel("exports")->selectByTable($this->table));
		view::apply("inc_body","export/index");
		view::display("page_main");
	}

	function score(){
        $code = input::getMix('code');
        if(!$code){
            $this->error('请选择专科类型',site_url('office/score/search_guide'));
        }
        $rule = sf::getModel('Rules')->selectByCode($code);
        if($rule->isNew()){
            $this->error('找不到该专科的评审指标');
        }
        $fields['project_subject'] = '项目名称';
        $fields['project_corporation_name'] = '申报单位';
        $fields['project_district'] = '片区';
        $fields['project_department_name'] = '主管部门';
        $fields['project_state'] = '项目状态';
        $fields['project_objective_score'] = '客观指标分数';
        $fields['project_subjective_score'] = '主观指标分数';
        $fields['project_score'] = '总分';
        $items = $rule->selectItems();
        while($item = $items->getObject()){
            $fields[$item->getMark()] = $item->getSubject() ?: $item->getSubject3();
        }
		view::set("fields",$fields);
		view::set("code",$code);
		view::apply("inc_body","export/score");
		view::display("page_main");
	}
	
//	function output(){
//        @ini_set('memory_limit', '200M');//设置最大使用内存为200M
//        set_time_limit(0);
//		if(!input::getInput("post.select"))//如果没有选择打印字段直接提示
//		$this->page_debug(lang::get("Must select fileds for print!"),getFromUrl());
//		//处理打印字段
//		$exports = sf::getModel("exports")->selectAll("id IN (".input::getInput("post.select").") AND tables = '".$this->table."'");
//		$select = array_flip(explode(",",input::getInput("post.select")));
//		while($export = $exports->getObject()){
//			$data['titles'][$select[$export->getId()]] = $export->getSubject();
//			$data['funcs'][$select[$export->getId()]] = $export->getMethod();
//		}
//		//处理打印条件
//		$addWhere = base64_decode(input::getInput("session.".$this->table.".sqlStr"));
//		$addSql = base64_decode(input::getInput("session.".$this->table.".orderStr"));
//		if($this->table=='projects'){
//            //不导出测试数据
//            $addWhere.=" and department_id !='BF7900E0-D81F-D9D6-8C83-4B5EA8AC9EFF' and user_id !='963049C2-EB30-1A8B-22A3-2CD688C40449'";
//            $addSql = "order by id asc";
//        }
//		$showMax = 0;
//		//纪录
//		Log::write("字段：".implode("、",$data['titles'])."<br />条件：".str_replace("'","’",$addWhere).' By:'.input::getInput("session.nickname").',From:'.input::getIp());
//		//读取打印内容
//		$data['pager'] = sf::getModel($this->table)->getPager($addWhere,$addSql,$showMax);
//		$data['file_name'] = input::getInput("post.file_name");
//		$data['ext'] = input::getInput("post.ext");
//		//输出内容文件
//		view::set($data);
//		$htmlStr = view::getContent("export/output");
//		header("Content-type:application");
//		header("Content-Disposition: attachment; filename=".input::getInput("post.file_name").'.'.input::getInput("post.ext"));
//		exit($htmlStr);
//	}

    function output()
    {
        @ini_set('memory_limit', '200M');//设置最大使用内存为200M
        set_time_limit(0);
        if(!input::getInput("post.select"))//如果没有选择打印字段直接提示
            $this->page_debug(lang::get("Must select fileds for print!"),getFromUrl());
        //处理打印字段
        $exports = sf::getModel("exports")->selectAll("id IN (".input::getInput("post.select").") AND tables = '".$this->table."'");
        $select = array_flip(explode(",",input::getInput("post.select")));
        while($export = $exports->getObject()){
            $data['titles'][$select[$export->getId()]] = $export->getSubject();
            $data['funcs'][$select[$export->getId()]] = $export->getMethod();
        }
        //处理打印条件
        $addWhere = base64_decode(input::getInput("session.".$this->table.".sqlStr"));
        $addSql = base64_decode(input::getInput("session.".$this->table.".orderStr"));

        //$addSql = " ORDER BY radicate_id ASC,updated_at DESC ";
        $showMax = 50;

        //纪录
        Log::write("字段：".implode("、",$data['titles'])."<br />条件：".str_replace("'","’",$addWhere).' By:'.input::getInput("session.nickname").',From:'.input::getIp(),'export');
        //读取打印内容
        $data['file_name'] = input::getInput("post.file_name");
        $data['ext'] = input::getInput("post.ext");
        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header("Content-Disposition: attachment; filename=".input::post("file_name").'.'.input::post("ext"));
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        //输出内容文件
        $fp = fopen('php://output', 'a');
        $htmlStr = '<table width="100%" border="0" cellspacing="0" cellpadding="3" style="border:solid 1px #000;border-collapse:collapse;"><tr><td colspan="'.count($data['titles']).'" align="center" style="border:solid 1px #000;"><h1>'.input::post("file_name").'</h1></td></tr>';
        $htmlStr .= '<tr>';
        for($i=0,$n=count($data['titles']);$i<$n;$i++){
            $htmlStr .= '<th style="border:solid 1px #000;">'.$data['titles'][$i].'</th>';
        }
        $htmlStr .= '</tr>';
        //mb_convert_variables('GBK', 'UTF-8',$rowData);
        fwrite($fp,$htmlStr);//表头
        for($x=0;$x<1000;$x++){
            $pager = sf::getModel($this->table)->select($addWhere,$addSql,$x*$showMax,$showMax);
            if($pager->getTotal() == 0) break;
            while($obj = $pager->getObject())
            {
                $htmlStr = '<tr>';
                for($i=0,$n<count($data['funcs']);$i<$n;$i++){
                    $htmlStr .= '<td style="border:solid 1px #000; vnd.ms-excel.numberformat:@;">'.Exports::getMethodValue($obj,$data['funcs'][$i]).'</td>';
                }
                $htmlStr .= '</tr>';
                //mb_convert_variables('GBK', 'UTF-8', $rowData);
                fwrite($fp,$htmlStr);
            }
        }
        fwrite($fp,'<tr><td colspan="'.count($data['titles']).'" align="right" style="border:solid 1px #000;">制表日期:'.date("Y年m月d日").'</td></tr></table>');//表格结尾
        ob_flush();
        flush();
        fclose($fp);
    }

	function output_score(){
		if(!input::getInput("post.select"))//如果没有选择打印字段直接提示
		$this->page_debug(lang::get("Must select fileds for print!"),getFromUrl());
        if(!input::getInput("post.code"))//如果没有选择打印字段直接提示
            $this->page_debug('未选择专科类型',getFromUrl());

        $subjectName = getSubjectName(input::post('code'));
        if(empty($subjectName)) $subjectName = getQyzxName(input::post('code'));
        $template = WEBROOT . '/up_files/tpl/index_output.xls';          //使用导出模板
        $objPHPExcel = \PHPExcel_IOFactory::load($template);     //加载excel文件,设置模板
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);  //设置保存版本格式
        //设置第一页
        $objPHPExcel->setActiveSheetIndex(0);
        $objActSheet = $objPHPExcel->getActiveSheet();
        //设置默认字体
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');


        $selects = explode(',',input::post("select"));
        $fields['project_subject'] = '项目名称';
        $fields['project_corporation_name'] = '申报单位';
        $fields['project_district'] = '片区';
        $fields['project_department_name'] = '主管部门';
        $fields['project_state'] = '项目状态';
        $fields['project_objective_score'] = '客观指标分数';
        $fields['project_subjective_score'] = '主观指标分数';
        $fields['project_score'] = '总分';
        $head[] = '序号';
        $body = [];
        $avg[] = '';
        $rule = sf::getModel('Rules')->selectByCode(input::post('code'));
        $i=0;
        foreach ($selects as $select){
            if(substr($select,0,7)=='project') {
                $avg[$i] = '';
                $head[] = $fields[$select];
            }else{
                $ruleItem = sf::getModel('RuleItems')->selectByRuleIdAndMark($rule->getId(),$select);
                $index = sf::getModel('Indexs')->selectByCode($select);
                $indexType = $index->getType()=='subjective' ? '【主】' : '【客】';
                if(input::post('export_index_data') && $index->getType()=='objective'){
                    $head[] = $ruleItem->getIsVeto()==9 ? $ruleItem->getSubject2()."\n【数据】" : $ruleItem->getSubject3()."\n【数据】";
                    if($index->getIsProvince()==1){
                        $avg[$i] = '1';
                    }else{
                        $avg[$i] = '';
                    }
                    $i++;
                }
                $head[] = $ruleItem->getIsVeto()==9 ? $ruleItem->getSubject2()."\n".$indexType: $ruleItem->getSubject3()."\n".$indexType;
                $avg[$i] = '';
            }
            $i++;
        }
		//处理打印条件
        $addWhere = base64_decode(input::getInput("session.projects.sqlStr"));
		$addWhere = "subject_code = '".input::post("code")."' and ".$addWhere;
		$addSql = "order by score desc,id asc";

        $projects = sf::getModel('Projects')->getPager($addWhere,$addSql);
        $indexCodes = explode(',',input::post("select"));
        $i=0;
        $avgData[] = '';
        while($project = $projects->getObject()){
            $body[$i][] = $projects->getIndex();
            $j=0;
            foreach($indexCodes as $k=>$indexCode){
                if(substr($indexCode,0,7)=='project'){
                    $method = substr($indexCode,8);
                    $flag = substr($method,-5)=='score' ? true : '';
                    $method = _cName($method);
                    $method = 'get'.$method;
                    $body[$i][] = $project->$method($flag);
                    if($i==0) $avgData[] = '';
                }else{
                    $index = sf::getModel('Indexs')->selectByCode($indexCode);
                    if(input::post('export_index_data') && $index->getType()=='objective'){
                        $body[$i][] = $index->getIsAnnual() ? $project->getAvgDataByIndexCode($indexCode) :     $project->getDataByIndexCode($indexCode)->getData('user');
                        if($i==0) $avgData[$j+1] = $project->getProvinceAvgData($indexCode);
                        $j++;
                    }

                    $body[$i][] = $project->getScoreByIndexCode($indexCode)->getScore(true);
                    $avgData[$j+1] = '';
                }
                $j++;
            }
            $i++;
        }
        $startRow = 1;
        $index='A';
        $lastIndex='A';
        foreach($head as $key=>$value){
            $objActSheet->setCellValue($index.$startRow,$value);
            $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
            $lastIndex = $index;
            $index++;
        }

        $startRow = 2;
        $index='C';
        foreach($avgData as $key=>$value){
            if($key<2) continue;
            $objActSheet->setCellValue($index.$startRow,$value);
            $index++;
        }


        $startRow = 2;
        foreach($body as $key=>$value){
            $startRow++;
            $index='A';
            for($i=0;$i<count($value);$i++){
                $objActSheet->setCellValue($index.$startRow,$value[$i]);
                $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
                $index++;
            }
        }
        //设置单元格边框
        $styleArray = array(
            'borders' => array(
                'allborders' => array(
                    'style' => \PHPExcel_Style_Border::BORDER_THIN,//细边框
                ),
            ),
        );
        $objPHPExcel->getActiveSheet()->getStyle('A1:'.$lastIndex.$startRow)->applyFromArray($styleArray);

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="'.$subjectName.'.xls"');
        header('Cache-Control: max-age=0');
        ob_end_clean();
        $objWriter->save('php://output');
        exit;
//        excel_out($head,$body,input::getInput("post.file_name"),input::getInput("post.file_name"));
	}

	function output_score_old(){
		if(!input::getInput("post.select"))//如果没有选择打印字段直接提示
		$this->page_debug(lang::get("Must select fileds for print!"),getFromUrl());
        if(!input::getInput("post.code"))//如果没有选择打印字段直接提示
            $this->page_debug('未选择专科类型',getFromUrl());

        $selects = explode(',',input::post("select"));
        $fields['project_subject'] = '项目名称';
        $fields['project_corporation_name'] = '申报单位';
        $fields['project_district'] = '片区';
        $fields['project_department_name'] = '主管部门';
        $fields['project_state'] = '项目状态';
        $fields['project_objective_score'] = '客观指标分数';
        $fields['project_subjective_score'] = '主观指标分数';
        $fields['project_score'] = '总分';
        foreach ($selects as $select){
            if(substr($select,0,7)=='project') {
                $data['titles'][] = $fields[$select];
            }else{
                $data['titles'][] = sf::getModel('Indexs')->selectByCode($select)->getSubject();
            }
        }

		//处理打印条件
        $addWhere = base64_decode(input::getInput("session.projects.sqlStr"));
		$addWhere = "subject_code = '".input::post("code")."' and ".$addWhere;
		$addSql = "order by accept_id asc,id asc";

		$showMax = 0;

		//读取打印内容
		$data['pager'] = sf::getModel('Projects')->getPager($addWhere,$addSql,$showMax);
		$data['file_name'] = input::getInput("post.file_name");
		$data['ext'] = input::getInput("post.ext");
		$data['index_codes'] = explode(',',input::post("select"));
		//输出内容文件
		view::set($data);
		$htmlStr = view::getContent("export/output_score");
		header("Content-type:application");
		header("Content-Disposition: attachment; filename=".input::getInput("post.file_name").'.'.input::getInput("post.ext"));
		exit($htmlStr);
	}
	
}
?>