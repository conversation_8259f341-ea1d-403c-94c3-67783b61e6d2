<?php
namespace App\Controller\Link;

use App\Controller\BaseController;
use Sofast\Core\Config;
use Sofast\Core\Input;
use Sofast\Core\Sf;

class Target extends BaseController
{
    public $auth = ['index', 'maps'];
    /**
     * 短链接引导页面，负责权限判断等，最后将用户引导到目标页面
     * 用例如：/links?url=174427040560481
     * @return [type] [description]
     */
    public function index()
    {
        $_link = input::get("url");
        if ($_maps = $this->getMap($_link)) {
            if ($_SESSION['openid'] && $_SESSION['roleuserid'] && $_SESSION['userlevel']) {
                if (in_array($_SESSION['userlevel'], $_maps['role'])) {
                    $this->jump($_maps['url']);
                } else {
                    $_SESSION['sso']['jump_url'] = $_maps['url'];
                    $this->page_debug("你当前的身份不能完成该操作！你可以切换身份后再试...", site_url("manager/choose"));
                }
            } else {
                $_SESSION['sso']['jump_url'] = $_maps['url'];
                //需要的角色组
                $_SESSION['sso']['needroles'] = $_maps['role'];
                $this->jump(site_url("sso/oauth/login"));
            }
        } else {
            $this->page_debug("访问的链接已经失效！", site_url("manager/index"));
        }
    }

    private function getMap($url = '')
    {
        $cache = sf::getLib("Cache", config::get('cache.path', WEBROOT . '/cache'), config::get('cache.limit', 360));
        if (true  || ! $maps = $cache->getCache('link_target_url')) {
            $links = sf::getModel("LinkMaps")->selectAll();
            while ($link = $links->getObject()) {
                $maps[$link->getUrlName()]['link']    = $link->getLink();
                $maps[$link->getUrlName()]['subject'] = $link->getSubject();
                $maps[$link->getUrlName()]['role']    = $link->getRoles();
                $maps[$link->getUrlName()]['url']     = $link->getUrl();
            }
            $cache->setCache('link_target_url', $maps);
        }
        if ($url) {
            return $maps[$url];
        } else {
            return $maps;
        }
    }

    /**
     * 链接地图资源，便于其他系统调用
     * @return json 链接地图
     */
    public function maps()
    {
        @header("Content-type: application/json;");
        $maps = $this->getMap();
        echo json_encode($maps, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
