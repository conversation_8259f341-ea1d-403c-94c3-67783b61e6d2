<div class="main">
  <div class="tools btn-group btn-group-sm" role="group">
    <?php echo btn("back") ?>
<?php echo btn("window", "新增链接", site_url("link/manager/edit"), "add") ?>
<?php echo btn("window", "链接资源", site_url("link/target/maps"), "list") ?>
<p style="clear:both"></p>
  </div>
  <div class="search">
  <form action="" method="post" name="search" id="search">
   关键词：
    <input name="search" type="text" value="<?php echo input::post("search") ?>" />
    <label>
      <input type="radio" name="field" value="url_name" checked="checked" />
      链接标记</label>
    <label>
      <input name="field" type="radio" value="subject" />
      链接名称</label>
    <label>
      <input name="field" type="radio" value="note" />
      链接说明</label>
    <input type="submit" name="Submit4" value=" 搜索 " class="btn btn-sm btn-success" />
  </form>
  </div>
  <div class="box">
    <table align="center" cellpadding="3" cellspacing="1"  class="tb_data">
      <tr>
        <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
        <th><?php echo getColumnStr('链接标记', 'url_name') ?></th>
        <th><?php echo getColumnStr('链接名称', 'subject') ?></th>
        <th><?php echo getColumnStr('链接说明', 'note') ?></th>
        <th><?php echo getColumnStr('需要角色', 'nedd_roles') ?></th>
        <th><?php echo getColumnStr('目标地址', 'target_url') ?></th>
        <th width="150">可用操作</th>
      </tr>
      <?php while ($url = $pager->getObject()): ?>
      <tr>
        <td align="center"><input name="select_id[]" type="checkbox" value="<?php echo $url->getId() ?>" /></td>
        <td align="center"><a href="<?php echo site_url("link/target?url=" . $url->getUrlName()) ?>" target="_blank"><?php echo $url->getUrlName() ?></a></td>
        <td><?php echo $url->getSubject() ?></td>
        <td align="center"><?php echo $url->getNote() ?></td>
        <td align="center"><?php echo $url->getNeedRoles() ?></td>
        <td><?php echo $url->getUrl() ?></td>
        <td align="center">
          <?php echo btn("window", "编辑", site_url("link/manager/edit/url_name/" . $url->getUrlName())) ?>
<?php echo btn("window", "删除", site_url("link/manager/delete/url_name/" . $url->getUrlName())) ?></td>
      </tr>
      <?php endwhile; ?>
      <tr>
        <td colspan="6" align="right">&nbsp;<span class="pager_bar">
          <?php echo $pager->total() . $pager->navbar(10) ?>
          </span></td>
      </tr>
    </table>
  </div>
</div>