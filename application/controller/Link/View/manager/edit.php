<div class="main">
  <div class="tools btn-group btn-group-sm" role="group">
    <?php echo btn("back") ?>
    <p style="clear:both"></p>
  </div>
  <div class="box">
    <form class="form-horizontal" id="form_1" action="" method="post">
      <fieldset>
        <legend align="center">链接管理</legend>
        <div class="form-group">
          <label for="subject"  class="col-sm-3 control-label">链接名称<font color="red">*</font></label>
          <div class="col-sm-5">
            <input placeholder="填写链接名称，便于管理" name="subject" type="text" id="subject" class="form-control col-sm-5" value="<?php echo $link->getSubject() ?>" />
            <p class="help-block">填写链接名称，便于记忆和管理。</p>
          </div>
        </div>
        <div class="form-group">
          <label for="note"  class="col-sm-3 control-label">链接说明</label>
          <div class="col-sm-5">
            <input name="note" type="text" id="note" class="form-control col-sm-5" value="<?php echo $link->getNote() ?>" />
            <p class="help-block">请填写链接详细说明。</p>
          </div>
        </div>
        <div class="form-group">
          <label for="number" class="col-sm-3 control-label">需要的权限<font color="red">*</font></label>
          <div class="col-sm-5">
            <?php echo get_checkbox(get_role_option(), 'need_roles', $link->getRoles(), "", false) ?>
            <p class="help-block">请选择该链接需要的用户角色。</p>
          </div>
        </div>
        <div class="form-group">
          <label for="number" class="col-sm-3 control-label">目标地址<font color="red">*</font></label>
          <div class="col-sm-5">
            <input name="target_url" type="text" id="target_url"  class="form-control col-sm-5" value="<?php echo $link->getTargetUrl() ?>" />
            <p class="help-block">请填写目标地址。站外地址请以http(s)开头，站内地址请输入短地址。</p>
          </div>
        </div>
        <div class="form-group">
          <div class="text-center">
          <input type="hidden" name="url_name" value="<?php echo $link->getUrlName() ?>"  />
            <button type="submit" class="btn btn-info">保存</button>
            <button type="reset" class="btn btn-warning">重置</button>
          </div>
        </div>
      </fieldset>
    </form>
  </div>
</div>