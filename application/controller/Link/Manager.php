<?php
namespace App\Controller\Link;

use App\Controller\BaseController;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Support\Template;

class Manager extends BaseController
{
    public $view = null;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/View/');
    }

    public function index()
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode  = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql     = 'ORDER BY ' . $orderfield . ' ' . $ordermode . ' ';

        $addWhere .= "1 ";
        input::mix("search") && $addWhere .= "AND `" . input::mix("field") . "` like '%" . input::mix("search") . "%' ";

        $this->view->set('pager', sf::getModel("LinkMaps")->getPager($addWhere, $addSql, 20));
        $this->view->apply("inc_body", "manager/index");
        $this->view->display("page");
    }

    public function edit()
    {
        $link = sf::getModel("LinkMaps")->selectByUrlName(input::mix("url_name"));

        if (input::post()) {
            $link->setSubject(trim(input::post("subject")));
            $link->setNote(input::post("note"));
            $link->setTargetUrl(trim(input::post("target_url")));
            $link->setRoles(input::post("need_roles"));
            $link->setUpdatedAt(date("Y-m-d H:i:s"));
            $link->save();
            exit("<script>parent.location.reload();</script>");
        }

        $this->view->set("link", $link);
        $this->view->apply("inc_body", "manager/edit");
        $this->view->display("page_blank");
    }

    public function delete()
    {
        $link = sf::getModel("LinkMaps")->selectByUrlName(input::mix("url_name"));

        if (input::post('content')) {
            if (! $link->isNew()) {
                $link->delete();
            }
            exit("<script>parent.location.reload();</script>");
        }

        $this->view->set("link", $link);
        $this->view->apply("inc_body", "manager/delete");
        $this->view->display("page_blank");
    }

}
