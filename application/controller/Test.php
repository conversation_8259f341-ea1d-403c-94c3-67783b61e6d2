<?php
namespace App\Controller;
use App\Controller\BaseController;
use App\Facades\Button;
use App\Facades\Cache;
use App\Facades\PDF;
use App\lib\MyString;
use Sofast\Core\config;
use Sofast\Core\input;
use Sofast\Core\sf;
use Sofast\Core\Storage;
use Sofast\Support\Storage\Device\S3;
use Sofast\Support\View;

set_time_limit(1000000);

class test extends BaseController
{
    public function __construct()
    {
        echo 1;die;
        dd(1);

//        $complete = sf::getModel('Completes')->selectByProjectId('8F9ECF18-768F-A602-C682-52FFB5F4C331');
//        dd($complete->getIndexYearsDefault());

//        echo 1;die;
//        $projects = sf::getModel('Projects')->selectAll("guide_id in (select id from `guides`  where (parent_id = 135 or parent_id = 147))");
//        $i=0;
//        while($project = $projects->getObject()){
//            $project->setConfigs('data.source','user');
//            $i++;
//        }
//        echo $i;
//
//
//        $projectId = 'AB521821-B577-BFCE-8118-6DB67812D721';
//        $project = sf::getModel('Projects')->selectByProjectId($projectId);
//        $project->setConfigs('data.source','user');
//        echo 1;die;
//        $projectId = '65BC0D31-D8DF-3CF2-FC13-0DDC2BFEEAA0';
//        $project = sf::getModel('Projects')->selectByProjectId($projectId);
//        $scoreLib = sf::getLib('DataSync');
//        $scoreLib->setRuleUsage('apply');
//        $scoreLib->setProject($project);
//        $scoreLib->setIndexType('performance');
//        $result = $scoreLib->doSync();
//        dd($result);
//
//        $projectId = '169A7154-6DCA-D1EE-37D0-BDECC9B32574';
//        $project = sf::getModel('Projects')->selectByProjectId($projectId);
//        $rule = $project->getRule();
//        $rule->setProject($project);
//        $data = $rule->getScoreByMark('ability_xytx');
//        dd($data);
    }

    public function index2()
    {
        debounce(function() {
            echo "执行搜索操作: " . date('Y-m-d H:i:s') . "\n";
        },3000);
        echo 'ok';
    }


    public function rule()
    {
        $projectId =  '020091EE-E02A-BDB8-0ABE-538147B5860B';
        $project = sf::getModel('Projects')->selectByProjectId($projectId);
        $grades = sf::getModel('ProjectGrade')->selectAll("project_id = '{$projectId}' and is_submit = 1 and group_id = 1");
        $scores = [];
        $expertCount = 0;
        while($grade = $grades->getObject()){
            $expertCount++;
            $marks = $grade->getMark();
            foreach ($marks as $indexCode=>$indexScore){
                $scores[$indexCode] += $indexScore;
            }
        }
        foreach ($scores as $indexCode=>$score){
            $index = sf::getModel('Indexs')->selectByCode($indexCode);
            $avgScore = round($score/$expertCount , 2);
            $projectScore = $project->getScoreByIndexCode($indexCode);
            $projectScore->setIndexName($index->getSubject());
            $projectScore->setIndexType('subjective');
            $projectScore->setScore($avgScore);
            $projectScore->save();
        }
        dd('ok');
        dd($marks,$scores,$expertCount);
        die;
        $result = sf::getModel('Rules')->statisticIndexs('020091EE-E02A-BDB8-0ABE-538147B5860B',1);
        dd($result);
    }


    public function index()
    {
        $count = 2;
        $grade = 0;
        $scores = sf::getModel('IndexScores')->selectAll("mark = 'project_province'","order by value desc");
        while($score = $scores->getObject()){
            if($count>=$score->getValue()){
                $grade =  $score->getScore();
                break;
            }
        }
        dd($grade);
        die;
        Storage::setDevice("S3",new S3("oss-cn-yaan-scgzy-d01-a.ops.scntcloud.com",'MTC0v2LMfDFECznH','5VlGdr7ZLluIlqGVRYuWqqfO98F4oe','scnt-kgpt-bucket'));
//        print_r(Storage::getDevice("S3")->upload("d:/test.docx","2022/01/1642401906895.docx"));exit;
//        $rst = Storage::getDevice("S3")->read("2022/01/1642401906895.docx");
        $rst = Storage::getDevice("S3")->delete("2022/01/1642401906895.docx");
        print_r($rst);exit;
        die;
        $cache = sf::getLib('cache');
        $cache->setCacheTime(10);
        $str = $cache->getCache('test');
        if($str){
            dd($str);
        }
        $cache->setCache('test',date('Y-m-d H:i:s'));
        echo 'ok';die;

        Cache::setCacheTime(10);
        $str = Cache::getCache('test');
        if($str){
            dd($str);
            echo Cache::getCache('test');die;
        }
        Cache::setCache('test',date('Y-m-d H:i:s'));
        echo 'ok';die;
        $wf = sf::getLib('Wanfang');
        $token = $wf->search();
        dd($token);
        dd(1,sf::getModel('ProjectAttachments')->copyToTask('4E4D67A6-B829-52F9-FE94-8BE47DC3FD02'));
        $urls = [
            '编辑'=>site_url("apply/project/edit/id/"),
            '测试'=>'-'
        ];
        $str = Button::setName('按钮名称')->setIcon('check')->setSize('btn-sm')->setClass('btn-alt-danger')->group('操作',$urls);
        echo $str;die;
        phpinfo();
        exit();
//
    }

    public function jxkh()
    {
        $datas = [];
        $datas[0]['company_name'] = '测试单位';
        $datas[0]['strength_performance_level'] = 'A';
        $datas[0]['strength_performance_rank'] = 'B';
        foreach ($datas as $data){
            $company = sf::getModel('Corporations')->selectBySubject($data['company_name']);
            if(!$company->isNew()){
                //查找该单位申报的项目
                $projects = sf::getModel('Projects')->selectAll("corporation_id = '".$company->getUserId()."' and declare_year = 2022");
                while($project = $projects->getObject()){
                    $index = sf::getModel('ProjectIndexs')->selectByProjectId($project->getProjectId(),'strength_performance');
                    if($index->isNew()){
                        $index->setType('common');
                        $index->setSourse('system');
                        $index->setPid(0);
                        $index->setCreatedAt(date('Y-m-d H:i:s'));
                        $index->save();
                    }

                    $sonIndexs = ['strength_performance_level','strength_performance_rank'];
                    $totalScore = 0;
                    foreach ($sonIndexs as $sonIndex){
                        $indexchild = sf::getModel('ProjectIndexs')->selectByProjectId($project->getProjectId(),$sonIndex);
                        if($indexchild->isNew()){
                            $indexchild->setType('common');
                            $indexchild->setSourse('system');
                            $indexchild->setPid($index->getId());
                            $index->setCreatedAt(date('Y-m-d H:i:s'));
                        }
                        $indexchild->setText($data[$sonIndex]);
                        $indexchild->setValue($data[$sonIndex]);
                        $score = $indexchild->getScoreByMark($sonIndex,$data[$sonIndex]);
                        $totalScore+=$score;
                        $indexchild->setScore($score);
                        $indexchild->save();
                    }
                    $index->setScore($totalScore);
                    $index->save();
                }
            }
        }
        echo 'ok';
    }


    public function edit_engine_worker_configs()
    {
        $engineWorkers = sf::getModel("EngineWorkers")->selectAll("is_show = 1");
        while($engineWorker = $engineWorkers->getObject()){
            $configs = $engineWorker->getConfigs('member');
            dd($configs);
        }
    }

    public function zzk()
    {
        $parts = sf::getModel('ProjectParts')->selectAll("ptjs is not null");
        $i=0;
        while($part = $parts->getObject()){
            $projectId = $part->getProjectId();
            $project = sf::getModel('Projects')->selectByProjectId($projectId);
            $project->setTextByIndexCode($part->getPtjs(),'platform_zzk');
            $i++;
        }
        echo 'finish->'.$i;
    }


    public function flow()
    {
        $project = sf::getModel('Projects')->selectByProjectId("8ABA6FB3-9A9E-308B-94ED-038E3C4B487F");
        $project->go();
        echo 'finish->';
    }

    function flow_map()
    {
        $flow = sf::getModel('Flows')->selectByFlowId('5B1CD9A4-AA0D-6CAE-57D4-45A3E7108B99');
        if($flow->isNew()) return false;
//        dd($flow->getFlowchartStr());
        view::set("flow",$flow);
        view::apply("inc_body",'test/flow_map');
        view::display('page');
    }

    function projecttargets()
    {
        $projectTargets = sf::getModel('ProjectTargets')->selectAll();
        $i=0;
        while($projectTarget = $projectTargets->getObject()){
            $jxzbapplys = $projectTarget->getJxzbApply();
            $jxzbtasks = $projectTarget->getJxzbTask();
            $jxzbapplysels = $projectTarget->getJxzbApplySel();
            $jxzbtasksels = $projectTarget->getJxzbTaskSel();
            $newjxzbapplys = $this->getNewTargets($jxzbapplys);
            $newjxzbtasks = $this->getNewTargets($jxzbtasks);
            $newjxzbapplysels = $this->getNewTargets($jxzbapplysels);
            $newjxzbtasksels = $this->getNewTargets($jxzbtasksels);

            $projectTarget->setJxzbApply($newjxzbapplys);
            $projectTarget->setJxzbTask($newjxzbtasks);
            $projectTarget->setJxzbApplySel($newjxzbapplysels);
            $projectTarget->setJxzbTaskSel($newjxzbtasksels);
            $projectTarget->save();
            $i++;
        }
        echo 'finish-->'.$i;
    }

    private function getNewTargets($oldTargets)
    {
        $newTargets = [];
        foreach ($oldTargets as $k=>$v){
            $newk = $this->getNewKey($k);
            $newTargets[$newk] = $v;
        }
        return $newTargets;
    }

    private function getNewKey($k)
    {
        $newk = $k;
        switch ($k){
            case 'specialty_zlaq_apache':
                $newk = 'safety_zzk_apache';
                break;
            case 'specialty_fwnl_icu':
                $newk = 'ability_icu';
                break;
            case 'specialty_cxjc_kyxm':
                $newk = 'innovate_kyxm';
                break;
            case 'specialty_zlaq_zdfx':
                $newk = 'safety_zdfx';
                break;
            case 'specialty_fwxl_sjxh':
                $newk = 'efficient_sjxh';
                break;
            case 'specialty_fwxl_fyxh':
                $newk = 'efficient_fyxh';
                break;
            case 'specialty_fwnl_wcss':
                $newk = 'ability_wcss';
                break;
            case 'specialty_fwnl_sjss':
                $newk = 'ability_sjss';
                break;
            case 'specialty_fwnl_cmi':
                $newk = 'ability_cmi';
                break;
            case 'specialty_fwnl_drgs':
                $newk = 'ability_drgs';
                break;
        }
        return $newk;
    }

    public function addNewRuleItem()
    {
        $ids = [98,99,100,101,102,103,104,105,106,107,108];
        foreach ($ids as $id){
            $rule = sf::getModel('Rules',$id);
            $ruleItems = $rule->selectItems();
            while($ruleItem = $ruleItems->getObject()){
                if($ruleItem->getSubject3()=='目标任务完成情况' && $ruleItem->getWeight()=='80'){
                    $ruleItem->setSubject3('量化目标设置');
                    $ruleItem->setNote('按照项目实际情况设定量化目标');
                    $ruleItem->setStandard('量化目标设置合理，得20分；设置较合理，得10分；设置不合理，不得分。');
                    $ruleItem->setWeight(20);
                    $ruleItem->save();
                }elseif($ruleItem->getSort()>1){
                    $ruleItem->setSort($ruleItem->getSort()+3);
                    $ruleItem->save();
                }
            }


            $db = sf::getLib('db');
            $sqls = [
                "INSERT INTO `rule_items` (`id`, `pid`, `level`, `rule_id`, `subject1`, `subject2`, `subject3`, `subject`, `note`, `standard`, `check_method`, `weight`, `mark`, `sort`, `is_veto`, `is_objective`, `role`, `usage`, `score_type`, `step`) VALUES (null, 0, 1, ".$rule->getId().", '项目管理', '目标任务完成情况', '项目实施效果', NULL, '项目建设产生的成果和效益', '项目实施效果优秀，得20分；效果良好，得10分；效果较差，不得分。', '查阅任务书和自评报告，以及相关证明材料', 20, 'stagexmgl_mbrw', 4, 0, 0, 'JS', 'stage', 'expert', 1);",
                "INSERT INTO `rule_items` (`id`, `pid`, `level`, `rule_id`, `subject1`, `subject2`, `subject3`, `subject`, `note`, `standard`, `check_method`, `weight`, `mark`, `sort`, `is_veto`, `is_objective`, `role`, `usage`, `score_type`, `step`) VALUES (null, 0, 1, ".$rule->getId().", '项目管理', '目标任务完成情况', '建设任务完成情况', NULL, '按照任务书要求完成各项建设任务', '建设任务完成情况优秀，得20分；完成情况良好，得10分；完成情况较差，不得分。', '查阅任务书和自评报告，以及相关证明材料', 20, 'stagexmgl_mbrw', 3, 0, 0, 'JS', 'stage', 'expert', 1)",
                "INSERT INTO `rule_items` (`id`, `pid`, `level`, `rule_id`, `subject1`, `subject2`, `subject3`, `subject`, `note`, `standard`, `check_method`, `weight`, `mark`, `sort`, `is_veto`, `is_objective`, `role`, `usage`, `score_type`, `step`) VALUES (null, 0, 1, ".$rule->getId().", '项目管理', '目标任务完成情况', '建设进度安排', NULL, '按照项目实际情况和建设目标安排建设进度', '建设进度安排合理，得20分；安排较合理，得10分；不合理，不得分。', '查阅任务书和自评报告，以及相关证明材料', 20, 'stagexmgl_mbrw', 2, 0, 0, 'JS', 'stage', 'expert', 1);",
            ];
            foreach ($sqls as $sql){
                $db->exec($sql);
            }
            $rule->setNumber($rule->getNumber()+3);
            $rule->save();
        }

        echo 'finish';
    }

    public function addNewRuleItemGz()
    {
        $ids = [110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125];
        foreach ($ids as $id){
            $rule = sf::getModel('Rules',$id);
            $ruleItems = $rule->selectItems();
            while($ruleItem = $ruleItems->getObject()){
                if($ruleItem->getSubject3()=='目标任务完成情况' && $ruleItem->getWeight()=='160'){
                    $ruleItem->setSubject3('量化目标设置');
                    $ruleItem->setNote('按照项目实际情况设定量化目标');
                    $ruleItem->setStandard('量化目标设置合理，得40分；设置较合理，得20分；设置不合理，不得分。');
                    $ruleItem->setWeight(40);
                    $ruleItem->save();
                }elseif($ruleItem->getSort()>1){
                    $ruleItem->setSort($ruleItem->getSort()+3);
                    $ruleItem->save();
                }
            }


            $db = sf::getLib('db');
            $sqls = [
                "INSERT INTO `rule_items` (`id`, `pid`, `level`, `rule_id`, `subject1`, `subject2`, `subject3`, `subject`, `note`, `standard`, `check_method`, `weight`, `mark`, `sort`, `is_veto`, `is_objective`, `role`, `usage`, `score_type`, `step`) VALUES (null, 0, 1, ".$rule->getId().", '项目管理', '目标任务完成情况', '项目实施效果', NULL, '项目建设产生的成果和效益', '项目实施效果优秀，得40分；效果良好，得20分；效果较差，不得分。', '查阅任务书和自评报告，以及相关证明材料', 40, 'stagexmgl_mbrw', 4, 0, 0, 'JS', 'stage', 'expert', 1);",
                "INSERT INTO `rule_items` (`id`, `pid`, `level`, `rule_id`, `subject1`, `subject2`, `subject3`, `subject`, `note`, `standard`, `check_method`, `weight`, `mark`, `sort`, `is_veto`, `is_objective`, `role`, `usage`, `score_type`, `step`) VALUES (null, 0, 1, ".$rule->getId().", '项目管理', '目标任务完成情况', '建设任务完成情况', NULL, '按照任务书要求完成各项建设任务', '建设任务完成情况优秀，得40分；完成情况良好，得20分；完成情况较差，不得分。', '查阅任务书和自评报告，以及相关证明材料', 40, 'stagexmgl_mbrw', 3, 0, 0, 'JS', 'stage', 'expert', 1)",
                "INSERT INTO `rule_items` (`id`, `pid`, `level`, `rule_id`, `subject1`, `subject2`, `subject3`, `subject`, `note`, `standard`, `check_method`, `weight`, `mark`, `sort`, `is_veto`, `is_objective`, `role`, `usage`, `score_type`, `step`) VALUES (null, 0, 1, ".$rule->getId().", '项目管理', '目标任务完成情况', '建设进度安排', NULL, '按照项目实际情况和建设目标安排建设进度', '建设进度安排合理，得40分；安排较合理，得20分；不合理，不得分。', '查阅任务书和自评报告，以及相关证明材料', 40, 'stagexmgl_mbrw', 2, 0, 0, 'JS', 'stage', 'expert', 1);",
            ];
            foreach ($sqls as $sql){
                $db->exec($sql);
            }
            $rule->setNumber($rule->getNumber()+3);
            $rule->save();
        }

        echo 'finish';
    }

    public function updateEngine()
    {
        $worker = sf::getModel('EngineWorkers',1001);
        $widgets = $worker->widgets();
        while($widget = $widgets->getObject()){
            $configs = $widget->getConfigs();
            if($configs['engine']!='completer'){
                $configs['engine'] = 'completer';
                $widget->setConfigs($configs);
                $widget->save();
            }
        }
        echo 'finish';
    }

    public function updateEngine2()
    {
        $widgets = sf::getModel('EngineWorkerWidgets')->selectAll("engine_worker_id >=1029 and widget_name = 'ability'");
//        $widgets = sf::getModel('EngineWorkerWidgets')->selectAll("engine_worker_id = 1052 and widget_name = 'support'");
        while($widget = $widgets->getObject()){
            $configs = $widget->getConfigs();
            if($configs['langs']['common.cmi']=='病例组合指数（CMI）'){
                $configs['langs']['common.cmi'] = '本专科病例组合指数（CMI）';
                $widget->setConfigs($configs);
                $widget->save();
            }
//            if($configs['langs']['needfile']['influence.self']=='八、专业影响力'){
//                $configs['langs']['needfile']['influence.self'] = '七、专业影响力';
//            }
//            if($configs['langs']['needfile']['root']=='六、专科建设与服务情况-（一）专科建设'){
//                $configs['langs']['needfile']['root'] = '五、专科建设与服务情况-（一）专科建设';
//            }
//            if($configs['langs']['needfile']['subspecialty.common']=='六、专科建设与服务情况-（一）专科建设-1.亚专科建设'){
//                $configs['langs']['needfile']['subspecialty.common'] = '五、专科建设与服务情况-（一）专科建设-1.亚专科建设';
//            }
//            if($configs['langs']['needfile']['subspecialty.mzk']=='六、专科建设与服务情况-（一）专科建设-1.亚专科建设'){
//                $configs['langs']['needfile']['subspecialty.mzk'] = '五、专科建设与服务情况-（一）专科建设-1.亚专科建设';
//            }
//            if($configs['langs']['needfile']['subspecialty.kfk']=='六、专科建设与服务情况-（一）专科建设-1.亚专科建设'){
//                $configs['langs']['needfile']['subspecialty.kfk'] = '五、专科建设与服务情况-（一）专科建设-1.亚专科建设';
//            }
//            if($configs['langs']['needfile']['support.hxk']=='六、专科建设与服务情况-（一）专科建设-4.支持条件'){
//                $configs['langs']['needfile']['support.hxk'] = '五、专科建设与服务情况-（一）专科建设-4.支持条件';
//            }
//            $widget->setConfigs($configs);
//            $widget->save();
        }
        echo 'finish';
    }

    public function test()
    {
        $heads[0] = ['姓名','身份证','性别','地址'];
        $heads[1] = ['姓名1','身份证1','性别1','地址1'];
        $bodys[0] = [
            ['周杰伦','500242197605300015','男','四川省成都市双流县成都信息工程大学航空港校区'],
            ['蔡依林','500242197605300014','女','成都市武侯区成科西路3号'],
        ];
        $bodys[1] = [
            ['张三','500242197605300011','男1','四川省成都市双流县成都信息工程大学航空港校区1'],
            ['李四','500242197605300011','女1','成都市武侯区成科西路3号1'],
        ];
        $sheetNames = ['表1','表2'];
        excel_outs($heads,$bodys,$sheetNames,'test');

    }


    public function test1()
    {
        //打印九九乘法表
        $str = '';
        for($i=1;$i<=9;$i++){
            for($j=1;$j<=$i;$j++){
                $str .= $j.'*'.$i.'='.$i*$j."\t";
            }
            $str .= "\n";
        }
    }

}
