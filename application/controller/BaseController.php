<?php
namespace App\Controller;
use Sofast\Core\Sf;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Config;
use Sofast\Core\Log;
use Sofast\Core\Router;
class BaseController
{
	public $auth = array();
	public $hash = '';
	
	public function __construct()
	{
		$this->auth();
	}
	
	public function load()
	{
		$this->hash = md5(input::getInput("mix.controller").input::getInput("mix.method").input::server('QUERY_STRING').input::server('QUERY_STRING').input::session('userlevel').input::session('roleuserid'));
		$data['hash'] = $this->hash;
		view::set($data);
	}

    public function checkCsrf()
    {
        if(!View::checkCsrf()) $this->page_debug("请不要从重复提交页面！",getFromUrl());
    }
	
	public function auth()
	{
		//排除不需要验证的方法
		if(in_array(router::getMethod(),(array)$this->auth) || $_SERVER['HTTP_USER_AGENT'] == 'PHPER_'.substr(md5(date("YdmH")),-5)) return true;
		//判断是否登录信息发生变化
		if($_GET['token'] && ($_GET['token'] != getToken()))
			$this->page_debug(lang::get("The permissions have changed!"),site_url("login/logout"),'_top');
		
		//验证用户权限
		if(!sf::getModel("Authorizations")->isAuth()){
			if($_SESSION['userid']) $this->page_debug(lang::get("You do not have permission to visit!"),getFromUrl());
			else $this->page_debug(lang::get("Login expired or your account login in other places!"),site_url('login/index'));
		}
	}
	
	public function shutdown(){
		$time = getmicrotime() - config::get('start_time');
		if($time > 2) Log::write($_SERVER['REQUEST_URI']."的执行时间为：".$time);	
	}
	
	/**
	 * 页面调试，系统提示方法 
	 */
	public function page_debug($msg,$url='',$target='_self',$title='')
	{
		$title == "" && $title = lang::get("Controller::debug");
		$url == '' && $url = "javascript:history.go(-1);";
		ob_start();
		if(is_file(APPPATH."Error/debug.php")) include_once(APPPATH."Error/debug.php");
		else include_once(SYSTEMPATH."Error/debug.php");
		ob_end_flush();
		exit;
	}

    public function success($msg,$url='',$target='_self',$title='')
    {
        if (php_sapi_name() == 'cli') {
            echo $msg.PHP_EOL;exit();
        }
        $title == "" && $title = lang::get("Controller::debug");
        $url == '' && $url = "javascript:history.go(-1);";
        $type = 'success';
        ob_start();
        if(is_file(APPPATH."Error/debug.php")) include_once(APPPATH."Error/debug.php");
        else include_once(SYSTEMPATH."Error/debug.php");
        ob_end_flush();
        exit;
    }

    public function engine_toast($msg,$type='success',$target='_self')
    {
        $jscode = $type=='success' ? "top.showSuccess('{$msg}')" : "top.showError('{$msg}')";
        exit("<script>top.closeWindow();{$jscode}</script>");
    }

//    public function toast($msg,$url='',$type='success',$target='_self')
//    {
//        $url.='/_action/'.$type.'/_msg/'.$msg;
//        if($target=='_top'){
//            $suffix='/_action/'.$type.'/_msg/'.$msg;
//            exit("<script>top.location.href=top.location.href+'{$suffix}';</script>");
//        }
//        $this->jump($url);
//    }

    public function error($msg,$url='',$target='_self',$title='')
    {
        $title == "" && $title = lang::get("Controller::debug");
        $url == '' && $url = "javascript:history.go(-1);";
        $type = 'error';
        ob_start();
        if(is_file(APPPATH."Error/debug.php")) include_once(APPPATH."Error/debug.php");
        else include_once(SYSTEMPATH."Error/debug.php");
        ob_end_flush();
        exit;
    }
	
	public function page_loading()
	{
		$url = base64_decode(Input::getInput('mix.url'));
		$message = base64_decode(Input::getInput('mix.message'));
		$url == '' && $url = "javascript:history.go(-1);";

		ob_start();
		if(is_file(APPPATH."error/loading.php")) include_once(APPPATH."error/loading.php");
		else include_once(SYSTEMPATH."error/loading.php");
		ob_end_flush();
		exit;
	}
	/**
	 * 页面跳转
	 */
	public function jump($url='')
	{
		@header("Location:$url");
		exit;
	}

    /**
     * 弹出提示并关闭弹窗
     * @param string $msg
     * @param string $type 类型：success：成功，error：失败，loading：加载
     * @param int $second 跳转等待时间 单位：秒
     */
    public function refresh()
    {
        exit("<script>parent.location.reload();</script>");
    }

	public function closeWindow()
	{
		exit("<script>parent.closeWindow();</script>");
	}
	
	/**
	 * 集中处理项目的搜索信息
	 *
	 */
	public function grid($tpl = 'admin/plan/submit_list',$addWhere = '1',$showMax=16,$page='page_main',$modelName='projects')
	{
		if(input::getInput("mix.type")=='custom') $this->grid_custom($tpl,$addWhere,$showMax,$page,$modelName);
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
//        if(input::getInput("get.orderfield") || !$_SESSION['projects']['sqlStr']) $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
//		else $addSql = 	base64_decode($_SESSION['orderStr']);

		//处理搜索
		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

        if(input::session('auth')!=-1 && input::session('auth.cat')){
            $addWhere .= " AND `cat_id` IN (".implode(',',input::session('auth.cat')).") ";
        }

		if(input::getInput("mix.declare_year"))
			$addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
		//else $addWhere .= " AND `declare_year` = '".date('Y')."' ";

		input::getInput("mix.radicate_year") && $addWhere .= " AND `radicate_year` = '".input::getInput("mix.radicate_year")."' ";
		input::getInput("mix.type_current_group") && $addWhere .= " AND `type_current_group` = '".input::getInput("mix.type_current_group")."' ";
		input::getInput("mix.level") && $addWhere .= " AND `level` = '".input::getInput("mix.level")."' ";
		input::getInput("mix.type_id") && $addWhere .= " AND `type_id` = '".input::getInput("mix.type_id")."' ";
		input::getInput("mix.subject_code") && $addWhere .= " AND `subject_code` = '".input::getInput("mix.subject_code")."' ";
        input::getInput("mix.district") && $addWhere .= " AND `district` = '".input::getInput("mix.district")."' ";
		input::getInput("mix.cat_id") && $addWhere .= " AND `cat_id` = '".input::getInput("mix.cat_id")."' ";
		input::getInput("mix.flow_type") && $addWhere .= " AND `flow_type` = '".input::getInput("mix.flow_type")."' ";
		input::getInput("mix.research_type") && $addWhere .= " AND `research_type` = '".input::getInput("mix.research_type")."' ";
		input::getInput("mix.project_type") && $addWhere .= " AND `project_type` = '".input::getInput("mix.project_type")."' ";
		input::getInput("mix.project_type_id") && $addWhere .= " AND `project_type` = '".input::getInput("mix.project_type_id")."' ";
        //按指南搜索
        if(input::getInput("mix.guide_id")){
            $guide = sf::getModel('Guides',input::getInput("mix.guide_id"));
            $guide_ids = $guide->getChildren();
            $addWhere .= " AND `guide_id` IN (".implode(',',$guide_ids).") ";
        }
//		input::session("office_id")>0 && $addWhere .= " AND `office_id` = '".input::session("office_id")."' ";
		input::getInput("mix.subject_id") && $addWhere .= " AND `subject_id` LIKE '".input::getInput("mix.subject_id")."%' ";
		input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
        if(input::getInput("mix.statement")==2029){
            $addWhere .= " AND `statement` IN (20,28,29,30) ";
        }elseif(input::getInput("mix.statement")==2930){
            $addWhere .= " AND `statement` IN (29,30) ";
        }elseif(input::getInput("mix.statement")){
            $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";
        }
		input::getInput("mix.type_subject") && $addWhere .= " AND `type_id` = '".input::getInput("mix.type_subject")."' ";
		input::getInput("mix.project_domain") && $addWhere .= " AND `project_domain` = '".input::getInput("mix.project_domain")."' ";
		input::getInput("mix.state_for_budget_book") && $addWhere .= " AND `state_for_budget_book` = '".input::getInput("mix.state_for_budget_book")."' ";
		input::getInput("mix.state_for_plan_book") && $addWhere .= " AND `state_for_plan_book` = '".input::getInput("mix.state_for_plan_book")."' ";
		input::getInput("mix.state_for_complete_book") && $addWhere .= " AND `state_for_complete_book` = '".input::getInput("mix.state_for_complete_book")."' ";
		input::getInput("mix.state_for_stage") && $addWhere .= " AND `state_for_stage` = '".input::getInput("mix.state_for_stage")."' ";
		input::getInput("mix.state_for_out") && $addWhere .= " AND `state_for_out` = '".input::getInput("mix.state_for_out")."' ";
		input::getInput("mix.state_for_apply") && $addWhere .= " AND `state_for_apply` = '".input::getInput("mix.state_for_apply")."' ";
		//按照时间检索
		input::getInput("mix.declare_start") && $addWhere .= " AND unix_timestamp(`declare_at`) >= unix_timestamp('".input::getInput("mix.declare_start")."') ";
		input::getInput("mix.declare_end") && $addWhere .= " AND unix_timestamp(`declare_at`) <= unix_timestamp('".input::getInput("mix.declare_end")."') ";
		input::getInput("mix.update_start") && $addWhere .= " AND unix_timestamp(`updated_at`) >= unix_timestamp('".input::getInput("mix.update_start")."') ";
		input::getInput("mix.update_end") && $addWhere .= " AND unix_timestamp(`updated_at`) <= unix_timestamp('".input::getInput("mix.update_end")."') ";
		
		if(input::getInput("mix.is_shift")) $addWhere .= " AND `is_shift` = '".input::getInput("mix.is_shift")."' ";

		if(input::getInput("mix.is_wait")) $addWhere .= " AND `is_wait` = '".input::getInput("mix.is_wait")."' ";
//		else $addWhere .= " AND `is_wait` < 3 ";
        if(input::getInput("mix.is_short")) $addWhere .= " AND `is_short` = '".(input::getInput("mix.is_short")-1)."' ";
        if(input::getInput("mix.is_minority")) $addWhere .= " AND `is_minority` = '".(input::getInput("mix.is_minority")-1)."' ";
        if(strlen(input::getMix('diagnosis_code'))==2){
            $addWhere .= " AND `diagnosis_code` like '".input::getInput("mix.diagnosis_code")."%' ";
        }
        if(strlen(input::getMix('diagnosis_code'))>2){
            $addWhere .= " AND `diagnosis_code` = '".input::getInput("mix.diagnosis_code")."' ";
        }
        if(input::getInput("mix.is_test")==1) $addWhere .= " AND `is_test` = '0' ";

		//变更申请状态
		if(input::getInput("post.change_statement")) $addWhere .= " AND project_id in (select project_id from project_changes where statement = ".input::getInput("post.change_statement").") ";

		// dd($this->hash);
//        unset($_SESSION['hash']);
		//将搜索条件保存以备打印或者导出
		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		{
			//保存标记
			$_SESSION['hash'] = $this->hash;
			$_SESSION['projects']['baseSql'] = base64_encode($addWhere);
			//打印
			$_SESSION['projects']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['projects']['orderStr'] = base64_encode($addSql);
		}else{
//			$_SESSION['queryOptions'] = '';
//			$_SESSION['url_str'];
//			$addWhere = base64_decode($_SESSION['projects']['sqlStr']);
		}
//		dd($addWhere);
		//显示页面
		$form_vars = array('search','level','declare_year','radicate_year','type_current_group','type_id','cat_id','subject_id','department_id','statement','office_id','field','state_for_complete_book','state_for_plan_book','state_for_budget_book','is_wait','is_shift','guide_id','subject_code','district','is_short','is_minority','diagnosis_code','is_test','state_for_stage','guide_id1','guide_id2','guide_id3');
		
		// if(input::session('username')=='lijx') dd($addWhere);
		view::set("pager",sf::getModel(ucfirst($modelName))->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);
	}

    public function platform_grid($addWhere='1',$tpl='gather/unit/index')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        if(input::getInput("mix.field")=='user_name' && input::getInput("mix.search")){
            $addWhere .= " AND `platform_id` IN (SELECT user_role_id FROM `user_roles` where role_id = 5 and user_id in (select user_id from users where user_name = '".input::getInput("mix.search")."'))";
        }elseif(input::getInput("mix.field") && input::getInput("mix.search")){
            $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
        }

        input::getInput("mix.platform_type") && $addWhere .= " AND `platform_type` = '".input::getInput("mix.platform_type")."'";
        input::getInput("mix.industry") && $addWhere .= " AND `industry` = '".input::getInput("mix.industry")."'";
        input::getInput("mix.department_id") && $addWhere .= " AND (`top_department_id` = '".input::getInput("mix.department_id")."' or `department_id` = '".input::getInput("mix.department_id")."')";

        strlen(input::getInput("mix.is_lock"))>0 && $addWhere .= " AND `is_lock` = '".input::getInput("mix.is_lock")."' ";


        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['platforms']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['platforms']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['platforms']['orderStr'] = base64_encode($addSql);
        }else{
            $addWhere = base64_decode($_SESSION['platforms']['sqlStr']);
        }
        $from_vars = array('search','field','type','is_lock','department_id','category','platform_type','industry');
        view::set("pager",sf::getModel("Platforms")->getPager($addWhere ,$addSql ,20,'','',$from_vars));
        view::apply("inc_body",$tpl);
        view::display("page");
    }

	public function grid_custom($tpl = '',$addWhere = '1',$showMax=16,$page='page',$modelName='projects')
	{
		$fields = ['subject','corporation_name','user_name'];
		$str = decrypt_url(urldecode(input::getInput("get.str")));
		$queryOptions = [];

		if($str && $str==$_SESSION['url_str'])
		{
			$strs = explode('&',$str);
			foreach($strs as $key => $value)
			{
				if(!$value) continue;
				$qs = explode('|',$value);
				$flag = explode('|',$qs[1]);
				$subjects = explode('|',$qs[2]);
				if(count($flag)==1)
					$addWhere .= ' AND '.$qs[0].' = "'.$qs[1].'"';
				else
					$addWhere .= ' AND '.$qs[0].' IN('.$qs[1].')';
				$queryOptions[trim($qs[0])] = implode('、',$subjects);
			}
		}
		else $_SESSION['queryOptions'] = '';

		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

		//处理搜索
		if(input::getInput("mix.search"))
		{
			if($field=Input::post('field'))
				$addWhere .= " AND ".$field." LIKE '%".trim(input::getInput("mix.search"))."%' ";
			else
				$addWhere .= " AND CONCAT(".implode(',',$fields).") LIKE '%".trim(input::getInput("mix.search"))."%' ";
		}
		$_SESSION['search'] = input::getInput("mix.search");

		//按照时间检索
		input::getInput("mix.declare_start") && $addWhere .= " AND unix_timestamp(`declare_at`) >= unix_timestamp('".input::getInput("mix.declare_start")."') ";
		input::getInput("mix.declare_end") && $addWhere .= " AND unix_timestamp(`declare_at`) <= unix_timestamp('".input::getInput("mix.declare_end")."') ";
		input::getInput("mix.update_start") && $addWhere .= " AND unix_timestamp(`updated_at`) >= unix_timestamp('".input::getInput("mix.update_start")."') ";
		input::getInput("mix.update_end") && $addWhere .= " AND unix_timestamp(`updated_at`) <= unix_timestamp('".input::getInput("mix.update_end")."') ";
		
		if(input::getInput("mix.is_shift")) $addWhere .= " AND `is_shift` = '".input::getInput("mix.is_shift")."' ";
		
		if(input::getInput("mix.is_wait")) $addWhere .= " AND `is_wait` = '".input::getInput("mix.is_wait")."' ";
		else $addWhere .= " AND `is_wait` < 3 ";

		//将搜索条件保存以备打印或者导出
		if(input::getInput("get") || $this->hash != $_SESSION['hash'])
		{
			$_SESSION['queryOptions'] = $queryOptions;
			//保存标记
			$_SESSION['hash'] = $this->hash;
			$_SESSION['projects']['baseSql'] = base64_encode($addWhere);
			//打印
			$_SESSION['projects']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['projects']['orderStr'] = base64_encode($addSql);
		}

		$form_vars = array('search','declare_year','radicate_year','type_current_group','type_id','cat_id','subject_id','department_id','statement','office_id','field','state_for_complete_book','state_for_plan_book','state_for_budget_book','is_wait','is_shift');
		view::set("pager",sf::getModel(ucfirst($modelName))->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);
	}
	//项目负责人
	public function user_gird($addWhere='1',$tpl='unit/declarer/index')
	{
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'created_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		
		if(input::getInput("mix.search") && !in_array(input::getInput("mix.field"),['all','self','base','fruits'])){
			$addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";	
		} 

		input::getInput("mix.ignore_complete") && $addWhere .= " AND `ignore_complete` = 1 ";
		input::getInput("mix.ignore_declare") && $addWhere .= " AND `ignore_declare` = 1 ";
		input::getInput("mix.user_honor") && $addWhere .= " AND user_honor = '".input::getInput('mix.user_honor')."'";
		if(in_array(input::getInput("mix.is_lock"),['0','1','2','5','7','8','9'])){
			$addWhere .= " AND is_lock = '".input::getInput('mix.is_lock')."'";
		}
		input::getInput("mix.political") && $addWhere .= " AND political = '".input::getInput('mix.political')."'";
		input::getInput("mix.user_degree") && $addWhere .= " AND user_degree = '".input::getInput('mix.user_degree')."'";
		input::getInput("mix.education") && $addWhere .= " AND education = '".input::getInput('mix.education')."'";
		input::getInput("mix.department_id") && $addWhere .= " AND department_id = '".input::getInput('mix.department_id')."'";
		input::getInput("mix.user_sex") && $addWhere .= " AND user_sex = '".input::getInput('mix.user_sex')."'";
		input::getInput("mix.talent") && $addWhere .= " AND talent = '".input::getInput('mix.talent')."'";
		input::getInput("mix.job_qualification") && $addWhere .= " AND job_qualification like '%".input::getInput('mix.job_qualification')."%'";
		input::getInput("mix.user_academichonor") && $addWhere .= " AND user_academichonor like '%".input::getInput('mix.user_academichonor')."%'";

		input::getInput("mix.corporation_id") && $addWhere .= " AND corporation_id = '".input::getInput('mix.corporation_id')."'";
		input::getInput("mix.subject_code") && $addWhere .= " AND subject_code = '".input::getInput('mix.subject_code')."'";

		//年龄区间
		if(input::getInput("mix.start_age")){
			if(input::getInput("mix.end_age")){
				$addWhere .= " AND age BETWEEN ".input::getInput("mix.start_age")." AND ".input::getInput("mix.end_age");
			}else{
				$addWhere .= " AND age >= ".input::getInput("mix.start_age");
			}
		}
		if(input::getInput("mix.end_age")){
			if(!input::getInput("mix.start_age")){
				$addWhere .= " AND age <= ".input::getInput("mix.end_age");
			}
		}
		// if(input::getInput("mix.start_age") && input::getInput("mix.end_age")){
		// 	$addWhere .= " AND age BETWEEN ".input::getInput("mix.start_age")." AND ".input::getInput("mix.end_age");
		// }
		// dd($addWhere);
		//将搜索条件保存以备打印或者导出
		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		{
			//保存标记
			$_SESSION['hash'] = $this->hash;
			$_SESSION['declarers']['baseSql'] = base64_encode($addWhere);
			//打印
			$_SESSION['declarers']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['declarers']['orderStr'] = base64_encode($addSql);
		}else{
			$addWhere = base64_decode($_SESSION['declarers']['sqlStr']);
		}
		$from_vars = array('search','field','is_lock','subject_code','corporation_id','user_degree','education');
		view::set("pager",sf::getModel("Declarers")->getPager($addWhere ,$addSql ,20,'','',$from_vars));
		view::apply("inc_body",$tpl);
		view::display("page_main");
	}

	public function unit_gird($addWhere='1',$tpl='gather/unit/index',$addSql = '')
	{
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = $addSql?:'ORDER BY '.$orderfield.' '.$ordermode.' ';

		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";

		input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
		input::getInput("mix.property") && $addWhere .= " AND `property` = '".input::getInput("mix.property")."' ";
		input::getInput("mix.type") && $addWhere .= " AND `type` = '".input::getInput("mix.type")."' ";
		if(strlen(input::getInput("mix.is_lock"))>0) $addWhere .= " AND `is_lock` = '".input::getInput("mix.is_lock")."' ";
		input::getInput("mix.district") && $addWhere .= " AND `district` = '".input::getInput("mix.district")."' ";
        $areaCode = input::getInput("mix.area_code");
		if($areaCode && $areaCode!='510000'){
		    if(substr($areaCode,-2)=='00'){
                $addWhere .= " AND `area_code` like '".substr($areaCode,0,4)."%' ";
            }else{
                $addWhere .= " AND `area_code` = '{$areaCode}' ";
            }
        }

		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		{
			//保存标记
			$_SESSION['hash'] = $this->hash;
			$_SESSION['corporations']['baseSql'] = base64_encode($addWhere);
			//打印
			$_SESSION['corporations']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['corporations']['orderStr'] = base64_encode($addSql);
		}else{
			$addWhere = base64_decode($_SESSION['corporations']['sqlStr']);
		}
		$from_vars = array('search','field','is_lock','department_id','type','property','district','level');
		view::set("pager",sf::getModel("Corporations")->getPager($addWhere ,$addSql ,20,'','',$from_vars));
		view::apply("inc_body",$tpl);
		view::display("page_main");
	}

	/**
	 * 集中处理项目的搜索信息
	 *
	 */
	public function awardgrid($tpl = 'admin/award/submit_list',$addWhere = '1',$showMax=25,$page='page',$order='')
	{
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = $order ? $order : 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		//处理搜索
		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
		//input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
		if(input::getInput("mix.declare_year")) $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
		else $addWhere .= " AND `declare_year` = '".config::get('current_declare_year',date("Y"))."' ";
		
		input::getInput("mix.type_id") && $addWhere .= " AND `type_id` = '".input::getInput("mix.type_id")."' ";
		input::getInput("mix.subject_id") && $addWhere .= " AND `subject_id` LIKE '".input::getInput("mix.subject_id")."%' ";
		input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
		input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";
		
		input::getInput("mix.xyleibie") && $addWhere .= " AND `xyleibie` = '".input::getInput("mix.xyleibie")."' ";
		input::getInput("mix.fruit_type") && $addWhere .= " AND `fruit_type` = '".input::getInput("mix.fruit_type")."' ";
		input::getInput("mix.group_id") && $addWhere .= " AND `group_id` = '".input::getInput("mix.group_id")."' ";
		input::getInput("mix.group_mark") && $addWhere .= " AND `group_mark` = '".input::getInput("mix.group_mark")."' ";
		input::getInput("mix.sjdengji") && $addWhere .= " AND `sjdengji` = '".input::getInput("mix.sjdengji")."' ";
		//将搜索条件保存一边打印或者导出
		$_SESSION['sqlStr'] = base64_encode($addWhere);
		$_SESSION['orderStr'] = base64_encode($addSql);
		//显示页面
		//dd($addWhere);
		$form_vars = array('search','declare_year','type_id','subject_id','xylx','fruit_type','group_id','department_id','statement','field','group_id');
		view::set("pager",sf::getModel("Awards")->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);	
	}
	/**
	 * 集中处理人才的搜索信息
	 *
	 */
	public function talentgrid($tpl = 'admin/declarer/index',$addWhere = '1',$showMax=25,$page='page',$order='')
	{
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'score';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = $order ? $order : 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		//处理搜索
		if(strpos(input::getInput("mix.search"),';')){
			$declarers = str_replace(';', "','", input::getInput("mix.search"));
			$addWhere .= " AND personname in ('".$declarers."') ";
		}else{
			if(input::getInput("mix.search")){
				$addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
			} 
		}
		$addWhere .= " AND `talent` > 0 ";
		input::getInput("mix.personname") && $addWhere .= " AND `personname` = '".input::getInput("mix.personname")."' ";
		input::getInput("mix.corporation_name") && $addWhere .= " AND `corporation_name` = '".input::getInput("mix.corporation_name")."' ";
		//将搜索条件保存一边打印或者导出
		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		{
			//保存标记
			$_SESSION['hash'] = $this->hash;
			$_SESSION['declarers']['baseSql'] = base64_encode($addWhere);
			//打印
			$_SESSION['declarers']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['declarers']['orderStr'] = base64_encode($addSql);
		}else{
			$addWhere = base64_decode($_SESSION['declarers']['sqlStr']);
		}
		//显示页面
		//dd($addWhere);
		$form_vars = array('search','declare_year','type_id','subject_id','xylx','fruit_type','group_id','department_id','is_lock','field','group_id');
		view::set("pager",sf::getModel("Declarers")->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);	
	}
	/**
	 * 集中处理项目的搜索信息
	 *
	 */
	public function propertyrightgrid($tpl = 'admin/propertyright/submit_list',$addWhere = '1',$showMax=25,$page='page',$order='')
	{
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = $order ? $order : 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		//筛选自己分管的专业组
		//$addWhere .= " AND `group_id` IN ('".implode("','",(array)input::getInput("session.group_id"))."') ";
		//处理搜索
		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

		if(input::getInput("mix.declare_year")) $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
		else $addWhere .= " AND `declare_year` = '".config::get('current_declare_year',date("Y"))."' ";
		
		input::getInput("mix.type_id") && $addWhere .= " AND `type_id` = '".input::getInput("mix.type_id")."' ";
		input::getInput("mix.subject_id") && $addWhere .= " AND `subject_id` LIKE '".input::getInput("mix.subject_id")."%' ";
		input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
		input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";
		
		input::getInput("mix.fruit_type") && $addWhere .= " AND `fruit_type` = '".input::getInput("mix.fruit_type")."' ";
		input::getInput("mix.group_id") && $addWhere .= " AND `group_id` = '".input::getInput("mix.group_id")."' ";
		input::getInput("mix.group_mark") && $addWhere .= " AND `group_mark` = '".input::getInput("mix.group_mark")."' ";
		//将搜索条件保存一边打印或者导出
		$_SESSION['sqlStr'] = base64_encode($addWhere);
		$_SESSION['orderStr'] = base64_encode($addSql);
		//显示页面
		//dd($addWhere);
		$form_vars = array('search','declare_year','type_id','subject_id','xylx','fruit_type','group_id','department_id','statement','field','group_id');
		view::set("pager",sf::getModel("Propertyrights")->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);	
	}

	public function devotegrid($tpl = 'admin/devote/submit_list',$addWhere = '1',$showMax=25,$page='page',$order='')
	{
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = $order ? $order : 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		//处理搜索
		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
		
		if(input::getInput("mix.declare_year")) $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
		else $addWhere .= " AND `declare_year` = '".config::get('current_declare_year',date("Y"))."' ";

		input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
		input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";

		//将搜索条件保存一边打印或者导出
		$_SESSION['sqlStr'] = base64_encode($addWhere);
		$_SESSION['orderStr'] = base64_encode($addSql);
		//显示页面
		$form_vars = array('search','declare_year','department_id','statement','field');
		view::set("pager",sf::getModel("devotes")->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);	
	}
	
	/**
	 * 页面调试，系统提示方法 
	 */
	public function returnAjax($info='',$data=array(),$status=1,$type='JSON')
	{
		$result  =  array();
		$result['status']  =  $status;
		$result['info'] =  $info;
		$result['data'] = $data;
		
		if(strtoupper($type)=='JSON') {
			header("Content-Type:text/html; charset=utf-8");
			exit(json_encode($result));
		}else{
            // TODO 增加其它格式
		}
	}
	
/*	function success($info='',$data=array())
	{
		return $this->returnAjax($info,$data,true);	
	}
	
	function error($info='',$data=array())
	{
		return $this->returnAjax($info,$data,false);	
	}*/

	public function project_out_gird($addWhere='1',$tpl='user/projectOut/index')
	{
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		{
			//保存标记
			$_SESSION['hash'] = $this->hash;
			$_SESSION['projectouts']['baseSql'] = base64_encode($addWhere);
			//打印
			$_SESSION['projectouts']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['projectouts']['orderStr'] = base64_encode($addSql);
		}else{
			$addWhere = base64_decode($_SESSION['projectouts']['sqlStr']);
		}

		$from_vars = array('search','field','is_lock');
		view::set("pager",sf::getModel("ProjectOuts")->getPager($addWhere ,$addSql ,20,'','',$from_vars));
		view::apply("inc_body",$tpl);
		view::display("page");
	}

    function __destruct()
    {
//        if(APP_DEBUG===true){
        if(false){
            // 获取基本信息
            $startTime = config::get('start_time');
            $runtime = number_format(microtime(true) - $startTime, 10);
            $reqs    = $runtime > 0 ? number_format(1 / $runtime, 2) : '∞';

            if (isset($_SERVER['HTTP_HOST'])) {
                $uri = $_SERVER['SERVER_PROTOCOL'] . ' ' . $_SERVER['REQUEST_METHOD'] . ' : ' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
            } else {
                $uri = 'cmd:' . implode(' ', $_SERVER['argv']);
            }
            // 页面Trace信息
            $db = sf::getLib('db');
            $base = [
                '请求信息' => date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']) . ' ' . $uri,
                '运行时间' => number_format($runtime, 6) . 's [ 吞吐率：' . $reqs . 'req/s ] 文件加载：' . count(get_included_files()),
                '查询信息' => $db::$queryTimes . ' queries , ' . $db::$executeTimes . ' writes ',
                '配置加载' => count(Config::get()),
            ];
            if (session_id()) {
                $base['会话信息'] = 'SESSION_ID=' . session_id();
            }
            $html = "<script type='text/javascript'>
            console.group('基本');";
            foreach ($base as $key=>$value):
                $html.="console.log('{$key} {$value}');";
            endforeach;
            $html.="\r\n console.groupEnd();
            </script>";
            echo $html;

            $error = error_get_last();
            $sqls = sf::getLib('db')->getRunSqls();
            if($error){
                $errorString = $error['message'].' File: '.$error['file'].' on line '.$error['line'];
                $errorString = var_export($errorString,1);
                $html = "<script type='text/javascript'>
            console.groupCollapsed('错误');
            console.log({$errorString});
            console.groupEnd();
            </script>";
                echo $html;
            }
            if($sqls){
                $html = "<script type='text/javascript'>
            console.groupCollapsed('SQL');";
                $count = count($sqls['sql']);
                for($i=0;$i<$count;$i++):
                    $sql = var_export($sqls['sql'][$i],1);
                    $html.="console.log(\"%c[ ".($i+1)." ]{$sql} [ RunTime:{$sqls['runtime'][$i]} ]\",\"color:#009bb4;\");";
                endfor;
                $html.="\r\n console.groupEnd();
            </script>";
                echo $html;
            }
            if($_SESSION){
                $html = "<script type='text/javascript'>
            console.groupCollapsed('SESSION');";
                foreach ($_SESSION as $key=>$item):
                    $value = var_export($key.' : '.$item,1);
                    $html.="console.log({$value});";
                endforeach;
                $html.="\r\n console.groupEnd();
            </script>";
                echo $html;
            }
            if($include_files = get_included_files()){
                $html = "<script type='text/javascript'>
            console.groupCollapsed('文件');";
                foreach ($include_files as $key=>$item):
                    $i = $key+1;
                    $value = var_export($i.' : '.$item,1);
                    $html.="console.log($value);";
                endforeach;
                $html.="\r\n console.groupEnd();
            </script>";
                echo $html;
            }
        }
    }
}
