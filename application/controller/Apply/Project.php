<?php
namespace App\Controller\Apply;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\lang;
/**
 * 项目的查看页面
 *
 */

class project extends BaseController
{	
	
	function load()
	{
		$this->project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
		
		if($this->project->isNew())
			$this->page_debug(lang::get("The project do not exist!"),getFromUrl());	
	}
	
	function index()
	{
		$this->show();
	}
	
	function show()
	{
        if($this->project->getConfigs("archive.apply"))
			$this->jump(site_url($this->project->getConfigs("archive.apply")."/show/id/".$this->project->getProjectId()));
        if($this->project->getConfigs("path.apply"))
			$this->jump(site_url($this->project->getConfigs("path.apply")."/show/id/".$this->project->getProjectId()));
        $this->error('该项目无具体内容信息',getFromUrl());
		$data['project'] = $this->project;
		$data['page'] = input::getInput("get.page") ? input::getInput("get.page") : 1;
		view::set($data);
		view::apply("inc_body","declare/".getFolder($data['project']->getTypeId())."/project/show");
		view::display("page");
	}
	
	/**
	 * 打印项目
	 */
	function output()
	{
		if(!in_array($_SESSION['userlevel'],array('1','2','3','4','5','6','7','8','11','14','16'))) $this->page_debug(lang::get("You do not have permission to do it!"),'javascript:window.close();');
		if($this->project->getConfigs("path.apply")) 
			$this->jump(site_url($this->project->getConfigs("path.apply")."/output/id/".$this->project->getProjectId()));

		if($this->project->getConfigs("file.project")) $this->download();//如果存在直接下载
		$data['project'] = $this->project;
		view::set($data);
		view::display("declare/".getFolder($data['project']->getTypeId())."/project/output");
	}
	
	/**
	 * 下载WORD文档
	 */
	function download()
	{
		if(!in_array($_SESSION['userlevel'],array('1','2','3','4','5','6','7','8','11','14','16'))) $this->page_debug(lang::get("You do not have permission to do it!"),'javascript:window.close();');
		if($this->project->getConfigs("path.apply")) 
			$this->jump(site_url($this->project->getConfigs("path.apply")."/download/id/".$this->project->getProjectId()));
		
		if($file_name = $this->project->getConfigs("file.project")){
			@header("Location:/up_files/".$file_name);
			exit;
		}else{
			header("Content-type:application");
			header("Content-Disposition: attachment; filename=".$this->project->getProjectId().'.doc');
			$data['start'] = date("Y",strtotime($this->project->getStartAt()));
			$data['end'] = date("Y",strtotime($this->project->getEndAt()));
			$data['project'] = $this->project;
			$data['download'] = 'yes';
			view::set($data);
			$output = view::getContent("declare/".getFolder($this->project->getTypeId())."/project/output");
			$output = str_replace('"/up_files/','"'.base_url().'up_files/',$output);
			exit($output);
		}
	}

    /**
     * 编辑项目
     */
    function edit()
    {
        if($this->project->enableWrite()){
            $_SESSION['declare']['project_id'] = $this->project->getProjectId();
            $_SESSION['declare']['type_id'] = $this->project->getTypeId();
//            $this->project->setStatement(1);
//            $this->project->save();
            if($this->project->getConfigs("path.apply")) $this->jump(site_url($this->project->getConfigs("path.apply")."/index/id/".$this->project->getProjectId()));
            else $this->jump(site_url("engine/worker/edit/id/".$this->project->getProjectId()));
        }else{
            unset($_SESSION['declare']['project_id']);
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        }
    }

    public function review()
    {
        view::set("project",$this->project);
        view::apply("inc_body","user/project/review");
        view::display("page_blank");
    }

}
?>