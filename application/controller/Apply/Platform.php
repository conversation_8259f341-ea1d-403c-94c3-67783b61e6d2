<?php
namespace App\controller\Apply;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\lang;
/**
 * 项目的查看页面
 *
 */

class Platform extends BaseController
{	
	
	function load()
	{
		$this->platform = sf::getModel("Platforms")->selectByPlatformId(input::getInput("mix.id"));
		
		if($this->platform->isNew())
			$this->page_debug(lang::get("The project do not exist!"),getFromUrl());	
	}
	
	function index()
	{
		$this->show();
	}
	
	function show()
	{
        $this->jump(site_url('platform/profile/show/userid/'.$this->platform->getPlatformId()));
	}
	
	/**
	 * 打印项目
	 */
	function output()
	{
		if(!in_array($_SESSION['userlevel'],array('1','2','3','4','5','6','7','8','11','14','16'))) $this->page_debug(lang::get("You do not have permission to do it!"),'javascript:window.close();');
		if($this->platform->getConfigs("path.apply")) 
			$this->jump(site_url($this->platform->getConfigs("path.apply")."/output/id/".$this->platform->getPlatformId()));

		if($this->platform->getConfigs("file.project")) $this->download();//如果存在直接下载
		$data['project'] = $this->platform;
		view::set($data);
		view::display("declare/".getFolder($data['project']->getTypeId())."/project/output");
	}
	
	/**
	 * 下载WORD文档
	 */
	function download()
	{
		if(!in_array($_SESSION['userlevel'],array('1','2','3','4','5','6','7','8','11','14','16'))) $this->page_debug(lang::get("You do not have permission to do it!"),'javascript:window.close();');
		if($this->platform->getConfigs("path.apply")) 
			$this->jump(site_url($this->platform->getConfigs("path.apply")."/download/id/".$this->platform->getPlatformId()));
		
		if($file_name = $this->platform->getConfigs("file.project")){
			@header("Location:/up_files/".$file_name);
			exit;
		}else{
			header("Content-type:application");
			header("Content-Disposition: attachment; filename=".$this->platform->getPlatformId().'.doc');
			$data['start'] = date("Y",strtotime($this->platform->getStartAt()));
			$data['end'] = date("Y",strtotime($this->platform->getEndAt()));
			$data['project'] = $this->platform;
			$data['download'] = 'yes';
			view::set($data);
			$output = view::getContent("declare/".getFolder($this->platform->getTypeId())."/project/output");
			$output = str_replace('"/up_files/','"'.base_url().'up_files/',$output);
			exit($output);
		}
	}

    /**
     * 编辑项目
     */
    function edit()
    {
        if(!$this->platform->isNew() && in_array($this->platform->getStatement(),array('0','1','3','6','12','22','24')) && input::session('roleuserid') == $this->platform->getUserId()){
            $_SESSION['declare']['project_id'] = $this->platform->getPlatformId();
            $_SESSION['declare']['type_id'] = $this->platform->getTypeId();
            $this->platform->setStatement(1);
            $this->platform->save();
            if($this->platform->getConfigs("path.apply")) $this->jump(site_url($this->platform->getConfigs("path.apply")."/index/id/".$this->platform->getPlatformId()));
            else $this->jump(site_url("engine/worker/edit/id/".$this->platform->getPlatformId()));
        }else{
            unset($_SESSION['declare']['project_id']);
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        }
    }

}
?>