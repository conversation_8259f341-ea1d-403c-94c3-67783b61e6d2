<?php
namespace App\controller\Apply;
use App\Controller\BaseController;
use Sofast\Core\lang;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
class Summary extends BaseController
{	
	private $summary = NULL;
	
	function load()
	{
		$this->summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput("mix.id"));
		
		if($this->summary->isNew())
			$this->page_debug(lang::get("The project do not exist!"),getFromUrl());
	}
	
	function index()
	{
		$this->show();
	}

	/**
	 * 编辑项目
	 */
	function edit()
	{
//        $this->error("中期评估模块还未开启！",getFromUrl());

		if($this->summary->getConfigs("path.apply"))
			$this->jump(site_url($this->summary->getConfigs("path.apply")."/index/id/".$this->summary->getSummaryId()));

		if(!$this->summary->isNew()){
			$this->summary->setStatement(1);
			$this->summary->save();
			$this->jump(site_url('engine/summaryer/edit/id/'.$this->summary->getSummaryId()));
		}else{
			$this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		}
	}
	
	/**
	 * 查看项目
	 */
	function show()
	{
//		 if($this->summary->getConfigs("file.stage")) $this->download();//如果存在直接下载

		if($this->summary->getConfigs("path.apply"))
			$this->jump(site_url($this->summary->getConfigs("path.apply")."/show/id/".$this->summary->getSummaryId()));
		
		$this->error('该项目年度监测报告还未填写');
	}

	
}
?>