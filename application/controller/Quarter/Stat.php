<?php
namespace App\Controller\Quarter;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Stat extends BaseController
{

    public function load()
    {
        $this->hash = md5(input::getInput("mix.controller").input::getInput("mix.method").input::getInput("mix.guideid"));
        $data['hash'] = $this->hash;
        view::set($data);
    }

    /**
     * 按平台类型统计
     * @return void
     */
    public function type()
    {
        $guide = sf::getModel('Guides',input::getMix('guideid'));
        if($guide->isNew()) $this->page_debug('找不到该报表类型');
        //保存导出条件
        $addSql = 'ORDER BY updated_at DESC ';
        $addWhere = " `guide_id` = '".$guide->getId()."'";
        //保存标记
        $_SESSION['hash'] = $this->hash;
        $_SESSION['quarters']['baseSql'] = base64_encode($addWhere);
        //打印
        $_SESSION['quarters']['sqlStr'] = base64_encode($addWhere);
        $_SESSION['quarters']['orderStr'] = base64_encode($addSql);

        $types = get_select_data('platform_type');
        $tpl = 'quarter/stat/type';
        view::set('guide',$guide);
        view::set('types',$types);
        view::apply("inc_body",$tpl);
        view::display('page');
    }

    /**
     * 按依托单位性质统计
     * @return void
     */
    public function property()
    {
        $guide = sf::getModel('Guides',input::getMix('guideid'));
        if($guide->isNew()) $this->page_debug('找不到该报表类型');
        $propertys = get_select_data('property');
        $tpl = 'quarter/stat/property';
        view::set('guide',$guide);
        view::set('propertys',$propertys);
        view::apply("inc_body",$tpl);
        view::display('page');
    }


    /**
     * 按产业领域统计
     * @return void
     */
    public function industry()
    {
        $guide = sf::getModel('Guides',input::getMix('guideid'));
        if($guide->isNew()) $this->page_debug('找不到该报表类型');
        $industrys = get_select_data('industry');
        $tpl = 'quarter/stat/industry';
        view::set('industrys',$industrys);
        view::set('guide',$guide);
        view::apply("inc_body",$tpl);
        view::display('page');
    }


}