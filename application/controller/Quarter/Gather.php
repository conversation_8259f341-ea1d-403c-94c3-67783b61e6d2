<?php
namespace App\Controller\quarter;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class gather extends BaseController
{
    public function index()
    {
        $this->gird();
    }

    public function wait_list()
    {
        $this->gird('quarter/gather/wait_list','statement  = 2');
    }

    public function accept_list()
    {
        $this->gird('quarter/gather/accept_list','statement = 10');
    }

    public function back_list()
    {
        $this->gird('quarter/gather/back_list','statement = 3');
    }

    public function gird($tpl = 'quarter/gather/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere.=" AND (`department_id` = '".input::session('roleuserid')."' or `top_department_id` = '".input::session('roleuserid')."')";
        input::getInput("mix.guideid") && $addWhere .= " AND `guide_id` = '".input::getInput("mix.guideid")."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";

        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['quarters']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['quarters']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['quarters']['orderStr'] = base64_encode($addSql);
        }else{
//            $addWhere = base64_decode($_SESSION['quarters']['sqlStr']);
        }

        $form_vars = array('subject','linkman');
        view::set("pagers",sf::getModel('Quarters')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }

    /**
     * 受理
     */
    public function doAccept()
    {
        $quarter = sf::getModel('Quarters')->selectByItemId(input::getInput('mix.id'));
        //如果项目不存在
        if($quarter->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if($quarter->getGuide()->isEnd()){
            $this->error('时间已截止，不允许执行该操作！',getFromUrl());
        }
        $quarter->setStatement(10);
        $quarter->save();
        //更新统计表
        $stat = sf::getModel('DepartmentStats')->selectByDepartmentId($quarter->getDepartmentId(),$quarter->getGuideId());
        $stat->updateAcceptTotal();

        sf::getModel("historys")->addHistory($quarter->getItemId(),'报表已审核','quarter');//保存历史记录
        $this->success('已审核',getFromUrl());
    }

    public function doBack()
    {
        $quarter = sf::getModel("Quarters")->selectByItemId(input::mix("id"));
        if($quarter->isNew()) $this->refresh(lang::get('The project is not found!'),'error');
        if($quarter->getGuide()->isEnd()){
            $this->refresh('时间已截止，不允许执行该操作！','error');
        }
        if(input::getInput("post.content")){
            $_statement = $quarter->getStatement();
            $quarter->setStatement(3);//归口退回
            $quarter->save();
            if($_statement==10){
                //更新统计表
                $stat = sf::getModel('DepartmentStats')->selectByDepartmentId($quarter->getDepartmentId(),$quarter->getGuideId());
                $stat->updateAcceptTotal();
            }
            addHistory($quarter->getItemId(),'报表退回！其原因是：<br />'.input::getInput("post.content"),'quarter');
            $this->refresh('已退回','success');
        }
        //原因表单
        $form = Form::load('quarter/gather/doBack')
            ->addItem(Form::Label('单位名称：'.$quarter->getSubject())
                ->setAttribute('class','col-md-12')
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin'))
            ->addItem(Form::hidden('id',$quarter->getItemId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

    public function doDelete()
    {
        $quarter = sf::getModel("Quarters")->selectByItemId(input::mix("id"));
        if($quarter->isNew()) $this->refresh(lang::get('The project is not found!'),'error');
        if($quarter->getGuide()->isEnd()){
            $this->refresh('时间已截止，不允许执行该操作！','error');
        }
        if(input::getInput("post.content")){
            $quarter->delete();
            addHistory($quarter->getItemId(),'报表删除！其原因是：<br />'.input::getInput("post.content"),'quarter');
            $this->refresh('已删除','success');
        }
        //原因表单
        $form = Form::load('quarter/gather/doDelete')
            ->addItem(Form::Label('单位名称：'.$quarter->getSubject())
                ->setAttribute('class','col-md-12')
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'删除原因'])->setWidth(12)->setAttribute('class','no-margin'))
            ->addItem(Form::hidden('id',$quarter->getItemId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

    public function manage()
    {
        $pagers = sf::getModel('Guides')->getPager("enabled = 1","order by id desc");
        view::set('pagers',$pagers);
        view::apply("inc_body",'quarter/gather/manage');
        view::display('page');
    }

    /**
     * 上交情况
     */
    public function submit()
    {
        $pagers = sf::getModel('Guides')->getPager("enabled = 1","order by id desc");
        view::set('pagers',$pagers);
        view::apply("inc_body",'quarter/gather/submit');
        view::display('page');
    }

    public function stat()
    {
        $pagers = sf::getModel('Guides')->getPager("enabled = 1","order by id desc");
        view::set('pagers',$pagers);
        view::apply("inc_body",'quarter/gather/stat');
        view::display('page');
    }

    public function stat_area()
    {
        $departmentId = input::session('roleuserid');
        $departments = sf::getModel('Departments')->selectAll("`parent_id` = '{$departmentId}' or user_id = '{$departmentId}'","order by id asc");
        $tpl = 'quarter/gather/stat_area';
        if(input::getMix('guideid')>=15){
            $tpl = 'quarter/gather/stat_area_2020';
        }
        if(input::getMix('guideid')>=18){
            $tpl = 'quarter/gather/stat_area_202002';
        }
        view::set('departments',$departments);
        view::set('guideid',input::getMix('guideid'));
        view::set('isHightech',intval(input::getMix('is_hightech')));
        view::set('isService',intval(input::getMix('is_service')));
        view::apply("inc_body",$tpl);
        view::display('page');
    }

    public function stat_industry()
    {
        $industrys = sf::getModel('Industrys')->selectAll("","order by id asc");
        $tpl = 'quarter/gather/stat_industry';
        if(input::getMix('guideid')>=15){
            $tpl = 'quarter/gather/stat_industry_2020';
        }
        if(input::getMix('guideid')>=18){
            $tpl = 'quarter/gather/stat_industry_202002';
        }
        view::set('industrys',$industrys);
        view::set('guideid',input::getMix('guideid'));
        view::set('isHightech',intval(input::getMix('is_hightech')));
        view::set('isService',intval(input::getMix('is_service')));
        view::apply("inc_body",$tpl);
        view::display('page');
    }

    public function stat_industry_top()
    {
        $industrys = sf::getModel('Industrys')->selectAll("`level` = 1","order by id asc");
        $tpl = 'quarter/gather/stat_industry_top';
        if(input::getMix('guideid')>=15){
            $tpl = 'quarter/gather/stat_industry_top_2020';
        }
        if(input::getMix('guideid')>=18){
            $tpl = 'quarter/gather/stat_industry_top_202002';
        }
        view::set('industrys',$industrys);
        view::set('guideid',input::getMix('guideid'));
        view::set('isHightech',intval(input::getMix('is_hightech')));
        view::set('isService',intval(input::getMix('is_service')));
        view::apply("inc_body",$tpl);
        view::display('page');
    }

}