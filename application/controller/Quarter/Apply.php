<?php
namespace App\Controller\quarter;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class apply extends BaseController
{
    private $quarter;
    private $tabs = array();

    public function load()
    {
        $this->quarter = sf::getModel('Quarters')->selectByItemId(input::getInput('mix.id'));
    }

    public function init()
    {
        if($this->quarter->isNew()){
            $this->quarter->setGuideId(input::getInput('mix.guide_id'));
            $this->quarter->setFormId($this->quarter->getGuide()->getFormId());
            $this->quarter->setStatement(1);
            $this->quarter->setPlatformId($this->quarter->getPlatform()->getPlatformId());
            $this->quarter->setSubject($this->quarter->getPlatform()->getSubject());
            //复制上一次的数据
            $this->quarter->copyLastData();
            $this->quarter->save();
        }
        if($_SESSION['wait_quarterid']) unset($_SESSION['wait_quarterid']);
        if($this->quarter->getPlatform()->validate()===false){
            $_SESSION['wait_quarterid'] = $this->quarter->getQuarterId();
            $this->error("请先完善平台基础资料！",site_url('platform/profile/base'));
        }
        if(!$this->quarter->allowEdit() && input::session('userlevel')==3) $this->error("当前状态禁止编辑！",getFromUrl());
        if($this->quarter->getGuide()->isEnd() && input::session('userlevel')==4){
            $this->error('时间已截止，不允许执行该操作！',getFromUrl());
        }
    }

    public function index()
    {
        //已完成的指南id
        $finishIds = getFinishGuideIds('string');
        $addWhere = ' 1';
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        if($finishIds){
            $addWhere.=" AND id not in({$finishIds})";
        }
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        $form_vars = array();
        view::set("pagers",sf::getModel('QuarterGuides')->getPager($addWhere,$addSql,16,'','',$form_vars));
        view::apply("inc_body",'quarter/apply/index');
        view::display('page');
    }

    public function finish_list()
    {
        //已完成的指南id
        $finishIds = getFinishGuideIds('string');
        $addWhere = ' 1';
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        if($finishIds){
            $addWhere.=" AND id in({$finishIds})";
        }else{
            $addWhere.=" AND id = -1";
        }
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        $form_vars = array();
        view::set("pagers",sf::getModel('QuarterGuides')->getPager($addWhere,$addSql,16,'','',$form_vars));
        view::apply("inc_body",'quarter/apply/finish_list');
        view::display('page');
    }

    public function gird($tpl = 'quarter/apply/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " AND `corporation_id` = '".input::getInput('session.roleuserid')."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        $form_vars = array();
        view::set("pagers",sf::getModel('Quarters')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }

    public function edit()
    {
        $this->init();
        if(input::getInput('post')){
//            if(!View::checkCsrf())  $this->error("请不要重复提交页面！",getFromUrl());
            if($this->quarter->isNew()) $this->quarter->setGuideId((int)input::getInput('post.guide_id'));
            $this->quarter->setPlatformId($this->quarter->getPlatform()->getPlatformId());
            $this->quarter->setSubject($this->quarter->getPlatform()->getSubject());
            $this->quarter->setPlatformType($this->quarter->getPlatform()->getPlatformType());
            $this->quarter->setCorporationName($this->quarter->getPlatform()->getCorporationName());
            $this->quarter->setCorporationProperty($this->quarter->getPlatform()->getCorporationProperty());
            $this->quarter->setIndustry($this->quarter->getPlatform()->getIndustry());
            $this->quarter->setLinkman(input::post('linkman'));
            $this->quarter->setMobile(input::post('mobile'));
            $this->quarter->setDatas(input::post('data'));
            $this->quarter->setNotes(input::post('note'));
            if(input::session('userlevel')==3) $this->quarter->setStatement(1);
            $this->quarter->save();
            if(input::post('is_submit')==1){
                $this->jump(site_url('quarter/apply/submit/id/'.$this->quarter->getItemId()));
            }
            $this->success(lang::get('Has been saved!'),site_url('quarter/apply/edit/id/'.$this->quarter->getItemId()));
        }
        view::set('quarter',$this->quarter);
        view::apply("inc_body",$this->quarter->getForm()->getWriteTemplate());
        view::display('page');
    }

    public function show()
    {
        if ($this->quarter->isNew()) $this->page_debug('未找到该报表');
        view::set('quarter', $this->quarter);
        view::apply('inc_body', $this->quarter->getForm()->getPreviewTemplate());
        view::display('page');
    }

    /**
     * 上报
     */
    public function submit()
    {
        if(input::getInput('post')){
            //判断项目验证是否通过
            if($message = $this->quarter->validate())
                $this->page_debug(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());
            $this->quarter->setSubmitAt(date("Y-m-d H:i:s"));
            $this->quarter->setStatement(9);        //待科技厅审核
            $this->quarter->save();

            addHistory($this->quarter->getItemId(),'报表上报','quarter');
            $this->success(lang::get("Has been submit!"),site_url("quarter/apply/index"));
        }
        view::set('quarter',$this->quarter);
        view::set('msg',$this->quarter->validate());
        view::apply("inc_body","quarter/apply/submit");
        view::display("page");
    }

    function export()
    {
        if ($this->quarter->isNew()) $this->page_debug('未找到该报表');
        view::set('quarter', $this->quarter);
        header("Content-type:application");
        header("Content-Disposition: attachment; filename=季报-".$this->quarter->getSubject().".doc");
        $output = view::getContent('quarter/apply/show');
        $output = str_replace('<span class="space-2"></span>','&nbsp;&nbsp;',$output);
        $output = str_replace('<span class="space-5"></span>','&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;',$output);
        exit($output);
    }

    public function doBack()
    {
        if ($this->quarter->isNew()) $this->refresh('未找到该报表！','error');
        if($this->quarter->getGuide()->isEnd()){
            $this->refresh('时间已截止，不允许执行该操作！','error');
        }
        if($this->quarter->getStatement()!=9){
            $this->refresh('不允许执行该操作！','error');
        }
        if(input::getInput("post.content")){
            $this->quarter->setStatement(1);
            $this->quarter->save();
            addHistory($this->quarter->getItemId(),'报表撤回！其原因是：<br />'.input::getInput("post.content"),'quarter');
            $this->refresh('已撤回','success');
        }
        //原因表单
        $form = Form::load('quarter/apply/doBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'平台名称：'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($this->quarter->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'撤回原因：'])->setWidth(12)->setAttribute('class','no-margin')->setValue('撤回修改！'))
            ->addItem(Form::hidden('id',$this->quarter->getItemId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

}