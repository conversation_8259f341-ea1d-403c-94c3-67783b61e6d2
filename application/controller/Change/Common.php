<?php
namespace App\controller\Change;
use App\Controller\BaseController;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Common extends BaseController
{

    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/view/');
    }



    function ajax()
    {
        @header("Content-type:text/html;charset=utf-8");
        $platform = sf::getModel("Platforms")->selectByPlatformId(input::post("id"));
        $data['code'] = 1;
        if($platform->isNew()) {
            $data['msg'] = '平台不存在';
            echo json_encode($data,JSON_UNESCAPED_UNICODE);
            exit();
        }
        $data['code'] = 0;
        $data['type'] = input::post("k");
        switch(input::post("k"))
        {
            case 'subject':
                $data['data']['subject'] = $platform->getSubject();
                break;
            case 'principal_name':
                $data['data']['principal_name'] = $platform->getPrincipalName();
                break;
            case 'corporation_id':
                $data['data']['corporation_id'] = $platform->getCorporationId();
                $data['data']['corporation_name'] = $platform->getCorporationName();
                break;
            case 'corporation_name':
                $data['data']['corporation_id'] = $platform->getCorporationId();
                $data['data']['corporation_name'] = $platform->getCorporationName();
                break;
            case 'department_id':
                $data['data']['department_id'] = $platform->getDepartmentId();
                $data['data']['department_name'] = $platform->getDepartmentName();
                break;
            case 'cooperation':
                $data['data']['cooperation'] = implode('、',$platform->getCooperation());
                break;
            case 'target':
                $data['data']['target'] = '';
                break;
            case 'member':
                $data['data']['member'] = '';
                break;
            case 'other':
                $data['data']['other'] = '';
                break;
            default:
                break;
        }
        echo json_encode($data,JSON_UNESCAPED_UNICODE);
        exit();
    }

    function search_account()
    {
        $addWhere = $addSql = '';
        $addWhere .= "`is_lock` = 0 ";
        //限定到自己的单位
        $user = sf::getModel("Declarers")->selectByUserId(input::session("roleuserid"));
        if($user->isNew()) $this->page_debug("身份确认失败，请重新登录后再试...");

        $addWhere .= " AND user_id  <> '".$user->getUserId()."' and corporation_id = '".$user->getCorporationId()."'";

        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";

        $from_vars = array('search','field');
        $this->view->set("pager",sf::getModel("Declarers")->getPager($addWhere,"ORDER BY ID DESC",12,'','',$from_vars));
        $this->view->apply("inc_body","common/account_list");
        $this->view->display("page_blank");
    }


}