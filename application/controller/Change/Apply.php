<?php
namespace App\controller\Change;
use App\Controller\BaseController;
use App\Facades\PDF;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Apply extends BaseController
{

    private $view = NULL;
    private $platform = NULL;
    private $change = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/view/');
        $this->platform = sf::getModel('Platforms')->selectByPlatformId(input::getMix('platform_id'));
        $this->change = sf::getModel('PlatformChanges')->selectByChangeId(input::getMix('id'));
        if($this->change->isNew()){
            if($this->platform->isNew()) $this->error('请选择要变更的平台');
            $this->change->setUserId(input::session('roleuserid'));
            $this->change->setPlatformId($this->platform->getPlatformId());
            $this->change->setCompanyId($this->platform->getCorporationId());
            $this->change->setCompanyName($this->platform->getCorporationName());
            $this->change->setDepartmentId($this->platform->getDepartmentId());
            $this->change->setPlatformName($this->platform->getSubject());
        }
        $this->tabs = [
            [
                'method' => 'edit',
                'text' => '变更申请表',
                'url' => site_url('change/apply/edit/id/' . $this->change->getChangeId())
            ],
            [
                'method' => 'attachment',
                'text' => '相关附件',
                'url' => site_url('change/apply/attachment/id/' . $this->change->getChangeId())
            ]
        ];
        $this->view->set('method', input::getInput("mix.method"));
        $this->view->set('tabs', $this->tabs);
    }

    function edit()
    {
        if(input::post()){
            $this->change->setCompanyId($this->platform->getCorporationId());
            $this->change->setCompanyName($this->platform->getCorporationName());
            $this->change->setLinkman(input::post("linkman"));
            $this->change->setLinkmanMobile(input::post("linkman_mobile"));
            $this->change->setChangeType(input::post("change_type"));
            $this->change->setBefore(input::post("before"));
            $this->change->setAfter(input::post("after"));
            $this->change->setSummary(input::post("summary"));
            $this->change->setFilePath('');//清空文件
            $this->change->setStatement(1);
            if($this->change->isNew())
                $this->change->setCreatedAt(date('Y-m-d H:i:s'));
            $this->change->save();
            //提示保存成功
            $this->success("保存成功！",site_url('change/apply/edit/id/'.$this->change->getChangeId()));
        }
        $user = sf::getModel('Declarers')->selectByUserId(input::session("roleuserid"));
        $this->view->set("change",$this->change);
        $this->view->set("user",$user);
        $this->view->apply("inc_body","apply/edit");
        $this->view->display("page");
    }

    public function attachment()
    {
        if($this->change->isNew()) $this->error('请先填写变更申请表',getFromUrl());
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        $this->view->set('change',$this->change);
        $this->view->set('itemType','project_change');
        $this->view->apply('inc_body', 'apply/attachment');
        $this->view->display('page');
    }

    public function show()
    {
        if($this->change->isNew()) $this->error('请先填写变更申请表',getFromUrl());
        $this->view->set('change',$this->change);
        $this->view->display('apply/output');
    }

    /**
     * 导出打印
     */
    function download()
    {
        if($this->change->isNew()) $this->error('找不到该变更申请表',getFromUrl());
        if(false && $file_path = $this->change->getFilePath()){//如果已经生成文件，直接打开下载
            @header("Location:".site_path('up_files/'.$file_path));
            exit;
        }else{
            $file_path = date("Y").'/'.date("m").'/'.time().mt_rand(1000,9999).'.pdf';
            $data['change'] = $this->change;
            $data['download'] = 'yes';
            $this->view->set($data);
            $output = $this->view->getContent("apply/output");
            $output = str_replace('font-family','font-myname',$output);
            $hash = substr(md5(strip_tags($output)),0,20);
            $pdf = PDF::setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="22%" style="border: 0px solid #fff;">项目变更申请表</td><td width="78%" style="text-align: right;border: 0px solid #fff;">'.$this->change->getPlatformName().'</td></tr></table>')
                ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;">- {PAGENO} -</td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
                ->setContent($output)
                ->setTitle($this->change->getPlatformName())
                ->setSubject($this->change->getPlatformName())
                ->setCreator(input::getInput("session.nickname"))
                ->setAuthor(input::getInput("session.nickname"));
            if($this->change->getStatement() > 9){
                $pdf->setWaterMark('项目变更申请表',0.1);
                $pdf->save(WEBROOT."/up_files/".$file_path);
                $this->change->setFilePath($file_path);
                $this->change->save();
                $this->change->setShortUrl($hash);
                @header("Location:".site_path('up_files/'.$file_path));
                exit;
            }else{
                $pdf->setWaterMark('项目变更申请表',0.1);
                $pdf->show();
            }
        }
    }

    /**
     * 上报
     */
    public function submit()
    {
        if($this->change->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput('post')){
            //判断验证是否通过
            if($message = $this->change->validate())
                $this->error(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());
            $this->change->setSubmitAt(date("Y-m-d H:i:s"));
            //待承担单位审核
            $this->change->setStatement(2);
            $this->change->save();
            sf::getModel('Historys')->addHistory($this->change->getChangeId(),'平台变更申请上报','platform_change');
            $this->success(lang::get("Has been submit!"),site_url("change/user/submit_list"));
        }
        $this->view->set('change',$this->change);
        $this->view->set('msg',$this->change->validate());
        $this->view->apply("inc_body","apply/submit");
        $this->view->display("page");
    }

    /**
     * 集中处理项目的搜索信息
     *
     */
    public function mygrid($tpl = 'user/stage/index',$addWhere = '1',$showMax=25,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
        input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";

        //显示页面
        $form_vars = array('search','declare_year','department_id','statement','field');
        $this->view->set("pager",sf::getModel("Projects")->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }



}