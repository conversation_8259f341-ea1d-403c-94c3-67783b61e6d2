<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            项目变更申请
        </h2>
        <div class="content-options">
            <?php
                if(!$change->isNew()):
            ?>
            <?=Button::setUrl(site_url("change/apply/submit/id/".$change->getChangeId()))->setIcon('submit')->link('上报')?>
            <?=Button::setUrl(site_url("change/apply/download/id/".$change->getChangeId()))->setIcon('print')->link('打印')?>
            <?php endif;?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):?>
                        <li class="nav-item"><a class="nav-link <?php if($tab['method'] == $method):?>active<?php endif;?>" href="<?=$tab['url']?>">
                                <?=$tab['text']?>
                            </a></li>
                    <?php endforeach;?>
                    <li class="nav-item ml-auto">
                        <button class="btn btn-link" type="submit" style="padding-top: .75rem;
    padding-bottom: .75rem;"><i class="fa fa-save"></i> 保存</button>
                    </li>
                </ul>
                <div class="block-content">
                    <div class="page-body">
                        <div class="tab-content">
                            <div role="tabpanel" class="tab-pane active">
                                <form class="form-horizontal" id="form_1" action="" method="post">
                                    <div class="form-group row">
                                        <label class="control-label text-right col-xs-3 col-sm-3 col-md-3">申请变更的平台</label>
                                        <div class="col-xs-5 col-sm-5 col-md-5">
                                            <?=$change->getPlatformName()?>
                                        </div>
                                        <div class="clearfix"></div>
                                    </div>
                                    <input name="platform_id" id="platform_id" type="hidden" value="<?=$change->getPlatformId()?>" />
                                    <div class="form-group row">
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">变更类型</label>
                                        <div class="col-xs-12 col-sm-9 col-md-9">
                                            <?=get_checkbox($change->getMap(),'change_type',$change->getChangeType()?$change->getChangeType():'其他','',false)?>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">联系人</label>
                                        <div class="col-xs-12 col-sm-6 col-md-6">
                                            <input name="linkman" type="text" class="form-control" value="<?=$change->getLinkman()?:$user->getPersonname()?>" required="required" />
                                            <p class="help-block">项目联系人的姓名。</p>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">联系电话</label>
                                        <div class="col-xs-12 col-sm-6 col-md-6">
                                            <input name="linkman_mobile" type="number" class="form-control" value="<?=$change->getLinkmanMobile()?:$user->getUserMobile()?>" required="required" />
                                            <p class="help-block">项目联系人的手机号码。</p>
                                        </div>
                                    </div>
                                    <div class="form-group row" id="user_name"<?php if(!in_array('user_name',$change->getChangeType())):?> style="display:none;"<?php endif;?>>
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3"><strong>项目负责人变更</strong></label>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 项目负责人：
                                            <input name="before[user_name]" id="before_user_name" type="text" class="form-control" value="<?=$change->getBefore('user_name')?>" readonly="readonly" />
                                            <input name="before[user_id]" id="before_user_id" type="hidden" value="<?=$change->getBefore('user_id')?$change->getBefore('user_id'):$change->getUserId()?>"/>
                                        </div>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 变更为：
                                            <input name="after[user_name]" id="after_user_name" onclick="return showWindow('变更项目负责人','<?=site_url("change/common/search_account")?>');" type="text" class="form-control" value="<?=$change->getAfter('user_name')?>"<?php if(in_array('user_name',$change->getChangeType())):?> required="required"<?php endif;?> />
                                            <input name="after[user_id]" id="after_user_id" type="hidden" value="<?=$change->getAfter('user_id')?>"/>
                                        </div>
                                    </div>
                                    <div class="form-group row" id="subject"<?php if(!in_array('subject',$change->getChangeType())):?> style="display:none;"<?php endif;?>>
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3"><strong>平台名称变更</strong></label>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 平台名称：
                                            <input name="before[subject]" id="before_subject" type="text" class="form-control" value="<?=$change->getBefore('subject')?>" readonly="readonly" />
                                        </div>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 变更为：
                                            <input name="after[subject]" id="after_subject" type="text" class="form-control" value="<?=$change->getAfter('subject')?>" />
                                        </div>
                                    </div>
                                    <div class="form-group row" id="principal_name"<?php if(!in_array('principal_name',$change->getChangeType())):?> style="display:none;"<?php endif;?>>
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3"><strong>平台负责人变更</strong></label>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 平台负责人：
                                            <input name="before[principal_name]" id="before_principal_name" type="text" class="form-control" value="<?=$change->getBefore('principal_name')?>" readonly="readonly" />
                                        </div>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 变更为：
                                            <input name="after[principal_name]" id="after_principal_name" type="text" class="form-control" value="<?=$change->getAfter('principal_name')?>" />
                                        </div>
                                    </div>

                                    <div class="form-group row" id="corporation_id"<?php if(!in_array('corporation_id',$change->getChangeType())):?> style="display:none;"<?php endif;?>>
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3"><strong>承担单位变更</strong></label>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 承担单位：
                                            <input name="before[corporation_id]" id="before_corporation_id" type="text" class="form-control" value="<?=$change->getBefore('corporation_id')?>" readonly="readonly" />
                                        </div>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 变更为：
                                            <input name="after[corporation_id]" id="after_corporation_id" type="text" class="form-control" value="<?=$change->getAfter('corporation_id')?>"<?php if(in_array('corporation_id',$change->getChangeType())):?> required="required"<?php endif;?> />
                                        </div>
                                    </div>
                                    <div class="form-group row" id="corporation_name"<?php if(!in_array('corporation_name',$change->getChangeType())):?> style="display:none;"<?php endif;?>>
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3"><strong>承担单位名称变更</strong></label>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 承担单位原名称：
                                            <input name="before[corporation_name]" id="before_corporation_name" type="text" class="form-control" value="<?=$change->getBefore('corporation_name')?>" readonly="readonly" />
                                        </div>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 变更为：
                                            <input name="after[corporation_name]" id="after_corporation_name" type="text" class="form-control" value="<?=$change->getAfter('corporation_name')?>"<?php if(in_array('corporation_name',$change->getChangeType())):?> required="required"<?php endif;?> />
                                        </div>
                                    </div>
                                    <div class="form-group row" id="department_id"<?php if(!in_array('department_id',$change->getChangeType())):?> style="display:none;"<?php endif;?>>
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3"><strong>主管部门变更</strong></label>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 主管部门：
                                            <input name="before[department_id]" id="before_department_id" type="hidden" class="form-control" value="<?=$change->getBefore('department_id')?>" readonly="readonly" />
                                            <input name="before[department_name]" id="before_department_name" type="text" class="form-control" value="<?=$change->getBefore('department_name')?>" readonly="readonly" />
                                        </div>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 变更为：
                                            <select name="after[department_id]" id="after_department_id" class="form-control" <?php if(in_array('department_id',$change->getChangeType())):?> required="required"<?php endif;?>>
                                                <option value="">请选择</option>
                                                <?=getDepartmentList($change->getAfter('department_id'))?>
                                            </select>
                                            <input name="after[department_name]" id="after_department_name" type="hidden" class="form-control" value="<?=$change->getAfter('department_name')?>" readonly="readonly" />
                                        </div>
                                    </div>
                                    <div class="form-group row" id="other"<?php if(!in_array('other',$change->getChangeType())):?> style="display:none;"<?php endif;?>>
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3"><strong>其他</strong></label>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 申请调整前
                                            <textarea name="before[other]" id="before_other" class="form-control" rows="5" <?php if(in_array('other',$change->getChangeType())):?> required="required"<?php endif;?>><?=$change->getBefore('other')?></textarea>
                                        </div>
                                        <div class="col-xs-12 col-sm-3 col-md-3"> 申请调整为：
                                            <textarea name="after[other]" id="after_other" class="form-control" rows="5" <?php if(in_array('other',$change->getChangeType())):?> required="required"<?php endif;?>><?=$change->getAfter('other')?></textarea>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">申请调整的理由及情况</label>
                                        <div class="col-xs-12 col-sm-6 col-md-6">
            <textarea name="summary" rows="10" class="form-control" required="required"><?=$change->getSummary()?>
</textarea>
                                            <p class="help-block">申请调整的理由及情况，一般不超过500个汉字。</p>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3"></label>
                                        <div class="col-xs-12 col-sm-6 col-md-9">
                                            <input name="id" type="hidden" value="<?=$change->getId()?>" />
                                            <?=btn('button','保存资料','submit','save')?>
                                        </div>
                                    </div>
                                </form>
                                <p style="clear:both"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
function setBefore(k,show)
{
	if($('#platform_id').val() =='') alert('请选择调整的项目！');
	if(show){
		$.post('<?=site_url("change/common/ajax")?>',{"id":$('#platform_id').val(),"k":k},function(json){
		    if(json.code==1){
                alert(json.msg);
            }else{
		        var  data = json.data;
		        switch (json.type){
		            case 'subject':
                        $("#before_subject").val(data.subject).attr('required',true);
                        $("#after_subject").attr('required',true);
                        $("#subject").show();
		                break;
		            case 'principal_name':
                        $("#before_principal_name").val(data.principal_name).attr('required',true);
                        $("#after_principal_name").attr('required',true);
                        $("#principal_name").show();
		                break;
		            case 'corporation_name':
                        $("#before_corporation_name").val(data.corporation_name).attr('required',true);
                        $("#after_corporation_name").attr('required',true);
                        $("#corporation_name").show();
		                break;
		            case 'department_id':
                        $("#before_department_id").val(data.department_id).attr('required',true);
                        $("#before_department_name").val(data.department_name).attr('required',true);
                        $("#after_department_id").attr('required',true);
                        $("#department_id").show();
		                break;
		            case 'real_end_at':
                        $("#before_real_end_at").val(data.real_end_at).attr('required',true);
                        $("#after_real_end_at").attr('required',true);
                        $("#real_end_at").show();
		                break;
		            case 'total_money':
                        $("#before_total_money").val(data.total_money).attr('required',true);
                        $("#after_total_money").attr('required',true);
                        $("#total_money").show();
		                break;
		            case 'target':
                        $("#before_target").val(data.target).attr('required',true);
                        $("#after_target").attr('required',true);
                        $("#target").show();
		                break;
		            case 'member':
                        $("#before_member").val(data.member).attr('required',true);
                        $("#after_member").attr('required',true);
                        $("#member").show();
		                break;
		            case 'other':
                        $("#before_other").val(data.other).attr('required',true);
                        $("#after_other").attr('required',true);
                        $("#other").show();
		                break;
                }

            }
		},'json');
	}else{
		$("#before_"+k).attr('required',false).val('');
		$("#after_"+k).attr('required',false);
		$("#"+k).hide();
	}
	
}

function todo(json)
{
  $("#after_user_id").val(json.userid);
  $("#after_user_name").val(json.username);
  closeWindow();
}

function getCheckValue()
{
    var chk_value = [];
    $("input[name*='change_type']:checked").each(function(){
        chk_value.push($(this).val());
    });
    return chk_value;
}

$(function(){
	$("input[name*='change_type']").change(function(){
	    var selval = getCheckValue();
		setBefore($(this).val(),$(this).is(':checked') );
	});
	$("#after_department_id").change(function(){
        var selectedOptionLabel = $("option:selected", this).text();
        if(selectedOptionLabel!='请选择'){
            $("#after_department_name").val(selectedOptionLabel);
        }else{
            $("#after_department_name").val('');
        }

	});
});
</script>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>