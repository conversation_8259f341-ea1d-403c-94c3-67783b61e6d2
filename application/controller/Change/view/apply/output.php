<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>项目变更申请表</title>
    <meta name="robots" content="noindex, nofollow">
    <link rel="stylesheet" href="<?=site_path("css/template.css")?>?v=1">
    <?php if($download != 'yes'):?>
        <link rel="icon" type="image/png" sizes="192x192" href="<?=site_path('assets/media/favicons/favicon-192x192.png')?>">
        <link rel="apple-touch-icon" sizes="180x180" href="<?=site_path('assets/media/favicons/apple-touch-icon-180x180.png')?>">
        <link rel="stylesheet" id="css-main" href="<?=site_path('assets/css/compress.min.css')?>?v=<?=config::get('css.main_version')?>">
        <script src="<?=site_path('assets/js/compress.core.min.js')?>"></script>
        <script src="<?=site_path('assets/js/compress.app.min.js')?>"></script>
        <style>
            .project_review{width: 800px;margin: 100px auto;padding: 0;background: none}
            .project_review .main{margin: 80px auto;padding:60px !important;;box-shadow:0 .5rem 2rem #d4dcec;-webkit-transform:translateY(-2px);transform:translateY(-2px);opacity:1}
            .content .block{
                margin: 0;padding: 0
            }
            .content .block .block-content{
                margin-top: 0
            }
            .project_review .pagebreak {
                clear: both;
            }

        </style>
    <?php endif;?>
    <style>
        table td{
            font-size: 18px !important;
        }
    </style>
</head>
<body>
<div id="preloader" style="display: none">
    <div class="boxes">
        <div class="box">
            <div></div><div></div><div></div><div></div>
        </div>
        <div class="box">
            <div></div><div></div><div></div><div></div>
        </div>
        <div class="box">
            <div></div><div></div><div></div><div></div>
        </div>
        <div class="box">
            <div></div><div></div><div></div><div></div>
        </div>
    </div>
</div>
<div id="page-container" class="page-header-dark main-content-boxed">
    <?php if($download != 'yes'):?>
        <header id="page-header" style="position: fixed;top: 0;z-index: 999;">
            <!-- Header Content -->
            <div class="content-header">
                <!-- Left Section -->
                <div class="d-flex align-items-center">
                    <a class="font-w600 text-dual tracking-wide" href="#">项目变更申请表</a>
                </div>
                <!-- END Left Section -->
                <!-- Right Section -->
                <div>
                    <div class="pull-right">
                        <a href="<?=site_url("manager/index")?>" class="btn btn-sm btn-dual ml-2" role="button"><i class="fa fa-home"></i> 返回主页</a>
                        <a href="<?=site_url("change/apply/download/id/".$change->getChangeId())?>" onclick="loadPdf()" class="btn btn-sm btn-dual ml-2" role="button"><i class="fa fa-file-pdf"></i> 下载打印</a>
                    </div>
                </div><!-- END Right Section -->
            </div><!-- END Header Content -->
            <!-- Header Loader -->
            <!-- Please check out the Loaders page under Components category to see examples of showing/hiding it -->
            <div id="page-header-loader" class="overlay-header bg-white-90">
                <div class="content-header">
                    <div class="w-100 text-center">
                        <i class="fa fa-fw fa-2x fa-spinner fa-spin text-primary"></i>
                    </div>
                </div>
            </div><!-- END Header Loader -->
        </header>
    <?php endif;?>
    <!-- Main Container -->
    <main id="main-container">
        <!-- Page Content -->
        <div class="content project_review" style="">
            <div class="block block-rounded">
                <div class="block-content main">
                    <p align="center"><span class="STYLE4"><strong>平台变更申请表</strong></span></p>
                    <?php $platform = $change->platform(true);?>
                    <table width="680" border="0" align="left" cellpadding="5" cellspacing="1" class="abc" style="overflow:wrap">
                        <tr>
                            <td width="115" height="36" align="center" valign="middle">依托单位</td>
                            <td colspan="3" valign="middle"><?=$platform->getCorporationName()?></td>
                        </tr>
                        <tr>
                            <td height="36" align="center" valign="middle">平台名称</td>
                            <td colspan="3" valign="middle"><?=$change->getPlatformName()?></td>
                        </tr>
                        <tr>
                            <td height="36" align="center" valign="middle">平台类别</td>
                            <td colspan="3" valign="middle"><?=$platform->getPlatformType()?></td>
                        </tr>
                        <tr>
                            <td height="36" align="center" valign="middle">联系人</td>
                            <td width="204" valign="middle"><?=$change->getLinkman()?></td>
                            <td align="center" valign="middle">联系人手机</td>
                            <td width="197" valign="middle"><?=$change->getLinkmanMobile()?></td>
                        </tr>
                        <tr>
                            <td height="36" align="center" valign="middle">申请调整事项</td>
                            <td colspan="3" valign="middle"><?=getCheckedStr($change->getMap(),$change->getChangeTypeNameArr())?></td>
                        </tr>
                        <tr>
                            <td height="36" align="center" valign="middle">申请调整内容</td>
                            <td colspan="3" valign="middle"><?=$change->getChangeContent()?></td>
                        </tr>
                        <tr>
                            <td height="36" align="center" valign="middle">申请调整的理由及情况</td>
                            <td colspan="3" valign="middle"><?=$change->getSummaryHtml()?></td>
                        </tr>
                    </table>
                    <table width="680" border="0" align="left" cellpadding="5" cellspacing="1" class="abc" style="overflow:wrap">
                        <tr>
                            <td colspan="2" height="50" align="left" style="border-bottom:none;border-right:none; border-top:none;width: 50%">平台负责人(签字)：</td>
                            <td colspan="2" height="50" align="left" style="border-bottom:none;border-right:none; border-top:none; border-left:none;width: 50%"><?php if(in_array('principal_name',$change->getChangeType())):?>新平台负责人(签字)：<?php endif;?></td>
                        </tr>
                        <tr>
                            <td colspan="3" height="33" align="left" style="border-right:none; border-top:none;width: 50%">&nbsp;</td>
                            <td width="179" height="33" align="right" style="border-top:none; border-left:none;width: 50%">年  月  日</td>
                        </tr>
                        <tr>
                            <td height="50" colspan="2" align="left" style="border-bottom:none;width: 50%">依托单位意见： </td>
                            <td colspan="2" align="left" style="border-bottom:none;width: 50%">主管部门意见：</td>
                        </tr>
                        <tr>
                            <td height="100" colspan="2" align="center" valign="middle" style="border-bottom:none; border-top:none;width: 50%"><?php if(in_array('corporation_id',$change->getChangeType())):?>（新单位公章）&nbsp;&nbsp;&nbsp;&nbsp;<?php endif;?>（单位公章）</td>
                            <td colspan="2" align="center" valign="middle" style="border-bottom:none;  border-top:none;width: 50%">（单位公章）</td>
                        </tr>
                        <tr>
                            <td height="32" colspan="2" align="right" style="border-top:none;width: 50%">年  月  日</td>
                            <td colspan="2" align="right" style="border-top:none;width: 50%">年  月  日</td>
                        </tr>
                    </table>
                    <?php if($download != 'yes'):?>
                        <pagebreak></pagebreak>
                        <p><span class="STYLE4"><strong>相关附件</strong></span></p>
                        <table width="100%" border="0" cellpadding="3" cellspacing="1" class="abc">
                            <thead>
                            <tr>
                                <td width="31%" align="center">文件名</td>
                                <td width="15%" align="center">文件大小</td>
                                <td width="35%" align="center">备注</td>
                                <td width="19%" align="center">上传时间</td>
                            </tr>
                            </thead>
                            <tbody id="paper-content">
                            <?php
                            $attachments = $change->getAttachment();
                            while($attachment = $attachments->getObject()):
                                ?>
                                <tr>
                                    <td><a href="<?=site_path("up_files/".$attachment->getFilePath())?>" target="_blank">
                                            <?=$attachment->getFileName()?>
                                        </a>
                                    </td>
                                    <td><?=$attachment->getFileSize()?></td>
                                    <td><?=$attachment->getFileNote(100)?></td>
                                    <td><?=$attachment->getCreatedAt("Y-m-d")?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                    <?php endif;?>
                </div>
            </div>

        </div><!-- END Page Content -->
    </main><!-- END Main Container -->

</div><!-- END Page Container -->
<?php if($download != 'yes'):?>
    <script>
        jQuery(function () { Dashmix.helpers('sparkline'); });
    </script>
    <script src="<?=site_path('js/layer/layer.js') ?>"></script>
    <script src="<?=site_path('js/func.js') ?>?v=<?=config::get('js.func_version')?>"></script>
    <script src="<?=site_path('js/common.js') ?>?v=<?=config::get('js.common_version')?>"></script>
    <script type="text/javascript">
        $(function(){
            $('pagebreak').before('<div class="pagebreak"></div>');
        });
    </script>
<?php endif;?>
</body>
</html>