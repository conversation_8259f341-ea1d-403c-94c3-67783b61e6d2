<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                变更申请上报
            </h3>
            <div class="block-options">
                <?=Button::back()?>
                <?=Button::setUrl(site_url("change/apply/edit/id/".$change->getChangeId()))->setIcon('edit')->link('编辑')?>
            </div>
        </div>
        <div class="block-content">
            <div class="page-body mt-3 mb-3">
                <div class="alert alert-warning" role="alert">
                    <h4>温馨提示：</h4>
                    <ul>
                        <li>上报前请仔细校对填写的内容及上传的附件内容，一经上报将不可更改。</li>
                        <li>上报后还需要依托单位、主管部门、科技厅审核。</li>
                    </ul>
                </div>
                <?php if(count($msg)):?>
                    <div class="alert alert-danger" role="alert">
                        <h4>系统检查到有如下错误，请检查并修改：</h4>
                        <ul>
                            <?php for($i=0,$n=count($msg);$i<$n;$i++):?>
                                <li>
                                    <?=($i+1)?>
                                    、
                                    <?=$msg[$i]?>
                                </li>
                            <?php endfor;?>
                        </ul>
                    </div>
                <?php endif;?>
                <form name="up" id="up" method="post" action="">
                    <?php if(count($msg)): ?>
                        <button type="button" class="btn btn-danger" disabled="disabled" ><i class="ace-icon glyphicon glyphicon-ban-circle"></i> 信息有误，请返回修改后再上报！</button>
                    <?php else:?>
                    <?=Button::setType('submit')->setEvent("return confirm('一经上报将不可更改，您确定要上报吗？')")->setIcon('submit')->setSize('btn-lg')->button('上报资料')?>
                    <?php endif;?>
                    <input type="hidden" name="id" value="<?=$change->getChangeId()?>">
                    <br>
                </form>
            </div>
        </div>
    </div>
</div>