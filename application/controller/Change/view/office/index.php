<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        变更申请综合查询
                    </h3>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include_once('search_part.php') ?>
                    </div>
                    <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover">
                        <thead>
                        <tr>
                            <th width="30">详</th>
                            <th><?=getColumnStr('平台名称','platform_name')?></th>
                            
                            <th><?=getColumnStr('变更类型','change_type')?></th>
                            <th>依托单位</th>
                            <th><?=getColumnStr('申请日期','created_at')?></th>
                            <th width="150">状态</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php while($change = $pager->getObject()):
                            
                            ?>
                            <tr>
                                <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$change->getChangeId()?>').toggle()">+</label></td>
                                <td><?=link_to("apply/platform/show/id/".$change->getPlatformId(),$change->getPlatformName(),array('target'=>"_blank"))?></td>
                                
                                <td><a href="<?=site_url("change/apply/show/id/".$change->getChangeId())?>" target="_blank"><?=$change->getChangeTypeName()?></a> <?=$change->Mark()?></td>
                                <td><?=$change->getCompanyName()?></td>
                                <td><?=$change->getCreatedAt("Y/m/d")?></td>
                                <td><?=$change->getState()?></td>
                            </tr>
                            <tr id="show_<?=$change->getChangeId()?>" style="display:none;">
                                <td colspan="7">
                                    <p><?=$change->getChangeContent()?></p>
                                    <p><?=$change->getSummaryHtml()?></p>
                                </td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="7" align="right">&nbsp;<?=$pager->navbar(10)?></td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
