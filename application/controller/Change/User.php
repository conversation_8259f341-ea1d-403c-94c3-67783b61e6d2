<?php
namespace App\controller\Change;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class User extends BaseController
{

    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/view/');
    }

    /**
     * 可变更的项目
     * @return void
     */
    function index()
    {
        $this->mygrid("user/index","platform_id = '".input::getInput("session.roleuserid")."' and `statement` = 0");
    }

    /**
     * 填写中的申请
     * @return void
     */
    function wait_list()
    {
        $this->changeGrid("user/wait_list","user_id = '".input::getInput("session.roleuserid")."' and `statement` IN (0,1,3,5,12)");
    }

    /**
     * 已上报的申请
     * @return void
     */
    function submit_list()
    {
        $this->changeGrid("user/submit_list","user_id = '".input::getInput("session.roleuserid")."' and `statement` IN (2,4,9,10)");
    }


    /**
     * 撤回
     */
    function doBack()
    {
        $change = sf::getModel('ProjectChanges')->selectByChangeId(input::getMix('id'));
        if($change->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput("post.content")){
            $change->setStatement(1);
            $change->save();
            sf::getModel("Historys")->addHistory($change->getChangeId(),'变更申请已撤回！<br/>'.input::getInput("post.content"),'project_change');
            $this->refresh();
        }

        //原因表单
        $form = Form::load('change/user/doBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($change->getProjectName())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'撤回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('撤回修改！'))
            ->addItem(Form::hidden('id',$change->getChangeId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
    }

    /**
     * 集中处理项目的搜索信息
     *
     */
    public function mygrid($tpl = 'user/index',$addWhere = '1',$showMax=25,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
        input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";

        //显示页面
        $form_vars = array('search','declare_year','department_id','statement','field');
        $this->view->set("pager",sf::getModel("Platforms")->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }


    public function changeGrid($tpl = 'user/wait_list',$addWhere = '1',$showMax=25,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
        input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";

        //显示页面
        $form_vars = array('search','declare_year','department_id','statement','field');
        $this->view->set("pager",sf::getModel("PlatformChanges")->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }



}