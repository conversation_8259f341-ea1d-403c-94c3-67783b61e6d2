<?php
namespace App\controller\Change;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Company extends BaseController
{

    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/view/');
    }



    function index()
    {
        $this->changeGrid("company/index","company_id = '".input::getInput("session.roleuserid")."'");
    }


    /**
     * 待审核的申请
     * @return void
     */
    function wait_list()
    {
        $this->changeGrid("company/wait_list","company_id = '".input::getInput("session.roleuserid")."' and `statement` = 2");
    }

    /**
     * 已上报的申请
     * @return void
     */
    function submit_list()
    {
        $this->changeGrid("company/submit_list","company_id = '".input::getInput("session.roleuserid")."' and `statement` IN (4,9,10)");
    }


    /**
     * 已退回的申请
     * @return void
     */
    function back_list()
    {
        $this->changeGrid("company/back_list","company_id = '".input::getInput("session.roleuserid")."' and `statement` = 3");
    }


    /**
     * 审核
     */
    function doAccept()
    {
        $change = sf::getModel('PlatformChanges')->selectByChangeId(input::getMix('id'));
        if($change->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::post("content")){
            if(!$change->platform()->isNeedDepartmentCheck()) {
                //无须归口审核
                $change->setStatement(9);
            }else{
                $change->setStatement(4);
            }
            $change->save();
            sf::getModel("Historys")->addHistory($change->getChangeId(),input::post("content"),'platform_change');
            $this->refresh();
        }
        //原因表单
        $form = Form::load('change/company/doAccept')
            ->addItem(Form::Input(['name'=>'subject','label'=>'平台名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($change->getPlatformName())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'审核意见'])->setWidth(12)->setAttribute('class','no-margin')->setValue('审核通过！'))
            ->addItem(Form::hidden('id',$change->getChangeId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
    }

    /**
     * 驳回
     */
    function doBack()
    {
        $change = sf::getModel('PlatformChanges')->selectByChangeId(input::getMix('id'));
        if($change->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput("post.content")){
            $change->setStatement(3);
            $change->save();
            sf::getModel("Historys")->addHistory($change->getChangeId(),'已退回！<br/>'.input::getInput("post.content"),'platform_change');
            $this->refresh();
        }

        //原因表单
        $form = Form::load('change/company/doBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'平台名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($change->getPlatformName())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改！'))
            ->addItem(Form::hidden('id',$change->getChangeId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
    }

    public function changeGrid($tpl = 'user/wait_list',$addWhere = '1',$showMax=25,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
        input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";

        //显示页面
        $form_vars = array('search','declare_year','department_id','statement','field');
        $this->view->set("pager",sf::getModel("PlatformChanges")->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }



}