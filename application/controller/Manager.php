<?php
namespace App\Controller;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
use Sofast\Core\Config;
use App\Models\User;
use App\Models\UserRoles;

class manager extends BaseController
{	
	/**
	 * 数据列表
	 */
	function index()
	{
		$alerts = array();

		switch(input::getInput("session.userlevel"))
		{
			case 2://课题负责人
			$data['user'] = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
			//警告信息
			if(!$data['user']->getVerify()){
				$data['warning'][] = "您的身份证号码不正确！请及时更正！";
			}
//			if($end_num = $data['user']->selectExpires()->getTotal()){
//                $data['warning'][] = '您有'.$end_num.'个项目即将到期，请尽快安排验收！';
//			}
//			if($has_end_num = $data['user']->selectHasExpires()->getTotal()){
//                $data['warning'][] = '您有'.$has_end_num.'个项目已逾期，请尽快完成验收！';
//			}
            //完整性校验
            if($data['user']->validate()){
                $data['warning'][] = '系统检测到您的帐号问题：'.implode(";",$data['user']->validate());
            }
            //菜单控制
            if($data['user']->getIsLock() && $data['user']->getIsLock()!=29){
                $data['warning'][] = '您的账号还没有获得认证，只能进行有限的操作！请 <a href="'.site_url("user/profile/base").'" class="btn btn-alt-primary btn-sm" role="button">点击这里</a> 按照要求修改资料并完成认证！';
            }
            //检查季度监测是否填写
//            $reviewQuarterNum = $data['user']->getNeedUserWriteReviewQuarterCount();
//            if($reviewQuarterNum>0){
//                $data['warning'][] = '您有<b> '.$reviewQuarterNum.' </b>个项目还未填写季度监测。<a href="'.site_url("user/reviewquarter/guide_list").'" >去填写</a>';
//            }
            $tpl = 'manager/user';
            break;
			case 3://申报单位
			$data['user'] = sf::getModel("Corporations")->selectByUserId(input::getInput("session.roleuserid"));
            $tpl = 'manager/unit';
			break;
			case 4://主管部门
			$data['user'] = sf::getModel("Departments")->selectByUserId(input::getInput("session.roleuserid"));
			$tpl = 'manager/gather';
			break;
			case 5://创新平台
			$data['user'] = sf::getModel("Platforms")->selectByPlatformId(input::getInput("session.roleuserid"));
			$tpl = 'manager/platform';
			break;
			case 6://管理员
			$data['user'] = sf::getModel("Managers")->selectByUserId(input::getInput("session.roleuserid"));
            //警告信息
			if(!isMobile($data['user']->getUserMobile())){
				$data['warning'][] = "您的账号还没有绑定手机号码。 <a href=\"".site_url("admin/login/edit")."\" class=\"btn btn-alt-info btn-sm\">点击这里</a> 设置手机号码。";
			}
			$tpl = 'manager/plan';
			break;
			case 10://专家
			$data['user'] = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
            if($data['user']->getAttachments()->getTotal()==0){
                $data['warning'][] = '您还未上传职称证明文件。<a href="'.site_url("expert/profile/attachment/unlock/yes").'" >去上传</a>';
            }
			$tpl = 'manager/expert';
			break;
			default:
			$data['user'] = sf::getModel("Managers")->selectByUserId(input::getInput("session.roleuserid"));
            //警告信息
			if(!isMobile($data['user']->getUserMobile())){
				$data['warning'][] = "您的账号还没有绑定手机号码。 <a href=\"".site_url("admin/login/edit")."\" class=\"btn btn-alt-info btn-sm\">点击这里</a> 设置手机号码。";
			}
			$tpl = 'manager/system';
			break;
		}
        //清除菜单cookie信息
        unset($_SESSION['navigation_id']);

		$data['navbars'] = $navbars;
//		$data['menus'] = $menus;
        //查询未完成的任务
		$data['alerts'] = $alerts;
		view::set($data);
		view::apply("inc_body",$tpl);
		view::display("manager/index");
	}
	
	function main()
	{
		$data['user'] = sf::getModel("managers")->selectByUserId(input::getInput("session.roleuserid"));
		view::set($data);
		view::apply("inc_body","admin/main");
		view::display("page");
	}

	public function role()
	{
		$role_id = input::getInput('mix.role_id');
		$this->activation($role_id);
	}

    /**
     * 切换角色
     */
    public function changeto()
    {
        $roles = sf::getModel("UserRoles",input::mix('role_id'));
        if($roles->isNew()) $this->page_debug("请求切换的角色不存在！");
        if($roles->getUserId() != input::session("userid")) $this->page_debug("你没有权限执行该角色切换！");
        if($role = $roles->getRole()){
            //黑名单不能登录
            if($role->getIsLock() == 4) $this->page_debug("该角色目前禁止登录！");
            //账号信息
            $user = sf::getModel("Users")->selectByUserId(input::session("userid"));
            if($user->isNew()) $this->page_debug("账号不存在或登录已过期！");

            //清除菜单cookie信息
            unset($_SESSION['navigation_id']);

            //注册session信息
            $_SESSION['userlevel'] 	=  $roles->getRoleId();
            $_SESSION['groupname'] 	=  $roles->getRoleName();
            if(in_array($roles->getRoleId(),array('6'))){
                if($role->getOfficeId() > 0){
                    $cat = sf::getModel("Categorys",$role->getOfficeId(),'office');
                    if(!$cat->isNew()) $_SESSION['groupname'] .= "(".$cat->getSubject().")";
                }

            }

            if($roles->getRoleId()==2){
                $_SESSION['companyid'] =  $role->getCompanyId();
            }
            $_SESSION['roleuserid'] = $roles->getUserRoleId();

            if($roles->getRoleId()==10){
                $_SESSION['nickname'] =  $role->getUserName();
                if($role->getIsLock()==6){
                    //未激活
                    $this->jump(site_url("manager/expert_profile"));
                }
            }


            $_SESSION['fullname'] =  method_exists($role,'getFullName') ? $role->getFullName() : $user->getUserUsername();
            $_SESSION['office_id'] 	=  method_exists($role,'getOfficeId') ? $role->getOfficeId() : '-1';
            $_SESSION['auth'] 	=  method_exists($role,'getAuths') ? $role->getAuths() : '-1';

            //记录登录日志
            if(!$_SESSION['loginfrom'] || $_SESSION['loginfrom']!='admin'){
                sf::getModel("Logs")->write('账号以'.$_SESSION['groupname'].'身份成功登录系统!');
            }

            $this->jump(site_url('manager/index'));
        }else $this->page_debug("切换的角色已不存在了！");
    }
	
	function choose()
	{	
		$roles = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"))->selectRoles();
		view::set('roles',$roles);
		view::display("manager/choose");
	}

    function activation($role_id)
	{
		if(!$role_id) return false;
		if(changeRole($role_id)){
			$this->jump(site_url('manager/index'));
		}else{
			if($role_id == 2){
				$this->jump(site_url("register/researcher"));
			}else if($role_id == 3){
				$this->jump(site_url("register/company"));
			}else if($role_id == 10){
				$this->jump(site_url("register/expert"));	
			}
			$this->page_debug("您没有权限切换到该权限组！",getFromUrl());
		}
	}

    /**
     * 完善主管部门账号信息
     */
    public function profile()
    {
        $user = sf::getModel("Users")->selectByUserId(input::session("userid"));
        $department = sf::getModel('Departments')->selectByUserId(input::session("roleuserid"));
//        if(!$department->needProfile()){
                //不需要完善资料，直接跳转
//            $this->page_debug('您已经激活了账号，请直接完善您的资料即可！',site_url("manager/choose"));
//        }
        if(input::post()){
            if(input::getInput("session.SafetyCode") != input::getInput("post.safe_code"))//安全性判断
                $this->error("安全校验码不正确",getFromUrl());

            $personname = trim(input::getInput("post.personname"));
            $userName = trim(input::getInput("post.user_name"));
            $userPassword = trim(input::getInput("post.user_password"));
            $newPassword = trim(input::getInput("post.new_password"));
            $phone = trim(input::getInput("post.user_phone"));
            $mobile = trim(input::getInput("post.user_mobile"));
            $idcard = trim(input::getInput("post.user_idcard"));
            $email = trim(input::getInput("post.user_email"));
            $linkmanDepartment = trim(input::getInput("post.linkman_department"));


            if(!$personname){
                $this->error("管理员姓名必须填写",getFromUrl());
            }
            if(!$userName){
                $this->error("登录账号必须填写",getFromUrl());
            }
            if(!$userPassword){
                $this->error("初始密码必须填写",getFromUrl());
            }
            if(strlen($newPassword)<8){
                $this->error("新密码长度必须大于8位",getFromUrl());
            }
            if($user->hasByUserName($userName)){
                $this->error("登录账号已经被占用，请更换一个登录账号试试",getFromUrl());
            }
            if(!$user->check($userPassword)){
                $this->error("初始密码错误，请重新输入",getFromUrl());
            }

            if($user->hasByUserName($mobile))
                $this->error("手机号已被占用",getFromUrl());

            if(!isEmail($email))
                $this->error("填写内容不是有效的电子信箱",getFromUrl());

            if(!isIdcard($idcard))
                $this->error("填写内容不是有效的身份证号码",getFromUrl());
            if($user->hasByCardId($idcard))
                $this->error("身份证号码已经注册",getFromUrl());

            if(!isMobile($mobile))
                $this->error("填写内容不是有效的手机号码",getFromUrl());
            if($user->hasByMobile($mobile))
                $this->error("填写的手机号码已经注册",getFromUrl());

            $user->setUserName($userName);
            $user->setUserPassword($newPassword);
            $user->setUserUsername($personname);
            $user->setUserGroupId(4);
            $user->setUserIdcard($idcard);
            $user->setUserMobile($mobile);
            $user->setUserPhone($phone);
            $user->setUserEmail($email);
            $user->save();
            $department->setLinkman($personname);
            $department->setLinkmanPhone($phone);
            $department->setLinkmanMobile($mobile);
            $department->setLinkmanEmail($email);
            $department->setEmail($email);
            $department->setLinkmanDepartment($linkmanDepartment);
            $department->save();
            sf::getPlugin("authentic")->logout();
            $this->success('激活成功！请使用新的账号密码重新登录！',site_url('login/index'));
        }
        $roles = $user->selectRoles();
        view::set("user",$user);
        view::set('roles',$roles);
        view::set('department',$department);
        view::display("manager/profile");
    }

    /**
     * 完善专家账号信息
     */
    public function expert_profile()
    {
        $user = sf::getModel("Users")->selectByUserId(input::session("userid"));
        $expert = sf::getModel('Experts')->selectByUserId(input::session('roleuserid'));
        if(input::post()){
            if(input::getInput("session.SafetyCode") != input::getInput("post.safe_code"))//安全性判断
                $this->error("安全校验码不正确",getFromUrl());

            $username = trim(input::getInput("post.user_name"));
            $phone = trim(input::getInput("post.user_phone"));
            $mobile = trim(input::getInput("post.user_mobile"));
            $idcard = trim(input::getInput("post.user_idcard"));
            $email = trim(input::getInput("post.user_email"));
            $workUnit = trim(input::getInput("post.work_unit"));
            $workDepartment = trim(input::getInput("post.work_department"));
            $subject = trim(input::getInput("post.subject"));
            $areaCode = trim(input::getInput("post.area_code"));
            $titleType = trim(input::getInput("post.title_type"));
            $title = trim(input::getInput("post.title"));
            $userDuty = trim(input::getInput("post.user_duty"));
            $education = trim(input::getInput("post.education"));
            $userDegree = trim(input::getInput("post.user_degree"));
            $graduateSchool = trim(input::getInput("post.graduate_school"));
            $major = trim(input::getInput("post.major"));
            $bankCardNumber = trim(input::getInput("post.bank_card_number"));
            $bankName = trim(input::getInput("post.bank_name"));

            if($user->hasByUserName($mobile))
                $this->error("手机号已被占用",getFromUrl());

            if(!isEmail($email))
                $this->error("填写内容不是有效的电子信箱",getFromUrl());

            if(!isIdcard($idcard))
                $this->error("填写内容不是有效的身份证号码",getFromUrl());
            if($user->hasByCardId($idcard))
                $this->error("身份证号码已经注册",getFromUrl());

            if(!isMobile($mobile))
                $this->error("填写内容不是有效的手机号码",getFromUrl());
            if($user->hasByMobile($mobile))
                $this->error("填写的手机号码已经注册",getFromUrl());
            if(!$bankCardNumber){
                $this->error('请填写银行卡卡号');
            }
            if(!$bankName){
                $this->error('请填写开户银行名称');
            }

            $user->setUserIdcard($idcard);
            $user->setUserMobile($mobile);
            $user->setUserPhone($phone);
            $user->setUserEmail($email);
            $user->save();
            $expert->setUserName($username);
            $expert->setCardId($idcard);
            $expert->setUserBirthday(sf::getLib('idcard')->getBirthday($idcard));
            $expert->setUserSex(sf::getLib('idcard')->getSex($idcard));
            $expert->setAge(sf::getLib('idcard')->getAge($idcard));
            $expert->setUnitPhone($phone);
            $expert->setUserMobile($mobile);
            $expert->setUserEmail($email);
            $company = sf::getModel('Corporations')->selectBySubject(getTrueCorporationName($workUnit));
            $expert->setWorkUnit($workUnit);
            if(!$company->isNew()) $expert->setCorporationId($company->getUserId());
            $expert->setWorkDepartment($workDepartment);
            $expert->setEducation($education);
            $expert->setUserDegree($userDegree);
            $expert->setGraduateSchool($graduateSchool);
            $expert->setSubjectNames($subject);
            $expert->setTitleType(sf::getModel('CategoryWorks',$titleType)->getSubject());
            $expert->setTitle($title);
            $expert->setMajor($major);
            $expert->setUserDuty($userDuty);
            $expert->setBankCardNumber($bankCardNumber);
            $expert->setBankName($bankName);
            if($areaCode){
                $expert->setArea(sf::getModel("Region")->selectByCode($areaCode)->getFullRegionName());
                $expert->setAreaCode($areaCode);
                if(substr($expert->getAreaCode(),0,2)!='51'){
                    $expert->setTag('省外专家');
                    $expert->setIsOut(1);
                }else{
                    $expert->setIsOut(0);
                    $expert->setTag('');
                }
            }
            $expert->setIsLock(0);
            $expert->save();

            $_SESSION['roleuserid'] = $expert->getUserId();
            $_SESSION['fullname'] =  $expert->getUserName();
            $_SESSION['office_id'] 	=  '-1';
            $_SESSION['auth'] 	=  '-1';

            //记录登录日志
            sf::getModel("Logs")->write('账号激活专家身份并成功登录系统!');
            $this->success('激活成功！',site_url('manager/index'));
        }
        $roles = $user->selectRoles();
        view::set("user",$user);
        view::set('roles',$roles);
        view::set('expert',$expert);
        view::display("manager/expert_profile");
    }
}
?>