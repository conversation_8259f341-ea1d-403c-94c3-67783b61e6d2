<?php
namespace App\Controller\Unit;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\config;
use Sofast\Core\Lang;
class project extends BaseController
{
	
	/**
	 * 综合搜索
	 */
	public function index()
	{
        $addWhere = "`corporation_id` = '".input::getInput("session.roleuserid")."'";
		$this->grid('unit/project/index',$addWhere,20);
	}

	/**
	 * 综合搜索
	 */
	public function nation_list()
	{
        $addWhere = "`corporation_id` = '".input::getInput("session.roleuserid")."' and cat_id = '230'";
		$this->grid('unit/project/nation_list',$addWhere,20);
	}

    /**
     * 还未关联项目负责人的项目
     */
    public function no_user_list()
    {
        $addWhere = "`corporation_id` = '".input::getInput("session.roleuserid")."' and user_id = '0' and statement in (29,30)";
        $this->grid('unit/project/no_user_list',$addWhere,20);
    }

	/**
	 * 申请综合搜索
	 */
	public function index_out()
	{
		$addWhere = "`corporation_id` = '".input::getInput("session.roleuserid")."' and state_for_out > 0";
		$this->grid('unit/project/index_out',$addWhere,20);
	}
	/**
	 * 等待审核的项目列表
	 */
	public function wait_list()
	{
        $addWhere = "statement IN (2,21) and `corporation_id` = '".input::getInput("session.roleuserid")."'";
		$this->grid('unit/project/wait_list',$addWhere,20);
	}
	
	/**
	 * 上报的项目列表
	 */
	public function submit_list()
	{
        $addWhere = "`corporation_id` = '".input::session("roleuserid")."'  AND statement IN (5,9,10,26)";
		$this->grid('unit/project/submit_list',$addWhere,20);
	}
	/**
	 * 上报的申请列表
	 */
	public function submit_out()
	{
        $addWhere = "`corporation_id` = '".input::session("roleuserid")."'  AND state_for_out IN (9,10)";
		$this->grid('unit/project/submit_out',$addWhere,20);
	}
	/**
	 * 已经立项的项目列表
	 */
	public function radicate_list()
	{
		$this->grid('unit/project/radicate_list',"corporation_id = '".input::getInput("session.roleuserid")."' and statement >= '".config::get("HAS_RADICATE")."' ",20);
	}

	/**
	 * 已退回的项目列表
	 */
	public function rejected_list()
	{
        $addWhere = "statement IN (3,6,12) and `corporation_id` = '".input::getInput("session.roleuserid")."'";
		$this->grid('unit/project/rejected_list',$addWhere,20);
	}
	/**
	 * 已退回的申请列表
	 */
	public function rejected_out()
	{
		$addWhere = " state_for_out = 3 and corporation_id = '".input::getInput("session.roleuserid")."' ";
		$this->grid('unit/project/rejected_out',$addWhere,20);
	}	

	/**
	 * 已经结题的项目列表
	 */
	public function complete_list()
	{
		$this->grid('unit/project/index',"corporation_id = '".input::getInput("session.roleuserid")."' and statement >= '".config::get("HAS_COMPLETE")."' ",20);
	}
	/**
	 * 等待审核的申请列表
	 */
	public function wait_out()
	{
        $addWhere = "`corporation_id` = '".input::session("roleuserid")."'  AND state_for_out = 2";
		$this->grid('unit/project/wait_out',$addWhere,20);
	}
	/**
	 * 审核项目
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		for($i=0,$n=count($ids);$i<$n;$i++){
			$project = sf::getModel("Projects")->selectByProjectId($ids[$i]);
			
			//检查单位帐号是否可以上报
			// if($msg = $project->getCorporation(true)->validate()) 
			// 	$this->page_debug(sprintf(lang::get("You can not operate, because the:%s!"),'<br />'.implode('<br />',$msg)),getFromUrl());
			if($project->getCorporation()->getIsLock())  $this->page_debug("单位账号还没有获得实名认证！",getFromUrl());
			//检查项目是否可以上报
			if($msg = $project->validate()) 
				$this->page_debug(sprintf(lang::get("You can not operate, because the:%s!"),'<br />'.implode('<br />',$msg)),getFromUrl());

			if($project->getWaitForCompany()<=2){
                //单位已审核
                $project->setStatement(7);
            }else{
			    //待二级单位审核
                $project->setWaitForCompany(2);
            }
			if($project->save())
				$msg =  '单位审核成功！';
			else
				$msg =  lang::get("Error has happened!");
			sf::getModel("Historys")->addHistory($project->getProjectId(),$msg);
		}		
		$this->success($msg,site_url('unit/project/wait_list'));
	}

    /**
     * 审核项目
     */
    function doAcceptOnlyOne()
    {
        $project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->isNew()) $this->page_debug('没有找到该项目！');
        if(!$project->getAcceptId()){
            $accept_id = sf::getModel('Types',$project->getTypeId())->getSerialNumber($project->getDeclareYear());
            $project->setAcceptId($accept_id);
        }
        //分管部门
        $office_id = $project->getGuide(true)->getOfficeId();
        if($office_id){
            $project->setOfficeId($office_id);
            $project->setOfficeSubject(sf::getModel("categorys",$office_id,'office')->getSubject());
        }
        $project->setStatement(5);
        $project->setStateForAssess(0);
        $project->save();
        $msg = lang::get("Has been accepted!");
        sf::getModel("historys")->addHistory($project->getProjectId(),$msg);
        $this->success('审核通过！申报编号为：'.$project->getAcceptId(),getFromUrl());
    }

	/**
	 * 审核项目
	 */
	function doSubmitOnlyOne()
	{
		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
		if($project->isNew()){
		    $this->error('找不到该项目！');
        }
        //申报时间验证
        if(!$project->time_validate()) {
            $this->error('申报通道已关闭', 'javascript:parent.closeWindow();');
        }

		if(input::getInput("post.content")){
            if($project->getStatement()==21){
                $project->setStatement(26);     //待科技厅审核
            }else{
                $project->setStatement(config::get("GATHER_WAIT"));
                if(!$project->isNeedDepartmentCheck()){
                    //无须归口审核
                    $project->setIsReport(1);
                    $project->setStatement(config::get("PLAN_WAIT"));
                }
            }
			$project->save();
			sf::getModel("Historys")->addHistory($project->getProjectId(),'申报单位审核通过！<br/>'.input::getInput("post.content"));
			exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/审核通过！';</script>");
		}
		view::set("project",$project);
		view::set("msg","同意申报！");
		view::apply("inc_body","unit/project/note_submit");
		view::display("page_blank");
	}

	/**
	 * 返至待审核
	 */
	function doWait()
	{

		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("get.id"));
		if($project->getCorporationId() != input::getInput("session.roleuserid") || $project->getStatement() > 6) 
			$this->page_debug(lang::get("You are't the owner!"),getFromUrl());	
		$project->setStatement(config::get("UNIT_WAIT"));
		if($project->save()) $msg =  "此项目已成功返回至“待审核”状态！";
		else $msg =  lang::get("Error has happened!");
		sf::getModel("Historys")->addHistory($project->getProjectId(),$msg);
		$this->page_debug($msg,getFromUrl());					
	}	
	/**
	 * 驳回项目
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		if(!$ids[0])
			$this->page_debug('请至少选择一个项目！',getFromUrl());	
		for($i=0,$n=count($ids);$i<$n;$i++){
			$project = sf::getModel("Projects")->selectByProjectId($ids[$i]);
			if($project->getStatement()>9){
			    $this->error('没有权限退回');
            }
            $project->setStatement(config::get("UNIT_BACK"));
			if($project->save()) $msg =  lang::get("Has been rejected!");
			else $msg =  lang::get("Error has happened!");
			sf::getModel("Historys")->addHistory($project->getProjectId(),$msg);
		}
		$this->page_debug($msg,getFromUrl());	
	}
	/**
	 * 驳回项目
	 */
	function doRejectedOnlyOne()
	{
		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
		if(input::getInput("post.content")){
			if($project->getStatement() == '4'){
				$project->setStatement(7);//上级单位退回
			}else{
				$project->setStatement(3);//申报单位退回
			}			
			$project->save();
			sf::getModel("Historys")->addHistory($project->getProjectId(),'单位退回！<br/>'.input::getInput("post.content"));
//            pushMsgToResearcher('你的项目申请书已被退回','user/project/deformity_list',$project->getUserId());
			exit("<script>parent.location.reload();</script>");
		}
		view::set("project",$project);
		view::apply("inc_body","unit/project/note");
		view::display("page_blank");
	}

    /**
     * 更换项目负责人
     */
    function changeUserName()
    {
        $project = sf::getModel("projects")->selectByProjectId(input::getMix('id'));
        if($project->isNew()) exit('项目不存在！');
        if(!input::getInput("post.userid")) exit('替换用户不存在！');
        if($project->getCorporationId()!=input::session('roleuserid')){
            exit('没有权限！');
        }
        $_username = $project->getUserName();
        $project->setUserName(input::getInput("post.username"));
        $project->setUserId(input::getInput("post.userid"));
        $project->setUpdatedAt(date("Y-m-d H:i:s"));
        $project->save();
        //更改中期报告的负责人
        $stage = $project->stage();
        if(!$stage->isNew()) {
            $stage->setUserName(input::getInput("post.username"));
            $stage->setUserId(input::getInput("post.userid"));
            $stage->save();
        }
        //更改验收报告的负责人
        $complete = $project->complete();
        if(!$complete->isNew()) {
            $complete->setUserName(input::getInput("post.username"));
            $complete->setUserId(input::getInput("post.userid"));
            $complete->save();
        }
        sf::getModel("historys")->addHistory($project->getProjectId(),sprintf('项目负责人已关联为：%s',input::getInput("post.username")));
        exit('项目负责人关联成功！');
    }

	/**
	 * 发回重填
	 */
	function doBack()
	{
		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
		if(input::getInput("post.content")){
            if($project->getStatement()==21) $project->setStatement(20);
            else $project->setStatement(3);
            $project->save();
			sf::getModel("Historys")->addHistory($project->getProjectId(),lang::get("Has been rejected!")."<br />".input::getInput("post.content"));
			exit("<script>parent.location.reload();</script>");
		}
		view::set("project",$project);
		view::apply("inc_body","unit/project/back");
		view::display("page_blank");
	}

	public function reBack()
    {
        $project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->isNew()) $this->error('没有找到该项目',getFromUrl());
        if(!in_array($project->getStatement(),[5,9])) $this->error('当前状态不允许撤回',getFromUrl());
        if(input::getInput("post.content")){
            $project->setStatement(2);
            $project->save();
            sf::getModel("Historys")->addHistory($project->getProjectId(),'单位撤回！<br/>'.input::getInput("post.content"));
            $this->success('撤回成功！','javascript:top.location.reload();');
        }

        //原因表单
        $form = Form::load('unit/project/reBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($project->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'撤回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改'))
            ->addItem(Form::hidden('id',$project->getProjectId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

}