<?php
namespace App\controller\Target;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Core\config;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class Qyzx extends BaseController
{
    private $view = NULL;
    private $modelName = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/View/');
        $this->modelName = 'TargetsQyzx';
    }
    
    public function index()
    {
        $form_vars = array('field','search','subject');
        if(input::getMix('level')){
            $addwhere = " `level`  = '".input::getMix('level')."'";
        }else{
            $addwhere = "level = 1 ";
        }
        if(input::getMix('search') && input::getMix('field')){
            $addwhere .= " and `".input::getMix('field')."` like '%".input::getMix('search')."%'";
        }
        $this->view->set("target",sf::getModel($this->modelName));
        $this->view->set("pagers",sf::getModel($this->modelName)->getPager($addwhere,"order by code asc,id asc",20,'','',$form_vars));
        $this->view->apply("inc_body",'qyzx/index');
        $this->view->display('page');
    }

    public function child_index()
    {
        $target = sf::getModel($this->modelName,input::getMix('id'));
        if($target->isNew()) $this->error('没有找到父级指标');
        $form_vars = array('field','search','subject');
        $this->view->set("target",$target);
        $this->view->set("pagers",sf::getModel($this->modelName)->getPager("level = '".($target->getLevel()+1)."' and pid = '".$target->getId()."'","order by code asc,id asc",20,'','',$form_vars));
        $this->view->apply("inc_body",'qyzx/index');
        $this->view->display('page');
    }

    public function edit()
    {
        $target = sf::getModel($this->modelName,input::getMix('id'));
        if(input::getMix('pid')) $target->setPid(input::getMix('pid'));
        if($target->isNew()){
            $newCode = $target->getNewCode();
            $target->setCode($newCode);
        }
        if(input::post()){
            $target->setPid((int)input::post('pid'));
            $target->setTopId(0);
            $target->setLevel(1);
            if(input::post('pid')){
                $pIndex = $target->getParent();
                $target->setLevel($pIndex->getLevel()+1);
                $target->setTopId($pIndex->getTopId());
            }
            $target->setSubject(input::post('subject'));
            $target->setCode(input::post('code'));
            $target->setIndexCode(input::post('index_code'));
//            $target->setSubjectCode(input::post('subject_code'));
            $target->setCycle(input::post('cycle'));
            $target->setType(input::post('type'));
            $target->setDataType(input::post('data_type'));
            $target->setRequired((int)input::post('required'));
            $target->setSource(input::post('source'));
            $target->setNote(input::post('note'));
            $target->setOptions(input::post('options'));
            $target->save();
            $url = site_url('target/qyzx/index');
            if($target->getPid()){
                $url = site_url('target/qyzx/child_index/id/'.$target->getPid());
            }
            $this->success('保存成功！',$url);
        }
        $this->view->set("target",$target);
        $this->view->apply("inc_body",'qyzx/edit');
        $this->view->display('page');
    }

}