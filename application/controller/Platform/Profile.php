<?php
namespace App\controller\Platform;
use App\Controller\BaseController;
use App\Facades\Button;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
use Sofast\Core\Config;
use App\Facades\PDF;

class Profile extends BaseController
{
    private $tabs = array();
    private $platform = null;

    public function load()
    {
        $id = input::session('userlevel') == 5 ? input::session("roleuserid") : input::getMix('userid');
        $this->platform = sf::getModel("Platforms")->selectByPlatformId($id);
        $this->tabs = [
            ['method'=>'base','text'=>'基本资料','url'=>site_url('platform/profile/base')],
        ];
        if(in_array($this->platform->getCatId(),[271])){
            $this->tabs[] = ['method'=>'principal','text'=>'实验室主任','url'=>site_url('platform/profile/principal')];
            $this->tabs[] = ['method'=>'staff','text'=>'固定人员','url'=>site_url('platform/profile/staff')];
            $this->tabs[] = ['method'=>'committee','text'=>'学术委员会','url'=>site_url('platform/profile/committee')];
        }
        $htmlStr = Button::setName('查看记录')->setUrl(site_url("admin/history/index/type/platform/id/".$this->platform->getPlatformId()))->setWidth('550px')->setHeight('80%')->setIcon('time')->window();

        view::set('method', input::getInput("mix.method"));
        view::set('tabs', $this->tabs);
        view::set('htmlStr', $htmlStr);
    }

	function index()
	{
		$this->base();
	}
	
	function edit()
	{
		if($this->platform->isNew())
			$this->page_debug('请先登录！',site_url("login/index"));
	}

	/**
	 * 保存平台基本资料
	 */
	public function base()
	{
		if($this->platform->isNew() && input::get('cat_id')) {
            $this->platform->setCatId(input::get('cat_id'));
        }
		if (Input::getInput("post"))
		{
            //数据验证
            if(input::post("industry_other")=='其他' && empty(input::post("industry_other"))) $this->page_debug("其他产业领域请注明！",getFromUrl());
            if(substr(input::post("area_code"),-2) =='00') $this->page_debug("所在地区请选到第三级!",getFromUrl());
            $area = sf::getModel('Region')->selectByCode(input::post("area_code"));
            if($area->isNew()) $this->page_debug("请选择正确的所在地区!",getFromUrl());
            if(input::post('cat_id')){
                $this->platform->setCatId(input::post('cat_id'));
                $this->platform->setPlatformType(getCatSubject(input::post('cat_id')));
            }
            if($this->platform->getCatId()==271 && count(input::getInput('post.research_direction'))>5){
                $this->page_debug('研究方向最多不能超过5个！');
            }
            $_researchDirections = $this->platform->getResearchDirectionArray();
            $researchDirections = input::getInput('post.research_direction');
            foreach ($_researchDirections as $k=>$_researchDirection){
                if($_researchDirection!=$researchDirections[$k] && $this->platform->getStaffCount($_researchDirection)>0){
                    //替换人员对应的研究方向
                    $msg[] = '研究方向由“'.$_researchDirection.'”调整为“'.$researchDirections[$k].'”';
                    $this->platform->doReplaceStaffResearch($_researchDirection,$researchDirections[$k]);
                }
            }
            input::post("subject") && $this->platform->setSubject(input::post("subject"));
            $this->platform->setAreaCode(input::post("area_code"));
            $this->platform->setArea($area->getFullRegionName());
            $industryOther = '';
            if(input::post("industry")=='其他') $industryOther = input::post("industry_other");
            $this->platform->setIndustry(input::post("industry"));
            $this->platform->setIndustryOther($industryOther);
            $this->platform->setCorporationName(input::post("corporation_name"));
            $this->platform->setCorporationProperty(input::post("corporation_property"));
            $this->platform->setCooperatation(input::post("cooperatation_name"),input::post("cooperatation_property"));
            $this->platform->setPrincipalName(input::post("principal_name"));
            $this->platform->setPrincipalMobile(input::post("principal_mobile"));
            $this->platform->setLinkmanName(input::post("linkman_name"));
            $this->platform->setLinkmanMobile(input::post("linkman_mobile"));
            $this->platform->setResearchDirection(input::getInput('post.research_direction'));
            $this->platform->setAddress(input::post("address"));
			$this->platform->setUpdatedAt(date('Y-m-d H:i:s'));
			if($this->platform->save()){
                addHistory($this->platform->getPlatformId(),"修改平台资料",'platform');
            }
			$this->success("保存成功！",getFromUrl());
		}

		view::set('platform', $this->platform);
		view::apply('inc_body', 'platform/profile/base');
		view::display('page');
	}

	public function principal()
	{
		if($this->platform->isNew()) $this->page_debug('请重新登录后修改！',getFromUrl());
        $leader = $this->platform->getLeader();
		if ($postdata = Input::getInput("post"))
		{
            if($postdata['leader']['idcard_type']=='身份证' && !isIdcard($postdata['leader']['idcard'])) $this->page_debug('实验室主任身份证不正确！');

            if(!$this->platform->isNew()){
                $this->platform->getPrincipalName()!=$postdata['leader']['user_name'] && $msg[] = '实验室主任由“'.$this->platform->getPrincipalName().'”调整为“'.$postdata['leader']['user_name'].'”';
            }

            $this->platform->setPrincipalName($postdata['leader']['user_name']);
            $this->platform->save();

            
            if(!$leader->isNew()){
                $leader->getUserTitle()!=$postdata['leader']['user_title'] && $msg[] = '实验室主任职称由“'.$leader->getUserTitle().'”调整为“'.$postdata['leader']['user_title'].'”';
                $leader->getEducation()!=$postdata['leader']['education'] && $msg[] = '实验室主任学历由“'.$leader->getEducation().'”调整为“'.$postdata['leader']['education'].'”';
                $leader->getDegree()!=$postdata['leader']['degree'] && $msg[] = '实验室主任学位由“'.$leader->getDegree().'”调整为“'.$postdata['leader']['degree'].'”';
                $leader->getHonor()!=$postdata['leader']['honor'] && $msg[] = '实验室主任荣誉称号由“'.$leader->getHonor().'”调整为“'.$postdata['leader']['honor'].'”';
                $leader->getMajor()!=$postdata['leader']['major'] && $msg[] = '实验室主任专业由“'.$leader->getMajor().'”调整为“'.$postdata['leader']['major'].'”';
                $leader->getCorporationName()!=$postdata['leader']['corporation_name'] && $msg[] = '实验室主任工作单位由“'.$leader->getCorporationName().'”调整为“'.$postdata['leader']['corporation_name'].'”';
                $leader->getResearchArea()!=$postdata['leader']['research_area'] && $msg[] = '实验室主任研究领域由“'.$leader->getResearchArea().'”调整为“'.$postdata['leader']['research_area'].'”';
            }
            $leader->setUserName($postdata['leader']['user_name']);
            $leader->setIdcardType($postdata['leader']['idcard_type']);
            $leader->setIdcard($postdata['leader']['idcard']);
            $leader->setUserSex($postdata['leader']['user_sex']);
            $leader->setUserBirthday($postdata['leader']['user_birthday']);
            $leader->setUserDuty($postdata['leader']['user_duty']);
            $leader->setTitleRank($postdata['leader']['title_rank']);
            $leader->setUserTitle($postdata['leader']['user_title']);
            $leader->setEducation($postdata['leader']['education']);
            $leader->setDegree($postdata['leader']['degree']);
            $leader->setHonor($postdata['leader']['honor']);
            $leader->setMajor($postdata['leader']['major']);
            $leader->setCorporationName($postdata['leader']['corporation_name']);
            $leader->setResearchArea($postdata['leader']['research_area']);
            $leader->setNote($postdata['leader']['note']);
            $leader->save();
			$this->success("保存成功！",getFromUrl());
		}

		view::set('leader', $leader);
		view::set('platform', $this->platform);
		view::apply('inc_body', 'platform/profile/principal');
		view::display('page');
	}

	public function staff()
	{
		if($this->platform->isNew()) $this->page_debug('请重新登录后修改！',getFromUrl());
		view::set('platform', $this->platform);
		view::apply('inc_body', 'platform/profile/staff');
		view::display('page');
	}

    public function staff_edit()
    {
        $staff = $this->platform->getStaffById(input::mix('pid'));
        if(!$staff->isNew() && $staff->getPlatformId()!=$this->platform->getPlatformId()) $this->page_debug('没有权限编辑');
//        if($staff->isNew() && $this->platform->getStaffCount()>=20){
//            $this->page_debug('固定人员人数不能超过20人');
//        }
        $directions = $this->platform->getResearchDirectionArray();
        $researchDirection = $directions[input::get('direction_key')];

        if($staff->isNew() && $this->platform->getStaffCount($researchDirection)>0) $staff->setType('成员');
        $staff->setPlatformId($this->platform->getPlatformId());
        if($researchDirection) $staff->setResearchDirection($researchDirection);
        if(input::post()){
            if(input::post('idcard_type')=='身份证' && !isIdcard(input::post('idcard'))) $this->page_debug('身份证不正确！');
            if(!input::post('research_direction')){
                $this->page_debug('请选择研究方向');
            }
            if($staff->isNew()){
                input::post('user_name') &&  $msg[] = '新增实验室固定人员：“'.input::post('user_name').'”';
            }else{
                $staff->getUserName()!=input::post('user_name') && $msg[] = '实验室主任固定人员由“'.$staff->getUserName().'”调整为“'.input::post('user_name').'”';
            }
            $staff->setType(input::post('type'));
            $staff->setResearchDirection(input::post('research_direction'));
            $staff->setUserName(input::post('user_name'));
            $staff->setIdcardType(input::post('idcard_type'));
            $staff->setIdcard(input::post('idcard'));
            $staff->setUserSex(input::post('user_sex'));
            $staff->setUserBirthday(input::post('user_birthday'));
            $staff->setUserDuty(input::post('user_duty'));
            $staff->setTitleRank(input::post('title_rank'));
            $staff->setUserTitle(input::post('user_title'));
            $staff->setEducation(input::post('education'));
            $staff->setDegree(input::post('degree'));
            $staff->setHonor(input::post('honor'));
            $staff->setMajor(input::post('major'));
            $staff->setCorporationName(input::post('corporation_name'));
            $staff->setResearchArea(input::post('research_area'));
            $staff->setNote(input::post('note'));
            $staff->save();
            if($msg){
                sf::getModel("historys")->addHistory($this->platform->getPlatformId(),implode("|",$msg),'base',1);
            }
            $this->refresh(lang::get('Has been saved!'));
        }
        view::set('platform',$this->platform);
        view::set('staff',$staff);
        view::apply("inc_body",'platform/profile/staff_edit');
        view::display('page_blank');
    }

    public function staff_delete()
    {
        $staff = $this->platform->getStaffById(input::mix('pid'));
        if($staff->getPlatformId()!=$this->platform->getPlatformId()) $this->page_debug('没有权限删除');
        $staff->getUserName() && sf::getModel("historys")->addHistory($this->platform->getPlatformId(),'删除实验室主任固定人员：'.$staff->getUserName(),'base',1);
        $staff->delete();
        $this->page_debug(lang::get('Has been deleted!'),getFromUrl());
    }

    public function committee()
    {
        view::set('platform',$this->platform);
        view::apply("inc_body",'platform/profile/committee');
        view::display('page');
    }

    public function committee_edit()
    {
        $committee= $this->platform->getCommitteeById(input::mix('pid'));
        if(!$committee->isNew() && $committee->getPlatformId()!=$this->platform->getPlatformId()) $this->page_debug('没有权限编辑');
        if($committee->isNew() && $this->platform->getCommitteeCount()>=15){
            $this->page_debug('学术委员会人数不能超过15人');
        }

        if($committee->isNew() && $this->platform->getCommitteeCount()>0) $committee->setType('成员');
        $committee->setPlatformId($this->platform->getPlatformId());
        if(input::post()){
            if(input::post('idcard_type')=='身份证' && !isIdcard(input::post('idcard'))) $this->page_debug('身份证不正确！');
            $committee->setType(input::post('type'));
            //判断主任人数
            if($committee->isNew() && $committee->getType()=='主任'){
                if($this->platform->getCommitteeLeaderCount()>=1){
                    $this->page_debug('主任人数不能超过1个');
                }
            }
            if($committee->isNew()){
                input::post('user_name') &&  $msg[] = '新增实验室学术委员会'.$committee->getType().'：“'.input::post('user_name').'”';
            }else{
                if($committee->getUserName()!=input::post('user_name') && !empty($committee->getUserName())){
                    $msg[] = '实验室主任学术委员会成员由“'.$committee->getUserName().'”调整为“'.input::post('user_name').'”';
                }
            }
            $committee->setUserName(input::post('user_name'));
            $committee->setIdcardType(input::post('idcard_type'));
            $committee->setIdcard(input::post('idcard'));
            $committee->setUserSex(input::post('user_sex'));
            $committee->setUserBirthday(input::post('user_birthday'));
            $committee->setUserDuty(input::post('user_duty'));
            $committee->setTitleRank(input::post('title_rank'));
            $committee->setUserTitle(input::post('user_title'));
            $committee->setEducation(input::post('education'));
            $committee->setDegree(input::post('degree'));
            $committee->setHonor(input::post('honor'));
            $committee->setMajor(input::post('major'));
            $committee->setCorporationName(input::post('corporation_name'));
            $committee->setResearchArea(input::post('research_area'));
            $committee->setNote(input::post('note'));
            $committee->save();
            if($msg){
                sf::getModel("historys")->addHistory($this->platform->getPlatformId(),implode("|",$msg),'base',1);
            }
            $this->refresh(lang::get('Has been saved!'));
        }
        view::set('platform',$this->platform);
        view::set('committee',$committee);
        view::apply("inc_body",'platform/profile/committee_edit');
        view::display('page_blank');
    }

    public function committee_delete()
    {
        $committee = $this->platform->getCommitteeById(input::mix('pid'));
        if($committee->getPlatformId()!=$this->platform->getPlatformId()) $this->page_debug('没有权限删除');
        $committee->getUserName() && sf::getModel("historys")->addHistory($this->platform->getPlatformId(),'删除实验室学术委员会'.$committee->getType().'：'.$committee->getUserName(),'base',1);
        $committee->delete();
        $this->page_debug(lang::get('Has been deleted!'),getFromUrl());
    }

	/**
	 * 单位信息查看
	 */
	public function show()
	{
		$this->platform = sf::getModel("Platforms")->selectByPlatformId(input::getInput("mix.userid"));
		if($this->platform->isNew()) $this->page_debug('指定平台不存在！',getFromUrl());
		view::set('platform', $this->platform);
		view::apply('inc_body', 'platform/profile/show');
		view::display('page');
	}

    function coupling()
    {
        $this->platform = sf::getModel("Platforms")->selectByPlatformId(input::getInput("mix.id"));
        if($this->platform->isNew()) $this->page_debug('指定平台不存在！',getFromUrl());
        $user = sf::getModel("Users")->selectByUserId(input::getInput("post.userid"));
        if($this->platform->isNew()) exit('{msg:"关联平台不存在"}');
        if($user->isNew())    exit('{msg:"关联账号不存在"}');
        if($this->platform->coupling($user->getUserId()))
        {
            sf::getModel("historys")->addHistory($this->platform->getPlatformId(),"附加管理员【".$user->getUserUsername()."】到该平台！",'platforms',1);
            sf::getModel("historys")->addHistory($user->getUserId(),"被设置成【".$this->platform->getSubject()."】的管理员！",'users',1);
            exit('{msg:"绑定成功"}');
        }
        exit('{msg:"绑定失败"}');
    }

    function decoupling()
    {
        $this->platform = sf::getModel("Platforms")->selectByPlatformId(input::getInput("mix.id"));
        if($this->platform->isNew()) $this->page_debug('指定平台不存在！',getFromUrl());
        if(input::getInput("post.content")){
            $user = sf::getModel("Users")->selectByUserId(input::getInput("post.userid"));
            if($user->isNew()) exit("<script>parent.location.reload();</script>");
            if($this->platform->decoupling($user->getUserId()))
            {
                sf::getModel("historys")->addHistory($this->platform->getPlatformId(),"解除【".$user->getUserUsername()."】与该平台的关联，原因是：<br />".input::getInput("post.content"),'platforms');
                sf::getModel("historys")->addHistory($user->getUserId(),"解除与【".$this->platform->getSubject()."】的关联，原因是：<br />".input::getInput("post.content"),'users');
            }
            exit("<script>parent.location.reload();</script>");
        }
        view::set("userid",input::getInput("mix.userid"));
        view::set("platform",$this->platform);
        view::apply("inc_body","platform/profile/decoupling");
        view::display("page_blank");
    }

    function search_account()
    {
        $this->platform = sf::getModel("Platforms")->selectByPlatformId(input::getInput("mix.id"));
        if($this->platform->isNew()) $this->page_debug('指定平台不存在！',getFromUrl());
        $addWhere = $addSql = '';
        $addWhere .= " `user_username` <> '' AND `is_lock` <> 4 ";
        //限定到自己的单位
//		$addWhere .= " AND (user_id IN (select user_id from declarers where corporation_id = '".input::getInput("session.roleuserid")."') OR user_id IN (select user_id from experts where corporation_id = '".input::getInput("session.roleuserid")."')) ";
        $addWhere .= " AND (user_id IN (select user_id from user_roles where role_id = 2 and user_role_id in (select user_id from declarers where corporation_id = '".$this->platform->getCorporationId()."')) OR user_id IN (select user_id from user_roles where role_id = 10 and user_role_id in (select user_id from experts where corporation_id = '".$this->platform->getCorporationId()."'))) and user_id not in (select user_id from user_roles where role_id = 5 and user_role_id = '".$this->platform->getPlatformId()."') ";

        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
        $from_vars = array('search','field');

        view::set("pager",sf::getModel("Users")->getPager($addWhere,"ORDER BY ID DESC",12,'','',$from_vars));
        view::apply("inc_body","platform/profile/account_list");
        view::display("page_blank");
    }
	

}
