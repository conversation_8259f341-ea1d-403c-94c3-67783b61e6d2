<?php
namespace App\Controller\Expert;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Lang;
use App\Facades\PDF;

class profile extends BaseController
{
    public $auth = array('edit');
    private $tabs = array();

    public function load()
    {
        if(input::get('unlock')=='yes'){
            $_SESSION['unlock'] = 'yes';
        }
        $this->tabs = [
            ['method'=>'base','text'=>'基本资料<em>*</em>','url'=>site_url('expert/profile/base')],
//            ['method'=>'research','text'=>'研究领域<em>*</em>','url'=>site_url('expert/profile/research')],
            ['method'=>'bank','text'=>'银行账号<em>*</em>','url'=>site_url('expert/profile/bank')],
            ['method'=>'review','text'=>'评审经历','url'=>site_url('expert/profile/review')],
//		['method'=>'project','text'=>'科技项目','url'=>site_url('expert/profile/project')],
//		['method'=>'award','text'=>'科技奖励','url'=>site_url('expert/profile/award')],
//		['method'=>'patent','text'=>'专利信息','url'=>site_url('expert/profile/patent')],
            // ['method'=>'paper','text'=>'论文论著','url'=>site_url('expert/profile/paper')],
//            ['method'=>'employ','text'=>'聘任兼职','url'=>site_url('expert/profile/employ')],
            ['method'=>'attachment','text'=>'附件上传<em>*</em>','url'=>site_url('expert/profile/attachment')],
        ];
        // $button  = '<a href="'.site_url("expert/profile/download/userid/".input::getInput('session.userid')).'" class="btn btn-primary btn-sm btn-submit"><i class="glyphicon glyphicon-print"></i> 导出申请书</a>';
        $button  = '<a href="'.site_url("expert/profile/submit/userid/".input::getInput('session.userid')).'" class="btn btn-danger btn-sm btn-submit"><i class="glyphicon glyphicon-open"></i> 资料上报</a>';
        view::set('method', input::getInput("mix.method"));
        view::set('button', $button);
        view::set('tabs', $this->tabs);
    }

    function index()
    {
        $this->base();
    }

    function edit()
    {
        $_SESSION['navigation_id'] = 677;
        $user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
        if($user->isNew())
            $this->page_debug('请先登录！',site_url("login/index"));
        if(changeRole(10)) $this->jump(site_url("expert/profile/base"));
        else $this->page_debug("请先激活",site_url("register/expert"));
    }

    /**
     * 保存用户基本资料
     */
    public function base()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock() && input::session('unlock')!='yes') $this->jump(site_url("expert/profile/show/userid/".$expert->getUserId()));
        if (Input::getInput("post")) {
            //$user = $expert->getUser(true);//多个用户时候可能有问题
            $user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
            $user->getUserIdcard() && $expert->setCardId($user->getUserIdcard());
            $user->getUserMobile() && $expert->setUserMobile($user->getUserMobile());
            $user->getUserEmail()  && $expert->setUserEmail($user->getUserEmail());

            $expert->setType(input::getInput("post.type"));

            if($expert->getCardName()=='身份证'){
                $expert->setUserBirthday(sf::getLib('idcard')->getBirthday($expert->getCardId()));
                $expert->setUserSex(sf::getLib('idcard')->getSex($expert->getCardId()));
                $expert->setAge(sf::getLib('idcard')->getAge($expert->getCardId()));
            }
            $expert->setEducation(input::getInput("post.education"));
            $expert->setUserDegree(input::getInput("post.user_degree"));
            $expert->setGraduateSchool(input::getInput("post.graduate_school"));
            $expert->setSubjectNames(input::getInput("post.subject"));
            $expert->setTitleType(sf::getModel('CategoryWorks',input::getInput("post.title_type"))->getSubject());
            $expert->setTitle(input::getInput("post.title"));
            //保存单位信息
            $company = sf::getModel("Corporations")->selectByUserId(input::getInput("post.corporation_id"));
            if(!$company->isNew()){
                $expert->setCorporationId($company->getUserId());
                $expert->setWorkUnit($company->getSubject());
                $expert->setUnitAddress($company->getAddress());
                $expert->getUnitPostalCode($company->getPostalcode());
            }else{
                $expert->setWorkUnit(input::post("work_unit"));
                $expert->setCorporationId(input::post("corporation_id"));
            }
            input::getInput("post.unit_address") && $expert->setUnitAddress(input::getInput("post.unit_address"));
            input::getInput("post.unit_postcode") && $expert->setUnitPostalCode(input::getInput("post.unit_postcode"));
            $expert->setWorkDepartment(input::getInput("post.work_department"));
            $expert->setMajor(input::getInput("post.major"));
            $expert->setUserDuty(input::getInput("post.user_duty"));
            $expert->setUnitPhone(input::getInput("post.unit_phone"));
            if(input::getInput("post.area_code")){//专家所在地区
                $expert->setArea(sf::getModel("Region")->selectByCode(input::getInput("post.area_code"))->getFullRegionName());
                $expert->setAreaCode(input::getInput("post.area_code"));
                if(substr($expert->getAreaCode(),0,2)!='51'){
                    $expert->setTag('省外专家');
                    $expert->setIsOut(1);
                }else{
                    $expert->setIsOut(0);
                    $expert->setTag('');
                }
            }
            $expert->setIsLock(9);//修改中
            $expert->save();
            $this->success("保存成功！",getFromUrl());
        }
        $user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
        $user->getUserIdcard() && $expert->setCardId($user->getUserIdcard());
        $user->getUserMobile() && $expert->setUserMobile($user->getUserMobile());
        $user->getUserEmail()  && $expert->setUserEmail($user->getUserEmail());
        view::set('expert', $expert);
        view::apply('inc_body', 'expert/profile/base');
        view::display('page_main');
    }

    /**
     * 研究领域
     */
    public function research()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock() && input::session('unlock')!='yes') $this->jump(site_url("expert/profile/show/userid/".$expert->getUserId()));
        if (Input::getInput("post")) {
            $expert->setGbCodes(input::post("gb_codes"));
            $expert->setHyCodes(input::post("hy_codes"));
            $gb_code1 = array_pop (input::post("gb_codes"));
            $hy_code1 = array_pop (input::post("hy_codes"));
            $expert->setGbCode1($gb_code1);
            $expert->setHyCode1($hy_code1);
            $expert->setGbCode2(input::post('gb_code2'));
            $gbStr = sf::getModel("Subjects")->selectByCode($gb_code1,1)->getSubject();
            $hyStr = sf::getModel("Industrys")->selectByCode($hy_code1,3)->getSubject();
            $subjectNames[] = $gbStr;
            $subjectNames[] = $hyStr;
            $subjectNames[] = input::post('gb_code2');
            $subjectNames = array_filter($subjectNames);
            $expert->setSubjectNames($subjectNames);
            $expert->save();
            $this->success("保存成功！",getFromUrl());
        }
        view::set('expert', $expert);
        view::apply('inc_body', 'expert/profile/research');
        view::display('page_main');
    }

    /**
     * 研究领域
     */
    public function bank()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock() && input::session('unlock')!='yes') $this->jump(site_url("expert/profile/show/userid/".$expert->getUserId()));
        if (Input::getInput("post")) {
            $expert->setBankName(input::getInput("post.bank_name"));
            $expert->setBankBranch(input::getInput("post.bank_branch"));
            $expert->setBankNo(input::getInput("post.bank_no"));
            $expert->setBankCardNumber(input::getInput("post.bank_card_number"));
            $expert->save();
            $this->success("保存成功！",getFromUrl());
        }
        view::set('expert', $expert);
        view::apply('inc_body', 'expert/profile/bank');
        view::display('page_main');
    }

    /**
     * 成果奖励
     */
    public function award()
    {

        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock()) $this->page_debug('材料信息已锁定，暂时不能进行编辑操作！',site_url("expert/profile/show/userid/".$expert->getUserId()));
        if(Input::getInput("post")) {
//            dd(input::getInput("post"));
            $ids   = input::getInput("post.ids");
            $time  = input::getInput("post.time");
            $level = input::getInput("post.level");
            $subject = input::getInput("post.subject");
            $rank = input::getInput("post.rank");
            $dept = input::getInput("post.dept");
            $brief = input::getInput("post.brief");
            //删除前台删除的
            sf::getModel("UserAwards")->remove("id NOT IN ('".implode("','",$ids)."') AND user_id = '".input::getInput("session.roleuserid")."'");
            for($i=0,$n=count($subject);$i<$n;$i++){
                if(!$subject[$i] || !$level[$i]) continue;
                $award = sf::getModel("UserAwards",$ids[$i]);
                $award->setTime($time[$i]);
                $award->setLevel($level[$i]);
                $award->setSubject($subject[$i]);
                $award->setRank($rank[$i]);
                $award->setDepartment($dept[$i]);
                $award->setBrief($brief[$i]);
                $award->setUserId(input::getInput("session.roleuserid"));
                $award->save();
            }
            $this->page_debug("保存成功！",getFromUrl());
        }
        view::set('expert', $expert);
        view::apply('inc_body', 'expert/profile/award');
        view::display('page_main');
    }


    /**
     * 专利
     */
    public function patent()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock()) $this->page_debug('材料信息已锁定，暂时不能进行编辑操作！',site_url("expert/profile/show/userid/".$expert->getUserId()));
        if(Input::getInput("post")) {
            $ids   = input::getInput("post.ids");
            $type  = input::getInput("post.type");
            $sn = input::getInput("post.sn");
            $subject = input::getInput("post.subject");
            $fmr = input::getInput("post.fmr");
            $apply_at = input::getInput("post.apply_at");
            //删除前台删除的
            sf::getModel("Patents")->remove("id NOT IN ('".implode("','",$ids)."') AND user_id = '".input::getInput("session.roleuserid")."'");
            for($i=0,$n=count($subject);$i<$n;$i++){
                if(!$subject[$i] || !$type[$i]) continue;
                $patent = sf::getModel("Patents",$ids[$i]);
                $patent->setPatentId(sf::getLib("MyString")->getRandString());
                $patent->setType($type[$i]);
                $patent->setSn($sn[$i]);
                $patent->setSubject($subject[$i]);
                $patent->setFmr($fmr[$i]);
                $patent->setApplyAt($apply_at[$i]);
                $patent->setUserId(input::getInput("session.roleuserid"));
                $patent->setUserName(input::getInput('session.nickname'));
                $patent->setCorporationId($expert->getCorporationId());
                $patent->setCorporationName($expert->getWorkUnit());
                $patent->setRole(10);
                $patent->save();
            }
            $this->page_debug("保存成功！",getFromUrl());
        }
        view::set('expert', $expert);
        view::apply('inc_body', 'expert/profile/patent');
        view::display('page_main');
    }


    /**
     * 论文
     */
    public function paper()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock()) $this->page_debug('材料信息已锁定，暂时不能进行编辑操作！',site_url("expert/profile/show/userid/".$expert->getUserId()));
        if(Input::getInput("post")) {
            $ids   = input::getInput("post.ids");
            $subject  = input::getInput("post.subject");
            $author_order = input::getInput("post.author_order");
            $publisher = input::getInput("post.publisher");
            $pubilsh_time = input::getInput("post.pubilsh_time");
            $periods = input::getInput("post.periods");
            $level = input::getInput("post.level");
            //删除前台删除的
            sf::getModel("UserPapers")->remove("id NOT IN ('".implode("','",$ids)."') AND user_id = '".input::getInput("session.roleuserid")."'");
            for($i=0,$n=count($subject);$i<$n;$i++){
                if(!$subject[$i]) continue;
                $paper = sf::getModel("UserPapers",$ids[$i]);
                $paper->setSubject($subject[$i]);
                $paper->setAuthorOrder($author_order[$i]);
                $paper->setPublisher($publisher[$i]);
                $paper->setPubilshTime($pubilsh_time[$i]);
                $paper->setPeriods($periods[$i]);
                $paper->setLevel($level[$i]);
                $paper->setUserId(input::getInput("session.roleuserid"));
                $paper->save();
            }
            $this->page_debug("保存成功！",getFromUrl());
        }
        view::set('expert', $expert);
        view::apply('inc_body', 'expert/profile/paper');
        view::display('page');
    }

    /**
     * 参与国家、省部级评估、评审活动简介
     */
    public function review()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock() && input::session('unlock')!='yes') $this->jump(site_url("expert/profile/show/userid/".$expert->getUserId()));
        if(Input::getInput("post")) {
            $ids   = input::getInput("post.ids");
            $time  = input::getInput("post.time");
            $level = input::getInput("post.level");
            $brief = input::getInput("post.brief");
            //删除前台删除的
            sf::getModel("UserReviews")->remove("id NOT IN ('".implode("','",$ids)."') AND user_id = '".input::getInput("session.roleuserid")."'");
            for($i=0,$n=count($time);$i<$n;$i++){
                if(!$time[$i] || !$level[$i]) continue;
                $review = sf::getModel("UserReviews",$ids[$i]);
                $review->setTime($time[$i]);
                $review->setLevel($level[$i]);
                $review->setBrief($brief[$i]);
                $review->setUserId(input::getInput("session.roleuserid"));
                $review->save();
            }
            $this->page_debug("保存成功！",getFromUrl());
        }
        view::set('expert', $expert);
        view::apply('inc_body', 'expert/profile/review');
        view::display('page_main');
    }

    /**
     * 参与国家、省部级评估、评审活动简介
     */
    public function project()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock()) $this->page_debug('材料信息已锁定，暂时不能进行编辑操作！',site_url("expert/profile/show/userid/".$expert->getUserId()));
        if(Input::getInput("post")) {
            $ids   = input::getInput("post.ids");
            $start  = input::getInput("post.start");
            $end = input::getInput("post.end");
            $level = input::getInput("post.level");
            $role = input::getInput("post.role");
            $subject = input::getInput("post.subject");
            $brief = input::getInput("post.brief");
            //删除前台删除的
            sf::getModel("UserTechplans")->remove("id NOT IN ('".implode("','",$ids)."') AND user_id = '".input::getInput("session.roleuserid")."'");
            for($i=0,$n=count($subject);$i<$n;$i++){
                if(!$start[$i] || !$subject[$i]) continue;
                $project = sf::getModel("UserTechplans",$ids[$i]);
                $project->setStartAt($start[$i]);
                $project->setEndAt($end[$i]);
                $project->setSubject($subject[$i]);
                $project->setLevel($level[$i]);
                $project->setRole($role[$i]);
                $project->setBrief($brief[$i]);
                $project->setUserId(input::getInput("session.roleuserid"));
                $project->save();
            }
            $this->page_debug("保存成功！",getFromUrl());
        }
        view::set('expert', $expert);
        view::apply('inc_body', 'expert/profile/project');
        view::display('page_main');
    }

    /**
     * 社会兼职、聘任信息
     */
    public function employ()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock() && input::session('unlock')!='yes') $this->jump(site_url("expert/profile/show/userid/".$expert->getUserId()));
        if(Input::getInput("post")) {
            $ids   = input::getInput("post.ids");
            $start_at  = input::getInput("post.start_at");
            $end_at = input::getInput("post.end_at");
            $subject = input::getInput("post.subject");
            $brief = input::getInput("post.brief");
            //删除前台删除的
            sf::getModel("UserEmploys")->remove("id NOT IN ('".implode("','",$ids)."') AND user_id = '".input::getInput("session.roleuserid")."'");
            for($i=0,$n=count($subject);$i<$n;$i++){
                if(!$subject[$i] || !$start_at[$i]) continue;
                $employ = sf::getModel("UserEmploys",$ids[$i]);
                $employ->setStartAt($start_at[$i]);
                $employ->setEndAt($end_at[$i]);
                $employ->setSubject($subject[$i]);
                $employ->setBrief($brief[$i]);
                $employ->setUserId(input::getInput("session.roleuserid"));
                $employ->save();
            }
            $this->page_debug("保存成功！",getFromUrl());
        }
        view::set('expert', $expert);
        view::apply('inc_body', 'expert/profile/employ');
        view::display('page_main');
    }

    /**
     * 附件信息
     */
    public function attachment()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock() && input::session('unlock')!='yes') $this->jump(site_url("expert/profile/show/userid/".$expert->getUserId()));
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        view::set('expert', $expert);
        view::set('itemType', 'expert');
        view::apply('inc_body', 'expert/profile/attachment');
        view::display('page_main');
    }

    /**
     * 专家信息查看
     */
    public function show()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("mix.userid"));
        if($expert->isNew()) $this->page_debug('指定专家不存在！',getFromUrl());
        // if(!$expert->getIsReport()) $msg[] = '尊敬的专家，请你导出申报材料交单位盖章后报送到科协办，科协办收到材料后才会在线实名认证（专家入库）！';
        view::set('msg', implode('<br />',$msg));
        view::set('expert', $expert);
        view::apply('inc_body', 'expert/profile/show');
        view::display('page_main');
    }

    /**
     * 导出申请书
     */
    public function download()
    {
        $this->page_debug('此功能关闭中！',getFromUrl());
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("mix.userid"));
        if($expert->isNew()) $this->page_debug('指定专家不存在！',getFromUrl());
        view::set('expert', $expert);
        $html = view::getContent("expert/profile/output");
        $pdf = PDF::setContent($html)
            ->setHeader('<p style="text-align:left;border-bottom:1px solid #808080;color:#808080;">四川省临床重点专科专家库专家申请书</p>')
            ->setFooter(['center'=>'- {PAGENO} -','right'=>'<img src="'.PDF::getQRcode($expert->getUserId()).'" height="50" />'])
            ->setWaterMark('四川省临床重点专科专家库专家申请书')
            ->download('申请书（'.$expert->getUserName().'）.pdf');
    }
    /**
     * 导出承诺书
     */
    public function promise()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("mix.userid"));
        if($expert->isNew()) $this->page_debug('指定专家不存在！',getFromUrl());
        view::set('expert', $expert);
        $html = view::getContent("expert/profile/promise");
        $pdf = PDF::setContent($html)
            ->setFooter(['center'=>'- {PAGENO} -','right'=>'<img src="'.PDF::getQRcode($expert->getUserId()).'" height="50" />'])
            ->download('承诺书（'.$expert->getUserName().'）.pdf');
    }
    /**
     * 上报资料
     */
    public function submit()
    {
        $expert = sf::getModel("Experts")->selectByUserId(input::getInput("session.roleuserid"));
        if($expert->hasLock()) $this->page_debug('材料信息已锁定，暂时不能进行编辑操作！',site_url("expert/profile/show/userid/".$expert->getUserId()));
        if(Input::getInput("post")) {
            if($expert->getCorporationId()){
                //待单位审核
                $expert->setIsLock(7);
            }else{
                //待科技厅审核
                $expert->setIsLock(1);
            }
            $expert->save();
            sf::getModel("historys")->addHistory($expert->getUserId(),'资料维护上报！','experts');
            $this->success("上报成功！",site_url('expert/profile/show/userid/'.$expert->getUserId()));
        }
        view::set('expert', $expert);
        view::set('msg', $expert->validate());
        view::apply('inc_body', 'expert/profile/submit');
        view::display('page_main');
    }

    public function upload()
    {
        if($_FILES){
            $upload = sf::getLib("Upload",'file',config::get("upload_path","/up_files/"),2097152,array('jpg','bmp','pdf','png','doc','docx'));
            if($upload->upload())
            {
                $result = $upload->getSaveFileInfo();
                foreach($result as $files)
                {
                    $filemanager = sf::getModel("filemanager");
                    $filemanager->setFileName($files['name']);
                    $filemanager->setFileSavename($files['savename']);
                    $filemanager->setFilePath($files['path']);
                    $filemanager->setFileSize($files['size']);
                    $filemanager->setFileExt($files['type']);
                    $filemanager->setFileMinetype($files['minetype']);
                    $filemanager->setUserId(input::session('userid'));
                    $filemanager->setUserName(input::session('username'));
                    $filemanager->setCreatedAt(date("Y-m-d H:i:s"));
                    $filemanager->setFileNote(input::getInput("post.file_note"));
                    $filemanager->setItemId(input::session('roleuserid'));
                    $filemanager->setItemType('expert');
                    $filemanager->save();
                }
                $this->jump(site_url('expert/profile/attachment'));
            }
        }
        $this->jump(getFromUrl());
    }
}