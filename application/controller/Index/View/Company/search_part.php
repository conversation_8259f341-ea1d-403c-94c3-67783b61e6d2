<form action="" method="post" name="search" id="search">
<table width="100%" align="center">
    <tbody id="show_search">
      <tr>
        <td>
            <label>关键词：<input class="form-control w-auto custom-control-inline" name="search" type="text" id="search" value="<?=input::getInput('mix.search')?>" /></label>
            <label><input name="field" type="radio" id="radio" value="company_name" checked="checked" />申报单位</label>
            <?=btn('button','搜索','submit','find')?>
        </td>
      </tr>
      <tr>
        <td>
          <select name="usage" id="usage" class="form-control w-auto custom-control-inline">
            <option value="">=使用场景=</option>
            <?=getSelectFromArray(['apply'=>'申报','stage'=>'中期','complete'=>'验收'],input::getInput('mix.usage'),false)?>
          </select>
          <select name="index_year" id="index_year" class="form-control w-auto custom-control-inline">
            <option value="">=指标年度=</option>
            <?=getYearList(input::getInput('mix.index_year'),2018)?>
          </select>
          <select name="subject_code" id="subject_code" class="form-control w-auto custom-control-inline">
            <option value="">=专科=</option>
            <?=getSelectFromArray(getZdzkList('code'),input::getInput('mix.subject_code'),false)?>
          </select>
          <select name="index_code" id="index_code" class="form-control w-auto custom-control-inline">
            <option value="">=指标=</option>
              <?=getSelectFromArray(getIndexs(false,'is_company',1),input::getInput('mix.index_code'),false)?>
          </select>
            <select name="department_id" id="department_id" class="form-control w-auto custom-control-inline">
                <option value="">=主管部门=</option>
                <?=getDepartmentList(input::getMix('department_id'))?>
            </select>
            <select name="district" id="district" class="form-control w-auto custom-control-inline">
                <option value="">=所属片区=</option>
                <?=getSelectFromArray(getDistrictList(),input::getInput("mix.district"))?>
            </select>
        </td>
      </tr>
        </tbody>
    </table>
</form>
