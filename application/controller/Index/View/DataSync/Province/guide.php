<style>
    .control-label{
        line-height: 35px;
        padding-right: 0;
    }
</style>
<script language="javascript" type="text/javascript">
    function unit(json){
        $('#corporation_id').val(json.id);
        $('#corporation_name').val(json.subject);
        closeWindow();
    }
</script>
<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        全省平均数据同步
                    </h3>
                    <div class="block-options">

                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <form action="" method="post" name="search" id="search">

                            <div class="form-group row center">
                                <label class="control-label text-right col-xs-12 col-sm-4 col-md-4">评分表：</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <select name="rule_id" class="form-control">
                                        <?=getSelectFromArray(getBookMapList(4,true),0,false)?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row center">
                                <label class="control-label text-right col-xs-12 col-sm-4 col-md-4">评审编号：</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <select name="assess_id" class="form-control w-auto custom-control-inline" style="margin-right: 6px">
                                        <?=getSelectFromArray(getAssessList(),0,false)?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row center">
                                <label class="control-label text-right col-xs-12 col-sm-4 col-md-4">指标数据年度：</label>
                                <div class="col-xs-12 col-sm-7 col-md-7">
                                    <?=get_checkbox(range(2019,date('Y')),'index_year')?>
                                </div>
                            </div>
                            <div class="form-group row center">
                                <label class="control-label text-right col-xs-12 col-sm-4 col-md-4">指南ID（可选）：</label>
                                <div class="col-xs-12 col-sm-7 col-md-7">
                                    <input type="text" name="guide_id" id="guide_id" value="0" class="form-control">
                                </div>
                            </div>
                            <div class="form-group row center">
                                <div style="width: 200px;margin: 0 auto;">
                                    <?=Button::setType('button')->setClass('btn-alt-primary query')->setSize('btn-lg')->setIcon('check')->button('开始同步')?>
                                    <?=Button::setType('button')->setClass('btn-alt-danger reset d-none')->setSize('btn-lg')->button('重置')?>
                                </div>
                            </div>
                            <div class="progress" style="display: none">
                                <div class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="min-width: 2em;">
                                    0%
                                </div>
                            </div>
                            <div class="messages text-center">
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var t;
    $(function (){
        $(".query").click(function (){
            var url = '<?=site_url('index/datasync/province/init')?>';
            var data = $('#search').serializeArray();
            var me = $(this);
            me.html('<i class="fa fa-sync fa-spin"></i> 同步中').addClass('disabled').attr('disabled',true);
            showMessage('<p>正在同步数据，请勿关闭页面</p>');
            $(".reset").removeClass('d-none');
            $.post(url,data,function (response){
                if(response.code==0){
                    t = setInterval(getPercent,3000);
                }else{
                    $(".reset").addClass('d-none');
                    showError(response.msg);
                    $(".progress").hide();
                    $(".query").html('<i class="fa fa-check"></i> 开始同步').removeClass('disabled').attr('disabled',false);
                }
            },'json');
        });
        $(".reset").click(function (){
            clearInterval(t);
            showMessage('已重置');
            $(".progress").hide();
            $(".query").html('<i class="fa fa-check"></i> 开始同步').removeClass('disabled').attr('disabled',false);
            $(this).addClass('d-none');
        });
    });

    function showProgress(percent) {
        $(".progress").show();
        $(".progress-bar").attr('aria-valuenow',percent);
        $(".progress-bar").css('width',percent+'%');
        $(".progress-bar").text(percent+'%');
    }

    function getPercent()
    {
        var url = '<?=site_url('index/datasync/province/getPercent')?>';
        $.getJSON(url, function (data) {
            if(data.code==0){
                showProgress(data.percent);
                showMessage(data.msg);
            }
            if(data.code==0 && data.percent==100){
                $(".reset").addClass('d-none');
                clearInterval(t);
                showMessage(data.msg);
                $(".progress").hide();
                $(".query").html('<i class="fa fa-check"></i> 开始同步').removeClass('disabled').attr('disabled',false);
            }
        });
    }


    function showMessage(html)
    {
        $(".messages").html(html)
    }
</script>