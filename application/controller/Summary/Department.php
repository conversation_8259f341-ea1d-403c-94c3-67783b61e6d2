<?php
namespace App\controller\Summary;
use App\Controller\BaseController;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Department extends BaseController
{

    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/view/');
    }
    public function index()
    {
        $this->gird();
    }

    public function wait_list()
    {
        $this->gird('department/wait_list',"`department_id` = '".input::getInput('session.roleuserid')."' and statement  = 5");
    }

    public function submit_list()
    {
        $this->gird('department/submit_list',"`department_id` = '".input::getInput('session.roleuserid')."' and statement>6");
    }

    public function back_list()
    {
        $this->gird('department/back_list',"`department_id` = '".input::getInput('session.roleuserid')."' and statement = 6");
    }

    public function gird($tpl = 'department/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        if(input::getInput("mix.field")){
            if(input::getInput("mix.field")=='corporation_name'){
                $addWhere .= " AND (`corporation_name` LIKE '%".input::getInput("mix.search")."%' or `other_corporation` LIKE '%".input::getInput("mix.search")."%' )";
            }else{
                $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
            }
        }
        $form_vars = array('subject','corporation_name');
        $this->view->set("pagers",sf::getModel('Summarys')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

    /**
     * 受理
     */
    public function doAccept()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput('mix.id'));
        //如果项目不存在
        if($summary->isNew()) $this->page_debug(lang::get('The project is not found!'));
        $summary->setStatement(9);
        $summary->save();
        sf::getModel("historys")->addHistory($summary->getSummaryId(),'主管部门已审核','summary');//保存历史记录
        $this->page_debug('已审核',getFromUrl());
    }

    /**
     * 驳回
     */
    function doRejected()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput("mix.id"));
        if($summary->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if(input::getInput("post.content")){
            $summary->setStatement(6);
            $summary->save();
            sf::getModel("historys")->addHistory($summary->getSummaryId(),'主管部门退回！'.'<br/>'.input::getInput("post.content"),'summary');
            $this->refresh('已退回！');
        }
        $this->view->set("summary",$summary);
        $this->view->apply("inc_body","department/note");
        $this->view->display("page_blank");
    }

    public function export()
    {
        $summarys = sf::getModel('Summarys')->selectAll("`statement` > 6  AND `department_id` = '".input::getInput('session.roleuserid')."'","order by `declare_at` asc");
        ob_start(); //打开缓冲区
        echo '  
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" >  
<head>  
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>  
<xml><w:WordDocument><w:View>Print</w:View></xml>  
</head>';
        $html = '<body>  
<h1 align="center" style="font-size: 30px;">2018年度提交工作总结工程技术<br>
研究中心汇总表</p></h1>
<p>&nbsp;</p>
<p>填表人:<u>&nbsp;&nbsp;&nbsp;&nbsp; </u>联系电话:<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>归口管理部门（公章）<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></p>
<table border="1" cellpadding="3" cellspacing="0">
<tbody>
<tr>
<td width="73">
<p><strong>序号</strong></p>
</td>
<td width="211">
<p><strong>中心名称</strong></p>
</td>
<td width="142">
<p><strong>依托单位</strong></p>
</td>
<td width="142">
<p><strong>归口部门</strong></p>
</td>
</tr>';
        $i=0;
        while($summary = $summarys->getObject()):
            $i++;
            $html.='<tr>
<td width="73">
<p>'.$i.'</p>
</td>
<td width="211">
<p>'.$summary->getSubject().'</p>
</td>
<td width="142">
<p>'.$summary->getCorporationName().'</p>
</td>
<td width="142">
<p>'.$summary->getDepartmentName().'</p>
</td>
</tr>';
            endwhile;
            $html.='   
</tbody>
</table>
</body>';
            echo $html;
        header("Cache-Control: no-store"); //所有缓存机制在整个请求/响应链中必须服从的指令
        Header("Content-type: application/octet-stream");  //用于定义网络文件的类型和网页的编码，决定文件接收方将以什么形式、什么编码读取这个文件
        Header("Accept-Ranges: bytes");  //Range防止断网重新请求 。
        if (strpos($_SERVER["HTTP_USER_AGENT"],'MSIE')) {
            header('Content-Disposition: attachment; filename=提交工作总结四川省工程技术研究中心汇总表.doc');
        }else if (strpos($_SERVER["HTTP_USER_AGENT"],'Firefox')) {
            Header('Content-Disposition: attachment; filename=提交工作总结四川省工程技术研究中心汇总表.doc');
        } else {
            header('Content-Disposition: attachment; filename=提交工作总结四川省工程技术研究中心汇总表.doc');
        }
        header("Pragma:no-cache"); //不能被浏览器缓存
        header("Expires:0");  //页面从浏览器高速缓存到期的时间分钟数，设定expires属性为0，将使对一页面的新的请求从服务器产生
        ob_end_flush();//输出全部内容到浏览器

    }

}