<?php
namespace App\controller\Summary;
use App\Controller\BaseController;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Office extends BaseController
{
    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/view/');
    }
    public function index()
    {
        $this->gird();
    }

    public function wait_list()
    {
        $this->gird('office/wait_list','statement  = 9');
    }

    public function accept_list()
    {
        $this->gird('office/accept_list','statement = 10');
    }

    public function assess_list()
    {
        $this->gird('office/assess_list','statement = 20');
    }

    public function back_list()
    {
        $this->gird('office/back_list','statement = 12');
    }

    public function stat()
    {
        $addWhere = 'guide_id = 4';
        $year = input::getInput("mix.year");
        if(!$year) $year = date('Y');
        $addWhere .= " AND `declare_year` = '".$year."'";
        $this->view->set('year',$year);
        $this->gird('office/stat',$addWhere);
    }

    public function gird($tpl = 'office/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        if(input::getInput("mix.field") && input::getInput("mix.search")){
            if(input::getInput("mix.field")=='corporation_name'){
                $addWhere .= " AND (`corporation_name` LIKE '%".input::getInput("mix.search")."%' or `other_corporation` LIKE '%".input::getInput("mix.search")."%' )";
            }else{
                $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
            }
        }
        if(input::session('auth')!=-1 && input::session('auth.cat')){
            $addWhere .= " AND `cat_id` IN (".implode(',',input::session('auth.cat')).") ";
        }
        input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."'";
        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."'";
        input::getInput("mix.guide_id") && $addWhere .= " AND `guide_id` = '".input::getInput("mix.guide_id")."' ";
        input::getInput("mix.leader_name") && $addWhere .= " AND `leader_name` like '%".input::getInput("mix.leader_name")."%' ";
        input::getInput("mix.user_name") && $addWhere .= " AND `user_name` like '%".input::getInput("mix.user_name")."%' ";
        $form_vars = array('subject','corporation_name','declare_year','guide_id','leader_name','user_name');
        $this->view->set('guide_id',input::getInput("mix.guide_id"));
        $this->view->set("pagers",sf::getModel('Summarys')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

    /**
     * 受理
     */
    public function doAccept()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput('mix.id'));
        //如果项目不存在
        if($summary->isNew()) $this->page_debug(lang::get('The project is not found!'));
        $summary->setStatement(10);
        $summary->save();
        sf::getModel("historys")->addHistory($summary->getSummaryId(),'科技厅已审核','summary');//保存历史记录
        $this->page_debug('已审核',getFromUrl());
    }

    /**
     * 驳回
     */
    function doRejected()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput("mix.id"));
        if($summary->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if(input::getInput("post.content")){
            $summary->setStatement(12);
            $summary->save();
            sf::getModel("historys")->addHistory($summary->getSummaryId(),'科技厅退回！'.'<br/>'.input::getInput("post.content"),'summary');
            $this->refresh('已退回！');
        }
        $this->view->set("summary",$summary);
        $this->view->apply("inc_body","office/note");
        $this->view->display("page_blank");
    }

    /**
     * 批量受理
     */
    public function doAcceptSelected()
    {
        if(input::getInput("post.select_id")) $ids = input::getInput("post.select_id");
        else $ids[] = input::getInput("get.id");
        $ids = array_filter($ids);
        if(empty($ids)) $this->page_debug('请勾选要审核的项目');
        for($i=0,$n=count($ids);$i<$n;$i++) {
            $summary = sf::getModel("Summarys")->selectBySummaryId($ids[$i]);
            //如果项目不存在
            if ($summary->isNew()) continue;
            $summary->setStatement(10);
            $summary->save();
            sf::getModel("historys")->addHistory($summary->getSummaryId(),'科技厅已审核','summary');//保存历史记录
        }
        $this->page_debug('勾选项已审核通过！',getFromUrl());
    }

    /**
     * 批量退回
     */
    public function doRejectedSelected()
    {
        if(input::getInput("post.select_id")) $ids = input::getInput("post.select_id");
        else $ids[] = input::getInput("get.id");
        $ids = array_filter($ids);
        if(empty($ids)) $this->page_debug('请勾选要退回的项目');
        for($i=0,$n=count($ids);$i<$n;$i++) {
            $summary = sf::getModel("Summarys")->selectBySummaryId($ids[$i]);
            //如果项目不存在
            if ($summary->isNew()) continue;
            $summary->setStatement(12);
            $summary->save();
            sf::getModel("historys")->addHistory($summary->getSummaryId(),'科技厅退回！','summary');
        }
        $this->page_debug('勾选项已退回！',getFromUrl());
    }

    function change(){
        $rcid = input::getInput("post.rcid");
        $cid = input::getInput("post.cid");

        if(!$rcid) exit('NO1');
        if(!$cid) exit('NO2');
        //修改单位
        $center = sf::getModel("Summarys")->selectByRcId($rcid);
        $old_name = $center->getDepartmentName();
        $center->setDepartmentId($cid);
        $center->setDepartmentName(sf::getModel("Departments")->selectByUserId($cid)->getSubject());
        sf::getModel("historys")->addHistory(input::getInput('session.roleuserid'),"主管部门由“".$old_name."”调整为“".$center->getDepartmentName()."”。",'departments');
        if($center->save()) exit('OK');
        else exit("NO3");
    }

    function export()
    {
        $head = ['序号','年度','类别','实验室名称','依托单位','主管部门','实验室主任','联系人','联系电话','填报日期','所属领域','状态'];
        $body = [];
        $addwhere = '1 ';
        if(input::getInput('mix.statement')){
            $addwhere.=' and statement = '.input::getInput('mix.statement');
        }
        if(input::getInput('mix.guide_id')){
            $addwhere.=' and guide_id = '.input::getInput('mix.guide_id');
        }
        $projects = sf::getModel('Summarys')->selectAll($addwhere,"order by `declare_at` asc");
        $i=0;
        while($project =  $projects->getObject()){
            $body[$i][] = $i+1;
            $body[$i][] = $project->getDeclareYear();
            $body[$i][] = $project->getGuideSubject();
            $body[$i][] = $project->getSubject();
            $body[$i][] = $project->getCorporationName();
            $body[$i][] = $project->getDepartmentName();
            $body[$i][] = $project->getLeader();
            $body[$i][] = $project->getLinkman();
            $body[$i][] = $project->getMobile();
            $body[$i][] = $project->getDeclareAt('Y 年 m 月 d 日');
            $body[$i][] = $project->getResearchArea();
            $body[$i][] = strip_tags($project->getState());
            $i++;
        }
        $filename =input::getInput('mix.guide_id')==3 ? '年度考核列表' : '周期评估列表';
        excel_out($head,$body,$filename,$filename);
    }

    function export_stat()
    {
        $template = WEBROOT . '/up_files/2023/summary_stat.xlsx';          //使用导出模板
        $objPHPExcel = \PHPExcel_IOFactory::load($template);     //加载excel文件,设置模板
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);  //设置保存版本格式

        $objPHPExcel->setActiveSheetIndex(0);
        $objActSheet = $objPHPExcel->getActiveSheet();
        //设置默认字体
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        //主体数据
        $body = [];
        $i=0;
        $year = input::getMix('declare_year')?:date('Y');
        $addwhere = "declare_year = '{$year}'";
        if(input::getMix('statement')) $addwhere.=" and statement = '".input::getMix('statement')."'";
        $summarys = sf::getModel('Summarys')->selectAll($addwhere,"order by `declare_at` asc");
        while($summary = $summarys->getObject()){
            $stat = $summary->getStat(true);
            $datas = $stat->getSimple();
            foreach ($datas as $data){
                $body[$i][] = $data;
            }
            $i++;
        }
        $startRow = 7;
        foreach($body as $key=>$value){
            $startRow++;
            $index='A';
            for($i=0;$i<count($value);$i++){
                $objActSheet->setCellValue($index.$startRow,$value[$i]);
                $index++;
            }
        }
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="工作绩效统计汇总表.xls"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        ob_end_clean();
        $objWriter->save('php://output');

    }

    /**
     * 会评录入
     */
    function input()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput('mix.id'));
        if(input::post())
        {
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            if(input::post('score')){
                $summary->setScore(input::post('score'));
            }
            $summary->setStatement(20);//评审完毕
            if(input::getInput("post.radicate_money")) $summary->setRadicateMoney(input::getInput("post.radicate_money"));
            $summary->setConfigs(['path.assess'=>'summary/assess']);//评审完毕
            $summary->save();
            sf::getModel("historys")->addHistory(input::getInput("post.id"),'会评分数录入'."<br />".input::getInput("post.note"),'summary');
            exit("<script>parent.location.reload();</script>");
        }
        $this->view->set("summary",$summary);
        $this->view->apply("inc_body",'office/input');
        $this->view->display("page_blank");
    }

    /**
     * 会评录入
     */
    function inspect()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput('mix.id'));
        if(input::post())
        {
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $summary->setStatement(21);//初评完毕
            $summary->save();
            sf::getModel("historys")->addHistory(input::getInput("post.id"),'现场考察意见录入'."<br />".input::getInput("post.note"),'summary');
            exit("<script>parent.location.reload();</script>");
        }
        $this->view->set("summary",$summary);
        $this->view->apply("inc_body",'office/inspect');
        $this->view->display("page_blank");
    }


    /**
     * 经费拨付
     */
    function money()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput('mix.id'));
        if(input::post())
        {
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            if(input::getInput("post.radicate_money")){
                $summary->setRadicateMoney(input::getInput("post.radicate_money"));
                $summary->save();
                sf::getModel("historys")->addHistory(input::getInput("post.id"),'经费拨付'."<br />".input::getInput("post.radicate_money").'万元','summary');
            }
            exit("<script>parent.location.reload();</script>");
        }
        $this->view->set("summary",$summary);
        $this->view->apply("inc_body",'office/money');
        $this->view->display("page_blank");
    }

    /**
     * 初评录入
     */
    function firstAssess()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput('mix.id'));
        if(input::post())
        {
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $summary->setFirstScore(input::getInput("post.score"));
            if($summary->getStatement()<19) $summary->setStatement(19); //初评完毕
            $summary->setConfigs(['path.assess'=>'summary/Assess']);
            $summary->save();
            sf::getModel("historys")->addHistory(input::getInput("post.id"),'初评分数录入'."<br />".input::getInput("post.note"),'summary');
            exit("<script>parent.location.reload();</script>");
        }
        $this->view->set("summary",$summary);
        $this->view->apply("inc_body",'office/first_assess');
        $this->view->display("page_blank");
    }

    /**
     * 现场考察
     */
    function secondAssess()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput('mix.id'));
        if(input::post())
        {
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            if($summary->getStatement()<20) $summary->setStatement(21); //现场考察完毕
            $summary->setConfigs(['path.assess'=>'summary/Assess']);
            $summary->save();
            sf::getModel("historys")->addHistory(input::getInput("post.id"),'现场考察专家意见录入'."<br />".input::getInput("post.note"),'summary');
            exit("<script>parent.location.reload();</script>");
        }
        $this->view->set("summary",$summary);
        $this->view->apply("inc_body",'office/second_assess');
        $this->view->display("page_blank");
    }

    public function file_delete()
    {
        $file = sf::getModel("Filemanager",input::getInput("mix.fid"));
        $data['code'] = 0;
        if($file->isNew()){
            $data['msg'] = '找不到该文档';
            echo json_encode($data);exit();
        }
        $result = $file->remove(input::getInput("mix.fid"),true);
        if($result===true){
            $data['code'] = 1;
        }else{
            $data['msg'] = '删除失败';
        }
        echo json_encode($data);exit();
    }
}