<?php
namespace App\controller\Summary;
use App\Controller\BaseController;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Platform extends BaseController
{
    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/view/');
    }

    function create()
    {
        $platform = sf::getModel('Platforms')->selectByPlatformId(input::getMix('platform_id'));
        if($platform->isNew()){
            $this->error('找不到该平台');
        }
        if($platform->getStatement()!=0){
            $this->error('该平台无需填写年度考核报告');
        }
        $guide = sf::getModel('Guides')->selectLastSummaryGuide($platform->getCatId());
        if($guide->isNew()){
            $this->error('还未开放填报该平台的年度考核报告！');
        }
        $summary = sf::getModel("Summarys")->selectBySummaryId('');
        $summary->setCatId($platform->getCatId());
        $summary->setPlatformId($platform->getPlatformId());
        $summary->setDeclareYear($guide->getYear());
        $summary->setTypeCurrentGroup($guide->getCurrentGroup());
        $summary->setStartAt($guide->getReportStartAt());
        $summary->setEndAt($guide->getReportEndAt());
        $summary->setGuideId($guide->getId());
        $summary->setWorkerId($guide->types()->getWorkerId());
        $summary->setSubject($platform->getSubject());
        $summary->setCompanyId($platform->getCorporationId());
        $summary->setCompanyName($platform->getCorporationName());
        $summary->setDepartmentId($platform->getDepartmentId());
        $summary->setDepartmentName($platform->getDepartmentName());
        if(input::post()){
            if($summary->isRepeat()) {
                $jumpurl = site_url('summary/platform/write_list').'/_save/no/_msg/已存在'.$summary->getDeclareYear().'年度的报告，不能重复填写！';
                exit("<script>top.location.href='{$jumpurl}'</script>");
            }
            $summary->setLinkman(input::post('linkman'));
            $summary->setLinkmanMobile(input::post('linkman_mobile'));
            $summary->setCreatedAt(date('Y-m-d H:i:s'));
            $summary->setStatement(1);
            $summary->save();
            $summary->setConfigs('path.apply',"engine/summaryer");
            $jumpurl = site_url('engine/summaryer/edit/id/'.$summary->getSummaryId());
            exit("<script>top.location.href='{$jumpurl}'</script>");
        }
        $this->view->set("summary",$summary);
        $this->view->set("platform",$platform);
        $this->view->apply("inc_body","platform/create");
        $this->view->display("page_blank");
    }


    //年度考核报告填写向导
	function guide_list()
	{
        $addWhere = "`platform_id` = '".input::getInput("session.roleuserid")."' and statement = 0";
		$this->platformGrid('platform/guide_list',$addWhere,20);
	}

    //填写中的年度考核报告
	function write_list()
	{
        $addWhere = "statement IN (-1,0,1,0,1,3,6,12) and `platform_id` = '".input::getInput("session.roleuserid")."' ";
		$this->grid('platform/write_list',$addWhere,20);
	}

    function submit_list()
    {
        $addWhere = "statement in (2,5,9,10) and `platform_id` = '".input::getInput("session.roleuserid")."'";
        $this->grid('platform/submit_list',$addWhere,20);
    }

	function index()
	{
        $addWhere = "`platform_id` = '".input::getInput("session.roleuserid")."'";
		$this->grid('platform/index',$addWhere,20);
	}

    /**
     * 上报项目
     */
    function doSubmit()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput("mix.id"));
        if(input::session('roleuserid') != $summary->getPlatformId() || $summary->isNew())
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        $msg = $summary->validate();
        if(input::post("id"))
        {
            if(count($msg)) $this->page_debug("请按照提示修改完成后再次上报。",getFromUrl());
            $summary->setStatement(2);
            $summary->setDeclareAt(date('Y-m-d H:i:s'));
            $summary->save();
            $summary->setConfigs("file.apply",'');//清空生成的
            sf::getModel("Historys")->addHistory($summary->getSummaryId(),'年度监测上报','summary');
            $this->success(lang::get("Has been submit!"),site_url("summary/platform/submit_list"));
        }

        $data['msg'] = $msg;
        $data['summary'] = $summary;
        $this->view->set($data);
        $this->view->apply("inc_body","platform/submit_do");
        $this->view->display("page_main");
    }

    /**
     * 集中处理项目的搜索信息
     *
     */
    public function grid($tpl = 'platform/wait_list',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

        if(input::getInput("mix.declare_year"))
            $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";

        //将搜索条件保存以备打印或者导出
        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['summarys']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['summarys']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['summarys']['orderStr'] = base64_encode($addSql);
        }
//		dd($addWhere);
        //显示页面
        $form_vars = array('search','declare_year','declare_month','department_id','department_id','statement');

        $this->view->set("pager",sf::getModel('Summarys')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

    /**
     * 集中处理项目的搜索信息
     *
     */
    public function summaryGrid($tpl = 'user/summary/wait_list',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

        if(input::getInput("mix.declare_year"))
            $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";

        //将搜索条件保存以备打印或者导出
        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['summarys']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['summarys']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['summarys']['orderStr'] = base64_encode($addSql);
        }
//		dd($addWhere);
        //显示页面
        $form_vars = array('search','declare_year','declare_month','department_id','department_id','statement');

        $this->view->set("pager",sf::getModel('Summarys')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

    /**
     * 集中处理项目的搜索信息
     *
     */
    public function platformGrid($tpl = 'platform/wait_list',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

        if(input::getInput("mix.declare_year"))
            $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";

        //将搜索条件保存以备打印或者导出
        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['platforms']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['platforms']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['platforms']['orderStr'] = base64_encode($addSql);
        }
//		dd($addWhere);
        //显示页面
        $form_vars = array('search','declare_year','declare_month','department_id','department_id','statement');

        $this->view->set("pager",sf::getModel('Platforms')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

}