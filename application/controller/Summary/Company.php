<?php
namespace App\controller\Summary;
use App\Controller\BaseController;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Company extends BaseController
{
    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/view/');
    }
    
    public function index()
    {
        $this->gird();
    }

    public function wait_list()
    {
        $this->gird('company/wait_list',"`company_id` = '".input::getInput('session.roleuserid')."' and statement  = 2");
    }

    public function submit_list()
    {
        $this->gird('company/submit_list',"`company_id` = '".input::getInput('session.roleuserid')."' and statement>2");
    }

    public function back_list()
    {
        $this->gird('company/back_list',"`company_id` = '".input::getInput('session.roleuserid')."' and statement = 3");
    }

    public function gird($tpl = 'company/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        $form_vars = array('subject','corporation_name');
        $this->view->set("pagers",sf::getModel('Summarys')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

    /**
     * 受理
     */
    public function doAccept()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput('mix.id'));
        //如果项目不存在
        if($summary->isNew()) $this->page_debug(lang::get('The project is not found!'));
//        if(strtotime($summary->getGuide(true)->getEndAt()) < time()){
//            $this->page_debug('时间已经截止');
//        }
        $summary->setConfigs(array('file.summary' => ''));
        $summary->setStatement(5);
        $summary->save();
        sf::getModel("historys")->addHistory($summary->getSummaryId(),'依托单位已审核','summary');//保存历史记录
        $this->page_debug('已审核',getFromUrl());
    }


    /**
     * 驳回
     */
    function doRejected()
    {
        $summary = sf::getModel("Summarys")->selectBySummaryId(input::getInput("mix.id"));
        if($summary->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if(input::getInput("post.content")){
            $summary->setStatement(3);
            $summary->save();
            sf::getModel("historys")->addHistory($summary->getSummaryId(),'依托单位退回！'.'<br/>'.input::getInput("post.content"),'summary');
            $this->refresh('已退回！');
        }
        $this->view->set("summary",$summary);
        $this->view->apply("inc_body","company/note");
        $this->view->display("page_blank");
    }


}