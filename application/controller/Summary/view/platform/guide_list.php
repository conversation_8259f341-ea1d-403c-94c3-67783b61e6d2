<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                年度考核填报向导
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <?php include('search_part.php');?>
                </div>
                <div class="box">
                    <div class="no-padding no-margin panel panel-default">
                        <form id="validateForm" name="validateForm" method="post" action="">
                            <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
                                <thead>
                                <tr>
                                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th><?=getColumnStr('平台名称','subject')?></th>
                                    <th><?=getColumnStr('依托单位','corporation_id')?></th>
                                    <th><?=getColumnStr('主管部门','department_id')?></th>
                                    <th width="100">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($platform = $pager->getObject()):?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$platform->getPlatformId()?>" /></td>
                                        <td>
                                            <?=$platform->getMark()?><?=link_to("apply/platform/show/id/".$platform->getPlatformId(),$platform->getSubject(),array('target'=>"_blank"))?>
                                        </td>
                                        <td><?=$platform->getCorporationName()?></td>
                                        <td><?=$platform->getDepartmentName()?></td>
                                        <td>
                                            <?=Button::setUrl(site_url("summary/platform/create/platform_id/".$platform->getPlatformId()))->setIcon('edit')->setClass('btn-alt-primary')->window('填写')?>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="12" align="right"><span class="pager_bar">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></span></td>
                                </tr>
                                </tfoot>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

