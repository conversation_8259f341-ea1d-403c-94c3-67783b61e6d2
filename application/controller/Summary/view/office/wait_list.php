<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                待审核的年度考核
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                <!--
                <?=Button::setName('审核选中项')->setEvent('validateForm.action=\''.site_url("summary/office/doAccept").'\';$(\'#validateForm\').submit()')->setIcon('check')->button()?>
                <?=Button::setName('退回选中项')->setEvent('validateForm.action=\''.site_url("summary/office/doRejected").'\';$(\'#validateForm\').submit()')->setIcon('undo')->setClass('btn-alt-danger')->button()?>
                -->
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <?php include('search_part.php');?>
                </div>
                <div class="box">
                    <div class="no-padding no-margin panel panel-default">
                        <form id="validateForm" name="validateForm" method="post" action="">
                            <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
                                <thead>
                                <tr>
                                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th width="50">详</th>
                                    <th><?=getColumnStr('平台名称','subject')?></th>
                                    <th width="150"><?=getColumnStr('填报年度','declare_year')?></th>
                                    <th><?=getColumnStr('依托单位','corporation_id')?></th>
                                    <th><?=getColumnStr('主管部门','department_id')?></th>
                                    <th width="170">状态</th>
                                    <th width="170" class="text-center">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($summary = $pagers->getObject()):?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$summary->getSummaryId()?>" /></td>
                                        <td><label class="fold_bar" onclick="fold.toggle('<?=$pagers->getIndex()?>')">+</label></td>
                                        <td>
                                            <?=$summary->getMark()?><?=link_to("apply/summary/show/id/".$summary->getSummaryId(),$summary->getSubject(),array('target'=>"_blank"))?>
                                        </td>
                                        <td><?=$summary->getDeclareYear()?></td>
                                        <td><?=$summary->getCompanyName()?></td>
                                        <td><?=$summary->getDepartmentName()?></td>
                                        <td><?=$summary->getState()?></td>
                                        <td align="center">
                                            <?=Button::setName('审核')->setUrl(site_url("summary/office/doAccept/id/".$summary->getSummaryId()))->setIcon('check')->link()?>
                                            <?=Button::setName('退回')->setTitle('退回年度考核')->setUrl(site_url("summary/office/doRejected/id/".$summary->getSummaryId()))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                                        </td>
                                    </tr>
                                    <tr class="fold_body">
                                        <td colspan="12">
                                            <?php include('more_part.php') ?>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="12" align="right"><span class="pager_bar">&nbsp;<?=$pagers->fromto().$pagers->navbar(10)?></span></td>
                                </tr>
                                </tfoot>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

