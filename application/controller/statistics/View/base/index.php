<script language="javascript" type="text/javascript" src="<?=site_path("js/datepicker/WdatePicker.js")?>"></script>

<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                基本统计
            </h3>
            <div class="block-options">
                <?=Button::setUrl(site_url('statistics/base/index/declare_year/'.$declareYear.'/level/'.$level.'/update/yes'))->setEvent('btnLoading(this)')->link('更新统计数据')?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
              <div class="btn-group btn-group-sm" role="group">
              <p style="clear:both;"></p>
              </div>
              <div class="search">
              <?php include_once('search_part.php')?>
            </div>
              <div class="box">
                <h4><?=$declareYear?>年<?=$level!='ALL'?$level:''?>创新平台项目申报数量统计（按平台类别）</h4>
                  <div style="width: 100%;overflow-x: auto;">
                      <table class="table table-bordered">
                          <tr>
                              <th class="text-center" style="width: 100px">&nbsp;</th>
                              <th class="text-center">合计</th>
                              <?php
                              foreach($datas['province']['zk'] as $data):
                                  ?>
                                  <th class="text-center"><?=$data['subject']?></th>
                              <?php endforeach;?>
                          </tr>
                          <?php
                          $applyCountArr=array_column($datas['province']['zk'], 'apply_count');
                          $applyCountTotal=array_sum($applyCountArr);
                          $radicateCountArr=array_column($datas['province']['zk'], 'radicate_count');
                          $radicateCountTotal=array_sum($radicateCountArr);
                          ?>
                          <tr>
                              <td align="center">申报数</td>
                              <td align="center"><?=$applyCountTotal?></td>
                              <?php
                              foreach($datas['province']['zk'] as $data):
                                  ?>
                                  <td class="text-center"><?=$data['apply_count']?></td>
                              <?php endforeach;?>
                          </tr>
                          <tr>
                              <td align="center">立项数</td>
                              <td align="center"><?=$radicateCountTotal?></td>
                              <?php
                              foreach($datas['province']['zk'] as $data):
                                  ?>
                                  <td class="text-center"><?=$data['radicate_count']?></td>
                              <?php endforeach;?>
                          </tr>
                      </table>
                  </div>

                <h4><?=$declareYear?>年<?=$level!='ALL'?$level:''?>创新平台项目申报数量统计（按单位性质）</h4>
              <table class="table table-bordered">
                <tr>
                  <th class="text-center" style="width: 100px">&nbsp;</th>
                  <th class="text-center">合计</th>
                  <?php
                    foreach($datas['province']['xz'] as $data):
                  ?>
                  <th class="text-center"><?=$data['subject']?></th>
                  <?php endforeach;?>
                </tr>
                <tr>
                    <td align="center">申报数</td>
                    <td align="center"><?=$applyCountTotal?></td>
                    <?php
                    foreach($datas['province']['xz'] as $data):
                        ?>
                        <td class="text-center"><?=$data['apply_count']?></td>
                    <?php endforeach;?>
                </tr>
                <tr>
                    <td align="center">立项数</td>
                    <td align="center"><?=$radicateCountTotal?></td>
                    <?php
                    foreach($datas['province']['xz'] as $data):
                        ?>
                        <td class="text-center"><?=$data['radicate_count']?></td>
                    <?php endforeach;?>
                </tr>
              </table>
              </div>
            </div>
        </div>
    </div>
</div>
<script>
    function btnLoading(me)
    {
        $(me).attr("disabled","disabled");
        $(me).addClass("disabled");
        $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 统计中...');
    }
</script>
