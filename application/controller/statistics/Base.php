<?php
namespace App\controller\statistics;
use App\Controller\BaseController;
use Sofast\Core\config;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Base extends BaseController
{
    private $view = NULL;
    private $declareYear = NULL;
    private $level = '省级';
    private $mark = 'base';

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/View/');
        $this->declareYear = config::get('current_declare_year');
        if(input::getMix('declare_year')) $this->declareYear = input::getMix('declare_year');
        if(input::getMix('level')) $this->level = input::getMix('level');
        switch ($this->level){
            case "国家级":
                $this->mark .= '_nation';
                break;
            case "省级":
                $this->mark .= '_province';
                break;
            case "市州级":
                $this->mark .= '_city';
                break;
            case "县级":
                $this->mark .= '_country';
                break;
        }
    }

    public function index()
    {
        if(input::getMix('update')=='yes'){
            $datas = $this->doStat();
        }else{
            $datas = $this->getStat();
        }
        $this->view->set('datas',$datas);
        $this->view->set('declareYear',$this->declareYear);
        $this->view->set('level',$this->level);
        $this->view->apply("inc_body","base/index");
        $this->view->display("page");
    }

    private function getStat()
    {
        $statistic = sf::getModel('Statistics')->selectByMark($this->mark,$this->declareYear);
        if($statistic->isNew()){
            return $this->doStat();
        }
        return $statistic->getData();
    }

    private function doStat()
    {
        $db = sf::getLib('db');
        $declareYear = $this->declareYear;
        $level = $this->level;
        //按专科统计
        $whereStr = "is_test = 0";
        if($declareYear!='ALL'){
            $whereStr.=" and `declare_year` = '{$declareYear}'";
        }
        if($level!='ALL'){
            $whereStr.=" and `level` = '{$level}'";
        }

        $query = $db->query("select cat_id,count(cat_id) c from projects where {$whereStr} GROUP BY cat_id order by cat_id asc");
        while($row = $db->fetch_array($query)) {
            //平台类别
            $datas['province']['zk'][$row['cat_id']]['subject'] = getCatSubject($row['cat_id']);
            //申报数
            $datas['province']['zk'][$row['cat_id']]['apply_count'] = (int)$db->result_first("select count(*) c from projects where {$whereStr} and cat_id = '{$row['cat_id']}' and is_accept = 1");
            //立项数
            $datas['province']['zk'][$row['cat_id']]['radicate_count'] = (int)$db->result_first("select count(*) c from projects where {$whereStr} and cat_id = '{$row['cat_id']}' and statement in (29,30)");
        }
        //按片区统计
        $districts = ['chengdu'=>'成都片区','cb'=>'川北片区','cd'=>'川东片区','cn'=>'川南片区','cx'=>'川西片区'];
        foreach ($districts as $k=>$district){
            $applyCount= $db->result_first("select count(*) c from projects where {$whereStr} and district = '{$district}' and is_accept = 1");
            $radicateCount= $db->result_first("select count(*) c from projects where {$whereStr} and district = '{$district}' and statement IN (29,30)");
            $datas['province']['pq'][$k]['subject'] = $district;
            $datas['province']['pq'][$k]['apply_count'] = $applyCount;
            $datas['province']['pq'][$k]['radicate_count'] = $radicateCount;
        }
        //按单位性质统计
        $propertys = get_select_data('property');
        foreach ($propertys as $k=>$property){
            $applyCount= $db->result_first("select count(*) c from projects where {$whereStr} and corporation_id in (select user_id from corporations where property = '{$property}') and is_accept = 1");
            $radicateCount= $db->result_first("select count(*) c from projects where {$whereStr} and corporation_id in (select user_id from corporations where property = '{$property}') and statement IN (29,30)");
            $datas['province']['xz'][$k]['subject'] = $property;
            $datas['province']['xz'][$k]['apply_count'] = $applyCount;
            $datas['province']['xz'][$k]['radicate_count'] = $radicateCount;
        }

        $statistic = sf::getModel('Statistics')->selectByMark($this->mark,$declareYear);
        $statistic->setData($datas);
        $statistic->save();
        return $datas;
    }
}