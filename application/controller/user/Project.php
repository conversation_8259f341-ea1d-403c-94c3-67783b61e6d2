<?php
namespace App\Controller\User;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
use Sofast\Core\Config;
class project extends BaseController
{	
	private $tabs = array();
	private $project;

	public function load()
	{
		$this->hash = md5(input::getInput("mix.controller").input::getInput("mix.method"));
		$this->project = sf::getModel('Projects')->selectByProjectId(input::getInput('mix.id'));
		$this->tabs = [
		[
		'method' => 'about',
		'text' => '填写说明',
		'url' => site_url('user/project/about/id/' . $this->project->getProjectId())
		],
		[
		'method' => 'edit',
		'text' => '基本信息',
		'url' => site_url('user/project/edit/id/' . $this->project->getProjectId())
		],
		[
		'method' => 'content',
		'text' => '正文内容',
		'url' => site_url('user/project/content/id/' . $this->project->getProjectId())
		],
		 [
		 'method' => 'attachment',
		 'text' => '附件上传',
		 'url' => site_url('user/project/attachment/id/' . $this->project->getProjectId())
		 ],
		];
        //用户信息
		$user = sf::getModel("Users")->selectByUserId(input::getInput("session.roleuserid"));
		view::set('user', $user);
		view::set('method', input::getInput("mix.method"));
		view::set('tabs', $this->tabs);
	}
	function guide_list()
	{
		$data['user'] = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		$data['types'] = sf::getModel("types")->selectAll("is_show = '1' AND start_at <= '".date("Y-m-d H:i:s")."' AND end_at >= '".date("Y-m-d H:i:s")."' ",'ORDER BY cat_id ASC,end_at ASC');
		view::set($data);
		view::apply("inc_body","user/project/guide_list");
		view::display("page");
	}
	
	/**
	 * 填写中的项目列表
	 */
	public function deformity_list()
	{
		$this->grid("user/project/deformity_list","user_id = '".input::getInput("session.roleuserid")."' and statement IN ('0','1','3','6','12') ");
	}
	
	/**
	 * 被驳回的项目
	 */
	public function back_list()
	{
		$this->grid("user/project/back_list","user_id = '".input::getInput("session.roleuserid")."' and statement IN ('3','6','7','11','28') ");
	}
	
	/**
	 * 上报的项目列表
	 */
	public function submit_list()
	{
		$this->grid("user/project/submit_list","user_id = '".input::getInput("session.roleuserid")."' and statement > ".config::get("USER_WRITE")."");
	}
	
	/**
	 * 已经立项的项目列表
	 */
	public function radicate_list()
	{
		$this->grid("user/project/radicate_list","user_id = '".input::getInput("session.roleuserid")."' and statement = '".config::get("HAS_RADICATE")."'");
	}
	
	/**
	 * 已经立项的项目列表
	 */
	public function upload_list()
	{
		$this->grid("user/project/upload_list","user_id = '".input::getInput("session.roleuserid")."' and statement = '".config::get("HAS_RADICATE")."' AND is_shift = 2 ");
	}

    /**
     * 待修正的申报书
     */
    public function wait_modify_list()
    {
        $this->grid('user/project/wait_modify_list',"user_id = '".input::getInput("session.roleuserid")."' and cat_id = 271 and `statement` IN (20,22,24,26)");
    }
	
	/**
	 * 外协项目列表
	 */
	public function complete_list()
	{
		$this->grid("user/project/complete_list","user_id = '".input::getInput("session.roleuserid")."' and statement = 29 and state_for_out = 0");
	}


	/**
	 * 过期的项目
	 */
	public function expired_list()
	{
		$this->grid("user/project/submit_list","user_id = '".input::getInput("session.roleuserid")."' and is_wait = 3");
	}
	
	/**
	 * 删除草稿箱中的项目
	 */
	public function doDelete()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("get.id"));
		if($project->getUserId() != input::getInput("session.roleuserid"))
			$this->error(lang::get("You do not have permission to do it!"),getFromUrl());
        if(!in_array($project->getStatement(),[0,1,3,6,12])) $this->error('当前状态不允许删除',getFromUrl());
		if($project->delete()){
			sf::getModel("historys")->addHistory($project->getProjectId(),sprintf(lang::get('The project has been delete,which subject is "%s"!'),$project->getSubject()));
			$this->success(lang::get('Has been deleted!'),getFromUrl());
		}else $this->error("Error has happened!",getFromUrl());
	}
	
	/**
	 * 上报项目
	 */
	function doSubmit()
	{
		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
		if(!$project->getSubject() || !$project->getTypeId()) $this->page_debug(lang::get("Incomplete information!"),getFromUrl());
		if(input::getInput("post.id"))
		{
			if($project->getUserId() != input::getInput("session.roleuserid")) 
				$this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
			//判断项目验证是否通过
			if($message = $project->validate()) 
				$this->page_debug(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());

			//验证单位信息
			$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
			$unit = sf::getModel("Corporations")->selectByUserId($user->getCorporationId());
			$dept = sf::getModel("Departments")->selectByUserId($unit->getDepartmentId());
			$project->setCorporationId($unit->getUserId());
			$project->setCorporationName($unit->getSubject());
			//验证归口部门
			$project->setDepartmentId($dept->getUserId());
			$project->setDepartmentName($dept->getSubject());
			$project->setDeclareAt(date("Y-m-d H:i:s"));
			$statement = 2;
            $msg = lang::get("Has been submit!");
            //如果是待修正的省重申报书
            if($project->isModify()){
                if(strstr($project->getSubject(),'专家意见修改版')===false){
                    $project->setSubject($project->getSubject().'（专家意见修改版）');
                }
                $statement = 21;
                $msg = '申报资料修正后上报';
            }
            $project->setStatement($statement);	//待申报单位审核
			if($project->save())
			{
                $project->setConfigs("file.apply",'');
				sf::getModel("historys")->addHistory($project->getProjectId(),$msg);
				unset($_SESSION['declare']['project_id']);
				$this->success(lang::get("Has been submit!"),site_url("user/project/submit_list"));
			}
		}
		
		$data['msg'] = $project->validate();
		$data['project'] = $project;
		view::set($data);
		view::apply("inc_body","user/project/submit_do");
		view::display("page_main");
	}
	
	/**
	 * 上传任务书验收书
	 */
	function doUpload()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
		if(!$project->getSubject() || !$project->getTypeId()) $this->page_debug(lang::get("Incomplete information!"),getFromUrl());
		if(input::getInput("post.id"))
		{
			if($project->getUserId() != input::getInput("session.roleuserid")) 
				$this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
			
			$msg = array();
			if(input::getInput("post.task_book")){
				$project->setConfigs("file.task",trim(input::getInput("post.task_book")));
				$msg[] = '任务书上传成功';
			}
			
			if(input::getInput("post.complete_book")){
				$project->setConfigs("file.complete",trim(input::getInput("post.complete_book")));
				$msg[] = '验收书上传成功';
			}
			
			sf::getModel("historys")->addHistory($project->getProjectId(),implode("<br />",$msg));
			
			exit("<script>parent.location.reload();</script>");
		}
		
		$data['project'] = $project;
		view::set($data);
		view::apply("inc_body","user/project/upload");
		view::display("page");
	}
	
	/**
	 *
	 * 校验数据正确性
	 */
	function check($project)
	{
		
		$msg = array();
		if($project->getTotalMoney() < $project->getDeclareMoney()) $msg[] ='项目申报经费不能大于项目总经费！';
		if($project->getDeclareMoney() > 10000) $msg[] ='您的项目经费过于庞大，请确认您的经费单位是万元！';
		if(!$project->getCorporationId()) $msg[] ='您填写的申报单位不存在！';
		if(!$project->getDepartmentId()) $msg[] ='您填写的归口部门不存在！';
		
		return $msg;
	}
	//填写说明
	public function about()
	{
		view::apply("inc_body",'user/project/about');
		view::display('page_main');
	}
	//基本信息
	public function edit()
	{
		$project = sf::getModel('Projects')->selectByProjectId(input::getInput('mix.id'));
		if($data = input::getInput('post')){
			if($project->isNew()) $this->page_debug('未找到该项目信息！');
			if(is_numeric($data['out_money'])){
				$project->setOutMoney(trim($data['out_money']));
				$project->setStateForOut(1);
				$project->setWaitForCompany($project->getCorporation()->getLevel());
				$project->save();
			}else{
				$this->page_debug('输入信息有误，请重新输入！',getFromUrl());
			}
			$this->success(lang::get('Has been saved!'),getFromUrl());
		}
		view::set('project',$project);
		view::apply("inc_body",'user/project/edit');
		view::display('page_main');
	}

	//正文内容
	public function content()
	{
		$project = sf::getModel('Projects')->selectByProjectId(input::getInput('mix.id'));
		view::set('project',$project);
		view::apply('inc_body', 'user/project/content');
		view::display('page_main');
	}

	//外协申请表
	public function showOut()
	{
		$project = sf::getModel('Projects')->selectByProjectId(input::getInput('mix.id'));
		if ($project->isNew()) $this->page_debug('未找到该项目信息！',getFromUrl());
		$content = $project->getOutContent();
		view::set('project',$project);
		view::set('content',$content);
		view::apply('inc_body', 'user/project/wxsqb');
		view::apply('inc_body_end', 'user/project/wxsqb_end');
		view::display('user/project/out_review');
	}
	 /**
     * 临时预览
     */
	 public function preview()
	 {
	 	$project = sf::getModel('Projects')->selectByProjectId(input::getInput('mix.id'));
	 	if(!$project->getOutContent()){
	 		$this->page_debug('请上传正文内容文件！',getFromUrl());
	 	}
	 	if ($project->isNew()) $this->page_debug('未找到该项目信息！',getFromUrl());
	 	if($path = $project->getConfigs('file.out')){
	 		$this->jump(site_url($path));
	 	}    
	 	$pdf_path = $project->makeOutPDF();
	 	$pdf_path = str_replace(config::get('base_url'),'',$pdf_path);
	 	$project->setConfigs(['file.out'=>$pdf_path]);
	 	$this->jump(site_url($pdf_path));        
	 }
	 public function makepdf()
	 {
	 	$project = sf::getModel('Projects')->selectByProjectId(input::getInput('mix.id'));	 
	 	if(!$project->getOutContent()){
	 		$this->page_debug('请上传正文内容文件！',getFromUrl());
	 	}
	 	if ($project->isNew()) $this->page_debug('未找到该项目信息！');
	 	$pdf_path = $project->makeOutPDF();
	 	$pdf_path = str_replace(config::get('base_url'),'',$pdf_path);
	 	$project->setConfigs(['file.out'=>$pdf_path]);
	 	$this->jump(site_url($pdf_path));  
	 }

	/**
	 * 上报申请
	 */
	function doSubmitOut()
	{
		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
		if(!$project->getOutContent()){
			$this->page_debug('正文内容未上传，无法上报！',getFromUrl());
		}
		$project->setStateForOut(2);
        $project->setWaitForCompany($project->getCorporation()->getLevel());
		$project->save();
		sf::getModel("historys")->addHistory($project->getProjectId(),'上报项目外协申请表！');
        pushMsgToCompanyManager('你有一个项目外协申请等待审核','unit/project/wait_out');
		$this->success('上报成功！',site_url('user/project/out_list'));
	}

	/**
	 * 删除外协申请
	 */
	public function doDeleteOut()
	{
		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("get.id"));
		$project->setApprovalMoney(0);
		$project->setOutMoney(0);
		$project->setStateForOut(0);
		$project->save();
		sf::getModel("historys")->addHistory($project->getProjectId(),'删除项目外协申请表！');
		$this->page_debug('删除成功！',getFromUrl());
	}
	// 签字盖章附件
	public function upFile(){
		if(input::getInput('post') && $_FILES) {
			$upload = sf::getLib("upload","attachment",config::get("upload_path","./up_files/"),20971520,array('jpg','bmp','png','jpeg','pdf'));
			if($upload->upload())
			{
				$result = $upload->getSaveFileInfo();
				foreach($result as $files)
				{
					$attachementModel = sf::getModel('Filemanager');
					$attachementModel->setFileName($files['name']);
					$attachementModel->setFileSavename($files['savename']);
					$attachementModel->setFilePath($files['path']);
					$attachementModel->setFileSize($files['size']);
					$attachementModel->setFileExt($files['type']);
					$attachementModel->setFileMinetype($files['minetype']);
					$attachementModel->setUserId(input::session('roleuserid'));
					$attachementModel->setUserName(input::session('username'));
					$attachementModel->setItemId(input::getInput('mix.id'));
					$attachementModel->setFileNote(input::post('file_note'));
					$attachementModel->setItemType(input::post('item_type'));
					$attachementModel->setCreatedAt(date('Y-m-d H:i:s'));
					$id = $attachementModel->save();
					$result['id'][] = $id;
					$result['name'][] = $files['name'];
					$result['path'][] = $files['path'];
					// exit("<script>parent.location.reload();</script>");
				}
			}else{
				$this->page_debug($upload->getError(),getFromUrl());
			}
		}
		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
		view::set('project', $project);
		view::set('item_type', input::getInput('mix.item_type'));
		view::apply("inc_body","user/project/up_file");
		view::display("page_blank");
	}

//删除附件
	function deleteFile()
	{
		$file = sf::getModel("Filemanager",input::getInput("mix.id"));
		if($file->getUserId() != input::session('roleuserid'))
			$this->page_debug("您没有权限删除该文件！",getFromUrl());
		$file->delete();
		$this->page_debug("文件删除成功！",getFromUrl());
	}
	//查看文件
	function showFile()
	{
		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
		$attachments = $project->getAttachments(input::getInput('mix.item_type'));
		view::set('project', $project);
		view::set('attachments', $attachments);
		view::apply("inc_body","user/project/show_file");
		view::display("page_blank");
	}

    public function attachment()
    {
        $project = sf::getModel('Projects')->selectByProjectId(input::getInput('mix.id'));
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        view::set('project',$project);
        view::set('itemType','out_file');
        view::apply('inc_body', 'user/project/attachment');
        view::display('page_main');
    }

    public function review()
    {
        $project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->isNew()) $this->page_debug(lang::get('The project is not found!'));
        view::set("project",$project);
        view::apply("inc_body","user/project/review");
        view::display("page_blank");
    }
}