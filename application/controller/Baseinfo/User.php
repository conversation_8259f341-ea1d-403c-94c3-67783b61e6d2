<?php
namespace App\controller\Baseinfo;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class User extends BaseController
{

    public function index()
    {
        $this->gird('baseinfo/user/index',"`user_id` = '".input::getInput('session.roleuserid')."'");
    }

    public function guide()
    {
        if(input::post()){
            $this->jump(site_url('baseinfo/apply/edit?cat_id='.input::post('cat_id')));
        }
        view::apply("inc_body",'baseinfo/user/guide');
        view::display('page');
    }


    public function gird($tpl = 'baseinfo/user/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";

        $form_vars = array();
        view::set("pagers",sf::getModel('Platforms')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }

}