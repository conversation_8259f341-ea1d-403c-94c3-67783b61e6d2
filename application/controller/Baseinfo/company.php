<?php
namespace App\Controller\baseinfo;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class company extends BaseController
{
    public function index()
    {
        $this->gird();
    }

    public function wait_list()
    {
        $this->gird('baseinfo/company/wait_list','statement=2');
    }

    public function submit_list()
    {
        $this->gird('baseinfo/company/submit_list','statement>=5');
    }

    public function back_list()
    {
        $this->gird('baseinfo/company/back_list','statement IN (3,6)');
    }

    public function gird($tpl = 'baseinfo/company/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " AND `corporation_id` = '".input::getInput('session.roleuserid')."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        $form_vars = array('subject','corporation_name');
        view::set("pagers",sf::getModel('LabBases')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }

    /**
     * 受理
     */
    public function doAccept()
    {
        $base = sf::getModel("LabBases")->selectByLabId(input::getInput('mix.id'));
        //如果项目不存在
        if($base->isNew()) $this->page_debug(lang::get('The project is not found!'));
        $base->setStatement(9);
        $base->save();
        sf::getModel("historys")->addHistory($base->getLabId(),'依托单位已审核','base');//保存历史记录
        $this->page_debug('已审核',getFromUrl());
    }


    /**
     * 驳回
     */
    function doRejected()
    {
        $base = sf::getModel("LabBases")->selectByLabId(input::getInput("mix.id"));
        if($base->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if(input::getInput("post.content")){
            $base->setStatement(3);
            $base->save();
            sf::getModel("historys")->addHistory($base->getLabId(),'依托单位退回！'.'<br/>'.input::getInput("post.content"),'base');
            $this->refresh('已退回！');
        }
        view::set("base",$base);
        view::apply("inc_body","baseinfo/company/note");
        view::display("page_blank");
    }


}