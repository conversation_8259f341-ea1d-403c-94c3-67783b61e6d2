<?php
namespace App\Controller\baseinfo;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class office extends BaseController
{
    public function index()
    {
        $this->gird();
    }

    public function wait_list()
    {
        $this->gird('baseinfo/office/wait_list','statement  = 9');
    }

    public function accept_list()
    {
        $this->gird('baseinfo/office/accept_list','statement = 10');
    }

    public function back_list()
    {
        $this->gird('baseinfo/office/back_list','statement = 12');
    }

    public function gird($tpl = 'baseinfo/office/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        if(input::getMix('frontier')) $addWhere .= " AND `frontier` = '".input::getInput("mix.frontier")."' ";
        if(input::getMix('industry')) $addWhere .= " AND `industry` = '".input::getInput("mix.industry")."' ";
        $subjectIds = input::post("subject_ids");
        $subjectIds = array_filter($subjectIds);
        if($subjectIds){
            $subjectId = array_pop($subjectIds);
            $addWhere .= " AND `subject_id` like '".$subjectId."%' ";
        }
        $form_vars = array('subject','corporation_name','frontier','industry','subject_id');
        view::set("pagers",sf::getModel('LabBases')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::set('subject_ids',input::post("subject_ids"));
        view::apply("inc_body",$tpl);
        view::display($page);
    }

    /**
     * 受理
     */
    public function doAccept()
    {
        $base = sf::getModel("LabBases")->selectByLabId(input::getInput('mix.id'));
        //如果项目不存在
        if($base->isNew()) $this->page_debug(lang::get('The project is not found!'));
        $base->setStatement(10);
        $base->save();
        sf::getModel("historys")->addHistory($base->getLabId(),'科技厅已审核','base');//保存历史记录
        $this->page_debug('已审核',getFromUrl());
    }

    /**
     * 驳回
     */
    function doRejected()
    {
        $base = sf::getModel("LabBases")->selectByLabId(input::getInput("mix.id"));
        if($base->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if(input::getInput("post.content")){
            $base->setStatement(12);
            $base->save();
            sf::getModel("historys")->addHistory($base->getLabId(),'科技厅退回！'.'<br/>'.input::getInput("post.content"),'base');
            $this->refresh('已退回！');
        }
        view::set("base",$base);
        view::apply("inc_body","baseinfo/office/note");
        view::display("page_blank");
    }

    function change(){
        $department_id = input::getInput("post.department_id");
        $id = input::getInput("post.id");

        if(!$department_id) exit('NO1');
        if(!$id) exit('NO2');
        //修改单位
        $base = sf::getModel("LabBases")->selectByLabId($id);
        $old_name = $base->getDepartmentName();
        $base->setDepartmentId($department_id);
        $base->setDepartmentName(sf::getModel("Departments")->selectByUserId($department_id)->getSubject());
        sf::getModel("historys")->addHistory(input::getInput('session.roleuserid'),"主管部门由“".$old_name."”调整为“".$base->getDepartmentName()."”。",'departments');
        if($base->save()) exit('OK');
        else exit("NO3");
    }

    function export()
    {
        $head = ['序号','实验室类型','类别','实验室名称','成立时间','第一依托单位','其他依托单位','主管部门','联系人','联系电话','地址','填报日期','学科分类','前沿领域分类','所属产业','研究方向','实验室主任','职称','职务','研究领域','状态'];
        $body = [];
        $addwhere = '1 ';
        if(input::getInput('mix.statement')){
            $addwhere.=' and statement = '.input::getInput('mix.statement');
        }
        $bases = sf::getModel('LabBases')->selectAll($addwhere,"order by `declare_at` asc");
        $i=0;
        while($base =  $bases->getObject()){
            $body[$i][] = $i+1;
            $body[$i][] = $base->getCategory();
            $body[$i][] = $base->getType();
            $body[$i][] = $base->getSubject();
            $body[$i][] = $base->getBuildAt()?:'';
            $body[$i][] = $base->getCorporationName();
            $body[$i][] = $base->getOtherCorporation();
            $body[$i][] = $base->getDepartmentName();
            $body[$i][] = $base->getLinkmanName();
            $body[$i][] = $base->getLinkmanMobile();
            $body[$i][] = $base->getAddress()
            $body[$i][] = $base->getDeclareAt() ? $base->getDeclareAt('Y 年 m 月 d 日') : $base->getCreatedAt('Y 年 m 月 d 日');
            $body[$i][] = $base->getGbSubjectStr();
            $body[$i][] = $base->getFrontier();
            $body[$i][] = $base->getIndustry();
            $body[$i][] = $base->getResearchDirectionStr();
//            $researchDirections = $base->getResearchDirectionArray();
//            $body[$i][] = $researchDirections[0]?:'无';
//            $body[$i][] = $researchDirections[1]?:'无';
//            $body[$i][] = $researchDirections[2]?:'无';
//            $body[$i][] = $researchDirections[3]?:'无';
            $leader = $base->getLeader(true);
            $body[$i][] = $leader->getUserName();
            $body[$i][] = $leader->getUserTitle()?:'无';
            $body[$i][] = $leader->getUserDuty()?:'无';
            $body[$i][] = $leader->getResearchArea()?:'无';
            $body[$i][] = strip_tags($base->getState());
            $i++;
        }
        $filename ='基本资料列表';
        excel_out($head,$body,$filename,$filename);
    }

    public function exportWord()
    {
        $base = sf::getModel("LabBases")->selectByLabId(input::getInput("mix.id"));
        if($base->isNew()) $this->page_debug(lang::get('The project is not found!'));
        $tpl = $base->getCategory()=='省级重点实验室' ? 'baseinfo/office/export_word' : 'baseinfo/office/export_word_nation';
        view::set('base',$base);
        header("Content-type:application");
        header("Content-Disposition: attachment; filename=".$base->getSubject()."基础信息.doc");
        $output = view::getContent($tpl);
        exit($output);
    }

    public function exportPdf()
    {
        $base = sf::getModel("LabBases")->selectByLabId(input::getInput("mix.id"));
        if($base->isNew()) $this->page_debug(lang::get('The project is not found!'));
        $base->makePDF();
    }

}