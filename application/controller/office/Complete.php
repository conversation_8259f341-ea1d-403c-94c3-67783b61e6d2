<?php
namespace App\controller\office;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Core\config;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class Complete extends BaseController
{
    function index()
    {
        $this->grid('office/complete/index');
    }

    function wait_list()
    {
        $backUrl = site_url('office/complete/wait_list/ordermode/asc');
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $backUrl.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        if(input::getInput('mix.department_id')){
            $backUrl.='/department_id/'.input::getInput('mix.department_id');
        }
        if(input::getInput('mix.user_role')){
            $backUrl.='/user_role/'.input::getInput('mix.user_role');
        }
        $showMax = 16;
        if(input::getInput('mix.showmax')){
            $showMax = intval(input::getInput('mix.showmax'));
        }
        $_SESSION['backurl'] = $backUrl;
        $this->grid('office/complete/wait_list',"statement = 9 AND (tags = '' or tags is null)",$showMax);
    }

    function submit_list()
    {
        $this->grid('office/complete/submit_list'," statement  >=10");
    }

    function rejected_list()
    {
        $this->grid('office/complete/rejected_list'," statement = 12");
    }

    function radicate_list()
    {
        $this->grid('office/complete/radicate_list',"statement = 10");
    }

    /**
     * 填报开关
     */
    public function open_list()
    {
        $addWhere = "statement = 29 ";
        $this->projectGrid('office/complete/open_list',$addWhere);
    }

    /**
     * 签署中期评估
     */
    function doSubmit()
    {
        if(input::getInput("post.select_id"))
            $ids = input::getInput("post.select_id");
        else
            $ids[] = input::getInput("get.id");
        if(!$ids[0])
            $this->page_debug('请至少选择一个项目！',getFromUrl());
        for($i=0,$n=count((array)$ids);$i<$n;$i++){
            $complete = sf::getModel("Stages")->selectByStageId($ids[$i]);
            $complete->setTags('');
            $complete->setStatement(10);
            if($complete->save())
                sf::getModel("historys")->addHistory($complete->getProjectId(),"中期评估已审核",'complete');
        }
        $this->success('中期评估审核成功！',getFromUrl());
    }

    function doGenerate()
    {
        if(input::getInput("post.select_id"))
            $ids = input::getInput("post.select_id");
        else
            $ids[] = input::getInput("get.id");
        if(!$ids[0])
            $this->page_debug('请至少选择一个项目！',getFromUrl());
        $guideId = 0;
        if(input::post('guide_id')){
            $guideId = input::post('guide_id');
        }
        $workerId = (int)input::post('worker_id');
        $createUserCount = 0;
        $createUnitCount = 0;
        $createGatherCount = 0;
        for($i=0,$n=count((array)$ids);$i<$n;$i++){
            $project = sf::getModel("Projects")->selectByProjectId($ids[$i]);
            if($project->isNew()) continue;
            $complete = sf::getModel('Completes')->selectByProjectId($project->getProjectId(),$guideId);
            if($complete->isNew()){
                $complete->setCatId($project->getCatId());
                $complete->setGuideId($guideId);
                $complete->setWorkerId($workerId);
                $guide = $complete->getGuide();
                $complete->setStartYear($guide->getStartYear());
                $complete->setEndYear($guide->getEndYear());
                $complete->setRadicateStartYear($guide->getRadicateStartYear());
                $complete->setRadicateEndYear($guide->getRadicateEndYear());
                $complete->setBuildStartYear($guide->getBuildStartYear());
                $complete->setBuildEndYear($guide->getBuildEndYear());
                $complete->setBuildEndAt($guide->getBuildEndAt());
                $complete->setSubjectCode($project->getSubjectCode());
                $complete->setSubject($project->getSubject());
                $complete->setDeclareYear($project->getDeclareYear());
                $complete->setCompleteYear($guide->getYear());
                $complete->setDistrict($project->getDistrict());
                $complete->setLevel($project->getLevel());
                $complete->setTypeCurrentGroup($guide->getCurrentGroup());
                $complete->setUserId($project->getUserId());
                $complete->setUserName($project->getUserName());
                $complete->setCompanyId($project->getCorporationId());
                $complete->setCompanyName($project->getCorporationName());
                $complete->setDepartmentId($project->getDepartmentId());
                $complete->setDepartmentName($project->getDepartmentName());
                $complete->setConfigs('path.apply',"engine/completer");
                $complete->save();
                $createUserCount++;
            }
            //生成国家委在川委直属医疗机构初评汇总表
            $result = $complete->createUnitReport();
            if($result===true) $createUnitCount++;
            //生成市州初评汇总表
            $result = $complete->createGatherReport();
            if($result===true) $createGatherCount++;
        }
        $this->success('已生成验收报告'.$createUserCount.'份，医院报告'.$createUnitCount.'份，市州报告'.$createGatherCount.'份！',site_url('office/complete/open_list'));
    }

    function generateCompany()
    {
        if(input::getInput("post.select_id"))
            $ids = input::getInput("post.select_id");
        else
            $ids[] = input::getInput("get.id");
        if(!$ids[0])
            $this->page_debug('请至少选择一个项目！',getFromUrl());
        $createUnitCount = 0;
        for($i=0,$n=count((array)$ids);$i<$n;$i++){
            $project = sf::getModel("Projects")->selectByProjectId($ids[$i]);
            if($project->isNew()) continue;
            $complete = sf::getModel('Completes')->selectByProjectId($project->getProjectId());
            if($complete->isNew()){
                $this->error('请先生成医院验收报告');
            }
            //生成国家委在川委直属医疗机构初评汇总表
            $result = $complete->createUnitReport();
            if($result===true) $createUnitCount++;
        }
        $this->success('已生成医院报告'.$createUnitCount.'份！',site_url('office/complete/open_list'));
    }

    function generateGather()
    {
        if(input::getInput("post.select_id"))
            $ids = input::getInput("post.select_id");
        else
            $ids[] = input::getInput("get.id");
        if(!$ids[0])
            $this->page_debug('请至少选择一个项目！',getFromUrl());
        $createGatherCount = 0;
        for($i=0,$n=count((array)$ids);$i<$n;$i++){
            $project = sf::getModel("Projects")->selectByProjectId($ids[$i]);
            if($project->isNew()) continue;
            $complete = sf::getModel('Completes')->selectByProjectId($project->getProjectId());
            if($complete->isNew()){
                $this->error('请先生成医院验收报告');
            }
            //生成市州初评汇总表
            $result = $complete->createGatherReport();
            if($result===true) $createGatherCount++;
        }
        $this->success('已生成市州报告'.$createGatherCount.'份！',site_url('office/complete/open_list'));
    }

    function generate()
    {
        if(input::getInput("post.select_id"))
            $ids = input::getInput("post.select_id");
        else
            $ids[] = input::getInput("get.id");
        if(!$ids[0])
            $this->page_debug('请至少选择一个项目！',getFromUrl());
        view::set("ids",$ids);
        view::apply("inc_body","office/complete/generate");
        view::display("page");

    }

    /**
     * 开启月报填报
     */
    function doOpenOnlyOne()
    {
        $project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->isNew()){
            $this->error('找不到该项目！');
        }
        if($project->getStatement()!=29){
            $this->error('项目当前状态不允许开启！');
        }
        $project->setCompleteOpen(1);
        $project->setUpdatedAt(date('Y-m-d H:i:s'));
        $project->save();
        sf::getModel("Historys")->addHistory($project->getProjectId(),'开启验收填报');
        $this->success('开启成功！',site_url('office/complete/open_list'));
    }
    /**
     * 关闭月报填报
     */
    function doCloseOnlyOne()
    {
        $project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->isNew()){
            $this->error('找不到该项目！');
        }
        $project->setCompleteOpen(0);
        $project->save();
        sf::getModel("Historys")->addHistory($project->getProjectId(),'关闭验收填报');
        $this->success('关闭成功！',site_url('office/complete/open_list'));
    }

    /**
     * 审核中期评估
     */
    function doAcceptOnlyOne()
    {
        $complete = sf::getModel("Completes")->selectByProjectId(input::getInput("mix.id"));
        if($complete->isNew()){
            $this->error('找不到该项目！');
        }
        if(input::getInput("post.content")){
            $complete->setTags('');
            $complete->setStatement(10);
            $complete->save();
            $msg = "验收报告已审核";
            sf::getModel("Historys")->addHistory($complete->getProjectId(),$msg.'<br/>'.input::getInput("post.content"),'complete');
            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/审核通过！';</script>");
        }
        view::set("complete",$complete);
        view::set("msg","审核通过！");
        view::apply("inc_body","office/complete/note_submit");
        view::display("page_blank");
    }

    /**
     * 驳回中期评估——单个
     */
    function doRejectedOnlyOne()
    {
        $complete = sf::getModel("Completes")->selectByProjectId(input::getInput("mix.id"));
        if(input::getInput("post.content"))
        {
            $complete->setTags('');
            $complete->setStatement(12);
            $complete->save();
            sf::getModel("historys")->addHistory($complete->getProjectId(),"验收报告已被退回"."<br />".input::getInput("post.content"),'complete');
            $backUrl = $_SESSION['backurl'] ?: site_url('office/complete/wait_list/ordermode/asc');
            if($_SESSION['backurl']) unset($_SESSION['backurl']);
            exit("<script>top.location.href='{$backUrl}/_save/yes/_msg/退回成功！';</script>");
        }
        view::set("complete",$complete);
        view::apply("inc_body","office/complete/back");
        view::display("page_blank");
    }
    /**
     * 驳回中期评估——多个
     */
    function doRejected()
    {
        if(input::getInput("post.select_id"))
            $ids = input::getInput("post.select_id");
        else
            $ids[] = input::getInput("get.id");
        if(!$ids[0])
            $this->page_debug('请至少选择一个项目！',getFromUrl());
        for($i=0,$n=count($ids);$i<$n;$i++){
            $complete = sf::getModel("Completes")->selectByProjectId($ids[$i]);
            $complete->setTags('');
            $complete->setStatement(12);
            $complete->save();
            sf::getModel("historys")->addHistory($complete->getProjectId(),"验收报告已被退回",'complete');
        }
        $this->success('退回成功！',getFromUrl());
    }

    function doPass()
    {
        $url = site_url('office/complete/wait_list/ordermode/asc');
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $url.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        if(input::getInput('mix.showmax')){
            $url.='/showmax/'.input::getInput('mix.showmax');
        }
        $url = input::session('backurl')?:$url;

        $complete = sf::getModel("Completes")->selectByProjectId(input::mix("id"));
        if($complete->isNew()) $this->page_debug('找不到该项目');

        if(input::getInput("post.content")){
            $complete->setTags('验收报告预受理');
//            $complete->setNote(input::getInput("post.content"));
            $complete->setUpdatedAt(date('Y-m-d H:i:s'));
            $complete->save();
            addHistory($complete->getProjectId(),"预受理：".input::getInput("post.content"),'complete',6);
            exit("<script>top.location.href='{$url}/_save/yes/_msg/处理成功！';</script>");
        }
        //原因表单
        $form = Form::load('office/complete/doPass')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($complete->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'处理意见'])->setWidth(12)->setAttribute('class','no-margin')->setValue('形式审查通过'))
            ->addItem(Form::hidden('id',$complete->getProjectId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

    function doNeedBack()
    {
        $url = site_url('office/complete/wait_list/ordermode/asc');
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $url.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        if(input::getInput('mix.showmax')){
            $url.='/showmax/'.input::getInput('mix.showmax');
        }
        $url = input::session('backurl')?:$url;

        $complete = sf::getModel("Completes")->selectByProjectId(input::getInput("mix.id"));
        if($complete->isNew()) $this->page_debug('找不到该项目');
        if(input::getInput("post.content")){
            $complete->setTags('验收报告预退回');
//            $complete->setNote(input::getInput("post.content"));
            $complete->setUpdatedAt(date('Y-m-d H:i:s'));
            $complete->save();
            addHistory($complete->getProjectId(),"预退回：原因：".input::getInput("post.content"),'complete',6);
            exit("<script>top.location.href='{$url}/_save/yes/_msg/处理成功！';</script>");
        }
        //原因表单
        $form = Form::load('office/complete/doNeedBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($complete->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('形式审查不合格'))
            ->addItem(Form::hidden('id',$complete->getProjectId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

    /**
     * 形审合格的项目列表
     */
    public function pass_list()
    {
        $backUrl = site_url('office/complete/pass_list/ordermode/asc');
        $showMax = 16;
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $backUrl.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        if(input::getInput('mix.department_id')){
            $backUrl.='/department_id/'.input::getInput('mix.department_id');
        }
        if(input::getInput('mix.showmax')){
            $showMax = intval(input::getInput('mix.showmax'));
        }
        $_SESSION['backurl'] = $backUrl;

        $_GET['orderfield'] = 'declare_at';
        $_GET['ordermode'] = 'asc';

        $addwhere = "`statement` = 9 AND tags = '验收报告预受理'";

        $this->grid('office/complete/pass_list',$addwhere,$showMax);
    }

    /**
     * 需退回的项目列表
     */
    public function needback_list()
    {
        $backUrl = site_url('office/complete/needback_list/ordermode/asc');
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $backUrl.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        $showMax = 16;
        if(input::getInput('mix.showmax')){
            $showMax = intval(input::getInput('mix.showmax'));
        }
        $_SESSION['backurl'] = $backUrl;

        $_GET['orderfield'] = 'declare_at';
        $_GET['ordermode'] = 'asc';

        $addwhere = "`statement` = 9 AND tags = '验收报告预退回'";

        $this->grid('office/complete/pass_list',$addwhere,$showMax);
    }

    public function stat()
    {
        if(input::post()){
            $declareYear = input::post('declare_year');
            $url = 'process/index/index/type/stage/declare_year/'.$declareYear;
            $this->jump(site_url($url));
        }
        $declareYear = input::getMix('declare_year')?:getLastYear();
        view::set("declareYear",$declareYear);
        view::apply("inc_body","office/complete/stat");
        view::display("page");
    }

    /**
     * 集中处理项目的搜索信息
     *
     */
    public function grid($tpl = 'office/complete/wait_list',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

        if(input::getInput("mix.declare_year"))
            $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        if(input::getInput("mix.stage_year"))
            $addWhere .= " AND `stage_year` = '".input::getInput("mix.stage_year")."' ";
        if(input::getInput("mix.type_current_group"))
            $addWhere .= " AND `type_current_group` = '".input::getInput("mix.type_current_group")."' ";
        if(input::getInput("mix.department_id"))
            $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
        if(input::getInput("mix.user_role"))
            $addWhere .= " AND `user_role` = '".input::getInput("mix.user_role")."' ";
        if(input::getInput("mix.statement"))
            $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";
        if(input::getInput("mix.subject_code"))
            $addWhere .= " AND `subject_code` = '".input::getInput("mix.subject_code")."' ";
        //按指南搜索
        if(input::getInput("mix.guide_id")){
            $guide = sf::getModel('Guides',input::getInput("mix.guide_id"));
            $guide_ids = $guide->getChildren();
            $addWhere .= " AND `guide_id` IN (".implode(',',$guide_ids).") ";
        }

        //将搜索条件保存以备打印或者导出
        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['completes']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['completes']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['completes']['orderStr'] = base64_encode($addSql);
        }
//		dd($addWhere);
        //显示页面
        $form_vars = array('search','declare_year','complete_year','type_current_group','department_id','department_id','statement','user_role','subject_code','guide_id');

        view::set("pager",sf::getModel('Completes')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }

    public function projectGrid($tpl = 'office/month/open_list',$addWhere = '1',$showMax=16)
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

        if(input::getInput("mix.declare_year"))
            $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        //else $addWhere .= " AND `declare_year` = '".date('Y')."' ";

        input::getInput("mix.radicate_year") && $addWhere .= " AND `radicate_year` = '".input::getInput("mix.radicate_year")."' ";
        input::getInput("mix.type_current_group") && $addWhere .= " AND `type_current_group` = '".input::getInput("mix.type_current_group")."' ";
        input::getInput("mix.level") && $addWhere .= " AND `level` = '".input::getInput("mix.level")."' ";
        input::getInput("mix.type_id") && $addWhere .= " AND `type_id` = '".input::getInput("mix.type_id")."' ";
        input::getInput("mix.subject_code") && $addWhere .= " AND `subject_code` = '".input::getInput("mix.subject_code")."' ";
        input::getInput("mix.district") && $addWhere .= " AND `district` = '".input::getInput("mix.district")."' ";
        input::getInput("mix.cat_id") && $addWhere .= " AND `cat_id` = '".input::getInput("mix.cat_id")."' ";

        //按指南搜索
        if(input::getInput("mix.guide_id")){
            $guide = sf::getModel('Guides',input::getInput("mix.guide_id"));
            $guide_ids = $guide->getChildren();
            $addWhere .= " AND `guide_id` IN (".implode(',',$guide_ids).") ";
        }
        input::getInput("mix.office_id") && $addWhere .= " AND `office_id` = '".input::getInput("mix.office_id")."' ";
        input::getInput("mix.subject_id") && $addWhere .= " AND `subject_id` LIKE '".input::getInput("mix.subject_id")."%' ";
        input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
        if(input::getInput("mix.statement")==2029){
            $addWhere .= " AND `statement` IN (20,28,29,30) ";
        }elseif(input::getInput("mix.statement")==2930){
            $addWhere .= " AND `statement` IN (29,30) ";
        }elseif(input::getInput("mix.statement")){
            $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";
        }
        input::getInput("mix.type_subject") && $addWhere .= " AND `type_id` = '".input::getInput("mix.type_subject")."' ";
        input::getInput("mix.project_domain") && $addWhere .= " AND `project_domain` = '".input::getInput("mix.project_domain")."' ";
        input::getInput("mix.state_for_budget_book") && $addWhere .= " AND `state_for_budget_book` = '".input::getInput("mix.state_for_budget_book")."' ";
        input::getInput("mix.state_for_plan_book") && $addWhere .= " AND `state_for_plan_book` = '".input::getInput("mix.state_for_plan_book")."' ";
        input::getInput("mix.state_for_complete_book") && $addWhere .= " AND `state_for_complete_book` = '".input::getInput("mix.state_for_complete_book")."' ";
        input::getInput("mix.state_for_out") && $addWhere .= " AND `state_for_out` = '".input::getInput("mix.state_for_out")."' ";
        input::getInput("mix.state_for_apply") && $addWhere .= " AND `state_for_apply` = '".input::getInput("mix.state_for_apply")."' ";
        //按照时间检索
        input::getInput("mix.declare_start") && $addWhere .= " AND unix_timestamp(`declare_at`) >= unix_timestamp('".input::getInput("mix.declare_start")."') ";
        input::getInput("mix.declare_end") && $addWhere .= " AND unix_timestamp(`declare_at`) <= unix_timestamp('".input::getInput("mix.declare_end")."') ";
        input::getInput("mix.update_start") && $addWhere .= " AND unix_timestamp(`updated_at`) >= unix_timestamp('".input::getInput("mix.update_start")."') ";
        input::getInput("mix.update_end") && $addWhere .= " AND unix_timestamp(`updated_at`) <= unix_timestamp('".input::getInput("mix.update_end")."') ";

        if(input::getInput("mix.is_shift")) $addWhere .= " AND `is_shift` = '".input::getInput("mix.is_shift")."' ";

        if(input::getInput("mix.is_wait")) $addWhere .= " AND `is_wait` = '".input::getInput("mix.is_wait")."' ";

        if(input::getInput("mix.is_short")) $addWhere .= " AND `is_short` = '".(input::getInput("mix.is_short")-1)."' ";
        if(input::getInput("mix.is_minority")) $addWhere .= " AND `is_minority` = '".(input::getInput("mix.is_minority")-1)."' ";
        if(strlen(input::getMix('diagnosis_code'))==2){
            $addWhere .= " AND `diagnosis_code` like '".input::getInput("mix.diagnosis_code")."%' ";
        }
        if(strlen(input::getMix('diagnosis_code'))>2){
            $addWhere .= " AND `diagnosis_code` = '".input::getInput("mix.diagnosis_code")."' ";
        }
        if(input::getInput("mix.is_test")==1) $addWhere .= " AND `is_test` = '0' ";

        //变更申请状态
        if(input::getInput("post.change_statement")) $addWhere .= " AND project_id in (select project_id from project_changes where statement = ".input::getInput("post.change_statement").") ";

        //将搜索条件保存以备打印或者导出
        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['projects']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['projects']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['projects']['orderStr'] = base64_encode($addSql);
        }else{
//			$_SESSION['queryOptions'] = '';
//			$_SESSION['url_str'];
//			$addWhere = base64_decode($_SESSION['projects']['sqlStr']);
        }
        //显示页面
        $form_vars = array('search','level','declare_year','radicate_year','type_current_group','type_id','cat_id','subject_id','department_id','statement','office_id','field','state_for_complete_book','state_for_plan_book','state_for_budget_book','is_wait','is_shift','guide_id','subject_code','district','is_short','is_minority','diagnosis_code','is_test');

        view::set("pager",sf::getModel('Projects')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display('page');
    }

}