<?php
namespace App\Controller\Office;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Lang;

class radicate extends BaseController
{	
	
	function index()
	{
		$this->grid('office/radicate/index',"statement BETWEEN 29 AND 30 ");
	}


	public function confirm_list()
	{
		$addWhere = "statement = 24 AND is_wait = 1 ";
		$this->grid('office/radicate/confirm_list',$addWhere);
	}
	
	/**
	 * 建议支持的项目
	 */
	public function wait_list()
	{
		$addWhere = "statement = 20 ";
		input::getInput("post.declare_year") &&	$addWhere .= "AND declare_year = '".input::getInput("post.declare_year")."' ";	
		input::getInput("post.type_current_group") && $addWhere .= "AND type_current_group = '".input::getInput("post.type_current_group")."' ";	
		$this->grid('office/radicate/wait_list',$addWhere);
	}

	/**
	 * 科技厅受理的项目列表(用于直接立项)
	 */
	public function accept_list()
	{
		$addWhere = "statement >= 10 AND statement < 28 AND type_id IN (select type_id from office_type_list where office_id = '".input::getInput("session.office_id")."') ";
		if(input::getInput("post.declare_year")) $addWhere .= "AND declare_year = '".input::getInput("post.declare_year")."' ";
		if(input::getInput("post.type_current_group")) $addWhere .= "AND type_current_group = '".input::getInput("post.type_current_group")."' ";
		
		$this->grid('office/radicate/wait_list',$addWhere);
	}
	
	/**
	 * 立项的项目
	 */
	public function submit_list()
	{
		$addWhere = "statement = 29 ";
		// $addWhere = "statement = 29 AND type_id IN (select type_id from office_type_list where office_id = '".input::getInput("session.office_id")."') ";
		if(input::getInput("post.declare_year")) $addWhere .= "AND declare_year = '".input::getInput("post.declare_year")."' ";
		if(input::getInput("post.type_current_group")) $addWhere .= "AND type_current_group = '".input::getInput("post.type_current_group")."' ";
		$this->grid('office/radicate/submit_list',$addWhere);
	}
	
	/**
	 * 未立项的项目
	 */
	public function rejected_list()
	{
		$addWhere = "statement = 28 ";
		// $addWhere = "statement = 28 AND type_id IN (select type_id from office_type_list where office_id = '".input::getInput("session.office_id")."') ";
		if(input::getInput("post.declare_year")) $addWhere .= "AND declare_year = '".input::getInput("post.declare_year")."' ";
		if(input::getInput("post.type_current_group")) $addWhere .= "AND type_current_group = '".input::getInput("post.type_current_group")."' ";
		
		$this->grid('office/radicate/rejected_list',$addWhere);
	}
	
	/**
	 * 不立项
	 */
	public function doRejectedOnlyOne()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
		if($project->isNew()) $this->page_debug("找不到该项目！",getFromUrl());
		if(input::getInput("post.content"))
		{
			//清空立项编号
			$project->setRadicateId('');
			//设置状态
			$project->setStatement(28);
			$project->save();
			sf::getModel("historys")->addHistory($project->getProjectId(),"项目未立项！<br />".input::getInput("post.content"));
			exit("<script>parent.location.reload();</script>");	
		}
		view::set("project",$project);
		view::apply("inc_body","office/radicate/back");
		view::display("page_blank");
		
	}

    function doRejected()
    {
        if(input::getInput("post.select_id"))
            $ids = input::getInput("post.select_id");
        else
            $ids[] = input::getInput("get.id");
        for($i=0,$n=count($ids);$i<$n;$i++){
            $project = sf::getModel("projects")->selectByProjectId($ids[$i]);
            if($project->isNew()) continue;
            //清空立项编号
            $project->setRadicateId('');
            //设置状态
            $project->setStatement(28);
            $project->save();
            sf::getModel("historys")->addHistory($project->getProjectId(),"项目未立项！<br />".input::getInput("post.content"));
        }
        $this->page_debug("操作成功！",getFromUrl());
    }

	/**
	 * 项目终止
	 */
	public function doStop()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
		if(input::getInput("post.content"))
		{
			//设置状态
			$project->setStatement(40);
			$project->save();
			sf::getModel("historys")->addHistory($project->getProjectId(),"项目终止。<br />".input::getInput("post.content"));
			exit("<script>parent.location.reload();</script>");	
		}
		view::set("project",$project);
		view::apply("inc_body","office/radicate/back");
		view::display("page_blank");
		
	}
	/**
	 * 返回待评审
	 */
	public function backWaitReview()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
		$project->setStatement(config::get('ENGINEER_ACCEPT',10));
		$project->save();
		sf::getModel("historys")->addHistory($project->getProjectId(),'返回待评审！');
		// $project->sendMessage(sprintf(lang::get('MSG.P102'),$project->getSubject()));//发送短信
		$this->page_debug('操作成功！',getFromUrl());
	}

	/**
	 * 返回建议支持
	 */
	public function backSupport()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
		$project->setRadicateId('');
		$project->setRadicateMoney(0);		
		$project->setRadicateYear('');		
		$project->setRadicateAt('');		
		$project->setStatement(20);
		$project->save();
		sf::getModel("historys")->addHistory($project->getProjectId(),'返回待立项！');
		// $project->sendMessage(sprintf(lang::get('MSG.P102'),$project->getSubject()));//发送短信
		$this->success('操作成功！',getFromUrl());
	}
	
	/**
	 * 立项操作
	 */
	public function doAcceptOnlyOne()
	{
		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
		if(input::getInput("post.subject") && input::getInput("post.id"))
		{
			if(!input::post("radicate_id")){
				$this->error('请填写立项编号！',getFromUrl());
			}
			if(!input::post("subject")){
				$this->error('请填写项目名称！',getFromUrl());
			}
			if($project->IfRadicateId(input::getInput("post.radicate_id"))==false){
				$project->setSubject(input::post("subject"));
				$project->setStatement(29);
				$project->setRadicateId(input::getInput("post.radicate_id"));
				$project->setRadicateYear(input::getInput("post.radicate_year")?input::getInput("post.radicate_year"):date("Y"));
                $project->setNote(input::post('note'));
                $project->setUpdatedAt(date("Y-m-d H:i:s"));
                $project->setRadicateAt(date("Y-m-d H:i:s"));
				$project->save();
				sf::getModel("historys")->addHistory($project->getProjectId(),lang::get("The project has been radicate!"));
				exit("<script>parent.location.reload();</script>");	
			}else{
				$this->error("项目立项编号【".input::getInput("post.radicate_id")."】已存在！",getFromUrl());
			}
		}
        $radicateId = $project->generateRadicateId();
		view::set("project",$project);
		view::set("radicate_str",$radicateId);
		view::apply("inc_body","office/radicate/accept");
		view::display("page_blank");
	}



    function doAccept()
    {
        if(input::getInput("post.select_id"))
            $ids = input::getInput("post.select_id");
        else
            $ids[] = input::getInput("get.id");
        for($i=0,$n=count($ids);$i<$n;$i++){
            $project = sf::getModel("projects")->selectByProjectId($ids[$i]);
            if($project->isNew()) continue;
            $project->setStatement(29);
            $radicateId = $project->generateRadicateId();
            $project->setRadicateId($radicateId);
            $project->setRadicateYear(config::get('current_declare_year',date('Y')));
            $project->setUpdatedAt(date("Y-m-d H:i:s"));
            $project->setRadicateAt(date("Y-m-d H:i:s"));
            $project->save();
            sf::getModel("historys")->addHistory($project->getProjectId(),lang::get("The project has been radicate!"));
        }
        $this->page_debug("操作成功！",getFromUrl());
    }
	
	/**
	 * 更改处室 (立项之后，填写计划任务书发现分流错误)
	 */
	public function doPartitionByProject()
	{
		if(input::getInput("post.office_id") && input::getInput("post.select_id"))
		{
			$office = sf::getModel("categorys",input::getInput("post.office_id"),'office');
			if($office->isNew()) $this->page_debug(lang::get("It's error!"),getFromUrl());
			
			if(sf::getModel("projects")->setChangeOfficeByIds(implode("','",input::getInput("post.select_id")),$office->getId(),$office->getSubject())) $msg = lang::get("Has been set up!");
			else $msg = lang::get("It's error!");
			sf::getModel("historys")->addHistory('system',sprintf(lang::get('Triage to offices by project,which name is "%s"!'),$office->getSubject()));
			$this->page_debug($msg,getFromUrl());
		}
		$this->page_debug(lang::get("Must select a project!"),getFromUrl());
	}

	/**
	 * 项目搜索
	 */
	public function search()
	{
		$this->grid('office/search/index');
	}
	
	/**
	 * 执行查重
	 */
	function doCheck()
	{
		if(input::getInput("post.num")){
			$num = (int)input::getInput("post.num");
			if((int)$_SESSION['progress-bar']['total'] == 0){
				$_SESSION['progress-bar']['total'] = sf::getModel("Projects")->selectAll("statement = 24 AND is_wait = 1 AND id >= '".$num."' ","ORDER BY id DESC")->getTotal();//读取总数用于统计百分比
				$_SESSION['progress-bar']['num'] = 0;
			}
			$pager = sf::getModel("Projects")->selectAll("statement = 24 AND is_wait = 1 AND id >= '".$num."' ","ORDER BY id ASC",1);
			while($project = $pager->getObject())
			{
				if($project->isNew()) continue;
				sf::getLib("check",$project)->save();//保存重复信息
				//进度计算
				$num = $project->getId() + 1;
				$_SESSION['progress-bar']['num'] += 1;
				$progress = round($_SESSION['progress-bar']['num']/$_SESSION['progress-bar']['total'],5)*100;
			}
			if($pager->getTotal() > 0){
				$has = 1;	
			}else{
				$has = $num = 0;
				$progress = 100;
				$_SESSION['progress-bar'] = array();//清空
			}
			exit("{has:$has,num:$num,progress:$progress}");
		}
		view::apply("inc_body","office/radicate/doCheck");
		view::display("page_blank");
	}

	/**
	 * 批量查看查重结果
	 */
	function showCheck()
	{
		$pager = sf::getModel("projects")->selectAll("statement = 24 AND is_wait = 1 AND is_error IN ('2','3')","ORDER BY id ASC");
		view::set("pager",$pager);
		view::apply("inc_body","office/radicate/showcheck");
		view::display("page_blank");
	}

	/**
	 * 下载查重结果
	 */
	function downloadCheck()
	{
		$pager = sf::getModel("projects")->selectAll("statement = 24 AND is_wait = 1 AND is_error IN ('2','3')","ORDER BY id ASC");
		view::set("pager",$pager);
		$htmlStr = view::getContent("office/radicate/downloadCheck");
		header("Content-type:application");
		header("Content-Disposition: attachment; filename=".time().'.doc');
		exit($htmlStr);
	}
	
	/**
	 * 发送查重结果，直接将项目设置为业务处室推荐
	 */
	function doSend()
	{
		if(input::getInput("post.i_konw"))
		{
			$this->checkCsrf();//检查跨站攻击
			sf::getLib("db")->query("UPDATE projects SET statement = 25 WHERE statement = 24 AND is_wait = 1");
			sf::getModel("historys")->addHistory("system","确认查重结果","project",1);
			exit("<script>parent.location.reload();</script>");
		}
		view::apply("inc_body","office/radicate/dosend");
		view::display("page_blank");
	}

}
?>