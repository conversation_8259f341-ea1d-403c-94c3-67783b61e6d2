<?php
namespace App\Controller\Office;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Lang;
class accept extends BaseController
{		
	
	/**
	 * 上报的项目列表
	 */
	public function index()
	{
		// $addWhere = 'statement = 6 AND is_shift <> 2 ';
		$addWhere = "statement = ".config::get('ENGINEER_WAIT') . " AND is_shift <> 2 ";
		$this->grid('office/accept/wait_list',$addWhere);
		// $this->grid('office/accept/index',"type_id IN (select type_id from office_type_list where office_id = '".input::getInput("session.office_id")."')");
	}
	
	/**
	 * 等待审核的项目列表
	 */
	public function wait_list()
	{
        $backUrl = site_url('office/accept/wait_list/ordermode/asc');
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $backUrl.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        if(input::getInput('mix.district')){
            $backUrl.='/district/'.input::getInput('mix.district');
        }
        if(input::getInput('mix.subject_code')){
            $backUrl.='/subject_code/'.input::getInput('mix.subject_code');
        }
        if(input::getInput('mix.department_id')){
            $backUrl.='/department_id/'.input::getInput('mix.department_id');
        }
        $showMax = 16;
        if(input::getInput('mix.showmax')){
            $showMax = intval(input::getInput('mix.showmax'));
        }
        $_SESSION['backurl'] = $backUrl;

        $_GET['orderfield'] = 'declare_at';
        $_GET['ordermode'] = 'asc';


        $addwhere = "statement = 9 AND tags = ''";

        $this->grid('office/accept/wait_list',$addwhere,$showMax);
	}
	
	/**
	 * 上报的项目列表
	 */
	public function submit_list()
	{
		$addWhere = 'statement = 10 ';
		$this->grid('office/accept/submit_list',$addWhere);
	}
	
	/**
	 * 驳回的项目列表
	 */
	public function rejected_list()
	{
		$addWhere = 'statement = 12 ';
		$this->grid('office/accept/rejected_list',$addWhere);
	}

    /**
     * 形审合格的项目列表
     */
    public function pass_list()
    {
        $backUrl = site_url('office/accept/pass_list/ordermode/asc');
        $showMax = 16;
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $backUrl.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        if(input::getInput('mix.district')){
            $backUrl.='/district/'.input::getInput('mix.district');
        }
        if(input::getInput('mix.subject_code')){
            $backUrl.='/subject_code/'.input::getInput('mix.subject_code');
        }
        if(input::getInput('mix.department_id')){
            $backUrl.='/department_id/'.input::getInput('mix.department_id');
        }
        if(input::getInput('mix.showmax')){
            $showMax = intval(input::getInput('mix.showmax'));
        }
        $_SESSION['backurl'] = $backUrl;

        $_GET['orderfield'] = 'declare_at';
        $_GET['ordermode'] = 'asc';

        $addwhere = "statement = 9 AND tags = '预受理' and declare_year = '".config::get('current_declare_year',date('Y'))."'";

        $this->grid('office/accept/pass_list',$addwhere,$showMax);
    }

    /**
     * 需退回的项目列表
     */
    public function needback_list()
    {
        $backUrl = site_url('office/accept/needback_list/ordermode/asc');
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $backUrl.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        $showMax = 16;
        if(input::getInput('mix.showmax')){
            $showMax = intval(input::getInput('mix.showmax'));
        }
        $_SESSION['backurl'] = $backUrl;

        $_GET['orderfield'] = 'declare_at';
        $_GET['ordermode'] = 'asc';

        $addwhere = "statement = 9 AND tags = '预退回' and declare_year = '".config::get('current_declare_year',date('Y'))."'";

        $this->grid('office/accept/pass_list',$addwhere,$showMax);
    }

    /**
     * 待修正的申报书
     */
    public function wait_modifiy_list()
    {
        $this->grid('office/accept/wait_modifiy_list','statement = 20 and cat_id = 271');
    }

    /**
     * 已修正的申报书
     */
    public function modifiy_list()
    {
        $this->grid('office/accept/modifiy_list','statement = 26 and cat_id = 271');
    }

    /**
     * 已推荐的申报书
     */
    public function finish_list()
    {
        $this->grid('office/accept/finish_list','statement = 29 and cat_id = 271');
    }
	
	/**
	 * 受理项目
	 */
	function doAccept()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		for($i=0,$n=count($ids);$i<$n;$i++){
			$project = sf::getModel("projects")->selectByProjectId($ids[$i]);
			//如果项目不存在
			if($project->isNew()) continue;
			//如果存在受理编号就不再重新生成
			if(!$project->getAcceptId()){
				//判断项目编号是否重复，重复则可能错处，直接重置新的序号
				$accept_id = '';
                $accept_id = sf::getModel('Types',$project->getTypeId())->getSerialNumber($project->getDeclareYear(),$project->getCatTag(),$project->getGuide()->getMark(),-1,$project->getTypeCurrentGroup());
				//分管部门
				$office_id = $project->getGuide(true)->getOfficeId();
				if($office_id){
					$project->setOfficeId($office_id);
					$project->setOfficeSubject(sf::getModel("categorys",$office_id,'office')->getSubject());
				}
				$project->setAcceptId($accept_id);
			}
            $project->setTags('');
            $project->setStatement(10);
            $project->setIsAccept(1);
            $project->save();

			sf::getModel("historys")->addHistory($project->getProjectId(),lang::get("Has been accepted!")."申报编号为：".$project->getAcceptId());//保存历史记录
			// $project->sendMessage(sprintf(lang::get('MSG.P101'),$project->getSubject(),$project->getAcceptId()));//发送短信
		}
		// $this->page_debug(lang::get("Has been accepted!")."受理编号为：".$project->getAcceptId(),getFromUrl());
		$this->success('审核成功！',getFromUrl());
	}
	/**
	 * 审核项目
	 */
	function doAcceptOnlyOne()
	{
        $project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->isNew()){
            $this->error('找不到该项目！');
        }
        if(input::getInput("post.content")){
            if(!$project->getAcceptId()){
//                $accept_id = sf::getModel('Types',$project->getTypeId())->getSerialNumber($project->getDeclareYear(),$project->getDepartment()->getLabel(),$project->getGuide()->getMark(),-1,$project->getTypeCurrentGroup());
                $accept_id = sf::getModel('Types',$project->getTypeId())->getSerialNumber($project->getDeclareYear(),$project->getCatTag(),$project->getGuide()->getMark(),-1,$project->getTypeCurrentGroup());
                $project->setAcceptId($accept_id);
            }
            //分管部门
            $office_id = $project->getGuide(true)->getOfficeId();
            if($office_id){
                $project->setOfficeId($office_id);
                $project->setOfficeSubject(sf::getModel("categorys",$office_id,'office')->getSubject());
            }
            $project->setTags('');
            $project->setStatement(10);
            $project->setIsAccept(1);
            $project->save();
            $msg = lang::get("Has been accepted!");
            sf::getModel("Historys")->addHistory($project->getProjectId(),$msg.'<br/>'.input::getInput("post.content"));
            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/审核通过！';</script>");
        }
        view::set("project",$project);
        view::set("msg","同意受理！");
        view::apply("inc_body","office/accept/note_submit");
        view::display("page_blank");
	}
	/**
	 * 驳回项目
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		for($i=0,$n=count($ids);$i<$n;$i++){
			$project = sf::getModel("projects")->selectByProjectId($ids[$i]);
            $project->setStatement(config::get('PLAN_BACK',12));
            $project->setIsAccept(0);
            $project->setTags('');
			$project->save();
			sf::getModel("historys")->addHistory($project->getProjectId(),lang::get("Has been rejected!"));
			$project->sendMessage(sprintf(lang::get('MSG.P102'),$project->getSubject()));//发送短信
		}
		$this->page_debug(lang::get("Has been rejected!"),getFromUrl());	
	}

    function doRejectedOnlyOne()
    {
        $project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->isNew()) $this->error('没有找到该项目',getFromUrl());
        if(input::getInput("post.content")){
            if(in_array($project->getStatement(),[26,29])) $project->setStatement(20);
            else $project->setStatement(12);
            $project->setIsAccept(0);
            $project->setTags('');
            $project->save();
            sf::getModel("historys")->addHistory($project->getProjectId(),lang::get("Has been rejected!").'<br/>'.input::getInput("post.content"));
            //发送短信
            $project->sendMessage(sprintf(lang::get('MSG.P102'),$project->getSubject())."原因：".input::getInput("post.content"));

            $backUrl = $_SESSION['backurl'] ?: site_url('office/accept/wait_list/ordermode/asc');
            if($_SESSION['backurl']) unset($_SESSION['backurl']);
            exit("<script>top.location.href='{$backUrl}/_save/yes/_msg/退回成功！';</script>");
        }

        //原因表单
        $reason = ($project->getTags()=='预退回' && $project->getNote()) ? $project->getNote() : '退回修改';
        $form = Form::load('office/accept/doRejectedOnlyOne')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($project->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue($reason))
            ->addItem(Form::hidden('id',$project->getProjectId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

    function doPass()
    {
        $url = site_url('office/accept/wait_list/ordermode/asc');
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $url.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        if(input::getInput('mix.cat_id')){
            $url.='/cat_id/'.input::getInput('mix.cat_id');
        }
        if(input::getInput('mix.type_id')){
            $url.='/type_id/'.input::getInput('mix.type_id');
        }
        if(input::getInput('mix.showmax')){
            $url.='/showmax/'.input::getInput('mix.showmax');
        }
        $url = input::session('backurl')?:$url;

        $project = sf::getModel("projects")->selectByProjectId(input::mix("id"));
        if($project->isNew()) $this->page_debug('找不到该项目');

        if(input::getInput("post.content")){
            $project->setTags('预受理');
            $project->setNote(input::getInput("post.content"));
            $project->setUpdatedAt(date('Y-m-d H:i:s'));
            $project->save();
            addHistory($project->getProjectId(),"预受理：".input::getInput("post.content"),'project',6);
            exit("<script>top.location.href='{$url}/_save/yes/_msg/处理成功！';</script>");
        }
        //原因表单
        $form = Form::load('office/accept/doPass')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($project->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'处理意见'])->setWidth(12)->setAttribute('class','no-margin')->setValue('形式审查通过'))
            ->addItem(Form::hidden('id',$project->getProjectId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

    function doNeedBack()
    {
        $url = site_url('office/accept/wait_list/ordermode/asc');
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $url.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        if(input::getInput('mix.cat_id')){
            $url.='/cat_id/'.input::getInput('mix.cat_id');
        }
        if(input::getInput('mix.type_id')){
            $url.='/type_id/'.input::getInput('mix.type_id');
        }
        if(input::getInput('mix.showmax')){
            $url.='/showmax/'.input::getInput('mix.showmax');
        }
        $url = input::session('backurl')?:$url;

        $project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->isNew()) $this->page_debug('找不到该项目');
        if(input::getInput("post.content")){
            $project->setTags('预退回');
            $project->setNote(input::getInput("post.content"));
            $project->setUpdatedAt(date('Y-m-d H:i:s'));
            $project->save();
            addHistory($project->getProjectId(),"预退回：原因：".input::getInput("post.content"),'project',6);
            exit("<script>top.location.href='{$url}/_save/yes/_msg/处理成功！';</script>");
        }
        //原因表单
        $form = Form::load('office/accept/doNeedBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($project->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('形式审查不合格'))
            ->addItem(Form::hidden('id',$project->getProjectId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

	/**
	 * 返回待受理
	 */
	function doBackWait()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
		$project->setStatement(config::get('PLAN_WAIT',9));
		$project->save();
		sf::getModel("historys")->addHistory($project->getProjectId(),'返回待受理！');
			// $project->sendMessage(sprintf(lang::get('MSG.P102'),$project->getSubject()));//发送短信
		$this->page_debug('操作成功！',getFromUrl());
	}

    /**
     * 进入已评审（待推荐）
     */
    function doWaitRecommend()
    {
        $project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));
        //如果项目不存在
        if($project->isNew()) $this->page_debug('没有找到该项目');
        $project->setStatement(20);
        $project->save();
        sf::getModel("historys")->addHistory($project->getProjectId(),'进入已评审！');
        $this->success('操作成功！',getFromUrl());
    }

    /**
     * 推荐
     */
    public function doRadicateByone()
    {
        $project = sf::getModel("Projects")->selectByProjectId(input::getInput('mix.id'));
        //如果项目不存在
        if($project->isNew()) $this->page_debug(lang::get('The project is not found!'));
        $project->setStatement(29);
        $project->setRadicateAt(date('Y-m-d'));
        $project->setRadicateYear(date('Y'));
        $project->save();
        sf::getModel("historys")->addHistory($project->getProjectId(),'科技厅已推荐','project');//保存历史记录
        $this->page_debug('已推荐',getFromUrl());
    }

    public function review()
    {
        $project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
        if($project->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if(input::getInput("post.content")){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $project->setText(input::post("content"),'review');
            $project->setStatement(20);
            $project->save();
            sf::getModel("historys")->addHistory($project->getProjectId(),'已上传评审意见，等待修正！'.'<br/>'.input::getInput("post.content"),'project');
            $this->refresh('已上传评审意见，等待修正！');
        }
        view::set("project",$project);
        view::apply("inc_body","office/accept/review");
        view::display("page_blank");
    }
}
?>