<?php
namespace App\controller\Archive;
use App\Controller\BaseController;
use App\Facades\PDF;
use Sofast\Core\config;
use Sofast\Core\input;
use Sofast\Core\sf;
use Sofast\Support\template;
use Sofast\Support\View;

class Apply extends BaseController
{

    private $view = NULL;
    private $project = NULL;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/View/');
        $this->project = sf::getModel('Projects')->selectByProjectId(input::getMix('id'));
    }

    public function show()
    {
        if($this->project->isNew()) $this->error('找不到该项目',getFromUrl());
        $this->jump(site_path('Documents/'.$this->project->getDeclareYear().'/apply/'.$this->project->getProjectId().'.html'));
    }

    function output()
    {
        @ini_set('memory_limit', '200M');//设置最大使用内存为200M
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        //已经生成的直接下载
        if($this->project->enablePrint('apply')){
            if($file_name = $this->project->getConfigs("file.apply")){//如果已经生成文件，直接打开下载
                @header("Location:".site_path('up_files/'.$file_name));exit();
            }
        }
        $htmlStr = file_get_contents(WEBROOT.'/Documents/'.$this->project->getDeclareYear().'/apply/'.$this->project->getProjectId().'/output.html');
//        $this->project->setWidgetConfigs('action',array('download'=>'yes','worker'=>'worker'));

        $hash = substr(md5(strip_tags($htmlStr)),0,20);
        //替换一下
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        $this->project->setShortUrl($hash,'apply');
        //拆分内容
        $htmlArray = explode('{{file:',$htmlStr);
        $title = '四川省临床重点专科建设项目';
        if($this->project->getCatId()==233) $title='“川渝共建”临床重点专科建设项目';
        if($this->project->getCatId()==235) $title='四川省区域医疗中心建设项目';
        if(in_array($this->project->getCatId(),[225,230,231])) $title=$this->project->getTopGuideSubject();
        $pdf =  PDF::setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="50%" style="border: 0px solid #fff;">'.$title.'申报书</td><td width="50%" style="text-align: right;border: 0px solid #fff;">'.$this->project->getSubject().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;">- {PAGENO} -</td><td width="33%" style="text-align: right;border: 0px solid #fff;"><barcode code="'.site_url('m/p/h/'.$hash).'" type="QR" disableborder="0" size="0.6" error="M" /></td></tr></table>')
            ->setTitle($this->project->getSubject())
            ->setSubject($this->project->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->project->getUserName());
        //设置输出内容
        for($i=0,$n=count($htmlArray);$i<$n;$i++){
            if($i > 0){//分析插入文件
                //如果是WORD文档将直接传输URL地址
                if(substr($htmlArray[$i],0,4) == 'http') $file = strstr($htmlArray[$i],'}}',true);
                else $file = config::get("upload_path").strstr($htmlArray[$i],'}}',true);
                $pdf->setContent($file,'file');

                $content = substr(strstr($htmlArray[$i],'}}'),2);
                $pdf->setContent($content);
            }else $pdf->setContent($htmlArray[$i]);
        }
        //保存的文件名
        $savename = time().mt_rand(1000,9999).'.pdf';
        $file_path = date("Y").'/'.date("m").'/'.$savename;
        $dir_path = WEBROOT."/up_files/".dirname($file_path);
        if (!file_exists($dir_path)) { //检查目录是否存在
            if (!@mkdir($dir_path, 0755, true)) {
                exit('不能创建目录: ' . \dirname($file_path));
            }
        }
        if($this->project->enablePrint('apply')){
            //水印
            $pdf->setWaterMark($title.'申报书正式版',0.1);
            $pdf->save(WEBROOT."/up_files/".$file_path);
            //保存到附件表内
            $filemanager = sf::getModel("ProjectAttachments");
            $filemanager->setFileName($this->project->getSubject()."-申报书");
            $filemanager->setFileSavename($savename);
            $filemanager->setFilePath($file_path);
            $filemanager->setFileSize(filesize(WEBROOT."/up_files/".$file_path));
            $filemanager->setFileExt('.pdf');
            $filemanager->setFileMinetype('application/pdf');
            $filemanager->setUserId($this->project->getUserId());
            $filemanager->setUserName($this->project->getUserName());
            $filemanager->setCreatedAt(date("Y-m-d H:i:s"));
            $filemanager->setItemId($this->project->getProjectId());
            $filemanager->setItemType('apply');
            $filemanager->setFileNote("项目《".$this->project->getSubject().'》的申报书PDF存档');
//			$filemanager->setServerIp(trim($_SERVER['SERVER_ADDR']));
            $filemanager->save();
            //记录到项目配置
            $this->project->setConfigs("file.apply",$file_path);
            addHistory($this->project->getProjectId(),'下载申报书','project',1);
            @header("Location:".site_path('up_files/'.$file_path));
            exit();
        }else{
            //水印
            $pdf->setWaterMark($title.'申报书预览版',0.1);
            $pdf->show();
            exit();
        }
    }

}
