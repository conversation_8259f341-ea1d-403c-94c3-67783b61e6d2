<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseLinkMaps;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class LinkMaps extends BaseLinkMaps
{
    /**
     * 按照链接名称获取链接记录
     * @param  string $url_name [description]
     * @return [type]           [description]
     */
    public function selectByUrlName($url_name = '')
    {
        if (! $url_name) {
            $url_name = getRandString(10);
        }

        $db = sf::getLib("Db");
        //多条记录登录最后登录的记录
        $result = $db->fetch_first("SELECT * FROM " . $this->table . " WHERE `url_name` = '" . $url_name . "' ORDER BY updated_at DESC");
        if ($result) {
            $this->fillObject($result);
            return $this;
        } else {
            $this->setUrlName($url_name);
        }
        return $this;
    }

    /**
     * 获取需要的角色
     * @return [type] [description]
     */
    public function getRoles()
    {
        $_roles = explode('|', $this->getNeedRoles());
        if (is_array($_roles)) {
            return $_roles;
        } else {
            return [];
        }
    }

    /**
     * 获取需要的角色
     * @return [type] [description]
     */
    public function setRoles($v)
    {
        if (is_array($v)) {
            $v = implode('|', $v);
        }
        parent::setNeedRoles($v);
        return $this;
    }

    /**
     * 获取跳转目标链接
     * @return [type] [description]
     */
    public function getUrl()
    {
        $_url = $this->getTargetUrl();
        if (stripos($_url, '://') !== false) {
            return $_url;
        } else {
            return site_url($_url);
        }
    }

    /**
     * 获取快捷链接
     * @return [type] [description]
     */
    public function getLink()
    {
        return site_url("link/target?url=" . $this->getUrlName());
    }
}