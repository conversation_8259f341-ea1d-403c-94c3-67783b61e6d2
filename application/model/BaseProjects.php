<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseProjects extends BaseModel
{
	private $id;
	private $is_error  = '0';
	private $worker_id  = '0';
	private $guide_id  = '0';
	private $subject_code;
	private $type_id  = '0';
	private $cat_id;
	private $level  = '省级';
	private $project_id;
	private $corporation_id;
	private $user_id;
	private $department_id;
	private $department_name;
	private $type_current_group  = '1';
	private $accept_id;
	private $radicate_id;
	private $subject;
	private $subtitle;
	private $declare_year;
	private $declare_at;
	private $end_at;
	private $start_at;
	private $real_start_at;
	private $real_end_at;
	private $build_year;
	private $radicate_year;
	private $radicate_at;
	private $radicate_money;
	private $province_money;
	private $money_use_rate  = '0';
	private $statement  = '0';
	private $subject_id;
	private $subject_name;
	private $subject_ids;
	private $office_id  = '0';
	private $group_id  = '0';
	private $type_group;
	private $user_name;
	private $corporation_name;
	private $cooperation;
	private $linkman;
	private $mobile;
	private $office_subject;
	private $type_subject;
	private $score;
	private $has_expert  = '0';
	private $state_for_plan_book  = '0';
	private $state_for_complete_book  = '0';
	private $state_for_stage  = '0';
	private $task_open  = '0';
	private $stage_open  = '0';
	private $inspect_open  = '0';
	private $review_open  = '0';
	private $complete_open  = '0';
	private $is_report  = '0';
	private $is_accept  = '0';
	private $is_wait  = '0';
	private $is_delay  = '0';
	private $is_test  = '0';
	private $is_migrate  = '0';
	private $configs;
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	private $guide_name;
	private $complete_apply_at;
	private $stop_at;
	private $complete_at;
	private $note;
	private $tags  = '';
	public $table = "projects";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getIsError()
	{
		return $this->is_error;
	}

	public function getWorkerId()
	{
		return $this->worker_id;
	}

	public function getGuideId()
	{
		return $this->guide_id;
	}

	public function getSubjectCode($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_code,0,$len,"utf-8");
			else return substr($this->subject_code,0,$len);
		}
		return $this->subject_code;
	}

	public function getTypeId()
	{
		return $this->type_id;
	}

	public function getCatId()
	{
		return $this->cat_id;
	}

	public function getLevel($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->level,0,$len,"utf-8");
			else return substr($this->level,0,$len);
		}
		return $this->level;
	}

	public function getProjectId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->project_id,0,$len,"utf-8");
			else return substr($this->project_id,0,$len);
		}
		return $this->project_id;
	}

	public function getCorporationId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->corporation_id,0,$len,"utf-8");
			else return substr($this->corporation_id,0,$len);
		}
		return $this->corporation_id;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getDepartmentId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department_id,0,$len,"utf-8");
			else return substr($this->department_id,0,$len);
		}
		return $this->department_id;
	}

	public function getDepartmentName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department_name,0,$len,"utf-8");
			else return substr($this->department_name,0,$len);
		}
		return $this->department_name;
	}

	public function getTypeCurrentGroup()
	{
		return $this->type_current_group;
	}

	public function getAcceptId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->accept_id,0,$len,"utf-8");
			else return substr($this->accept_id,0,$len);
		}
		return $this->accept_id;
	}

	public function getRadicateId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->radicate_id,0,$len,"utf-8");
			else return substr($this->radicate_id,0,$len);
		}
		return $this->radicate_id;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getSubtitle($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subtitle,0,$len,"utf-8");
			else return substr($this->subtitle,0,$len);
		}
		return $this->subtitle;
	}

	public function getDeclareYear($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->declare_year,0,$len,"utf-8");
			else return substr($this->declare_year,0,$len);
		}
		return $this->declare_year;
	}

	public function getDeclareAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->declare_at));
		else return $this->declare_at;
	}

	public function getEndAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->end_at,0,$len,"utf-8");
			else return substr($this->end_at,0,$len);
		}
		return $this->end_at;
	}

	public function getStartAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->start_at,0,$len,"utf-8");
			else return substr($this->start_at,0,$len);
		}
		return $this->start_at;
	}

	public function getRealStartAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->real_start_at,0,$len,"utf-8");
			else return substr($this->real_start_at,0,$len);
		}
		return $this->real_start_at;
	}

	public function getRealEndAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->real_end_at,0,$len,"utf-8");
			else return substr($this->real_end_at,0,$len);
		}
		return $this->real_end_at;
	}

	public function getBuildYear($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->build_year,0,$len,"utf-8");
			else return substr($this->build_year,0,$len);
		}
		return $this->build_year;
	}

	public function getRadicateYear()
	{
		return $this->radicate_year;
	}

	public function getRadicateAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->radicate_at));
		else return $this->radicate_at;
	}

	public function getRadicateMoney($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->radicate_money,0,$len,"utf-8");
			else return substr($this->radicate_money,0,$len);
		}
		return $this->radicate_money;
	}

	public function getProvinceMoney($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->province_money,0,$len,"utf-8");
			else return substr($this->province_money,0,$len);
		}
		return $this->province_money;
	}

	public function getMoneyUseRate($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->money_use_rate,0,$len,"utf-8");
			else return substr($this->money_use_rate,0,$len);
		}
		return $this->money_use_rate;
	}

	public function getStatement()
	{
		return $this->statement;
	}

	public function getSubjectId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_id,0,$len,"utf-8");
			else return substr($this->subject_id,0,$len);
		}
		return $this->subject_id;
	}

	public function getSubjectName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_name,0,$len,"utf-8");
			else return substr($this->subject_name,0,$len);
		}
		return $this->subject_name;
	}

	public function getSubjectIds($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_ids,0,$len,"utf-8");
			else return substr($this->subject_ids,0,$len);
		}
		return $this->subject_ids;
	}

	public function getOfficeId()
	{
		return $this->office_id;
	}

	public function getGroupId()
	{
		return $this->group_id;
	}

	public function getTypeGroup($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->type_group,0,$len,"utf-8");
			else return substr($this->type_group,0,$len);
		}
		return $this->type_group;
	}

	public function getUserName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_name,0,$len,"utf-8");
			else return substr($this->user_name,0,$len);
		}
		return $this->user_name;
	}

	public function getCorporationName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->corporation_name,0,$len,"utf-8");
			else return substr($this->corporation_name,0,$len);
		}
		return $this->corporation_name;
	}

	public function getCooperation($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->cooperation,0,$len,"utf-8");
			else return substr($this->cooperation,0,$len);
		}
		return $this->cooperation;
	}

	public function getLinkman($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman,0,$len,"utf-8");
			else return substr($this->linkman,0,$len);
		}
		return $this->linkman;
	}

	public function getMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->mobile,0,$len,"utf-8");
			else return substr($this->mobile,0,$len);
		}
		return $this->mobile;
	}

	public function getOfficeSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->office_subject,0,$len,"utf-8");
			else return substr($this->office_subject,0,$len);
		}
		return $this->office_subject;
	}

	public function getTypeSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->type_subject,0,$len,"utf-8");
			else return substr($this->type_subject,0,$len);
		}
		return $this->type_subject;
	}

	public function getScore($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->score,0,$len,"utf-8");
			else return substr($this->score,0,$len);
		}
		return $this->score;
	}

	public function getHasExpert()
	{
		return $this->has_expert;
	}

	public function getStateForPlanBook()
	{
		return $this->state_for_plan_book;
	}

	public function getStateForCompleteBook()
	{
		return $this->state_for_complete_book;
	}

	public function getStateForStage()
	{
		return $this->state_for_stage;
	}

	public function getTaskOpen()
	{
		return $this->task_open;
	}

	public function getStageOpen()
	{
		return $this->stage_open;
	}

	public function getInspectOpen()
	{
		return $this->inspect_open;
	}

	public function getReviewOpen()
	{
		return $this->review_open;
	}

	public function getCompleteOpen()
	{
		return $this->complete_open;
	}

	public function getIsReport()
	{
		return $this->is_report;
	}

	public function getIsAccept()
	{
		return $this->is_accept;
	}

	public function getIsWait()
	{
		return $this->is_wait;
	}

	public function getIsDelay()
	{
		return $this->is_delay;
	}

	public function getIsTest()
	{
		return $this->is_test;
	}

	public function getIsMigrate()
	{
		return $this->is_migrate;
	}

	public function getConfigs($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->configs,0,$len,"utf-8");
			else return substr($this->configs,0,$len);
		}
		return $this->configs;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function getGuideName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->guide_name,0,$len,"utf-8");
			else return substr($this->guide_name,0,$len);
		}
		return $this->guide_name;
	}

	public function getCompleteApplyAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->complete_apply_at,0,$len,"utf-8");
			else return substr($this->complete_apply_at,0,$len);
		}
		return $this->complete_apply_at;
	}

	public function getStopAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->stop_at,0,$len,"utf-8");
			else return substr($this->stop_at,0,$len);
		}
		return $this->stop_at;
	}

	public function getCompleteAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->complete_at,0,$len,"utf-8");
			else return substr($this->complete_at,0,$len);
		}
		return $this->complete_at;
	}

	public function getNote($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->note,0,$len,"utf-8");
			else return substr($this->note,0,$len);
		}
		return $this->note;
	}

	public function getTags($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->tags,0,$len,"utf-8");
			else return substr($this->tags,0,$len);
		}
		return $this->tags;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setIsError($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_error !== $v)
		{
			$this->is_error = $v;
			$this->fieldData["is_error"] = $v;
		}
		return $this;

	}

	public function setWorkerId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->worker_id !== $v)
		{
			$this->worker_id = $v;
			$this->fieldData["worker_id"] = $v;
		}
		return $this;

	}

	public function setGuideId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->guide_id !== $v)
		{
			$this->guide_id = $v;
			$this->fieldData["guide_id"] = $v;
		}
		return $this;

	}

	public function setSubjectCode($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_code !== $v)
		{
			$this->subject_code = $v;
			$this->fieldData["subject_code"] = $v;
		}
		return $this;

	}

	public function setTypeId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->type_id !== $v)
		{
			$this->type_id = $v;
			$this->fieldData["type_id"] = $v;
		}
		return $this;

	}

	public function setCatId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->cat_id !== $v)
		{
			$this->cat_id = $v;
			$this->fieldData["cat_id"] = $v;
		}
		return $this;

	}

	public function setLevel($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->level !== $v)
		{
			$this->level = $v;
			$this->fieldData["level"] = $v;
		}
		return $this;

	}

	public function setProjectId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->project_id !== $v)
		{
			$this->project_id = $v;
			$this->fieldData["project_id"] = $v;
		}
		return $this;

	}

	public function setCorporationId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_id !== $v)
		{
			$this->corporation_id = $v;
			$this->fieldData["corporation_id"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setDepartmentId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_id !== $v)
		{
			$this->department_id = $v;
			$this->fieldData["department_id"] = $v;
		}
		return $this;

	}

	public function setDepartmentName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_name !== $v)
		{
			$this->department_name = $v;
			$this->fieldData["department_name"] = $v;
		}
		return $this;

	}

	public function setTypeCurrentGroup($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->type_current_group !== $v)
		{
			$this->type_current_group = $v;
			$this->fieldData["type_current_group"] = $v;
		}
		return $this;

	}

	public function setAcceptId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->accept_id !== $v)
		{
			$this->accept_id = $v;
			$this->fieldData["accept_id"] = $v;
		}
		return $this;

	}

	public function setRadicateId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->radicate_id !== $v)
		{
			$this->radicate_id = $v;
			$this->fieldData["radicate_id"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setSubtitle($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subtitle !== $v)
		{
			$this->subtitle = $v;
			$this->fieldData["subtitle"] = $v;
		}
		return $this;

	}

	public function setDeclareYear($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->declare_year !== $v)
		{
			$this->declare_year = $v;
			$this->fieldData["declare_year"] = $v;
		}
		return $this;

	}

	public function setDeclareAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->declare_at !== $v)
		{
			$this->declare_at = $v;
			$this->fieldData["declare_at"] = $v;
		}
		return $this;

	}

	public function setEndAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->end_at !== $v)
		{
			$this->end_at = $v;
			$this->fieldData["end_at"] = $v;
		}
		return $this;

	}

	public function setStartAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->start_at !== $v)
		{
			$this->start_at = $v;
			$this->fieldData["start_at"] = $v;
		}
		return $this;

	}

	public function setRealStartAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->real_start_at !== $v)
		{
			$this->real_start_at = $v;
			$this->fieldData["real_start_at"] = $v;
		}
		return $this;

	}

	public function setRealEndAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->real_end_at !== $v)
		{
			$this->real_end_at = $v;
			$this->fieldData["real_end_at"] = $v;
		}
		return $this;

	}

	public function setBuildYear($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->build_year !== $v)
		{
			$this->build_year = $v;
			$this->fieldData["build_year"] = $v;
		}
		return $this;

	}

	public function setRadicateYear($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->radicate_year !== $v)
		{
			$this->radicate_year = $v;
			$this->fieldData["radicate_year"] = $v;
		}
		return $this;

	}

	public function setRadicateAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->radicate_at !== $v)
		{
			$this->radicate_at = $v;
			$this->fieldData["radicate_at"] = $v;
		}
		return $this;

	}

	public function setRadicateMoney($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->radicate_money !== $v)
		{
			$this->radicate_money = $v;
			$this->fieldData["radicate_money"] = $v;
		}
		return $this;

	}

	public function setProvinceMoney($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->province_money !== $v)
		{
			$this->province_money = $v;
			$this->fieldData["province_money"] = $v;
		}
		return $this;

	}

	public function setMoneyUseRate($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->money_use_rate !== $v)
		{
			$this->money_use_rate = $v;
			$this->fieldData["money_use_rate"] = $v;
		}
		return $this;

	}

	public function setStatement($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->statement !== $v)
		{
			$this->statement = $v;
			$this->fieldData["statement"] = $v;
		}
		return $this;

	}

	public function setSubjectId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_id !== $v)
		{
			$this->subject_id = $v;
			$this->fieldData["subject_id"] = $v;
		}
		return $this;

	}

	public function setSubjectName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_name !== $v)
		{
			$this->subject_name = $v;
			$this->fieldData["subject_name"] = $v;
		}
		return $this;

	}

	public function setSubjectIds($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_ids !== $v)
		{
			$this->subject_ids = $v;
			$this->fieldData["subject_ids"] = $v;
		}
		return $this;

	}

	public function setOfficeId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->office_id !== $v)
		{
			$this->office_id = $v;
			$this->fieldData["office_id"] = $v;
		}
		return $this;

	}

	public function setGroupId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->group_id !== $v)
		{
			$this->group_id = $v;
			$this->fieldData["group_id"] = $v;
		}
		return $this;

	}

	public function setTypeGroup($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->type_group !== $v)
		{
			$this->type_group = $v;
			$this->fieldData["type_group"] = $v;
		}
		return $this;

	}

	public function setUserName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_name !== $v)
		{
			$this->user_name = $v;
			$this->fieldData["user_name"] = $v;
		}
		return $this;

	}

	public function setCorporationName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_name !== $v)
		{
			$this->corporation_name = $v;
			$this->fieldData["corporation_name"] = $v;
		}
		return $this;

	}

	public function setCooperation($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->cooperation !== $v)
		{
			$this->cooperation = $v;
			$this->fieldData["cooperation"] = $v;
		}
		return $this;

	}

	public function setLinkman($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman !== $v)
		{
			$this->linkman = $v;
			$this->fieldData["linkman"] = $v;
		}
		return $this;

	}

	public function setMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->mobile !== $v)
		{
			$this->mobile = $v;
			$this->fieldData["mobile"] = $v;
		}
		return $this;

	}

	public function setOfficeSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->office_subject !== $v)
		{
			$this->office_subject = $v;
			$this->fieldData["office_subject"] = $v;
		}
		return $this;

	}

	public function setTypeSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->type_subject !== $v)
		{
			$this->type_subject = $v;
			$this->fieldData["type_subject"] = $v;
		}
		return $this;

	}

	public function setScore($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->score !== $v)
		{
			$this->score = $v;
			$this->fieldData["score"] = $v;
		}
		return $this;

	}

	public function setHasExpert($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->has_expert !== $v)
		{
			$this->has_expert = $v;
			$this->fieldData["has_expert"] = $v;
		}
		return $this;

	}

	public function setStateForPlanBook($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->state_for_plan_book !== $v)
		{
			$this->state_for_plan_book = $v;
			$this->fieldData["state_for_plan_book"] = $v;
		}
		return $this;

	}

	public function setStateForCompleteBook($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->state_for_complete_book !== $v)
		{
			$this->state_for_complete_book = $v;
			$this->fieldData["state_for_complete_book"] = $v;
		}
		return $this;

	}

	public function setStateForStage($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->state_for_stage !== $v)
		{
			$this->state_for_stage = $v;
			$this->fieldData["state_for_stage"] = $v;
		}
		return $this;

	}

	public function setTaskOpen($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->task_open !== $v)
		{
			$this->task_open = $v;
			$this->fieldData["task_open"] = $v;
		}
		return $this;

	}

	public function setStageOpen($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->stage_open !== $v)
		{
			$this->stage_open = $v;
			$this->fieldData["stage_open"] = $v;
		}
		return $this;

	}

	public function setInspectOpen($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->inspect_open !== $v)
		{
			$this->inspect_open = $v;
			$this->fieldData["inspect_open"] = $v;
		}
		return $this;

	}

	public function setReviewOpen($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->review_open !== $v)
		{
			$this->review_open = $v;
			$this->fieldData["review_open"] = $v;
		}
		return $this;

	}

	public function setCompleteOpen($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->complete_open !== $v)
		{
			$this->complete_open = $v;
			$this->fieldData["complete_open"] = $v;
		}
		return $this;

	}

	public function setIsReport($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_report !== $v)
		{
			$this->is_report = $v;
			$this->fieldData["is_report"] = $v;
		}
		return $this;

	}

	public function setIsAccept($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_accept !== $v)
		{
			$this->is_accept = $v;
			$this->fieldData["is_accept"] = $v;
		}
		return $this;

	}

	public function setIsWait($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_wait !== $v)
		{
			$this->is_wait = $v;
			$this->fieldData["is_wait"] = $v;
		}
		return $this;

	}

	public function setIsDelay($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_delay !== $v)
		{
			$this->is_delay = $v;
			$this->fieldData["is_delay"] = $v;
		}
		return $this;

	}

	public function setIsTest($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_test !== $v)
		{
			$this->is_test = $v;
			$this->fieldData["is_test"] = $v;
		}
		return $this;

	}

	public function setIsMigrate($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_migrate !== $v)
		{
			$this->is_migrate = $v;
			$this->fieldData["is_migrate"] = $v;
		}
		return $this;

	}

	public function setConfigs($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->configs !== $v)
		{
			$this->configs = $v;
			$this->fieldData["configs"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function setGuideName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->guide_name !== $v)
		{
			$this->guide_name = $v;
			$this->fieldData["guide_name"] = $v;
		}
		return $this;

	}

	public function setCompleteApplyAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->complete_apply_at !== $v)
		{
			$this->complete_apply_at = $v;
			$this->fieldData["complete_apply_at"] = $v;
		}
		return $this;

	}

	public function setStopAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->stop_at !== $v)
		{
			$this->stop_at = $v;
			$this->fieldData["stop_at"] = $v;
		}
		return $this;

	}

	public function setCompleteAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->complete_at !== $v)
		{
			$this->complete_at = $v;
			$this->fieldData["complete_at"] = $v;
		}
		return $this;

	}

	public function setNote($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->note !== $v)
		{
			$this->note = $v;
			$this->fieldData["note"] = $v;
		}
		return $this;

	}

	public function setTags($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->tags !== $v)
		{
			$this->tags = $v;
			$this->fieldData["tags"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `projects` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"is_error" => $this->getIsError(),
			"worker_id" => $this->getWorkerId(),
			"guide_id" => $this->getGuideId(),
			"subject_code" => $this->getSubjectCode(),
			"type_id" => $this->getTypeId(),
			"cat_id" => $this->getCatId(),
			"level" => $this->getLevel(),
			"project_id" => $this->getProjectId(),
			"corporation_id" => $this->getCorporationId(),
			"user_id" => $this->getUserId(),
			"department_id" => $this->getDepartmentId(),
			"department_name" => $this->getDepartmentName(),
			"type_current_group" => $this->getTypeCurrentGroup(),
			"accept_id" => $this->getAcceptId(),
			"radicate_id" => $this->getRadicateId(),
			"subject" => $this->getSubject(),
			"subtitle" => $this->getSubtitle(),
			"declare_year" => $this->getDeclareYear(),
			"declare_at" => $this->getDeclareAt(),
			"end_at" => $this->getEndAt(),
			"start_at" => $this->getStartAt(),
			"real_start_at" => $this->getRealStartAt(),
			"real_end_at" => $this->getRealEndAt(),
			"build_year" => $this->getBuildYear(),
			"radicate_year" => $this->getRadicateYear(),
			"radicate_at" => $this->getRadicateAt(),
			"radicate_money" => $this->getRadicateMoney(),
			"province_money" => $this->getProvinceMoney(),
			"money_use_rate" => $this->getMoneyUseRate(),
			"statement" => $this->getStatement(),
			"subject_id" => $this->getSubjectId(),
			"subject_name" => $this->getSubjectName(),
			"subject_ids" => $this->getSubjectIds(),
			"office_id" => $this->getOfficeId(),
			"group_id" => $this->getGroupId(),
			"type_group" => $this->getTypeGroup(),
			"user_name" => $this->getUserName(),
			"corporation_name" => $this->getCorporationName(),
			"cooperation" => $this->getCooperation(),
			"linkman" => $this->getLinkman(),
			"mobile" => $this->getMobile(),
			"office_subject" => $this->getOfficeSubject(),
			"type_subject" => $this->getTypeSubject(),
			"score" => $this->getScore(),
			"has_expert" => $this->getHasExpert(),
			"state_for_plan_book" => $this->getStateForPlanBook(),
			"state_for_complete_book" => $this->getStateForCompleteBook(),
			"state_for_stage" => $this->getStateForStage(),
			"task_open" => $this->getTaskOpen(),
			"stage_open" => $this->getStageOpen(),
			"inspect_open" => $this->getInspectOpen(),
			"review_open" => $this->getReviewOpen(),
			"complete_open" => $this->getCompleteOpen(),
			"is_report" => $this->getIsReport(),
			"is_accept" => $this->getIsAccept(),
			"is_wait" => $this->getIsWait(),
			"is_delay" => $this->getIsDelay(),
			"is_test" => $this->getIsTest(),
			"is_migrate" => $this->getIsMigrate(),
			"configs" => $this->getConfigs(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			"guide_name" => $this->getGuideName(),
			"complete_apply_at" => $this->getCompleteApplyAt(),
			"stop_at" => $this->getStopAt(),
			"complete_at" => $this->getCompleteAt(),
			"note" => $this->getNote(),
			"tags" => $this->getTags(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->is_error = '';
		$this->worker_id = '';
		$this->guide_id = '';
		$this->subject_code = '';
		$this->type_id = '';
		$this->cat_id = '';
		$this->level = '';
		$this->project_id = '';
		$this->corporation_id = '';
		$this->user_id = '';
		$this->department_id = '';
		$this->department_name = '';
		$this->type_current_group = '';
		$this->accept_id = '';
		$this->radicate_id = '';
		$this->subject = '';
		$this->subtitle = '';
		$this->declare_year = '';
		$this->declare_at = '';
		$this->end_at = '';
		$this->start_at = '';
		$this->real_start_at = '';
		$this->real_end_at = '';
		$this->build_year = '';
		$this->radicate_year = '';
		$this->radicate_at = '';
		$this->radicate_money = '';
		$this->province_money = '';
		$this->money_use_rate = '';
		$this->statement = '';
		$this->subject_id = '';
		$this->subject_name = '';
		$this->subject_ids = '';
		$this->office_id = '';
		$this->group_id = '';
		$this->type_group = '';
		$this->user_name = '';
		$this->corporation_name = '';
		$this->cooperation = '';
		$this->linkman = '';
		$this->mobile = '';
		$this->office_subject = '';
		$this->type_subject = '';
		$this->score = '';
		$this->has_expert = '';
		$this->state_for_plan_book = '';
		$this->state_for_complete_book = '';
		$this->state_for_stage = '';
		$this->task_open = '';
		$this->stage_open = '';
		$this->inspect_open = '';
		$this->review_open = '';
		$this->complete_open = '';
		$this->is_report = '';
		$this->is_accept = '';
		$this->is_wait = '';
		$this->is_delay = '';
		$this->is_test = '';
		$this->is_migrate = '';
		$this->configs = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->guide_name = '';
		$this->complete_apply_at = '';
		$this->stop_at = '';
		$this->complete_at = '';
		$this->note = '';
		$this->tags = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["is_error"]) && $this->is_error = $data["is_error"];
		isset($data["worker_id"]) && $this->worker_id = $data["worker_id"];
		isset($data["guide_id"]) && $this->guide_id = $data["guide_id"];
		isset($data["subject_code"]) && $this->subject_code = $data["subject_code"];
		isset($data["type_id"]) && $this->type_id = $data["type_id"];
		isset($data["cat_id"]) && $this->cat_id = $data["cat_id"];
		isset($data["level"]) && $this->level = $data["level"];
		isset($data["project_id"]) && $this->project_id = $data["project_id"];
		isset($data["corporation_id"]) && $this->corporation_id = $data["corporation_id"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["department_id"]) && $this->department_id = $data["department_id"];
		isset($data["department_name"]) && $this->department_name = $data["department_name"];
		isset($data["type_current_group"]) && $this->type_current_group = $data["type_current_group"];
		isset($data["accept_id"]) && $this->accept_id = $data["accept_id"];
		isset($data["radicate_id"]) && $this->radicate_id = $data["radicate_id"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["subtitle"]) && $this->subtitle = $data["subtitle"];
		isset($data["declare_year"]) && $this->declare_year = $data["declare_year"];
		isset($data["declare_at"]) && $this->declare_at = $data["declare_at"];
		isset($data["end_at"]) && $this->end_at = $data["end_at"];
		isset($data["start_at"]) && $this->start_at = $data["start_at"];
		isset($data["real_start_at"]) && $this->real_start_at = $data["real_start_at"];
		isset($data["real_end_at"]) && $this->real_end_at = $data["real_end_at"];
		isset($data["build_year"]) && $this->build_year = $data["build_year"];
		isset($data["radicate_year"]) && $this->radicate_year = $data["radicate_year"];
		isset($data["radicate_at"]) && $this->radicate_at = $data["radicate_at"];
		isset($data["radicate_money"]) && $this->radicate_money = $data["radicate_money"];
		isset($data["province_money"]) && $this->province_money = $data["province_money"];
		isset($data["money_use_rate"]) && $this->money_use_rate = $data["money_use_rate"];
		isset($data["statement"]) && $this->statement = $data["statement"];
		isset($data["subject_id"]) && $this->subject_id = $data["subject_id"];
		isset($data["subject_name"]) && $this->subject_name = $data["subject_name"];
		isset($data["subject_ids"]) && $this->subject_ids = $data["subject_ids"];
		isset($data["office_id"]) && $this->office_id = $data["office_id"];
		isset($data["group_id"]) && $this->group_id = $data["group_id"];
		isset($data["type_group"]) && $this->type_group = $data["type_group"];
		isset($data["user_name"]) && $this->user_name = $data["user_name"];
		isset($data["corporation_name"]) && $this->corporation_name = $data["corporation_name"];
		isset($data["cooperation"]) && $this->cooperation = $data["cooperation"];
		isset($data["linkman"]) && $this->linkman = $data["linkman"];
		isset($data["mobile"]) && $this->mobile = $data["mobile"];
		isset($data["office_subject"]) && $this->office_subject = $data["office_subject"];
		isset($data["type_subject"]) && $this->type_subject = $data["type_subject"];
		isset($data["score"]) && $this->score = $data["score"];
		isset($data["has_expert"]) && $this->has_expert = $data["has_expert"];
		isset($data["state_for_plan_book"]) && $this->state_for_plan_book = $data["state_for_plan_book"];
		isset($data["state_for_complete_book"]) && $this->state_for_complete_book = $data["state_for_complete_book"];
		isset($data["state_for_stage"]) && $this->state_for_stage = $data["state_for_stage"];
		isset($data["task_open"]) && $this->task_open = $data["task_open"];
		isset($data["stage_open"]) && $this->stage_open = $data["stage_open"];
		isset($data["inspect_open"]) && $this->inspect_open = $data["inspect_open"];
		isset($data["review_open"]) && $this->review_open = $data["review_open"];
		isset($data["complete_open"]) && $this->complete_open = $data["complete_open"];
		isset($data["is_report"]) && $this->is_report = $data["is_report"];
		isset($data["is_accept"]) && $this->is_accept = $data["is_accept"];
		isset($data["is_wait"]) && $this->is_wait = $data["is_wait"];
		isset($data["is_delay"]) && $this->is_delay = $data["is_delay"];
		isset($data["is_test"]) && $this->is_test = $data["is_test"];
		isset($data["is_migrate"]) && $this->is_migrate = $data["is_migrate"];
		isset($data["configs"]) && $this->configs = $data["configs"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		isset($data["guide_name"]) && $this->guide_name = $data["guide_name"];
		isset($data["complete_apply_at"]) && $this->complete_apply_at = $data["complete_apply_at"];
		isset($data["stop_at"]) && $this->stop_at = $data["stop_at"];
		isset($data["complete_at"]) && $this->complete_at = $data["complete_at"];
		isset($data["note"]) && $this->note = $data["note"];
		isset($data["tags"]) && $this->tags = $data["tags"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}