<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BasePlatformChanges extends BaseModel
{
	private $id;
	private $change_id;
	private $platform_id;
	private $platform_name;
	private $change_type;
	private $before;
	private $after;
	private $todo;
	private $user_id;
	private $company_id;
	private $company_name;
	private $department_id;
	private $linkman;
	private $linkman_mobile  = '';
	private $summary;
	private $file_path;
	private $state_for_todo  = '0';
	private $submit_at;
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	private $statement  = '1';
	public $table = "platform_changes";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getChangeId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->change_id,0,$len,"utf-8");
			else return substr($this->change_id,0,$len);
		}
		return $this->change_id;
	}

	public function getPlatformId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->platform_id,0,$len,"utf-8");
			else return substr($this->platform_id,0,$len);
		}
		return $this->platform_id;
	}

	public function getPlatformName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->platform_name,0,$len,"utf-8");
			else return substr($this->platform_name,0,$len);
		}
		return $this->platform_name;
	}

	public function getChangeType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->change_type,0,$len,"utf-8");
			else return substr($this->change_type,0,$len);
		}
		return $this->change_type;
	}

	public function getBefore($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->before,0,$len,"utf-8");
			else return substr($this->before,0,$len);
		}
		return $this->before;
	}

	public function getAfter($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->after,0,$len,"utf-8");
			else return substr($this->after,0,$len);
		}
		return $this->after;
	}

	public function getTodo($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->todo,0,$len,"utf-8");
			else return substr($this->todo,0,$len);
		}
		return $this->todo;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getCompanyId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->company_id,0,$len,"utf-8");
			else return substr($this->company_id,0,$len);
		}
		return $this->company_id;
	}

	public function getCompanyName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->company_name,0,$len,"utf-8");
			else return substr($this->company_name,0,$len);
		}
		return $this->company_name;
	}

	public function getDepartmentId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department_id,0,$len,"utf-8");
			else return substr($this->department_id,0,$len);
		}
		return $this->department_id;
	}

	public function getLinkman($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman,0,$len,"utf-8");
			else return substr($this->linkman,0,$len);
		}
		return $this->linkman;
	}

	public function getLinkmanMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman_mobile,0,$len,"utf-8");
			else return substr($this->linkman_mobile,0,$len);
		}
		return $this->linkman_mobile;
	}

	public function getSummary($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->summary,0,$len,"utf-8");
			else return substr($this->summary,0,$len);
		}
		return $this->summary;
	}

	public function getFilePath($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->file_path,0,$len,"utf-8");
			else return substr($this->file_path,0,$len);
		}
		return $this->file_path;
	}

	public function getStateForTodo()
	{
		return $this->state_for_todo;
	}

	public function getSubmitAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->submit_at));
		else return $this->submit_at;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function getStatement()
	{
		return $this->statement;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setChangeId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->change_id !== $v)
		{
			$this->change_id = $v;
			$this->fieldData["change_id"] = $v;
		}
		return $this;

	}

	public function setPlatformId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->platform_id !== $v)
		{
			$this->platform_id = $v;
			$this->fieldData["platform_id"] = $v;
		}
		return $this;

	}

	public function setPlatformName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->platform_name !== $v)
		{
			$this->platform_name = $v;
			$this->fieldData["platform_name"] = $v;
		}
		return $this;

	}

	public function setChangeType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->change_type !== $v)
		{
			$this->change_type = $v;
			$this->fieldData["change_type"] = $v;
		}
		return $this;

	}

	public function setBefore($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->before !== $v)
		{
			$this->before = $v;
			$this->fieldData["before"] = $v;
		}
		return $this;

	}

	public function setAfter($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->after !== $v)
		{
			$this->after = $v;
			$this->fieldData["after"] = $v;
		}
		return $this;

	}

	public function setTodo($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->todo !== $v)
		{
			$this->todo = $v;
			$this->fieldData["todo"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setCompanyId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->company_id !== $v)
		{
			$this->company_id = $v;
			$this->fieldData["company_id"] = $v;
		}
		return $this;

	}

	public function setCompanyName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->company_name !== $v)
		{
			$this->company_name = $v;
			$this->fieldData["company_name"] = $v;
		}
		return $this;

	}

	public function setDepartmentId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_id !== $v)
		{
			$this->department_id = $v;
			$this->fieldData["department_id"] = $v;
		}
		return $this;

	}

	public function setLinkman($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman !== $v)
		{
			$this->linkman = $v;
			$this->fieldData["linkman"] = $v;
		}
		return $this;

	}

	public function setLinkmanMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman_mobile !== $v)
		{
			$this->linkman_mobile = $v;
			$this->fieldData["linkman_mobile"] = $v;
		}
		return $this;

	}

	public function setSummary($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->summary !== $v)
		{
			$this->summary = $v;
			$this->fieldData["summary"] = $v;
		}
		return $this;

	}

	public function setFilePath($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->file_path !== $v)
		{
			$this->file_path = $v;
			$this->fieldData["file_path"] = $v;
		}
		return $this;

	}

	public function setStateForTodo($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->state_for_todo !== $v)
		{
			$this->state_for_todo = $v;
			$this->fieldData["state_for_todo"] = $v;
		}
		return $this;

	}

	public function setSubmitAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->submit_at !== $v)
		{
			$this->submit_at = $v;
			$this->fieldData["submit_at"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function setStatement($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->statement !== $v)
		{
			$this->statement = $v;
			$this->fieldData["statement"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `platform_changes` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"change_id" => $this->getChangeId(),
			"platform_id" => $this->getPlatformId(),
			"platform_name" => $this->getPlatformName(),
			"change_type" => $this->getChangeType(),
			"before" => $this->getBefore(),
			"after" => $this->getAfter(),
			"todo" => $this->getTodo(),
			"user_id" => $this->getUserId(),
			"company_id" => $this->getCompanyId(),
			"company_name" => $this->getCompanyName(),
			"department_id" => $this->getDepartmentId(),
			"linkman" => $this->getLinkman(),
			"linkman_mobile" => $this->getLinkmanMobile(),
			"summary" => $this->getSummary(),
			"file_path" => $this->getFilePath(),
			"state_for_todo" => $this->getStateForTodo(),
			"submit_at" => $this->getSubmitAt(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			"statement" => $this->getStatement(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->change_id = '';
		$this->platform_id = '';
		$this->platform_name = '';
		$this->change_type = '';
		$this->before = '';
		$this->after = '';
		$this->todo = '';
		$this->user_id = '';
		$this->company_id = '';
		$this->company_name = '';
		$this->department_id = '';
		$this->linkman = '';
		$this->linkman_mobile = '';
		$this->summary = '';
		$this->file_path = '';
		$this->state_for_todo = '';
		$this->submit_at = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->statement = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["change_id"]) && $this->change_id = $data["change_id"];
		isset($data["platform_id"]) && $this->platform_id = $data["platform_id"];
		isset($data["platform_name"]) && $this->platform_name = $data["platform_name"];
		isset($data["change_type"]) && $this->change_type = $data["change_type"];
		isset($data["before"]) && $this->before = $data["before"];
		isset($data["after"]) && $this->after = $data["after"];
		isset($data["todo"]) && $this->todo = $data["todo"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["company_id"]) && $this->company_id = $data["company_id"];
		isset($data["company_name"]) && $this->company_name = $data["company_name"];
		isset($data["department_id"]) && $this->department_id = $data["department_id"];
		isset($data["linkman"]) && $this->linkman = $data["linkman"];
		isset($data["linkman_mobile"]) && $this->linkman_mobile = $data["linkman_mobile"];
		isset($data["summary"]) && $this->summary = $data["summary"];
		isset($data["file_path"]) && $this->file_path = $data["file_path"];
		isset($data["state_for_todo"]) && $this->state_for_todo = $data["state_for_todo"];
		isset($data["submit_at"]) && $this->submit_at = $data["submit_at"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		isset($data["statement"]) && $this->statement = $data["statement"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}