<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseCorporations extends BaseModel
{
	private $id;
	private $user_id;
	private $zdsys_user_id;
	private $code;
	private $department_id  = '0';
	private $subject;
	private $property;
	private $address;
	private $phone;
	private $postalcode;
	private $linkman;
	private $mobile;
	private $principal;
	private $principal_cardid;
	private $personcount  = '0';
	private $homepage;
	private $remark;
	private $area;
	private $linkman_fax;
	private $linkman_email;
	private $linkman_department;
	private $linkman_duty;
	private $bank_name;
	private $bank_id;
	private $is_hightech  = '0';
	private $is_lock  = '9';
	private $area_code;
	private $manager_user_id;
	private $build_at;
	private $register_at;
	private $is_report  = '0';
	private $created_at;
	private $updated_at;
	private $ignore_endat  = '0';
	private $ignore_count_limit  = '0';
	public $table = "corporations";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getZdsysUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->zdsys_user_id,0,$len,"utf-8");
			else return substr($this->zdsys_user_id,0,$len);
		}
		return $this->zdsys_user_id;
	}

	public function getCode($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->code,0,$len,"utf-8");
			else return substr($this->code,0,$len);
		}
		return $this->code;
	}

	public function getDepartmentId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department_id,0,$len,"utf-8");
			else return substr($this->department_id,0,$len);
		}
		return $this->department_id;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getProperty($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->property,0,$len,"utf-8");
			else return substr($this->property,0,$len);
		}
		return $this->property;
	}

	public function getAddress($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->address,0,$len,"utf-8");
			else return substr($this->address,0,$len);
		}
		return $this->address;
	}

	public function getPhone($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->phone,0,$len,"utf-8");
			else return substr($this->phone,0,$len);
		}
		return $this->phone;
	}

	public function getPostalcode($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->postalcode,0,$len,"utf-8");
			else return substr($this->postalcode,0,$len);
		}
		return $this->postalcode;
	}

	public function getLinkman($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman,0,$len,"utf-8");
			else return substr($this->linkman,0,$len);
		}
		return $this->linkman;
	}

	public function getMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->mobile,0,$len,"utf-8");
			else return substr($this->mobile,0,$len);
		}
		return $this->mobile;
	}

	public function getPrincipal($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->principal,0,$len,"utf-8");
			else return substr($this->principal,0,$len);
		}
		return $this->principal;
	}

	public function getPrincipalCardid($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->principal_cardid,0,$len,"utf-8");
			else return substr($this->principal_cardid,0,$len);
		}
		return $this->principal_cardid;
	}

	public function getPersoncount()
	{
		return $this->personcount;
	}

	public function getHomepage($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->homepage,0,$len,"utf-8");
			else return substr($this->homepage,0,$len);
		}
		return $this->homepage;
	}

	public function getRemark($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->remark,0,$len,"utf-8");
			else return substr($this->remark,0,$len);
		}
		return $this->remark;
	}

	public function getArea($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->area,0,$len,"utf-8");
			else return substr($this->area,0,$len);
		}
		return $this->area;
	}

	public function getLinkmanFax($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman_fax,0,$len,"utf-8");
			else return substr($this->linkman_fax,0,$len);
		}
		return $this->linkman_fax;
	}

	public function getLinkmanEmail($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman_email,0,$len,"utf-8");
			else return substr($this->linkman_email,0,$len);
		}
		return $this->linkman_email;
	}

	public function getLinkmanDepartment($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman_department,0,$len,"utf-8");
			else return substr($this->linkman_department,0,$len);
		}
		return $this->linkman_department;
	}

	public function getLinkmanDuty($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman_duty,0,$len,"utf-8");
			else return substr($this->linkman_duty,0,$len);
		}
		return $this->linkman_duty;
	}

	public function getBankName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->bank_name,0,$len,"utf-8");
			else return substr($this->bank_name,0,$len);
		}
		return $this->bank_name;
	}

	public function getBankId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->bank_id,0,$len,"utf-8");
			else return substr($this->bank_id,0,$len);
		}
		return $this->bank_id;
	}

	public function getIsHightech()
	{
		return $this->is_hightech;
	}

	public function getIsLock()
	{
		return $this->is_lock;
	}

	public function getAreaCode($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->area_code,0,$len,"utf-8");
			else return substr($this->area_code,0,$len);
		}
		return $this->area_code;
	}

	public function getManagerUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->manager_user_id,0,$len,"utf-8");
			else return substr($this->manager_user_id,0,$len);
		}
		return $this->manager_user_id;
	}

	public function getBuildAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->build_at,0,$len,"utf-8");
			else return substr($this->build_at,0,$len);
		}
		return $this->build_at;
	}

	public function getRegisterAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->register_at));
		else return $this->register_at;
	}

	public function getIsReport()
	{
		return $this->is_report;
	}

	public function getCreatedAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->created_at,0,$len,"utf-8");
			else return substr($this->created_at,0,$len);
		}
		return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function getIgnoreEndat()
	{
		return $this->ignore_endat;
	}

	public function getIgnoreCountLimit()
	{
		return $this->ignore_count_limit;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setZdsysUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->zdsys_user_id !== $v)
		{
			$this->zdsys_user_id = $v;
			$this->fieldData["zdsys_user_id"] = $v;
		}
		return $this;

	}

	public function setCode($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->code !== $v)
		{
			$this->code = $v;
			$this->fieldData["code"] = $v;
		}
		return $this;

	}

	public function setDepartmentId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_id !== $v)
		{
			$this->department_id = $v;
			$this->fieldData["department_id"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setProperty($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->property !== $v)
		{
			$this->property = $v;
			$this->fieldData["property"] = $v;
		}
		return $this;

	}

	public function setAddress($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->address !== $v)
		{
			$this->address = $v;
			$this->fieldData["address"] = $v;
		}
		return $this;

	}

	public function setPhone($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->phone !== $v)
		{
			$this->phone = $v;
			$this->fieldData["phone"] = $v;
		}
		return $this;

	}

	public function setPostalcode($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->postalcode !== $v)
		{
			$this->postalcode = $v;
			$this->fieldData["postalcode"] = $v;
		}
		return $this;

	}

	public function setLinkman($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman !== $v)
		{
			$this->linkman = $v;
			$this->fieldData["linkman"] = $v;
		}
		return $this;

	}

	public function setMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->mobile !== $v)
		{
			$this->mobile = $v;
			$this->fieldData["mobile"] = $v;
		}
		return $this;

	}

	public function setPrincipal($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->principal !== $v)
		{
			$this->principal = $v;
			$this->fieldData["principal"] = $v;
		}
		return $this;

	}

	public function setPrincipalCardid($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->principal_cardid !== $v)
		{
			$this->principal_cardid = $v;
			$this->fieldData["principal_cardid"] = $v;
		}
		return $this;

	}

	public function setPersoncount($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->personcount !== $v)
		{
			$this->personcount = $v;
			$this->fieldData["personcount"] = $v;
		}
		return $this;

	}

	public function setHomepage($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->homepage !== $v)
		{
			$this->homepage = $v;
			$this->fieldData["homepage"] = $v;
		}
		return $this;

	}

	public function setRemark($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->remark !== $v)
		{
			$this->remark = $v;
			$this->fieldData["remark"] = $v;
		}
		return $this;

	}

	public function setArea($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->area !== $v)
		{
			$this->area = $v;
			$this->fieldData["area"] = $v;
		}
		return $this;

	}

	public function setLinkmanFax($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman_fax !== $v)
		{
			$this->linkman_fax = $v;
			$this->fieldData["linkman_fax"] = $v;
		}
		return $this;

	}

	public function setLinkmanEmail($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman_email !== $v)
		{
			$this->linkman_email = $v;
			$this->fieldData["linkman_email"] = $v;
		}
		return $this;

	}

	public function setLinkmanDepartment($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman_department !== $v)
		{
			$this->linkman_department = $v;
			$this->fieldData["linkman_department"] = $v;
		}
		return $this;

	}

	public function setLinkmanDuty($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman_duty !== $v)
		{
			$this->linkman_duty = $v;
			$this->fieldData["linkman_duty"] = $v;
		}
		return $this;

	}

	public function setBankName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->bank_name !== $v)
		{
			$this->bank_name = $v;
			$this->fieldData["bank_name"] = $v;
		}
		return $this;

	}

	public function setBankId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->bank_id !== $v)
		{
			$this->bank_id = $v;
			$this->fieldData["bank_id"] = $v;
		}
		return $this;

	}

	public function setIsHightech($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_hightech !== $v)
		{
			$this->is_hightech = $v;
			$this->fieldData["is_hightech"] = $v;
		}
		return $this;

	}

	public function setIsLock($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_lock !== $v)
		{
			$this->is_lock = $v;
			$this->fieldData["is_lock"] = $v;
		}
		return $this;

	}

	public function setAreaCode($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->area_code !== $v)
		{
			$this->area_code = $v;
			$this->fieldData["area_code"] = $v;
		}
		return $this;

	}

	public function setManagerUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->manager_user_id !== $v)
		{
			$this->manager_user_id = $v;
			$this->fieldData["manager_user_id"] = $v;
		}
		return $this;

	}

	public function setBuildAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->build_at !== $v)
		{
			$this->build_at = $v;
			$this->fieldData["build_at"] = $v;
		}
		return $this;

	}

	public function setRegisterAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->register_at !== $v)
		{
			$this->register_at = $v;
			$this->fieldData["register_at"] = $v;
		}
		return $this;

	}

	public function setIsReport($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_report !== $v)
		{
			$this->is_report = $v;
			$this->fieldData["is_report"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function setIgnoreEndat($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->ignore_endat !== $v)
		{
			$this->ignore_endat = $v;
			$this->fieldData["ignore_endat"] = $v;
		}
		return $this;

	}

	public function setIgnoreCountLimit($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->ignore_count_limit !== $v)
		{
			$this->ignore_count_limit = $v;
			$this->fieldData["ignore_count_limit"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `corporations` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"user_id" => $this->getUserId(),
			"zdsys_user_id" => $this->getZdsysUserId(),
			"code" => $this->getCode(),
			"department_id" => $this->getDepartmentId(),
			"subject" => $this->getSubject(),
			"property" => $this->getProperty(),
			"address" => $this->getAddress(),
			"phone" => $this->getPhone(),
			"postalcode" => $this->getPostalcode(),
			"linkman" => $this->getLinkman(),
			"mobile" => $this->getMobile(),
			"principal" => $this->getPrincipal(),
			"principal_cardid" => $this->getPrincipalCardid(),
			"personcount" => $this->getPersoncount(),
			"homepage" => $this->getHomepage(),
			"remark" => $this->getRemark(),
			"area" => $this->getArea(),
			"linkman_fax" => $this->getLinkmanFax(),
			"linkman_email" => $this->getLinkmanEmail(),
			"linkman_department" => $this->getLinkmanDepartment(),
			"linkman_duty" => $this->getLinkmanDuty(),
			"bank_name" => $this->getBankName(),
			"bank_id" => $this->getBankId(),
			"is_hightech" => $this->getIsHightech(),
			"is_lock" => $this->getIsLock(),
			"area_code" => $this->getAreaCode(),
			"manager_user_id" => $this->getManagerUserId(),
			"build_at" => $this->getBuildAt(),
			"register_at" => $this->getRegisterAt(),
			"is_report" => $this->getIsReport(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			"ignore_endat" => $this->getIgnoreEndat(),
			"ignore_count_limit" => $this->getIgnoreCountLimit(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->user_id = '';
		$this->zdsys_user_id = '';
		$this->code = '';
		$this->department_id = '';
		$this->subject = '';
		$this->property = '';
		$this->address = '';
		$this->phone = '';
		$this->postalcode = '';
		$this->linkman = '';
		$this->mobile = '';
		$this->principal = '';
		$this->principal_cardid = '';
		$this->personcount = '';
		$this->homepage = '';
		$this->remark = '';
		$this->area = '';
		$this->linkman_fax = '';
		$this->linkman_email = '';
		$this->linkman_department = '';
		$this->linkman_duty = '';
		$this->bank_name = '';
		$this->bank_id = '';
		$this->is_hightech = '';
		$this->is_lock = '';
		$this->area_code = '';
		$this->manager_user_id = '';
		$this->build_at = '';
		$this->register_at = '';
		$this->is_report = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->ignore_endat = '';
		$this->ignore_count_limit = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["zdsys_user_id"]) && $this->zdsys_user_id = $data["zdsys_user_id"];
		isset($data["code"]) && $this->code = $data["code"];
		isset($data["department_id"]) && $this->department_id = $data["department_id"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["property"]) && $this->property = $data["property"];
		isset($data["address"]) && $this->address = $data["address"];
		isset($data["phone"]) && $this->phone = $data["phone"];
		isset($data["postalcode"]) && $this->postalcode = $data["postalcode"];
		isset($data["linkman"]) && $this->linkman = $data["linkman"];
		isset($data["mobile"]) && $this->mobile = $data["mobile"];
		isset($data["principal"]) && $this->principal = $data["principal"];
		isset($data["principal_cardid"]) && $this->principal_cardid = $data["principal_cardid"];
		isset($data["personcount"]) && $this->personcount = $data["personcount"];
		isset($data["homepage"]) && $this->homepage = $data["homepage"];
		isset($data["remark"]) && $this->remark = $data["remark"];
		isset($data["area"]) && $this->area = $data["area"];
		isset($data["linkman_fax"]) && $this->linkman_fax = $data["linkman_fax"];
		isset($data["linkman_email"]) && $this->linkman_email = $data["linkman_email"];
		isset($data["linkman_department"]) && $this->linkman_department = $data["linkman_department"];
		isset($data["linkman_duty"]) && $this->linkman_duty = $data["linkman_duty"];
		isset($data["bank_name"]) && $this->bank_name = $data["bank_name"];
		isset($data["bank_id"]) && $this->bank_id = $data["bank_id"];
		isset($data["is_hightech"]) && $this->is_hightech = $data["is_hightech"];
		isset($data["is_lock"]) && $this->is_lock = $data["is_lock"];
		isset($data["area_code"]) && $this->area_code = $data["area_code"];
		isset($data["manager_user_id"]) && $this->manager_user_id = $data["manager_user_id"];
		isset($data["build_at"]) && $this->build_at = $data["build_at"];
		isset($data["register_at"]) && $this->register_at = $data["register_at"];
		isset($data["is_report"]) && $this->is_report = $data["is_report"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		isset($data["ignore_endat"]) && $this->ignore_endat = $data["ignore_endat"];
		isset($data["ignore_count_limit"]) && $this->ignore_count_limit = $data["ignore_count_limit"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}