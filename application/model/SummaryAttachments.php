<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseSummaryAttachments;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class SummaryAttachments extends BaseSummaryAttachments
{
    function selectByUserId($user_id='')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_id` = '".$user_id."' ");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setUserId($user_id ? $user_id : sf::getLib("MyString")->getRandString());
        return $this;
    }

    function selectByProjectId($project_id='')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `item_id` = '".$project_id."' ");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setProjectId($project_id ? $project_id : sf::getLib("MyString")->getRandString());
        return $this;
    }

    function deleteFile($file='')
    {
        $file = $file ? $file : parent::getFilePath();
        $file = WEBROOT."/up_files/".$file;
        if(is_file($file)) return @unlink($file);
        else return false;
    }

    function remove($ids,$foce=false)
    {
        $result = $this->selectAll("`id` IN ('$ids')",'',0);
        while($file = $result->getObject())
        {
            if($file->getUsed() > 0 || !$foce) return false;
            //if($file->getUserId() != input::getInput("session.roleuserid")) return false;
            if($file->getItemType(4)!='task') $file->deleteFile();
            $file->delete();
        }
        return true;
    }

    function getLink()
    {
        return "";
    }

    function getSumSize()
    {
        $sql = "select sum(file_size) as totalnum from filemanager where `user_id` = '".input::getInput("session.userid")."'";
        $db = sf::getLib("db");
        $num = $db->fetch_first($sql);
        $nums = round($num['totalnum']/1024/1024,2).M;
        return $nums;
    }

    function getFileSize()
    {
        if(parent::getFileSize() > 1048576)	return round((parent::getFileSize()/1024/1024),2).'M';
        else return round((parent::getFileSize()/1024),2).'KB';
    }

    function isImage()
    {
        if(in_array(strtolower($this->getFileExt()),['jpg','png','gif','bmp','jpeg'])) return true;
        else return false;
    }

    function getTypeName()
    {
        switch($this->getItemType())
        {
            case 'content':
                return '[正文]';
                break;
            case 'budget':
                return '[预算]';
                break;
            default:
                return '';
                break;
        }
    }

    function getMark()
    {
        return $this->getTypeName();
    }


}