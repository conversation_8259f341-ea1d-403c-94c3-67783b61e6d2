<?php
namespace App\Model;
use Sofast\Core\router;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseSummarys;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class Summarys extends BaseSummarys
{
    function selectBySummaryId($summaryId = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `summary_id` = '{$summaryId}' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setSummaryId($summaryId ?: sf::getLib("MyString")->getRandString());
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }

    /**
     * 判断是否重复填报
     * @return int
     */
    function isRepeat()
    {
        $addwhere = "summary_id!='" . $this->getSummaryId() . "' and `declare_year` = '" . $this->getDeclareYear() . "' and platform_id = '" . $this->getPlatformId() . "'";
        $count = (int)sf::getLib('db')->result_first("select count(*) c from `{$this->table}` where {$addwhere}");
        return $count > 0;
    }

    /**
     * 获取引擎工作器
     * @param string $type [description]
     * @return [type]       [description]
     */
    function worker($type = 'apply', $f = false)
    {
        if (!in_array($type, array('apply', 'summary',  'task', 'stage', 'complete'))) $type = 'apply';
        if ($this->worker[$type] === NULL || $f) {
            $this->worker[$type] = sf::getModel("EngineWorkers", $this->getWorkerId($type));
        }
        return $this->worker[$type];
    }

    function getWidgetConfigs($widget_name = '', $type = 'apply')
    {
        $_configs = $this->worker($type)->getConfigs();
        if ($widget_name) return $_configs[$widget_name];
        else return $_configs;
    }

    /**
     * 获取项目部件
     * @param string $type [description]
     * @return [type]       [description]
     */
    function widgets($_widget = '', $type = 'apply')
    {
        if (router::getMethod()!='read' && $this->isNew()) return false;
        $_widget = strtolower($_widget);
        if (in_array($_widget, array_keys((array)$this->widgets[$type]))) return $this->widgets[$type][$_widget]->setProject($this);
        else {
            //读取指南附件配置
            $configs = $this->getWidgetConfigs('', $type);
            $widget_configs = $this->widget_configs;
            $pager = $this->worker($type)->widgets();
            while ($w = $pager->getObject()) {
                if (count($configs[$w->getWidgetName()]) > 0) {
                    $_configs = array_merge($configs[$w->getWidgetName()], $widget_configs);
                } else $_configs = $widget_configs;

                $this->widgets[$type][$w->getWidgetName()] = $w->getWidget($_configs);
            }
            if (in_array($_widget, array_keys($this->widgets[$type]))) return $this->widgets[$type][$_widget]->setProject($this);
        }
        return '部件不存在！';
    }

    /**
     * 获取项目部件
     * @param string $type [description]
     * @return [type]       [description]
     */
    function getWidget($widgetName, $type = 'apply')
    {
        $widget = $this->worker($type)->getWidgetByName($widgetName);
        if($widget->isNew()){
            return false;
        }
        return $widget;
    }

    /**
     * 获取项目部件
     * @param string $type [description]
     * @return [type]       [description]
     */
    function widget_validate($type = 'apply')
    {
        $message = array();
        $configs = $this->getWidgetConfigs('',$type);
        $widget_configs = $this->widget_configs;
        $pager = $this->worker($type)->widgets();
        while ($w = $pager->getObject()) {
            $_msg = array();
            $_widget = NULL;
            if (count($configs[$w->getWidgetName()]) > 0) {
                $_configs = array_merge($configs[$w->getWidgetName()], $widget_configs);
            } else $_configs = $widget_configs;
            $_widget = $w->getWidget($_configs);
            $_msg = $_widget->setProject($this)->validate();
            if (count($_msg) > 0) $message = array_merge($message, $_msg);
        }
        return $message;
    }

    /**
     * 按照项目设置部件配置信息
     * @param string $type [description]
     * @return [type]       [description]
     */
    function setWidgetConfigs($key, $val)
    {
        $this->widget_configs[$key] = $val;
        return $this;
    }


    function getMark()
    {
        return;
    }

    function getStartAt($format='')
    {
        if($format){
            return date($format,strtotime(parent::getStartAt()));
        }
        return parent::getStartAt();
    }

    function getEndAt($format='')
    {
        if($format){
            return date($format,strtotime(parent::getEndAt()));
        }
        return parent::getEndAt();
    }

    /**
     * 项目状态（科技厅查看）
     */
    function getState()
    {
        switch ($this->getStatement()) {
            case 1:
                return '填写中';
            case 2:
                return '待依托单位审核';
            case 3:
                return '<span class="font-w600 text-danger"  onclick="return showWindow(\'退回原因\',\'' . site_url('admin/history/index/id/' . $this->getSummaryId().'/type/summary') . '\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因"><i class="fa fa-exclamation-triangle"></i> 依托单位退回</span>';
            case 5:
                return '待主管部门审核';
            case 6:
                return '<span class="font-w600 text-danger"  onclick="return showWindow(\'退回原因\',\'' . site_url('admin/history/index/id/' . $this->getSummaryId().'/type/summary') . '\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因"><i class="fa fa-exclamation-triangle"></i> 主管部门退回</span>';
            case 9:
                return '待科技厅审核';
            case 10:
                return '科技厅已审核';
            case 12:
                return '<span class="font-w600 text-danger"  onclick="return showWindow(\'退回原因\',\'' . site_url('admin/history/index/id/' . $this->getSummaryId().'/type/summary') . '\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因"><i class="fa fa-exclamation-triangle"></i> 科技厅退回</span>';
            case 13://已经分组
                return '科技厅已审核';
            case 14:
                return '项目评审中';
            case 15:
                return '项目评审中';
            case 17:
                return '项目评审中';
            case 18://分配专家
                return '已分配专家';
            case 20:
                return '评审完毕';
            case 19:
                return '初评完毕';
                break;
            case 21:
                return '现场考察完毕';
                break;
            case 22:
                return '科技厅已审核';
                break;
            case 23:
                return '科技厅已审核';
                break;
            case 24:
                return '科技厅已审核';
                break;
            case 25:
                return '科技厅已审核';
                break;
            case 26:
                return '科技厅已审核';
                break;
            case 27:
                return '科技厅已审核';
                break;
            case 28:
                return '未通过';
                break;
            case 29:
                return '已批建';
                break;
            case 30:
                return "通过验收";
                break;
            case 35:
                return '<s style="color:red">未通过验收</s>';
                break;
            case 40:
                return '<s style="color:red">已经终止</s>';
                break;
            case 99:
                return "已过期";
                break;
            default:
                return '<s style="color:red">未知状态</s>';
                break;
        }
    }

    public function getData($code,$indexYear='')
    {
        $summaryData = sf::getModel('SummaryDatas')->selectBySummaryId($this->getSummaryId(),$code,$indexYear);
        if($summaryData->isNew()){
            $summaryData->setDeclareYear($this->getDeclareYear());
            $summaryData->setPlatformId($this->getPlatformId());
            $summaryData->setCompanyId($this->getCompanyId());
        }
        return $summaryData;
    }

    public function getText($code,$indexYear='')
    {
        $summaryData = sf::getModel('SummaryTexts')->selectBySummaryId($this->getSummaryId(),$code,$indexYear);
        if($summaryData->isNew()){
            $summaryData->setCompanyId($this->getCompanyId());
            $summaryData->setPlatformId($this->getPlatformId());
        }
        return $summaryData;
    }

    public function setData($data,$code,$indexYear='')
    {
        $summaryData = sf::getModel('SummaryDatas')->selectBySummaryId($this->getSummaryId(),$code,$indexYear);
        if($summaryData->isNew()){
            $summaryData->setCompanyId($this->getCompanyId());
            $summaryData->setPlatformId($this->getPlatformId());
            $summaryData->setCreatedAt(date('Y-m-d H:i:s'));
        }
        $summaryData->setData($data);
        $summaryData->save();
        return $summaryData;
    }

    public function setDatas($datas,$indexYear='')
    {
        foreach ($datas as $code=>$data){
            $this->setData($data,$code,$indexYear);
        }
    }

    public function setText($content,$code,$indexYear='')
    {
        $summaryData = sf::getModel('SummaryTexts')->selectBySummaryId($this->getSummaryId(),$code,$indexYear);
        if($summaryData->isNew()){
            $summaryData->setCompanyId($this->getCompanyId());
            $summaryData->setPlatformId($this->getPlatformId());
            $summaryData->setCreatedAt(date('Y-m-d H:i:s'));
        }
        if(is_array($content)) $content = json_encode($content,JSON_UNESCAPED_UNICODE);
        $summaryData->setContent($content);
        $summaryData->save();
        return $summaryData;
    }

    public function setTexts($contents,$indexYear='')
    {
        foreach ($contents as $code=>$content){
            $this->setText($content,$code,$indexYear);
        }
    }

    public function getCatSubject()
    {
        return sf::getModel('Categorys',parent::getCatId())->getSubject();
    }

    /**
     * 判断是否可以打印
     */
    function enablePrint()
    {
        if (!in_array($this->getStatement(), array('0', '1', '3', '6'))) return true;
        else return false;
    }


    function attachements($item_type = '', $single_model = false)
    {
        if (!$item_type) return array();

        if ($single_model)
            $sql = ' limit 0,1';

        $files = sf::getModel("Filemanager")->selectAll(" item_id = '" . parent::getSummaryId() . "' AND item_type = '" . $item_type . "' ", ' order by id desc' . $sql);
        return $files->toArray();
    }

    /**
     * 取得学科路径
     */
    function getSubjectPath($level = 0, $dv = '/')
    {
        if ($level) {
            $data = [];
            $_code = [];
            for ($i = 1; $i <= $level; $i++) {
                if ($i == 1) $_code[] = substr(parent::getSubjectId(), 0, 3);
                if ($i == 2) $_code[] = substr(parent::getSubjectId(), 0, 6);
                if ($i == 3) $_code[] = substr(parent::getSubjectId(), 0, 8);
            }
            $addWhere = "`code` IN ('" . implode("','", $_code) . "') and type = 1 ";
            $trees = sf::getModel("Subjects")->selectAll($addWhere, "ORDER BY code ASC");
            while ($tree = $trees->getObject()) {
                $_tree[$tree->getCode()] = trim($tree->getSubject());
            }
            $data[] = implode($dv, $_tree);
//            $_code2 = [];
//            for ($i = 1; $i <= $level; $i++) {
//                if ($i == 1) $_code2[] = substr(parent::getSubjectId2(), 0, 3);
//                if ($i == 2) $_code2[] = substr(parent::getSubjectId2(), 0, 6);
//                if ($i == 3) $_code2[] = substr(parent::getSubjectId2(), 0, 8);
//            }
//            $addWhere = "`code` IN ('" . implode("','", $_code2) . "') and type = 1 ";
//            $trees = sf::getModel("Subjects")->selectAll($addWhere, "ORDER BY code ASC");
//            while ($tree = $trees->getObject()) {
//                $_tree2[$tree->getCode()] = trim($tree->getSubject());
//            }
//            $data[] = implode($dv, $_tree2);
            return implode('<br>', $data);
        } else return parent::getSubjectName();
    }

    public function getSubjectIds($k = '')
    {
        $ids = parent::getSubjectIds();
        $idArr = explode(',', $ids);
        if ($k) {
            return $idArr[$k - 1];
        }
        return $idArr;
    }

    function contents($item_type = '')
    {
        if (!$item_type) return array();

        $sql = ' limit 0,1';

        $files = sf::getModel("SummaryContents")->selectAll(" summary_id = '" . parent::getSummaryId() . "' AND widget_name = '" . $item_type . "' ", ' order by id desc' . $sql);
        $arr = $files->toArray();
        return $arr[0];
    }

    function setConfigs($key, $val = '')
    {
        $configs = (array)json_decode(parent::getConfigs(), true);

        if (!is_array($key)) $key = array($key => $val);
        foreach ($key as $_key => $_val) {
            $_ks = explode('.', $_key);
            $_code = '$configs';
            for ($i = 0, $n = count($_ks); $i < $n; $i++)
                $_code .= '[\'' . $_ks[$i] . '\']';
            $_code .= '= $_val;';
            @eval($_code);//执行语句,不是最佳选择，寻找替代方法
        }

        parent::setConfigs(json_encode($configs));
        return parent::save();
    }

    function getConfigs($key = '')
    {
        $configs = json_decode(parent::getConfigs(), true);
        $is_file = false;

        if ($key) {
            $_key = explode('.', $key);
            foreach ($_key as $k) {
                if ($k == 'file') $is_file = true;
                $configs = $configs[$k];
            }
        }
        return $configs;
    }

}