<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BasePlatformLeaders;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class PlatformLeaders extends BasePlatformLeaders
{
    function selectByPlatformId($platformId='')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `platform_id` = '{$platformId}' ");
        if($db->num_rows($query)) {
            $this->fillObject($db->fetch_array($query));
        }
        else {
            $platformId = $platformId ? $platformId : sf::getLib("MyString")->getRandString();
            $this->setPlatformId($platformId);
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }
}