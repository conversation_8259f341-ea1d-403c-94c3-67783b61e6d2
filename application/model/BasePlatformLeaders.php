<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BasePlatformLeaders extends BaseModel
{
	private $id;
	private $platform_id;
	private $user_name;
	private $idcard_type;
	private $idcard;
	private $user_sex;
	private $user_birthday;
	private $user_duty;
	private $title_rank;
	private $user_title;
	private $education;
	private $degree;
	private $honor;
	private $major;
	private $corporation_name;
	private $research_area;
	private $note;
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "platform_leaders";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getPlatformId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->platform_id,0,$len,"utf-8");
			else return substr($this->platform_id,0,$len);
		}
		return $this->platform_id;
	}

	public function getUserName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_name,0,$len,"utf-8");
			else return substr($this->user_name,0,$len);
		}
		return $this->user_name;
	}

	public function getIdcardType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->idcard_type,0,$len,"utf-8");
			else return substr($this->idcard_type,0,$len);
		}
		return $this->idcard_type;
	}

	public function getIdcard($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->idcard,0,$len,"utf-8");
			else return substr($this->idcard,0,$len);
		}
		return $this->idcard;
	}

	public function getUserSex($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_sex,0,$len,"utf-8");
			else return substr($this->user_sex,0,$len);
		}
		return $this->user_sex;
	}

	public function getUserBirthday($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_birthday,0,$len,"utf-8");
			else return substr($this->user_birthday,0,$len);
		}
		return $this->user_birthday;
	}

	public function getUserDuty($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_duty,0,$len,"utf-8");
			else return substr($this->user_duty,0,$len);
		}
		return $this->user_duty;
	}

	public function getTitleRank()
	{
		return $this->title_rank;
	}

	public function getUserTitle($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_title,0,$len,"utf-8");
			else return substr($this->user_title,0,$len);
		}
		return $this->user_title;
	}

	public function getEducation($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->education,0,$len,"utf-8");
			else return substr($this->education,0,$len);
		}
		return $this->education;
	}

	public function getDegree($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->degree,0,$len,"utf-8");
			else return substr($this->degree,0,$len);
		}
		return $this->degree;
	}

	public function getHonor($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->honor,0,$len,"utf-8");
			else return substr($this->honor,0,$len);
		}
		return $this->honor;
	}

	public function getMajor($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->major,0,$len,"utf-8");
			else return substr($this->major,0,$len);
		}
		return $this->major;
	}

	public function getCorporationName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->corporation_name,0,$len,"utf-8");
			else return substr($this->corporation_name,0,$len);
		}
		return $this->corporation_name;
	}

	public function getResearchArea($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->research_area,0,$len,"utf-8");
			else return substr($this->research_area,0,$len);
		}
		return $this->research_area;
	}

	public function getNote($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->note,0,$len,"utf-8");
			else return substr($this->note,0,$len);
		}
		return $this->note;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setPlatformId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->platform_id !== $v)
		{
			$this->platform_id = $v;
			$this->fieldData["platform_id"] = $v;
		}
		return $this;

	}

	public function setUserName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_name !== $v)
		{
			$this->user_name = $v;
			$this->fieldData["user_name"] = $v;
		}
		return $this;

	}

	public function setIdcardType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->idcard_type !== $v)
		{
			$this->idcard_type = $v;
			$this->fieldData["idcard_type"] = $v;
		}
		return $this;

	}

	public function setIdcard($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->idcard !== $v)
		{
			$this->idcard = $v;
			$this->fieldData["idcard"] = $v;
		}
		return $this;

	}

	public function setUserSex($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_sex !== $v)
		{
			$this->user_sex = $v;
			$this->fieldData["user_sex"] = $v;
		}
		return $this;

	}

	public function setUserBirthday($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_birthday !== $v)
		{
			$this->user_birthday = $v;
			$this->fieldData["user_birthday"] = $v;
		}
		return $this;

	}

	public function setUserDuty($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_duty !== $v)
		{
			$this->user_duty = $v;
			$this->fieldData["user_duty"] = $v;
		}
		return $this;

	}

	public function setTitleRank($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->title_rank !== $v)
		{
			$this->title_rank = $v;
			$this->fieldData["title_rank"] = $v;
		}
		return $this;

	}

	public function setUserTitle($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_title !== $v)
		{
			$this->user_title = $v;
			$this->fieldData["user_title"] = $v;
		}
		return $this;

	}

	public function setEducation($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->education !== $v)
		{
			$this->education = $v;
			$this->fieldData["education"] = $v;
		}
		return $this;

	}

	public function setDegree($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->degree !== $v)
		{
			$this->degree = $v;
			$this->fieldData["degree"] = $v;
		}
		return $this;

	}

	public function setHonor($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->honor !== $v)
		{
			$this->honor = $v;
			$this->fieldData["honor"] = $v;
		}
		return $this;

	}

	public function setMajor($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->major !== $v)
		{
			$this->major = $v;
			$this->fieldData["major"] = $v;
		}
		return $this;

	}

	public function setCorporationName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_name !== $v)
		{
			$this->corporation_name = $v;
			$this->fieldData["corporation_name"] = $v;
		}
		return $this;

	}

	public function setResearchArea($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->research_area !== $v)
		{
			$this->research_area = $v;
			$this->fieldData["research_area"] = $v;
		}
		return $this;

	}

	public function setNote($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->note !== $v)
		{
			$this->note = $v;
			$this->fieldData["note"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `platform_leaders` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"platform_id" => $this->getPlatformId(),
			"user_name" => $this->getUserName(),
			"idcard_type" => $this->getIdcardType(),
			"idcard" => $this->getIdcard(),
			"user_sex" => $this->getUserSex(),
			"user_birthday" => $this->getUserBirthday(),
			"user_duty" => $this->getUserDuty(),
			"title_rank" => $this->getTitleRank(),
			"user_title" => $this->getUserTitle(),
			"education" => $this->getEducation(),
			"degree" => $this->getDegree(),
			"honor" => $this->getHonor(),
			"major" => $this->getMajor(),
			"corporation_name" => $this->getCorporationName(),
			"research_area" => $this->getResearchArea(),
			"note" => $this->getNote(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->platform_id = '';
		$this->user_name = '';
		$this->idcard_type = '';
		$this->idcard = '';
		$this->user_sex = '';
		$this->user_birthday = '';
		$this->user_duty = '';
		$this->title_rank = '';
		$this->user_title = '';
		$this->education = '';
		$this->degree = '';
		$this->honor = '';
		$this->major = '';
		$this->corporation_name = '';
		$this->research_area = '';
		$this->note = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["platform_id"]) && $this->platform_id = $data["platform_id"];
		isset($data["user_name"]) && $this->user_name = $data["user_name"];
		isset($data["idcard_type"]) && $this->idcard_type = $data["idcard_type"];
		isset($data["idcard"]) && $this->idcard = $data["idcard"];
		isset($data["user_sex"]) && $this->user_sex = $data["user_sex"];
		isset($data["user_birthday"]) && $this->user_birthday = $data["user_birthday"];
		isset($data["user_duty"]) && $this->user_duty = $data["user_duty"];
		isset($data["title_rank"]) && $this->title_rank = $data["title_rank"];
		isset($data["user_title"]) && $this->user_title = $data["user_title"];
		isset($data["education"]) && $this->education = $data["education"];
		isset($data["degree"]) && $this->degree = $data["degree"];
		isset($data["honor"]) && $this->honor = $data["honor"];
		isset($data["major"]) && $this->major = $data["major"];
		isset($data["corporation_name"]) && $this->corporation_name = $data["corporation_name"];
		isset($data["research_area"]) && $this->research_area = $data["research_area"];
		isset($data["note"]) && $this->note = $data["note"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}