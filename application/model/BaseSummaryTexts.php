<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseSummaryTexts extends BaseModel
{
	private $id;
	private $summary_id;
	private $platform_id;
	private $company_id;
	private $index_code;
	private $index_year;
	private $content;
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "summary_texts";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getSummaryId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->summary_id,0,$len,"utf-8");
			else return substr($this->summary_id,0,$len);
		}
		return $this->summary_id;
	}

	public function getPlatformId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->platform_id,0,$len,"utf-8");
			else return substr($this->platform_id,0,$len);
		}
		return $this->platform_id;
	}

	public function getCompanyId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->company_id,0,$len,"utf-8");
			else return substr($this->company_id,0,$len);
		}
		return $this->company_id;
	}

	public function getIndexCode($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->index_code,0,$len,"utf-8");
			else return substr($this->index_code,0,$len);
		}
		return $this->index_code;
	}

	public function getIndexYear($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->index_year,0,$len,"utf-8");
			else return substr($this->index_year,0,$len);
		}
		return $this->index_year;
	}

	public function getContent($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->content,0,$len,"utf-8");
			else return substr($this->content,0,$len);
		}
		return $this->content;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setSummaryId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->summary_id !== $v)
		{
			$this->summary_id = $v;
			$this->fieldData["summary_id"] = $v;
		}
		return $this;

	}

	public function setPlatformId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->platform_id !== $v)
		{
			$this->platform_id = $v;
			$this->fieldData["platform_id"] = $v;
		}
		return $this;

	}

	public function setCompanyId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->company_id !== $v)
		{
			$this->company_id = $v;
			$this->fieldData["company_id"] = $v;
		}
		return $this;

	}

	public function setIndexCode($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->index_code !== $v)
		{
			$this->index_code = $v;
			$this->fieldData["index_code"] = $v;
		}
		return $this;

	}

	public function setIndexYear($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->index_year !== $v)
		{
			$this->index_year = $v;
			$this->fieldData["index_year"] = $v;
		}
		return $this;

	}

	public function setContent($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->content !== $v)
		{
			$this->content = $v;
			$this->fieldData["content"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `summary_texts` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"summary_id" => $this->getSummaryId(),
			"platform_id" => $this->getPlatformId(),
			"company_id" => $this->getCompanyId(),
			"index_code" => $this->getIndexCode(),
			"index_year" => $this->getIndexYear(),
			"content" => $this->getContent(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->summary_id = '';
		$this->platform_id = '';
		$this->company_id = '';
		$this->index_code = '';
		$this->index_year = '';
		$this->content = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["summary_id"]) && $this->summary_id = $data["summary_id"];
		isset($data["platform_id"]) && $this->platform_id = $data["platform_id"];
		isset($data["company_id"]) && $this->company_id = $data["company_id"];
		isset($data["index_code"]) && $this->index_code = $data["index_code"];
		isset($data["index_year"]) && $this->index_year = $data["index_year"];
		isset($data["content"]) && $this->content = $data["content"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}