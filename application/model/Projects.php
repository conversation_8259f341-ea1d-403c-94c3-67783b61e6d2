<?php
namespace App\Model;
use PhpOffice\PhpWord\Reader\HTML;
use Sofast\Core\Log;
use Sofast\Core\router;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use Sofast\Core\config;
use Sofast\Support\template;
use Sofast\Support\View;
use App\Model\BaseProjects;
use App\Facades\PDF;

class Projects extends BaseProjects
{
    private $baseinfo         = NULL;    //项目基本信息
    private $service         = NULL;    //住院患者医疗服务情况
    private $part             = NULL;    //项目主体信息
    private $details          = NULL;    //项目描述
    private $incomes          = NULL;    //经费收入（仅应用基础使用，考虑删除）
    private $outlays          = NULL;    //经费支付
    private $teams            = NULL;    //参加人员,老项目兼容
    private $team             = NULL;    //项目组成人员
    private $units            = NULL;    //参加单位
    private $user             = NULL;    //申报者
    private $researcher       = NULL;    //申报者
    private $corporation      = NULL;    //申报单位
    private $budget_book      = NULL;    //经费预算书
    private $plan_book        = NULL;    //计划书
    private $complete_book    = NULL;    //验收书
    private $audit            = NULL;    //审计报告
    private $expands          = array(); //听用
    private $type             = NULL;    //项目类型
    private $guide            = NULL;
    private $tags             = NULL;   //标签信息
    private $archive          = NULL;   //档案
    private $reports          = NULL;   //科技报告目标信息
    private $schedules        = NULL;   //项目执行进度
    private $achievements     = NULL;   //成果登记
    private $jxkh             = NULL;   //绩效考核
    private $members          = NULL;   //参加人员
    private $memberStructures          = NULL;   //参加人员
    private $memberSeniors          = NULL;   //参加人员
    private $experts          = NULL;   //专家表
    private $target           = NULL;   //绩效目标
    private $company          = NULL;   //申报单位
    private $money            = NULL;   //经费
    private $fund            = NULL;   //项目资金
    private $debt             = NULL;   //债权融资
    private $techPersons      = NULL;   //科研人员
    private $repair           = NULL;   //修购资金
    private $record           = NULL;   //备案信息表
    private $gazelleExtra     = NULL;   //瞪羚企业
    private $worker           = NULL;   //解析器
    private $widgets          = array();//部件列表
    private $widget_configs   = array();//部件附加配置信息
    private $summary          = NULL;   //项目简介
    private $manager          = NULL;   //项目管理员
    private $conclusion       = NULL;   //验收结论
    private $project_contract = NULL;   //任务合同扩展信息
    private $plan = NULL;               //项目计划
    private $index = NULL;              //项目指标
    private $awards = NULL;              //奖励
    private $patents = NULL;              //专利
    private $devices = NULL;              //设备
    private $diseases = NULL;              //专科主要病种
    private $researchProjects = NULL;      //科研项目
    private $papers = NULL;                 //论文
    private $supports = NULL;               //对口支援
    private $populars = NULL;               //科普及健康宣教
    private $remotes = NULL;               //远程医疗
    private $standards = NULL;               //标准
    private $missions = NULL;               //医疗救治任务
    private $eduprojects = NULL;            //继续教育项目
    private $syjsprojects = NULL;            //适宜技术推广项目
    private $works = NULL;
    private $memberChs = NULL;
    private $memberRcchs = NULL;
    private $memberHxs = NULL;
    private $szbzs = NULL;
    private $guidelines = NULL;
    private $centers = NULL;
    private $academics = NULL;
    private $magazines = NULL;
    private $index_years = [];

    //任务合同扩展对象
    function contract()
    {
        return sf::getModel("ProjectContracts")->selectByProjectId(parent::getProjectId());
    }

    function selectPayfor()
    {
        return sf::getModel("payfor")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY updated_at DESC");
    }

    function getDetails($f = false)
    {
        if ($this->details === NULL || $f) {
            if ($this->getConfigs("model.apply")) {
                $this->details = sf::getModel($this->getConfigs("model.apply"))->selectByProjectId(parent::getProjectId());
            } else {
                $this->details = sf::getModel("Detail" . ucfirst(getFolder(parent::getTypeId())))->selectByProjectId(parent::getProjectId());
            }
        }
        return $this->details;
    }

    function getBaseinfo($type = 'apply', $f = false)
    {
        if ($this->baseinfo[$type] === NULL || $f) $this->baseinfo[$type] = sf::getModel("ProjectBaseinfos")->selectByProjectId(parent::getProjectId(), $type);
        return $this->baseinfo[$type];
    }

    function getService($type = 'apply', $f = false)
    {
        if ($this->service[$type] === NULL || $f) $this->service[$type] = sf::getModel("ProjectServices")->selectByProjectId(parent::getProjectId(), $type);
        return $this->service[$type];
    }

    function getPart($type = 'apply', $f = false)
    {
        if ($this->part[$type] === NULL || $f) $this->part[$type] = sf::getModel("ProjectParts")->selectByProjectId(parent::getProjectId(), $type);
        return $this->part[$type];
    }

    function getIndex($mark='',$f = false)
    {
        if ($this->index[$mark] === NULL || $f) $this->index[$mark] = sf::getModel("ProjectIndexs")->selectByProjectId(parent::getProjectId(),$mark);
        return $this->index[$mark];
    }

    function getPlan($f = false)
    {
        if ($this->plan === NULL || $f) $this->plan = sf::getModel("ProjectPlans")->selectByPlanId(parent::getPlanId());
        return $this->plan;
    }

    /**
     * 获取产品信息
     */
    function product($f = false)
    {
        if ($this->product === NULL || $f) $this->product = sf::getModel("ProjectProducts")->selectByProjectId(parent::getProjectId());
        return $this->product;
    }

    /**
     * 获取项目指纹对象
     */
    function getFingerprint($f = false)
    {
        if ($this->fingerprint === NULL || $f) $this->fingerprint = sf::getModel("ProjectFingerprints")->selectByProjectId(parent::getProjectId());
        return $this->fingerprint;
    }

    function getExpand($f = false)
    {
        if ($this->expand === NULL || $f) $this->expand = sf::getModel("Expands")->selectByProjectId(parent::getProjectId());
        return $this->expand;
    }

    function getAssessment($f = false)
    {
        if ($this->assessment === NULL || $f) $this->assessment = sf::getModel("ProjectAssessments")->selectByProjectId(parent::getProjectId());
        return $this->assessment;
    }

    function conclusion($f = false)
    {
        if ($this->conclusion === NULL || $f) $this->conclusion = sf::getModel("ProjectConclusions")->selectByProjectId(parent::getProjectId());
        return $this->conclusion;
    }

    function getAudit($f = false)
    {
        return $this->audits($f);
    }

    function audits($f = false)
    {
        if ($this->audit === NULL || $f) $this->audit = sf::getModel("ProjectAudits")->selectByProjectId(parent::getProjectId());
        return $this->audit;
    }

    function target($type = 'task', $f = false)
    {
        if ($this->target[$type] === NULL || $f) $this->target[$type] = sf::getModel("ProjectTargets")->selectByProjectId(parent::getProjectId(), $type);
        return $this->target[$type];
    }

    /**
     * 返回项目简介对象
     *
     * @param boolean $f 是否强制更新
     * @return object     项目简介对象
     */
    function summary($f = false)
    {
        if ($this->summary === NULL || $f) $this->summary = sf::getModel("ProjectSummarys")->selectByProjectId(parent::getProjectId());
        return $this->summary;
    }

    /**
     * 获取项目简介
     * @return text 项目简介内容
     */
    function getSummary()
    {
        return $this->summary(true)->getContent();
    }

    /**
     * 保存项目简介
     * @param string $v 项目简介内容
     */
    function setSummary($v)
    {
        if ($this->isNew()) return false;
        $summary = $this->summary(true);
        $summary->setContent($v);
        return $summary->save();
    }

    function tags($f = false)
    {
        if ($this->tags === NULL || $f) $this->tags = sf::getModel("ProjectTags", parent::getId());
        return $this->tags;
    }

    function schedules($type = 'apply', $f = false)
    {
        if ($this->schedules[$type] === NULL || $f) $this->schedules[$type] = sf::getModel("ProjectSchedules")->selectAll("project_id = '" . parent::getProjectId() . "' AND type = '" . $type . "' ", "ORDER BY `sort` ASC,id ASC");
        return $this->schedules[$type];
    }

    function diseases($year = '', $f = false)
    {
        if(empty($year)) $year = $this->getDeclareYear();
        if ($this->diseases[$year] === NULL || $f) $this->diseases[$year] = sf::getModel("ProjectDiseases")->selectAll("project_id = '" . parent::getProjectId() . "' AND `year` = '" . $year . "' ", "ORDER BY id ASC");
        return $this->diseases[$year];
    }

    function researchProjects($type = 'apply', $f = false)
    {
        if ($this->researchProjects[$type] === NULL || $f) $this->researchProjects[$type] = sf::getModel("ProjectResearchProjects")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id asc");
        return $this->researchProjects[$type];
    }

    function papers($type = 'apply', $f = false)
    {
        if ($this->papers[$type] === NULL || $f) $this->papers[$type] = sf::getModel("ProjectPapers")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id asc");
        return $this->papers[$type];
    }

    function supports($type = 'apply', $f = false)
    {
        if ($this->supports[$type] === NULL || $f) $this->supports[$type] = sf::getModel("ProjectSupports")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id asc");
        return $this->supports[$type];
    }

    function populars($type = 'apply', $f = false)
    {
        if ($this->populars[$type] === NULL || $f) $this->populars[$type] = sf::getModel("ProjectPopulars")->selectAll("project_id = '" . parent::getProjectId() . "' and type = '{$type}'", "ORDER BY no ASC,id asc");
        return $this->populars[$type];
    }
    function remotes($type = 'apply', $f = false)
    {
        if ($this->remotes[$type] === NULL || $f) $this->remotes[$type] = sf::getModel("ProjectRemotes")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id asc");
        return $this->remotes[$type];
    }
    function standards($type = 'apply', $standardType='',$f = false)
    {
        if ($this->standards[$type][$standardType] === NULL || $f) $this->standards[$type][$standardType] = sf::getModel("ProjectStandards")->selectAll("project_id = '" . parent::getProjectId() . "' and standard_type = '{$standardType}'", "ORDER BY no ASC,id asc");
        return $this->standards[$type][$standardType];
    }

    function missions($type = 'apply', $f = false)
    {
        if ($this->missions[$type] === NULL || $f) $this->missions[$type] = sf::getModel("ProjectMissions")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id asc");
        return $this->missions[$type];
    }

    function educationProjects($type = 'apply', $f = false)
    {
        if ($this->eduprojects[$type] === NULL || $f) $this->eduprojects[$type] = sf::getModel("ProjectEducationProjects")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id asc");
        return $this->eduprojects[$type];
    }

    function syjsProjects($type = 'apply', $f = false)
    {
        if ($this->syjsprojects[$type] === NULL || $f) $this->syjsprojects[$type] = sf::getModel("ProjectSyjsProjects")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id asc");
        return $this->syjsprojects[$type];
    }

    function awards($type = 'leader', $f = false)
    {
        if ($this->awards[$type] === NULL || $f) $this->awards[$type] = sf::getModel("ProjectAwards")->selectAll("project_id = '" . parent::getProjectId() . "' and category = '{$type}'", "ORDER BY no ASC,id asc");
        return $this->awards[$type];
    }

    function devices($type = 'apply', $f = false)
    {
        if ($this->devices[$type] === NULL || $f) $this->devices[$type] = sf::getModel("ProjectDevices")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id asc");
        return $this->devices[$type];
    }

    function patents($type = 'apply', $f = false)
    {
        if ($this->patents[$type] === NULL || $f) $this->patents[$type] = sf::getModel("ProjectPatents")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id asc");
        return $this->patents[$type];
    }

    function changes()
    {
        return sf::getModel("ProjectChanges")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY id ASC");
    }

    function members($type = 'apply', $f = false)
    {
        $type = strtolower($type);
        if ($this->members[$type] === NULL || $f) {
            $this->members[$type] = sf::getModel("ProjectMembers")->selectAll("project_id = '" . parent::getProjectId() . "' AND type = '" . $type . "' ", "ORDER BY no ASC,id ASC");
            if ($type == 'task') {
                if ($this->members[$type]->getTotal() < 1) {
                    sf::getModel("ProjectMembers")->setProjectId(parent::getProjectId())->copyto('task');//将项目人员表导入任务合同人员表
                    $this->members[$type] = sf::getModel("ProjectMembers")->selectAll("project_id = '" . parent::getProjectId() . "' AND type = 'task' ", "ORDER BY no ASC,id ASC");
                }
            }
        }
        return $this->members[$type];
    }

    function memberStructures($type = 'apply', $f = false)
    {
        $type = strtolower($type);
        if ($this->memberStructures[$type] === NULL || $f) {
            $this->memberStructures[$type] = sf::getModel("ProjectMembers")->selectAll("project_id = '" . parent::getProjectId() . "' AND type = '" . $type . "' and user_type = 'structure'", "ORDER BY no ASC,id ASC");
        }
        return $this->memberStructures[$type];
    }

    /**
     * 防盲工作
     * @param string $type
     * @param false $f
     * @return mixed
     */
    function works($type = 'zzjs', $f = false)
    {
        $type = strtolower($type);
        if ($this->works[$type] === NULL || $f) {
            $this->works[$type] = sf::getModel("ProjectWorks")->selectAll("project_id = '" . parent::getProjectId() . "' AND type = '" . $type . "'", "ORDER BY no ASC,id ASC");
        }
        return $this->works[$type];
    }
    function szbzs( $f = false)
    {
        if ($this->szbzs === NULL || $f) {
            $this->szbzs = sf::getModel("ProjectSzbzs")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id ASC");
        }
        return $this->szbzs;
    }

    function memberSeniors($type = 'apply', $f = false)
    {
        $type = strtolower($type);
        if ($this->memberSeniors[$type] === NULL || $f) {
            $this->memberSeniors[$type] = sf::getModel("ProjectMembers")->selectAll("project_id = '" . parent::getProjectId() . "' AND type = '" . $type . "' and user_type = 'senior'", "ORDER BY no ASC,id ASC");
        }
        return $this->memberSeniors[$type];
    }

    function memberChs($type = 'apply', $f = false)
    {
        $type = strtolower($type);
        if ($this->memberChs[$type] === NULL || $f) {
            $this->memberChs[$type] = sf::getModel("ProjectMembers")->selectAll("project_id = '" . parent::getProjectId() . "' AND type = '" . $type . "' and user_type = 'ch'", "ORDER BY no ASC,id ASC");
        }
        return $this->memberChs[$type];
    }
    function memberRcchs($type = 'apply', $f = false)
    {
        $type = strtolower($type);
        if ($this->memberRcchs[$type] === NULL || $f) {
            $this->memberRcchs[$type] = sf::getModel("ProjectMembers")->selectAll("project_id = '" . parent::getProjectId() . "' AND type = '" . $type . "' and user_type = 'rcch'", "ORDER BY no ASC,id ASC");
        }
        return $this->memberRcchs[$type];
    }
    function memberHxs($type = 'apply', $f = false)
    {
        $type = strtolower($type);
        if ($this->memberHxs[$type] === NULL || $f) {
            $this->memberHxs[$type] = sf::getModel("ProjectMembers")->selectAll("project_id = '" . parent::getProjectId() . "' AND type = '" . $type . "' and user_type = 'hx'", "ORDER BY no ASC,id ASC");
        }
        return $this->memberHxs[$type];
    }

    function guidelines($type = 'apply', $f = false)
    {
        $type = strtolower($type);
        if ($this->guidelines[$type] === NULL || $f) {
            $this->guidelines[$type] = sf::getModel("ProjectGuidelines")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id ASC");
        }
        return $this->guidelines[$type];
    }

    function centers($type = 'apply', $f = false)
    {
        $type = strtolower($type);
        if ($this->centers[$type] === NULL || $f) {
            $this->centers[$type] = sf::getModel("ProjectCenters")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id ASC");
        }
        return $this->centers[$type];
    }

    function academics($type = 'leader', $f = false)
    {
        $type = strtolower($type);
        if ($this->academics[$type] === NULL || $f) {
            $this->academics[$type] = sf::getModel("ProjectAcademics")->selectAll("project_id = '" . parent::getProjectId() . "' and user_type = '{$type}'", "ORDER BY no ASC,id ASC");
        }
        return $this->academics[$type];
    }

    function magazines($type = 'apply', $f = false)
    {
        $type = strtolower($type);
        if ($this->magazines[$type] === NULL || $f) {
            $this->magazines[$type] = sf::getModel("ProjectMagazines")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id ASC");
        }
        return $this->magazines[$type];
    }

    function experts($f = false)
    {
        if ($this->experts === NULL || $f) {
            $this->experts = sf::getModel("ProjectExperts")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY no ASC,id ASC");
        }
        return $this->experts;
    }

    function achievements($f = false)
    {
        return sf::getModel("ProjectAchievements")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY id");
    }

    function reviews()
    {
        return sf::getModel("ProjectReviews")->selectAll("project_id = '" . parent::getProjectId() . "'", "ORDER BY sector_id asc,id asc");
    }

    /**
     * 取得成果登记对象
     * @param boolean $f [description]
     * @return [type]     [description]
     */
    function getAchievement($f = false)
    {
        if ($this->achievements === NULL || $f) $this->achievements = sf::getModel("ProjectAchievements")->selectByProjectId(parent::getProjectId());
        return $this->achievements;
    }

    function reportLists($f = false)
    {
        if ($this->reports === NULL || $f) $this->reports = sf::getModel("ReportLists")->selectByProjectId(parent::getProjectId());
        return $this->reports;
    }

    function archive($f = false)
    {
        if ($this->archive === NULL || $f) $this->archive = sf::getModel("ProjectArchives")->selectByProjectId(parent::getProjectId());
        return $this->archive;
    }

    function getTrains($type='train')
    {
        return sf::getModel("ProjectTrains")->selectAll("project_id = '".parent::getProjectId()."' and type = '{$type}'","order by no asc,id asc");
    }

    function getWorks($type='glpx')
    {
        return sf::getModel("ProjectWorks")->selectAll("project_id = '".parent::getProjectId()."' and type = '{$type}'","order by no asc,id asc");
    }

    function getSzbzs()
    {
        return sf::getModel("ProjectSzbzs")->selectAll("project_id = '".parent::getProjectId()."'","order by no asc,id asc");
    }

    function tagName($div = '、')
    {
        return implode($div, $this->tags()->getTagNames());
    }

    function getTaskBook($f = false)
    {
        if ($this->task_book === NULL || $f) {
            if ($this->getConfigs("model.task")) {
                $this->task_book = sf::getModel($this->getConfigs("model.task"))->selectByProjectId(parent::getProjectId());
            } else {
                $this->task_book = sf::getModel("Task" . ucfirst(getFolder(parent::getTypeId())))->selectByProjectId(parent::getProjectId());
            }
        }
        return $this->task_book;
    }

    function getBudgetBook($type = 'apply', $f = false)
    {
        if ($this->budget_book[$type] === NULL || $f) {
            if ($this->getConfigs("model.budget"))
                $this->budget_book[$type] = sf::getModel($this->getConfigs("model.budget"))->selectByProjectId(parent::getProjectId(), $type);
            else $this->budget_book[$type] = sf::getModel("Budgets")->selectByProjectId(parent::getProjectId(), $type);
        }
        return $this->budget_book[$type];
    }

    /**
     * 兼容老版本写法
     */
    function getPlanBook($f = false)
    {
        if ($this->plan_book === NULL || $f) $this->plan_book = $this->getPlanBookByType(parent::getTypeId());
        return $this->plan_book;
    }

    function getCompleteBook($f = false)
    {
        if ($this->complete_book === NULL || $f) {
            if ($this->getConfigs("model.complete")) {
                $this->complete_book = sf::getModel($this->getConfigs("model.complete"))->selectByProjectId(parent::getProjectId());
            } else {
                $this->complete_book = sf::getModel("Complete" . ucfirst(getFolder(parent::getTypeId())))->selectByProjectId(parent::getProjectId());
            }
        }
        return $this->complete_book;
    }

    /**
     * 兼容老版本写法
     */
    function getComplateBook($f = false)
    {
        if ($this->complate_book === NULL || $f) $this->complate_book = sf::getModel("ProjectCompleteBook")->selectByProjectId(parent::getProjectId());
        return $this->complate_book;
    }


    function getComplete($f = false)
    {
        if ($this->complete_book === NULL || $f) $this->complete_book = sf::getModel("ProjectCompletes")->selectByProjectId(parent::getProjectId());
        return $this->complete_book;
    }

    function getOutlays($f = false)
    {
        if ($this->outlays === NULL || $f) $this->outlays = sf::getModel("DetailOutlay")->selectByProjectId(parent::getProjectId());
        return $this->outlays;
    }

    function getOutlaysForFmqx($f = false)
    {
        if ($this->outlays === NULL || $f) $this->outlays = sf::getModel("DetailOutlayFmqx")->selectByProjectId(parent::getProjectId());
        return $this->outlays;
    }

    function getOutlaysForXxcp($f = false)
    {
        if ($this->outlays === NULL || $f) $this->outlays = sf::getModel("DetailOutlayXxcp")->selectByProjectId(parent::getProjectId());
        return $this->outlays;
    }

    function getPlanBookByType($type = 1)
    {
        //如果手动设置，直接使用手动设置的（解决一部分2012年老任务书改版造成的规则错误）
        if ($this->getConfigs("model.task")) {
            return $this->task_book = sf::getModel($this->getConfigs("model.task"))->selectByProjectId(parent::getProjectId());
        }
        //如果是2012年的项目，并不是成果转化和成果平台就使用统一的任务书
        if (substr($this->getRadicateId(), 0, 4) >= 2012 && !in_array($this->getTypeId(), array('0', '15', '16', '17', '21')))
            return sf::getModel("ProjectPlanBook")->selectByProjectId(parent::getProjectId());
        //如果是以前的项目就以前的任务书填写
        switch ($type) {
            case '3':
                return sf::getModel("ProjectPlanRkx")->selectByProjectId(parent::getProjectId());
                break;
            case '11':
                return sf::getModel("ProjectPlanGjjs")->selectByProjectId(parent::getProjectId());
                break;
            case '15':
            case '17':
            case '21':
                return sf::getModel("ProjectPlanCgzhzx")->selectByProjectId(parent::getProjectId());
                break;
            default:
                return sf::getModel("ProjectPlanBook")->selectByProjectId(parent::getProjectId());
                break;
        }
    }

    function getRKXDetails()
    {
        if ($this->details === NULL) $this->details = sf::getModel("DetailRkx")->selectByProjectId(parent::getProjectId());
        return $this->details;
    }


    //国际合作里的外方表
    function getGjwfDetails()
    {
        if ($this->custom === NULL) $this->custom = sf::getModel("DetailGjwf")->selectByProjectId(parent::getProjectId());
        return $this->custom;
    }

    function user($f = false)
    {
        if ($this->user === NULL || $f) {
            $this->user = sf::getModel("Declarers")->selectByUserId(parent::getUserId() ? parent::getUserId() : input::getInput("session.roleuserid"));
        }
        return $this->user;
    }



    function getUser($f = false)
    {
        return $this->user($f);
    }

    function getManagers()
    {
        return sf::getModel('Managers')->selectAll("is_lock=0");
    }

    function getGuide($f = false)
    {
        if ($this->guide === NULL || $f) $this->guide = sf::getModel("Guides", parent::getGuideid());
        return $this->guide;
    }

    function getCorporation($f = false)
    {
        if ($this->corporation === NULL || $f) $this->corporation = sf::getModel("Corporations")->selectByUserId(parent::getCorporationId() ? parent::getCorporationId() : $f);
        return $this->corporation;
    }

    function getCompany($type = 'apply', $f = false)
    {

        if ($this->company[$type] === NULL || $f) $this->company[$type] = sf::getModel("ProjectCompanys")->selectByProjectId(parent::getProjectId(), $type);
        return $this->company[$type];
    }

    function getDepartment($f = false)
    {

        if ($this->department === NULL || $f) $this->department = sf::getModel("Departments")->selectByUserId(parent::getDepartmentId());
        return $this->department;
    }

    function manager($f = false)
    {
        if ($this->manager === NULL || $f) $this->manager = sf::getModel("ProjectManagers")->selectByUserId(parent::getManagerId());
        return $this->manager;
    }

    function getIncomes()
    {
        if ($this->incomes === NULL) $this->incomes = sf::getModel("DetailIncomes")->selectByProjectId(parent::getProjectId());
        return $this->incomes;
    }

    function getTeams()
    {
        if ($this->teams === NULL) $this->teams = sf::getModel("DetailTeams")->selectByProjectId(parent::getProjectId());
        return $this->teams;
    }

    function teams($f = false)
    {
        if ($this->teams === NULL || $f) $this->teams = sf::getModel("ProjectTeams")->selectByProjectId(parent::getProjectId());
        return $this->teams;
    }


    function getTeam($type = 'apply')
    {
        if ($this->team === NULL) $this->team = sf::getModel('ProjectTeams')->selectByProjectId(parent::getProjectId(), $type);
        return $this->team;
    }

    function getUnits()
    {
        if ($this->units === NULL) $this->units = sf::getModel("DetailUnits")->selectByProjectId(parent::getProjectId());
        return $this->units;
    }

    function getHistoryProjectForUser($type = true)
    {
        $addWhere = $addSql = '';
        $addWhere .= "`user_id` = '" . parent::getUserId() . "' AND `statement` >= 29 ";
        if ($type) $addWhere .= "AND `state_for_complete_book` < 14 ";
        return $this->selectAll($addWhere, $addSql);
    }

    function hasUnCompleteProjectString()
    {
        if ($this->hasUnCompleteProject()) return '<b style="color:red">有</b>';
        else return '无';
    }

    /**
     * 导出数据使用
     */
    function getCompleteMark()
    {
        if ($this->hasUnCompleteProject()) return '有';
        else return '无';
    }

    function hasUnCompleteProject()
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `user_id` = '" . parent::getUserId() . "' AND statement = 29 AND is_subsidy = 0 and type_id <> 81 AND declare_year > '2006' AND type_id NOT IN (SELECT type_id FROM types_switch WHERE is_subsidy > 0) ");
        if ($db->num_rows($query)) return true;
        else return false;
    }

    function selectByProjectId($project_id = '', $type = 1)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `project_id` = '" . $project_id . "' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setProjectId($project_id ? $project_id : sf::getLib("MyString")->getRandString());
            $this->setTypeId($type);
            //$this->setIsShift(4);//最近新报的都是自筹
            $this->setUserId(input::getInput("session.roleuserid"));
            $this->setUserName(input::getInput("session.nickname"));
            $this->setDeclareAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }

    function selectBySubject($subject)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `subject` = '" . $subject . "' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setProjectId(sf::getLib("MyString")->getRandString());
            $this->setSubject($subject);
        }
        return $this;
    }

    function selectBySubjectAndCompanyId($subject,$companyId,$statement=29,$cat_id=225)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `subject` = '{$subject}' and corporation_id = '{$companyId}' and statement = {$statement} and cat_id = {$cat_id}");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setProjectId(sf::getLib("MyString")->getRandString());
            $this->setSubject($subject);
        }
        return $this;
    }

    /**
     * 查找残缺(没有上报的)，没有填写完整的项目
     */
    function selectDeformity($project_type = 1, $project_id = '')
    {
        $addWhere = "user_id = '" . input::getInput("session.roleuserid") . "' and statement IN ('0','1','3','6','12') and type_id = '" . $project_type . "' ";
        $project_id && $addWhere .= "AND `project_id` = '" . $project_id . "' ";
        $project = $this->selectAll($addWhere)->getObject();
        if ($project) return $project;
        else return $this->selectByProjectId($project_id, $project_type);
    }

    /**
     * 按照受理编号检索项目
     */
    function selectByAcceptId($accept_id)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `accept_id` = '" . trim($accept_id) . "' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->cleanObject();
            $this->setProjectId(sf::getLib("MyString")->getRandString());
            $this->setAcceptId($accept_id);
            $this->setUserId(input::getInput("session.roleuserid"));
            $this->setUserName(input::getInput("session.nickname"));
            $this->setDeclareAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }

    function save()
    {
        if ($this->IsNew() && !$this->getSubject()) return false;
//        if ($this->details) $result_details = $this->details->save();
//        if ($this->incomes) $result_incomes = $this->incomes->save();
//        if ($this->teams) $result_teams = $this->teams->save();
//        if ($this->units) $result_units = $this->units->save();
//        if ($this->outlays) $result_outlay = $this->outlays->save();
//        if ($this->plan_book) $result_plan_book = $this->plan_book->save();
//        if ($this->task_book) $result_plan_book = $this->task_book->save();
//        if ($this->audit) $result_audit = $this->audit->save();
//        if ($this->expand) $result_expand = $this->expand->save();
        //保存经费预算
//        if ($this->budget_book) {
//            if ($this->getStateForBudgetBook() != 30 || input::getInput("session.userlevel") == 1)//已经审定后只能超级管理员能修改
//                $result_budget_book = $this->budget_book->save();
//        }
//        if ($this->complete_book) $result_complete_book = $this->complete_book->save();

        return parent::save();
    }

    /**
     * 删除项目
     */
    function delete($force=false)
    {
        if ($this->getStatement() > 1 && $force===false) return false;
        if ($this->isNew()) return false;
        /*$this->getDetails()->delete();
		$this->getIncomes()->delete();
		$this->getTeams()->delete();
		$this->getUnits()->delete();
		$this->getOutlays()->delete();
		$this->getBudgetBook()->delete();
		$this->getTaskBook()->delete();
		$this->getPlanBook()->delete();*/
        //删除参与人员
        $members = $this->getMembers('apply');
        while ($member = $members->getObject()) {
            if ($member->getProjectId() != $this->getProjectId()) continue;
            $member->delete();
        }
        return parent::delete();
    }

    function getAcceptId($len = 0)
    {
        if (parent::getAcceptId()) return parent::getAcceptId();
        else return '';
    }

    function getScore($onlyNumber=false)
    {
        if($this->isCityCountryLevel() && input::getInput("session.userlevel")==4){

        }
        if(!in_array(input::getInput("session.userlevel"),array('1','6','10','99'))){
            if(parent::getScore() == '') return '未评审';
            else return '评审完毕';
        }

        if($onlyNumber) return parent::getScore();

        $score = !in_array(input::session('auth.assess_show'),['total','all']) ? '***' : parent::getScore();
        if(strlen(parent::getScore())>0) return link_to("apply/assess/show/id/".parent::getProjectId(),$score,array('title'=>'查看评分详细情况'));
        else if($this->getStatement() == 18) return link_to("apply/assess/show/id/".parent::getProjectId(),'等待评审',array('title'=>'查看专家分配情况'));
        else return '<s style="color:red">还未评审</s>';
    }

    function getObjectiveScore($onlyNumber=false)
    {
        if($onlyNumber) return parent::getObjectiveScore();

        if(strlen(parent::getObjectiveScore())>0) return link_to("apply/assess/show/id/".parent::getProjectId(),parent::getObjectiveScore(),array('title'=>'查看客观指标详细情况'));

        else return '<s style="color:red">还未打分</s>';
    }

    function getSubjectiveScore($onlyNumber=false)
    {
        if($onlyNumber) return parent::getSubjectiveScore();

        if(strlen(parent::getSubjectiveScore())>0) return link_to("apply/assess/show/id/".parent::getProjectId(),parent::getSubjectiveScore(),array('title'=>'查看主观指标详细情况'));

        else return '<s style="color:red">还未打分</s>';
    }

    function getSystemScore($onlyNumber=false)
    {
        if($onlyNumber) return parent::getSystemScore();

        if(strlen(parent::getSystemScore())>0) return link_to("apply/assess/show/id/".parent::getProjectId(),parent::getSystemScore(),array('title'=>'查看系统评分指标详细情况'));

        else return '<s style="color:red">还未打分</s>';
    }

    function getExpertScore($onlyNumber=false)
    {
        if($onlyNumber) return parent::getExpertScore();

        if(strlen(parent::getExpertScore())>0) return link_to("apply/assess/show/id/".parent::getProjectId(),parent::getExpertScore(),array('title'=>'查看专家评分指标详细情况'));

        else return '<s style="color:red">还未打分</s>';
    }



    function getTypeGroup($len=0)
    {
        if(parent::getTypeGroup()) return parent::getTypeGroup();
        elseif(parent::getGroupId()) return sf::getModel("ProjectGroup",parent::getGroupId())->getGroupSubject();
        else return '<s style="color:red">还未分组</s>';
    }

    function getCompanyTypeGroup($len = 0)
    {
        if (parent::getCompanyAssessType() == 'offline') return '线下评审';
        if (parent::getCompanyTypeGroup()) return parent::getCompanyTypeGroup();
        elseif (parent::getGroupId()) return sf::getModel("ProjectGroup", parent::getGroupId())->getGroupSubject();
        else return '<span style="color:red">还未分组</span>';
    }

    function getGroup()
    {
        return sf::getModel("ProjectGroup", parent::getGroupId());
    }

    function getOfficeSubject($len = 0)
    {
        if (parent::getOfficeSubject()) return parent::getOfficeSubject();
        else return '<span style="color:red">还未分流</span>';
    }

    function setOfficeByIds($ids = '', $office_id = '', $office_name = '')
    {
        if (!$ids) return false;
        $db = sf::getLib("db");
        //分流操作只能在处人员操作之前
        $db->query("UPDATE `" . $this->table . "` SET `office_id` = '" . $office_id . "',`office_subject` = '" . $office_name . "',`updated_at`='" . date("Y-m-d H:i:s") . "',`statement` = '20' WHERE `project_id` IN ('" . $ids . "') AND `statement` <= '20'");
        return $db->affected_rows();
    }

    /**
     * 按照ID更改处室 (立项之后，填写计划任务书发现分流错误)
     */
    function setChangeOfficeByIds($ids = '', $office_id = '', $office_name = '')
    {
        if (!$ids) return false;
        $db = sf::getLib("db");
        //分流操作只能在处人员操作之前
        $db->query("UPDATE `" . $this->table . "` SET `office_id` = '" . $office_id . "',`office_subject` = '" . $office_name . "',`updated_at`='" . date("Y-m-d H:i:s") . "' WHERE `project_id` IN ('" . $ids . "') AND `statement` = '29'");
        return $db->affected_rows();
    }

    /**
     * 按照ID设置处室
     */
    function setOfficeById($id = '', $office_id = '', $office_name = '')
    {
        if (!$ids) return false;
        $db = sf::getLib("db");
        //分流操作只能在处人员操作之前
        $db->query("UPDATE `" . $this->table . "` SET `office_id` = '" . $office_id . "',`office_subject` = '" . $office_name . "',`updated_at`='" . date("Y-m-d H:i:s") . "',`statement` = '20' WHERE `project_id` = '" . $id . "' AND `statement` <= '20'");
        return $db->affected_rows();
    }

    /**
     * 按照项目ID来修改项目的状态
     */
    function setStateByProjectIds($statement = 0, $ids = '')
    {
        if (!$ids) return false;
        $db = sf::getLib("db");
        $db->query("UPDATE `" . $this->table . "` SET `statement` = '" . $statement . "',`updated_at`='" . date("Y-m-d H:i:s") . "' WHERE `project_id` IN ('" . $ids . "') ");
        return $db->affected_rows();
    }

    /**
     * 查看有多少专家推荐该项目
     */
    function getElect()
    {
        return '-';//无用去掉
        //if($this->getExtends('getElect')) return $this->getExtends('getElect');
        //else{
        $total = $yes = 0;
        $result = sf::getModel("ProjectGrade")->selectAll("`project_id` = '" . parent::getProjectId() . "' AND `is_submit` = 2 ");
        while ($grade = $result->getObject()) {
            if (in_array($grade->getState(), array('1', '10', '11', '12', '3'))) $yes++;
            $total++;
        }
        if ($total > 0) $this->setExtends('getElect', $yes . " / " . $total);
        return $yes . " / " . $total;
        //}
    }

    /**
     * 查看有多少专家放弃该项目
     */
    function getWaiver()
    {
        //if($this->getExtends('getWaiver')) return $this->getExtends('getWaiver');
        //else{
        $total = $yes = 0;

        $result = sf::getModel("ProjectGrade")->selectAll("`project_id` = '" . parent::getProjectId() . "' ", "ORDER BY expert_id DESC");
        while ($grade = $result->getObject()) {
            if ($grade->getIsSubmit() == 3) $yes++;
            $total++;
        }
        if ($total > 0) $this->setExtends('getWaiver', $yes . " / " . $total);
        return $yes . " / " . $total;
        //}
    }

    /**
     * 项目导出，申报人信息
     */

    function getDeclarer($getUserId = '')
    {
        if ($this->user === NULL) $this->user = sf::getModel("Declarers")->selectByUserId($getUserId);
        return $this->user;
    }


//立项时判断立项编号是否重复
    function IfRadicateId($radicate_id = '')
    {
        if (!$radicate_id) return 0;
        $db = sf::getLib("db");
        $query = $db->query("select `radicate_id` from `" . $this->table . "` WHERE `radicate_id` = '" . $radicate_id . "' and `project_id`<>'" . parent::getProjectId() . "'");
        return ($db->num_rows($query));
    }

    /**
     * 项目状态（科技厅查看）
     */
    function getState()
    {
        //直接返回过期
        if ($this->getIsWait() == 3) return '<span style="color:red">项目已过期</span>';
//        if ($this->getIsWait() == 10) return '<span style="color:red">项目已移出</span>';
        switch ($this->getStatement()) {
            case 0:
            case 1:
                return '填写中';
            case 2:
                return '待依托单位审核';
            case 3:
                return '<span class="font-w600 text-danger"  onclick="return showWindow(\'退回原因\',\'' . site_url('admin/history/index/id/' . $this->getProjectId()) . '\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因"><i class="fa fa-exclamation-triangle"></i> 申报单位退回</span>';
            case 5:
                return "待主管部门推荐";
            case 6:
                return '<span class="font-w600 text-danger"  onclick="return showWindow(\'退回原因\',\'' . site_url('admin/history/index/id/' . $this->getProjectId()) . '\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因"><i class="fa fa-exclamation-triangle"></i> 主管部门退回</span>';
            case 9:
                return '待科技厅受理';
            case 10:
                return "科技厅已受理";
            case 12:
                return '<span class="font-w600 text-danger"  onclick="return showWindow(\'退回原因\',\'' . site_url('admin/history/index/id/' . $this->getProjectId()) . '\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因"><i class="fa fa-exclamation-triangle"></i> 不予受理</span>';
            case 13://已经分组
                if($this->isCityCountryLevel() && input::getInput("session.userlevel")==4){
                    return "项目已分组";
                }
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6', '7', '8', '20', '21', '31'))) return "项目已分组";
                if($this->isCityCountryLevel()){
                    return "科技厅已受理";
                }
                return "科技厅已受理";
            case 14:
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6', '7', '8', '20', '21', '31'))) return "项目评审中";
                return "科技厅已受理";
            case 15:
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6', '7', '8', '20', '21', '31'))) return "项目评审中";
                return "科技厅已受理";
            case 17:
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6', '7', '8', '20', '21', '31'))) return "项目评审中";
                return "科技厅已受理";
            case 18://分配专家
                if($this->isCityCountryLevel() && input::getInput("session.userlevel")==4){
                    return "已分配专家";
                }
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6')) && input::session('auth.assess_show')==='all') return "已分配专家";
                if($this->isCityCountryLevel()){
                    return "科技厅已受理";
                }
                return "科技厅已受理";
            case 20:
                if($this->getCatId()==271){
                    //重点实验室
                    return '<span class="font-w600 text-danger"  onclick="return showWindow(\'退回原因\',\'' . site_url('user/project/review/id/' . $this->getProjectId()) . '\');" role="button" title="查看退回原因"><i class="fa fa-exclamation-triangle"></i> 待修正</span>';
                }
                if(in_array(input::getInput("session.userlevel"), array('1', '5', '6')) && input::session('auth.assess_show')==='all') return "评审完毕";
                return "科技厅已受理";
            case 21:
                return '待依托单位审核';
                break;
            case 22:
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6', '7', '8', '20', '21', '31'))) return '<span style="color:red">不预推荐</span>';
                return "科技厅已受理";
            case 23:
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6', '7', '8', '20', '21', '31'))) return '预推荐';
                return "科技厅已受理";
            case 24:
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6', '7', '8', '20', '21', '31'))) return '<span style="color:red">不推荐</span>';
                return "科技厅已受理";
            case 25:
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6', '7', '8', '20', '21', '31'))) return '已推荐';
                return "科技厅已受理";
            case 26:
                return '待科技厅审核';
            case 27:
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6', '7', '8', '20', '21', '31'))) return '通过审定';
                return "科技厅已受理";
            case 28:
                return "未批建";
            case 29:
                return "已批建";
            case 30:
                return "已验收";
            case 31:
                return "已经结题";
            case 35:
                return '<span style="color:red">未通过验收</span>';
            case 40:
                return '<span style="color:red">已经终止</span>';
            case 99:
                return "已过期";
            default:
                return '<span style="color:red">未知状态</span>';
        }
    }


    function getStateForComplete()
    {
        if ($this->isGovernmentProject()) return '不填报验收报告';
        switch ($this->getStateForCompleteBook()) {
            case 0:
                return '未填写或未上报';
            case 1:
                return '结题报告填写中';
            case 2:
                return '待承担单位审核';
            case 3:
                return '<span class="font-w600 text-danger"  onclick="return showWindow(\'退回原因\',\'' . site_url('admin/history/index/id/' . $this->getProjectId()) . '\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因"><i class="fa fa-exclamation-triangle"></i> 承担单位退回</span>';
            case 5:
                return '待主管部门审核';
            case 6:
                return '<span class="font-w600 text-danger"  onclick="return showWindow(\'退回原因\',\'' . site_url('admin/history/index/id/' . $this->getProjectId()) . '\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因"><i class="fa fa-exclamation-triangle"></i> 主管部门退回</span>';
            case 9:
                return '待省卫生健康委审核';
            case 10:
                return '省卫生健康委已审核';
            case 12:
                return '<span class="font-w600 text-danger"  onclick="return showWindow(\'退回原因\',\'' . site_url('admin/history/index/id/' . $this->getProjectId()) . '\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因"><i class="fa fa-exclamation-triangle"></i> 省卫生健康委退回</span>';
            case 13://已分组
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6', '7', '8', '20', '21', '31'))) return "项目已分组";
                return "省卫生健康委已审核";
            case 18://分配专家
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6')) && input::session('auth.assess_show')==='all') return "已分配专家";
                return "省卫生健康委已审核";
            case 20:
                if (in_array(input::getInput("session.userlevel"), array('1', '5', '6')) && input::session('auth.assess_show')==='all') return "评审完毕";
                return "省卫生健康委已受理";
            default:
                return '<span style="color:red">未知状态</span>';
        }
    }

    function getStateForTask()
    {
//        if (!$this->needTask()) return '不填报任务合同';
        switch ($this->getStateForPlanBook()) {
            case 0:
                return '未填写或未上报';
            case 1:
                return '任务书填写中';
            case 2:
                return '待承担单位审核';
            case 3:
                return '<span style="color:red">承担单位退回</span>';
            case 4:
                return '待主管部门审核';
            case 5:
                return '<span style="color:red">主管部门退回</span>';
            case 6:
                return '待省卫生健康委审核';
            case 7:
                return '<span style="color:red">省卫生健康委退回</span>';
            case 9:
                return '<span style="color:red">省卫生健康委驳回</span>';
            case 10:
                return '省卫生健康委已审核';
            default:
                return '<span style="color:red">未知状态</span>';
        }
    }

    function getStateForReport()
    {
        switch ($this->getIsReport()) {
            case 1:
                return '待填写科技报告';
                break;
            case 5:
                return '科技报告通过';
                break;
            default:
                return '报告在填写中';
                break;
        }
    }

    /**
     * 经费退还凭证状态
     */
    function getStateForRefund($len = 0)
    {
        switch (parent::getStateForRefund()) {
            case 0:
                return '还未上传凭证';
                break;
            case 1:
                return '凭证上传中';
                break;
            case 9:
                if ($this->getIsShift() == 2) return '待推荐单位确认';
                else return '待省卫生健康委确认';
                break;
            case 20:
                return '未上传退款凭证';
                break;
            case 25:
                return '未按约定期限退款';
                break;
            case 27:
                return '未按约定金额退款';
                break;
            case 30:
                if ($this->getIsShift() == 2) return '推荐单位已确认';
                else return '省卫生健康委已确认';
                break;
            default:
                return '状态未知';
                break;
        }
    }

    /**
     * 绩效考核状态
     */
    function getStateForAudit($len = 0)
    {
        switch (parent::getStateForAudit()) {
            case 0:
            case 1:
                return '绩效考核填写中';
                break;
            case 2:
                return '待承担单位推荐';
                break;
            case 3:
                return '<span style="color:red">承担单位驳回</span>';
                break;
            case 4:
                return '等待推荐单位审核';
                break;
            case 5:
                return '<span style="color:red">推荐单位退回</span>';
                break;
            case 6:
                return '待省卫生健康委受理';
                break;
            case 7:
            case 9:
                return '<span style="color:red">省卫生健康委退回</span>';
                break;
            case 10:
                return '省卫生健康委同意签署';
                break;
            default:
                return '<span style="color:red">未知状态</span>';
                break;
        }
    }

    /**
     * 经费预算书状态
     */
    function getStateForBudget($len = 0)
    {
        if (!$this->getBudgetOpen()) return '不填写预算书';
        switch ($this->getStateForBudgetBook()) {
            case 0:
                return '经费预算未填写';
                break;
            case 1:
                return '经费预算填写中';
                break;
            case 2:
                return '申报单位退回';
                break;
            case 3:
                return '预算退回';
                break;
            case 4:
                return '预算退回';
                break;
            case 5:
                return '预算退回';
                break;
            case 6:
                return '预算退回';
                break;
            case 7:
                return '待补充材料';
                break;
            case 10:
                return '待申报单位推荐';
                break;
            case 15:
                return "等待省卫生健康委受理";
                break;
            case 16:
                return '形式审查通过';
                break;
            case 18:
                return '在线评审中';
                break;
            case 19:
                return '在线评审完毕';
                break;
            case 20:
                if (!in_array(input::getInput("session.userleve"), array('40', '41', '42'))) return '省卫生健康委审核中';
                return '等待省卫生健康委复审';
                break;
            case 21:
                return '预算已完善待审';
                break;
            case 22:
                return '已补充材料';
                break;
            case 23:
                return '预算书复评中';//等待复评分组
                break;
            case 24:
                return '预算书复评中';
                break;
            case 25:
                if (!in_array(input::getInput("session.userleve"), array('40', '41', '42'))) return '省卫生健康委审核中';
                return '等待省卫生健康委复核';
                break;
            case 26:
                return '预算书复评完毕';
                break;
            case 28:
                if (!in_array(input::getInput("session.userleve"), array('40', '41', '42'))) return '省卫生健康委审核中';
                return '等条财处审定';
                break;
            case 30:
                return '预算审定';
                break;
            case 99:
                return '预算书作废';
                break;
            default:
                return '<span style="color:red">未知状态</span>';
                break;
        }
    }

    //取得文件列表
//    function getAttachments($type = '', $limit = 0, $orders = 'order by no asc,id asc')
//    {
//        $addWhere = "item_id = '" . $this->getProjectId() . "' ";
//        $type && $addWhere .= " AND item_type = '" . $type . "' ";
//        return sf::getModel("filemanager")->selectAll($addWhere, $orders, $limit);
//    }

    //项目附件
    function attachments($type = '',$order='item_type ASC,created_at DESC')
    {
        $addWhere = "item_id = '" . $this->getProjectId() . "' ";
        if ($type) {
            if (stripos($type, '*') !== false) $addWhere .= " AND item_type Like '" . str_replace('*', '%', $type) . "' ";
            else $addWhere .= " AND item_type = '" . $type . "' ";
        }
        return sf::getModel("ProjectAttachments")->selectAll($addWhere, "ORDER BY {$order}");
    }

    //检查信息
    function checks($type = '')
    {
        $addWhere = "project_id = '" . $this->getProjectId() . "' ";
        $type && $addWhere .= " AND type = '" . $type . "' ";
        return sf::getModel("ProjectChecks")->selectAll($addWhere, "ORDER BY type ASC");
    }

    //
    function setCorporationId($ids = '')
    {
        parent::setCorporationId($ids);
        $this->getUser(true)->setCorporationId($ids);
        return true;
    }

    function getType($f = false)

    {
        if ($this->type === NULL || $f) $this->type = sf::getModel("types", parent::getTypeId());
        return $this->type;
    }

    /**
     * 返回常见URL地址
     * $type为返回类型包括project,budget,task,complete
     */
    function getUrl($t = 'project')
    {
        //如果项目内置则内置优先
        //如果内有内置则更具项目类型产生URL
        $type = sf::getModel("types", $this->getTypeId());
        switch ($t) {
            case 'project':
                $url = site_url($type->getProjectPath() . "/id/" . $this->getProjectId());
                break;
            case 'task':
                $url = site_url($type->getTaskPath() . "/id/" . $this->getProjectId());
                break;
            default:
                $url = site_url($type->getProjectPath() . "/id/" . $this->getProjectId());
                break;
        }
        return $url;
    }

    /**
     * 取得项目评审信息
     */
    function selectAssess()
    {
        return sf::getModel("ProjectGrade")->selectAll("project_id = '".parent::getProjectId()."'","ORDER BY score DESC");
    }

    /**
     * 取得子项目数据集
     */
    function selectSubordinate()
    {
        return sf::getModel("Projects")->selectAll("is_package = 2 AND main_project_id = '" . parent::getProjectId() . "' ", "ORDER BY updated_at DESC");
    }

    /**
     * 取得项目评审信息
     */
    function selectBudgetAssess($output = false)
    {
        $addWhere = "project_id = '" . parent::getProjectId() . "' ";
        $output && $addWhere .= "AND role = '是' ";
        //if(in_array(input::getInput("session.userlevel"),array('10','11')))
        //$addWhere .= "AND expert_id = '".input::getInput("session.roleuserid")."' ";
        return sf::getModel("AssessGrade")->selectAll($addWhere, "ORDER BY `role` DESC");
    }

    /**
     * 取得项目复审信息
     */
    function selectBudgetAssess_review($output = false, $group_id = '')
    {
        $addWhere = "project_id = '" . parent::getProjectId() . "' ";
        $group_id && $addWhere .= "AND group_id = " . $group_id;
        $output && $addWhere .= "AND role = '是' ";
        return sf::getModel("BudgetGrades")->selectAll($addWhere, "ORDER BY `role` DESC");
    }

    /**
     * 取得项目复审信息
     */
    function selectBudgetAssessGroup_review()
    {
        $addWhere = "project_id = '" . parent::getProjectId() . "' ";
        return sf::getModel("BudgetGrades")->selectAll($addWhere, "GROUP BY group_id");
    }

    /**
     * 取得项目评审信息
     */
    function getAssessRoler($user_id = '', $type = 'budget')
    {
        $user_id = $user_id ? $user_id : input::getInput("session.roleuserid");
        $addWhere = "project_id = '" . parent::getProjectId() . "' AND expert_id = '" . $user_id . "' ";
        $roles = sf::getModel("AssessGrade")->selectAll($addWhere, "ORDER BY updated_at DESC", 1);
        if ($roles->getTotal()) return $roles->getObject()->getRole();
        else return NULL;
    }

    /**
     * 返回特殊标记
     */
    function getSpecialMark()
    {
        $msg = array();
        if ($this->getTaskOpen()) $msg[] = '<em>预推荐</em>';
        if ($this->getBudgetOpen() == 1) $msg[] = '<em>服务中心评估预算</em>';
        if ($this->getBudgetOpen() == 2) $msg[] = '<em>处室评估预算</em>';
        if ($this->getIsSoftware() == 1) $msg[] = '<em>软科学、应用基础研究、软件类或集成电路</em>';
        if ($this->getIsSoftware() == 2) $msg[] = '<em>数学等纯理论基础研究</em>';
        return implode("、", $msg);
    }

    /**
     * 返回特殊标记
     */
    function getSoftwareType()
    {
        switch ($this->getIsSoftware()) {
            case 1:
                return '软科学、应用基础研究、软件类或集成电路类';
                break;
            case 2:
                return '数学等纯理论基础研究';
                break;
            default:
                return '一般类型';
                break;
        }
    }

    /**
     * 返回推荐经费
     */
    function getCommendMoney($return_array = false)
    {
        $db = sf::getLib("db");
        $row = $db->fetch_first("SELECT money,more FROM `commends` WHERE `project_id` = '" . parent::getProjectId() . "' ORDER BY updated_at DESC");
        if (!$return_array) return $row['money'];
        else {
            $more = (array)unserialize($row['more']);
            $more['money'] = $row['money'];
            return $more;
        }
    }

    /**
     * 打印扩展字段
     */
    function getLendingBank()
    {
        if (in_array(parent::getTypeId(), array('38', '40', '48', '50'))) {
            $unit = $this->getDetails(true)->getUnit();
            return $unit['dkyh'];
        } else return '-';
    }

    function getLendingMoney()
    {
        if (in_array(parent::getTypeId(), array('38', '40', '48', '50'))) {
            $unit = $this->getDetails(true)->getUnit();
            return $unit['dkje'];
        } else return 0;
    }

    /* 老项目直接忽略了
	function getInterest()
	{
		if(in_array(parent::getTypeId(),array('38','40','48','50'))){
			$unit = $this->getDetails(true)->getUnit();
			return $unit['lxje'];
		}else return 0;
	}*/

    function getLendingTime()
    {
        if (in_array(parent::getTypeId(), array('38', '40', '48', '50'))) {
            $unit = $this->getDetails(true)->getUnit();
            return $unit['dksj'];
        } else return '-';
    }

    public function getCooperatationNameArray()
    {
        $cooperatations = parent::getCooperation();
        $cooperatations = json_decode($cooperatations,true);
        return array_column($cooperatations,'subject');
    }

    public function getCooperatationArray()
    {
        $cooperatations = parent::getCooperation();
        $cooperatations = json_decode($cooperatations,true);
        return $cooperatations;
    }

    public function getOtherCooperatationNames()
    {
        $others = $this->getCooperatationArray();
        if(empty($others)) return '无';
        $names = [];
        foreach ($others as $other){
            $names[] = $other['subject'];
        }
        return implode('、',$names);
    }

    public function setCooperatation($names,$propertys=[])
    {
        $datas = [];
        $i=0;
        foreach ($names as $k=>$name){
            if(empty($name)) continue;
            $datas[$i]['subject'] = $name;
            $datas[$i]['property'] = $propertys[$k];
            $i++;
        }
        return parent::setCooperation(json_encode($datas, JSON_UNESCAPED_UNICODE));
    }

    function setCooperation($v)
    {
        if (is_array($v)) {
            //过滤
            $_v = array();
            foreach ($v as $val) {
                if ($val) $_v[] = $val;
            }
            parent::setCooperation(implode('|', $_v));
        } else parent::setCooperation($v);
    }

    function getCooperation($is_str = false, $dv = "、")
    {
        $result = $this->getCooperatationNameArray();
        if ($is_str) return $this->getCooperationStr();
        else return $result;
    }

    function getCooperationStr()
    {
        return $this->getOtherCooperatationNames();
    }

    function getMemberStr($type = 'apply')
    {
        $type = $type ?: 'apply';
        $members = sf::getModel('ProjectMembers')->selectAll("`project_id` = '" . parent::getProjectId() . "' and `type` = '{$type}'", "ORDER BY no asc,id ASC");
        $i = 0;
        while ($member = $members->getObject()) {
            $arr[$i] = $member->getName();
            $i++;
        }
        return implode('、', $arr);
    }

    /**
     * 读取合作单位
     */
    function cooperations($type = 'apply', $with = 'ALL')
    {
        return sf::getModel('ProjectCooperations')->find($this->getProjectId(), $type, $with);
    }

    /**
     * 应该废除
     */
    function getCooperations($type = 'apply', $with = 'ALL')
    {
        $addWhere = "`project_id` = '" . $this->getProjectId() . "' ";
        if ($type != '') $addWhere .= " AND `project_type` = '{$type}' ";
        if (in_array($with, array('A', 'B'))) $addWhere .= " AND `type` = '{$with}' ";
        return sf::getModel('ProjectCooperations')->selectAll($addWhere, "ORDER BY `no` ASC,`id` ASC");
    }

    /**
     * 亚专科
     */
    function specialtys()
    {
        $addWhere = "`project_id` = '" . $this->getProjectId() . "' ";
        return sf::getModel('ProjectSpecialtys')->selectAll($addWhere, "ORDER BY `no` ASC,`id` ASC");
    }

    /**
     * 技术特色
     */
    function technologys($type='apply')
    {
        $addWhere = "`project_id` = '" . $this->getProjectId() . "' and `type` = '{$type}'";
        return sf::getModel('ProjectTechnologys')->selectAll($addWhere, "ORDER BY `no` ASC,`id` ASC");
    }

    /**
     * 有希望近5年取得突破性进展的主要医疗技术研究项目
     */
    function newtechs()
    {
        $addWhere = "`project_id` = '" . $this->getProjectId() . "' ";
        return sf::getModel('ProjectNewTechs')->selectAll($addWhere, "ORDER BY `no` ASC,`id` ASC");
    }

    function getCooperationById($cid)
    {
        $cooperation = sf::getModel('ProjectCooperations', $cid);
        if ($cooperation->isNew()) $cooperation->setCreatedAt(date('Y-m-d H:I:s'));
        return $cooperation;
    }

    function getSpecialtyById($sid)
    {
        $specialty = sf::getModel('ProjectSpecialtys',$sid);
        if ($specialty->isNew()) $specialty->setCreatedAt(date('Y-m-d H:I:s'));
        return $specialty;
    }

    function getTechnologyById($sid)
    {
        $technology = sf::getModel('ProjectTechnologys',$sid);
        if ($technology->isNew()) $technology->setCreatedAt(date('Y-m-d H:I:s'));
        return $technology;
    }

    function getNewTech($sid)
    {
        $tech = sf::getModel('ProjectNewTechs',$sid);
        if ($tech->isNew()) $tech->setCreatedAt(date('Y-m-d H:I:s'));
        return $tech;
    }

    function getResearchProjectById($pid)
    {
        $researchProject = sf::getModel('ProjectResearchProjects',$pid);
        if ($researchProject->isNew()) $researchProject->setCreatedAt(date('Y-m-d H:I:s'));
        return $researchProject;
    }


    function getPaperById($pid)
    {
        $paper = sf::getModel('ProjectPapers',$pid);
        if ($paper->isNew()) $paper->setCreatedAt(date('Y-m-d H:I:s'));
        return $paper;
    }
    function getSupportById($pid)
    {
        $support = sf::getModel('ProjectSupports',$pid);
        if ($support->isNew()) $support->setCreatedAt(date('Y-m-d H:I:s'));
        return $support;
    }
    function getPopularById($pid)
    {
        $popular = sf::getModel('ProjectPopulars',$pid);
        if ($popular->isNew()) $popular->setCreatedAt(date('Y-m-d H:I:s'));
        return $popular;
    }
    function getRemoteById($pid)
    {
        $remote = sf::getModel('ProjectRemotes',$pid);
        if ($remote->isNew()) $remote->setCreatedAt(date('Y-m-d H:I:s'));
        return $remote;
    }
    function getStandardById($pid)
    {
        $standard = sf::getModel('ProjectStandards',$pid);
        if ($standard->isNew()) $standard->setCreatedAt(date('Y-m-d H:I:s'));
        return $standard;
    }
    function getMissionById($pid)
    {
        $mission = sf::getModel('ProjectMissions',$pid);
        if ($mission->isNew()) $mission->setCreatedAt(date('Y-m-d H:I:s'));
        return $mission;
    }

    function getEducationProjectById($pid)
    {
        $educationProject = sf::getModel('ProjectEducationProjects',$pid);
        if ($educationProject->isNew()) $educationProject->setCreatedAt(date('Y-m-d H:I:s'));
        return $educationProject;
    }

    function getDeviceById($id)
    {
        $device = sf::getModel('ProjectDevices',$id);
        if ($device->isNew()) $device->setCreatedAt(date('Y-m-d H:I:s'));
        return $device;
    }

    function getAwardById($pid)
    {
        $award = sf::getModel('ProjectAwards',$pid);
        if ($award->isNew()) $award->setCreatedAt(date('Y-m-d H:I:s'));
        return $award;
    }

    function getPatentById($pid)
    {
        $patent = sf::getModel('ProjectPatents',$pid);
        if ($patent->isNew()) $patent->setCreatedAt(date('Y-m-d H:I:s'));
        return $patent;
    }


    function getGuidelineById($pid)
    {
        $award = sf::getModel('ProjectGuidelines',$pid);
        if ($award->isNew()) $award->setCreatedAt(date('Y-m-d H:I:s'));
        return $award;
    }


    function getCenterById($pid)
    {
        $award = sf::getModel('ProjectCenters',$pid);
        if ($award->isNew()) $award->setCreatedAt(date('Y-m-d H:I:s'));
        return $award;
    }

    function getAcademicById($pid)
    {
        $award = sf::getModel('ProjectAcademics',$pid);
        if ($award->isNew()) $award->setCreatedAt(date('Y-m-d H:I:s'));
        return $award;
    }

    function getMagazineById($pid)
    {
        $magazine = sf::getModel('ProjectMagazines',$pid);
        if ($magazine->isNew()) $magazine->setCreatedAt(date('Y-m-d H:I:s'));
        return $magazine;
    }

    function getMembers($type = 'apply',$userType='')
    {
        $addwhere = "`project_id` = '" . $this->getProjectId() . "' AND `type` = '{$type}'";
        if($userType) $addwhere.=" and user_type = '{$userType}'";
        return sf::getModel('ProjectMembers')->selectAll($addwhere, "ORDER BY `no` ASC,`id` ASC");
    }

    function getMemberById($mid)
    {
        $member = sf::getModel('ProjectMembers', $mid);
        if ($member->isNew()) $member->setCreatedAt(date('Y-m-d H:i:s'));
        return $member;
    }

    function getWorkItemById($mid)
    {
        $work = sf::getModel('ProjectWorks', $mid);
        if ($work->isNew()) $work->setCreatedAt(date('Y-m-d H:i:s'));
        return $work;
    }

    function getLeaderMember()
    {
        $member = sf::getModel('ProjectMembers')->getLeaderMember($this->getProjectId());
        if ($member->isNew()) $member->setCreatedAt(date('Y-m-d H:i:s'));
        return $member;
    }

    function getBackboneMember($id='')
    {
        $member = sf::getModel('ProjectMembers')->getBackboneMember($this->getProjectId(),$id);
        if ($member->isNew()) $member->setCreatedAt(date('Y-m-d H:i:s'));
        return $member;
    }

    function getBackbones($type='apply')
    {
        return sf::getModel('ProjectMembers')->selectAll("project_id = '".$this->getProjectId()."' and `type` = '{$type}' and user_type = 'backbone'");
    }

    function getExpertById($mid)
    {
        $member = sf::getModel('ProjectExperts', $mid);
        if ($member->isNew()) $member->setCreatedAt(date('Y-m-d H:i:s'));
        return $member;
    }

    //判断经费预算是否审定
    function isLock($type = 'budget')
    {
        switch ($type) {
            case 'budget':
                if ($this->getBudgetOpen() && $this->getStateForBudgetBook() > 6) return true;
                else return false;
                break;
            case 'task':
                if (!in_array($this->getStateForPlanBook(), array(0, 1, 3, 5, 7, 9))) return true;
                else return false;
                break;
            case 'complete':
                if (!in_array($this->getStateForCompleteBook(), array(0, 1, 3, 5, 7))) return true;
                else return false;
                break;
            default:
                return true;
                break;
        }
    }

    function getRadicateYear($isString = false)
    {
        $year = parent::getRadicateYear();
        if (!$isString) return $year;
        if ($year == '') return '未立项';
        if (parent::getRadicateId()) return $year . '(' . parent::getTypeCurrentGroup() . ')';
        return $year . '(预推荐)';
    }

    //生成版本记录HTML
    function getVerHtml($type = 'budget')
    {
        $html = array();
        $vers = sf::getModel("Versions")->selectByItemId(parent::getProjectId(), $type);

        while ($ver = $vers->getObject()) {
            if ($ver->isNew()) continue;
            $html[] = $ver->getLink();
        }
        return implode('、', $html);
    }

    /**
     * 发送消息给申报人
     */
    function sendMessage($message = '', $send_at = '')
    {
        return $this->getUser(true)->sendMessage($message, $this->getProjectId(), 'projects', $send_at);
    }

    /**
     * 发送消息给申报人
     */
    function sendMessageToManager($message = '', $send_at = '')
    {
        $managers = $this->getManagers();
        while($manager = $managers->getObject()){
            if(!in_array($manager->getUserUsername(),['蒋胜'])) continue;
            $manager->sendMessage($message, $this->getProjectId(), 'projects', $send_at);
        }
    }

    /**
     * 项目验证
     * @param string $type [description]
     * @return [type]       [description]
     */
    public function validate($type = 'apply')
    {
        switch ($type) {
            case 'budget':
                return $this->budget_validate();
                break;
            case 'task':
                return $this->task_validate();
                break;
            case 'stage':
                return $this->stage_validate();
                break;
            case 'complete':
                return $this->complete_validate();
                break;
            case 'widget':
                return $this->widget_validate();
                break;
            default:
                return $this->apply_validate();
                break;
        }
    }

    public function old_validate()
    {
        $message = array();
        if (!$this->getSubject()) $message[] = '项目名称必须填写';
        if (!$this->getRadicateYear()) $message[] = '立项年度必须选择';
        if (!$this->getUserId()) $message[] = '项目负责人还未关联';
        if (!$this->getCatId()) $message[] = '项目来源必须选择';
        if($this->getCatId()==16 && !$this->getBaseinfo('apply')->getSpecialSubject()){
            $message[] = '项目专项名称必须填写';
        }
        if($this->getCatId()==15 && !$this->getProjectType()){
            $message[] = '项目类别必须选择';
        }
        if($this->getCatId()==15 && !$this->getTypeId()){
            $message[] = '研究对象必须选择';
        }
        if (!$this->getRadicateId()) $message[] = '立项编号必须填写';
        if ($this->schedules('apply')->getTotal()==0) $message[] = '阶段目标必须填写';
        if ($this->attachments()->getTotal()==0) $message[] = '缺少相关附件';
        if ($this->money('apply')->getTotal()==0) {
            $message[] = '经费预算必须填写';
        }else{
            if($this->getTotalMoney()!=$this->money('apply')->getTotal('hj')){
                $message[] = '经费来源合计与经费支出合计不等。经费来源合计：'.$this->getTotalMoney().'万元，经费支出合计：'.$this->money('apply')->getTotal('hj').'万元';
            }
            if($this->money('apply')->getPerformance('hj')>$this->money('apply')->getIndirect('hj')){
                $message[] = '经费支出表中的“其中：绩效支出”不能高于“间接费用”。“间接费用”：'.(float)$this->money('apply')->getIndirect('hj').'万元，“其中：绩效支出”：'.(float)$this->money('apply')->getPerformance('hj').'万元';
            }
        }
        return $message;
    }

    /**
     * 检查申报时间
     */
    public function time_validate()
    {
//        if(input::session('username')=='test2009') return true;
        //在指南设置了不限制单位上报时间的
        $guide = $this->getGuide(true);
        $configs = $guide->getConfigs('outtime');
        if($companys = $configs['companys']){
            $companys = explode("|",$companys);
            $companys = array_filter($companys);
            if(in_array($this->getCorporationName(),$companys)) return true;
        }
        //可在后台设置主管部门和医院是否限制截止时间，默认要限制
        if($this->getDepartment()->getIgnoreEndat()==0 && $this->getCorporation()->getIgnoreEndat()==0){
            $endAt = input::session('userlevel')==4 ? $guide->getGatherEndAt() : $guide->getEndAt();
            if (strtotime($endAt) < time() || strtotime($guide->getStartAt()) > time()) {
                $submitAt = $guide->getSubmitAt();
                if(!$submitAt) {
                    return false;
                }
                $submitAtTime = strtotime($submitAt);
                if($submitAtTime <= time()){
                    return false;
                }
                //如果是被卫健委驳回的项目，可以上报
                if($this->isBackByManager()===false) return false;
            }
        }
        return true;
    }

    /**
     * 是否是被省卫健委驳回的项目
     * @return false
     */
    public function isBackByManager()
    {
        $guide = $this->getGuide(true);
        $startAt = $guide->getStartAt();
        $reject_historys = sf::getModel('Historys')->selectAll("project_id = '".$this->getProjectId()."' and `type` = 'project' and  `user_group_id` in (1,6) and updated_at > '{$startAt}' and content like '%退回%'");
        return $reject_historys->getTotal()>0;
    }

    public function isUploadReference()
    {
        $departmentId = $this->getDepartmentId();
        if(input::session('userlevel')==3){
            $departmentId = $this->getCorporationId();
        }
        $uploadAt = $this->getGuide()->getStartAt();
        $filemangers = sf::getModel('Filemanager')->selectAll("user_id = '{$departmentId}' and item_type = 'reference' and created_at > '{$uploadAt}'");
        $count = $filemangers->getTotal();
        return $count>0;
    }
    public function isUploadSzxReference($type='建设申请',$userRole=4)
    {
        $userId = $this->getCorporationId();
        if($userRole==4){
            $userId = $this->getDepartmentId();
        }

        $uploadAt = $this->getGuide()->getStartAt();
        $filemangers = sf::getModel('Filemanager')->selectAll("user_id = '{$userId}' and item_type = 'szx_reference' and created_at > '{$uploadAt}' and file_note like '%{$type}'");
        $count = $filemangers->getTotal();
        return $count>0;
    }

    /**
     * 申报书上报检验
     * @return [type] [description]
     */
    public function apply_validate()
    {
        $message = array();
        if (!$this->getSubject()) $message[] = '项目名称必须填写';
        if (!$this->getGuideid()) $message[] = '项目指南不存在，请确认项目填报是否正确';
        if (!$this->getCorporationId()) $message[] = '你填写的项目申报单位不存在';

        //申报时间验证
        if(!$this->time_validate()) $message[] = '申报通道已关闭！';
        if($message) return $message;

        //申报书检查
        $_msg = array();
        if ($_msg = $this->widget_validate()) {
            $message = array_merge($message, $_msg);
        }

        if ($this->getGuide()->getAllowCompany(1) && !$this->getCorporation(true)->checkType($this->getGuide()->getAllowCompany())) $message[] = "你所属的单位不在指南要求的范围之内";



        //检查项目名称是否与指南一致
        if (in_array('title_same', $this->getGuide()->getProperty())) {
            if (trim($this->getGuide()->getSubject()) != trim($this->getSubject())) $message[] = "该指南规定：项目名称必须和指南名称一致";
        }
        //申报人验证
        if ($this->getGuide()->getUserlevel() == 2) {
            $allowGrades = array_filter($this->getGuide()->getAllowGrade());
            if ($allowGrades && !in_array($this->getUser()->getUserGrade(false), $allowGrades)) $message[] = "项目负责人的职称（或学历）不在申报要求的范围之内";
            if ($_msg = $this->getUser()->validate('apply')) $message = array_merge($message, $_msg);
            if ($this->getUser()->getIsLock()) $message[] = '申报人帐号还未实名认证不能上报项目';
        }
        //单位验证
//		if($this->getGuide()->getUserlevel() != 4){//推荐单位就不用验证了
//			//单位验证
//			$company = $this->getCorporation(true);
//			//检查是否为法人单位
////			if(!$company->isLegalPerson()) $message[] = "贵单位非独立法人单位，不允许申报项目。";
//			$_msg = array();
//			if($_msg = $company->validate()){
//				$message = array_merge($message,$_msg);
//			}
//		}
        //项目类型验证
//		$type = $this->getType(true);
//		$type_switch = $type->getSwitch();
//		if(in_array('is_only',(array)$type_switch)){//一个单位至允许申报一项
//			$db = sf::getLib("db");
//			$row = $db->fetch_first("SELECT count(*) as num FROM projects WHERE type_id = '".$this->getTypeId()."' and corporation_id = '".$this->getCorporationId()."' and declare_year = '".$this->getDeclareYear()."' and statement > 1 and project_id <> '".$this->getProjectId()."' ");
//			if($row['num'] > 0) $message[] = "贵单位已经上报了一个该类型的项目";
//		}
        return $message;
    }

    /**
     * 预算书上报检验
     * @return [type] [description]
     */
    public function budget_validate()
    {
        $msg = array();
        if ($this->getBudgetOpen() < 1) $msg[] = '该项目不需要单独上报预算书，请直接上报任务书即可';
        if (method_exists($this->getBudgetBook(true), 'validate')) $msg = array_merge($msg, $this->getBudgetBook()->validate());
        return $msg;
    }

    /**
     * 任务书检验
     * @return [type] [description]
     */
    public function task_validate()
    {
        $msg = array();
        if ($_msg = $this->widget_validate('task')) {
            $msg = array_merge($msg, $_msg);
        }
        return $msg;
        //如果是老板本忽略
        if (substr($this->getRadicateId(), 0, 4) < 2012 && $this->getTypeId() < 24) return $msg;
        //验证日期格式是否正确
        if (!checkDateIsValid($this->getRealStartAt())) $msg[] = "项目开始时间格式不对！正确格式如：" . date("Y-m-d") . "。";
        if (!checkDateIsValid($this->getRealEndAt())) $msg[] = "项目结束时间格式不对！正确格式如：" . date("Y-m-d") . "。";
        if (strtotime($this->getRealEndAt()) - strtotime($this->getRealStartAt()) < 15552000) $msg[] = "项目结束时间和开始时间过于接近。";
        //不用填写预算的任务书直接跳过检查
        if (!in_array($this->getTypeId(), array('2', '32', '54', '65', '66', '69', '80', '81', '116', '113', '114', '115', '117', '118', '119', '120', '155', '156')) && $this->getConfigs("check.budget") != "N" && $this->getIsShift() != 9) {
            //新版本需要校验
            $budget = $this->getBudgetBook(true);
            if ($budget->isNew()) {
                $msg[] = "任务书还没有填写完全！";
                return $msg;
            } else $msg = $budget->validate();
            //需要填写预算书的，没有签署前不能提交
            if ($this->getBudgetOpen() > 0 && $this->getStateForBudgetBook() != 30) $msg[] = "该项目开启了经费预算，需要经费预算审定后才能上报任务合同。";
        }
        //时间验证
        $guide = $this->getGuide(true);
        if (!$guide->isNew()) {
            if ($guide->getProjectStartAt() && ($guide->getProjectStartAt() != $this->getRealStartAt("Y-m-d"))) $msg[] = '项目开始时间与指南不一致。指南要求的开始时间为：' . $guide->getProjectStartAt();
            //if($guide->getProjectEndAt() && ($guide->getProjectEndAt() != $this->getRealEndAt("Y-m-d"))) $msg[] = '项目截至时间与指南不一致。指南要求的截至时间为：'.$guide->getProjectEndAt();
            if (!$guide->checkEndAt($this->getRealEndAt("Y-m-d"))) $msg[] = '项目截至时间与指南不一致。指南要求的截至时间为：' . $guide->getProjectEndRange();
        }
        //高新处重点项目特别验证
        if ($this->getOfficeId() == 15 && $this->getTypeId() == 92 and $this->getRadicateYear() == 2019) {
            $report = sf::getModel("ReportLists")->selectByProjectId($this->getProjectId());
            if ($report->getFinish() < 1) $msg[] = "根据相关要求，项目至少需要一篇最终报告，请修改！";
            if ($report->getMiddle() < 1) $msg[] = "根据相关要求，项目至少需要一篇中期报告，请修改！";
        }
        //if(method_exists($this->getTaskBook(),'validate')) $msg = array_merge($msg,$this->getTaskBook()->validate());
        return $msg;
    }

    /**
     * 中期报告检验
     * @return [type] [description]
     */
    public function stage_validate()
    {
        $msg = array();
        if ($_msg = $this->widget_validate('stage')) {
            $msg = array_merge($msg, $_msg);
        }
        return $msg;

    }

    /**
     * 验收报告检验
     * @return [type] [description]
     */
    public function complete_validate()
    {
        $msg = array();
        if($this->isSupportProject() && $this->getStateForPlanBook() < 10) $msg[] = '请先完成任务书签署后再上报验收申请！';
        $_msg = array();
        if ($_msg = $this->widget_validate('complete')) {
            $msg = array_merge($msg, $_msg);
        }
        return $msg;
        //如果是老板本忽略
        if ($this->getRadicateYear() < 2010) return $msg;
        if ($this->getStateForPlanBook() < 10 && $this->getTypeId() != 81) $msg[] = '请先完成任务书签署后再上报验收申请！';
        //读取类型开关
        $type_switch = $this->getType(true)->getSwitch();
        //需要签署科技报告的，验证科技报告
        if (in_array('must_report', $type_switch) && $this->getRadicateYear() > 2016 && $this->getIsReport() < 5) $msg[] = '请先完成科技报告填写后再上报验收申请！';
        //判断是否完成成果登记,软科学不限制
        if ($this->getRadicateYear() > 2016) {//2017年以后的项目才需要判断
            if (in_array('must_achievement', $type_switch) && $this->getConfigs("ignore.achievement") != 'Y') {
                $has_achievement = false;
                $achievements = $this->achievements();
                while ($achievement = $achievements->getObject()) {
                    if ($achievement->getNumber() && $achievement->getStatement() == 10) $has_achievement = true;
                }
                if (!$has_achievement) {
                    //如果是申报人，可以设置为不通过，取消成果登记验证
                    if ($this->getUserId() == input::session("roleuserid")) {
                        //$msg[] = '请先在线完成科技成果登记后再上报验收报告！(不通过验收的项目，标记后不需要进行成果登记。'.btn("window","不通过验收标记",site_url("user/complete/domark/id/".$this->getProjectId())).')';
                        $msg[] = '请先在线完成科技成果登记后再上报验收报告！(不通过验收的项目，可以申请不进行成果登记，请联系省卫生健康委资管处，电话：028-86726087。)';
                    } else $msg[] = '请先在线完成科技成果登记后再上报验收报告！';
                }
            }
        }
        //大于100万的需要做财务审计
        if ($this->getRadicateMoney() >= 100 && in_array('must_audit', $type_switch) && $this->getConfigs('ignore.audit') != 'Y' && $this->getIsShift() <> 2) {
            $audit = $this->audits();
            if ($audit->isNew()) $msg[] = '请先完成项目验收（结题）财务审计报告后，再上报验收申请！项目验收（结题）财务审计申请<a href="' . site_url("user/report/apply/id/" . $this->getProjectId()) . '">请点击这里</a>快速处理。';
            if ($audit->getStatement() < 30) $msg[] = '项目验收（结题）财务审计报告必须审定后才能提交验收报告。';
        }
        $completebook = $this->getCompleteBook(true);
        if ($completebook->isNew()) $msg[] = '还没有填写验收报告';
        //if(method_exists($completebook,'validate')) $msg = array_merge($msg,$completebook->validate());
        return $msg;
    }

    function getRadicateMoney($isString = false)
    {
        if (parent::getRadicateMoney() > 0) $money = parent::getRadicateMoney();
        else if ($_money = $this->getCommendMoney()) {
            if ($_money > 0) $money = $_money;
        } else $money = $this->getDeclareMoney();

        if (!$isString) return $money;
        return $money . '(预)';
    }

    function setConfigs($key, $val = '')
    {
        $configs = (array)json_decode(parent::getConfigs(), true);

        if (!is_array($key)) $key = array($key => $val);
        foreach ($key as $_key => $_val) {
            $_ks = explode('.', $_key);
            $_code = '$configs';
            for ($i = 0, $n = count($_ks); $i < $n; $i++)
                $_code .= '[\'' . $_ks[$i] . '\']';
            $_code .= '= $_val;';
            @eval($_code);//执行语句,不是最佳选择，寻找替代方法
        }

        parent::setConfigs(json_encode($configs));
        return parent::save();
    }

    function getConfigs($key = '')
    {
        $configs = json_decode(parent::getConfigs(), true);
        $is_file = false;

        if ($key) {
            $_key = explode('.', $key);
            foreach ($_key as $k) {
                if ($k == 'file') $is_file = true;
                $configs = $configs[$k];
            }
        }
        //判断文件是否存在
        if ($is_file && !is_file(WEBROOT . "/up_files/" . $configs)) return '';
        return $configs;
    }

    function setStateForAudit($v)
    {
        $this->getAudit()->setStatement($v);
        return parent::setStateForAudit($v);
    }

    /**
     * 取得项目标记
     */
    function getMark()
    {
        $str = '';
//        if ($this->getLevel()!='省级') $str .= '<span class="text-dark">['.$this->getLevel().']</span>';
//        if ($this->getLevel()=='国家级') $str .= '<span class="text-dark">['.$this->getLevel().']</span>';
        if ($this->getIsDelay()) $str .= '<span style="color:red;">[延]</span>';
        return $str;
    }

    function setShortUrl($hash = '', $type = 'task')
    {
        $shortUrl = sf::getModel("ShortUrls")->selectByHash($hash);
        if ($shortUrl->isNew()) {
            $shortUrl->doInvalid($this->getProjectId(), $type);//设置其他失效
            $shortUrl->setType($type);
            $shortUrl->setProjectId($this->getProjectId());
            $shortUrl->setUserId(input::getInput("session.roleuserid"));
            $shortUrl->setUserName(input::getInput("session.nickname"));
            $shortUrl->setIsValid(1);
            $shortUrl->setUpdatedAt(date("Y-m-d H:i:s"));
            $shortUrl->setUserIp(input::getIp());
            $shortUrl->save();
        }
    }

    function getShortUrl($type = 'task')
    {
        $path = strtolower($this->getConfigs("path.task"));
        if (!$path) return false;
        $_path = explode('/', $path);
        view::set("project", $this);
        $output = view::getContent("declare/" . $_path[1] . "/download");
        $hash = substr(md5(strip_tags($output)), 0, 20);
        return $hash;
    }

    function getSubjectTree($level = 0, $dv = '/')
    {
        return parent::getSubjectName();
        if ($level) {
            $_tree = $_code = array();
            $model = (substr(parent::getSubjectId(), 0, 1) == 'X') ? 'domain' : 'subjects';
            for ($i = 0; $i <= $level; $i++) {
                $_code[] = substr(parent::getSubjectId(), 0, strlen(parent::getSubjectId()) - (3 - $i) * 2);
            }
            $addWhere = "`code` IN ('" . implode("','", $_code) . "') and type = 1 ";
            $trees = sf::getModel($model)->selectAll($addWhere, "ORDER BY code ASC");
            while ($tree = $trees->getObject()) {
                $_tree[$tree->getCode()] = $tree->getSubject();
            }
            return implode($dv, $_tree);
        } else return parent::getSubjectName();
    }

    /**
     * 取得学科路径
     */
    function getSubjectPath($level = 0, $dv = '/')
    {
        if ($level) {
            $data = [];
            $_code = [];
            for ($i = 1; $i <= $level; $i++) {
                if ($i == 1) $_code[] = substr(parent::getSubjectId(), 0, 3);
                if ($i == 2) $_code[] = substr(parent::getSubjectId(), 0, 6);
                if ($i == 3) $_code[] = substr(parent::getSubjectId(), 0, 8);
            }
            $addWhere = "`code` IN ('" . implode("','", $_code) . "') and type = 1 ";
            $trees = sf::getModel("Subjects")->selectAll($addWhere, "ORDER BY code ASC");
            while ($tree = $trees->getObject()) {
                $_tree[$tree->getCode()] = trim($tree->getSubject());
            }
            $data[] = implode($dv, $_tree);
//            $_code2 = [];
//            for ($i = 1; $i <= $level; $i++) {
//                if ($i == 1) $_code2[] = substr(parent::getSubjectId2(), 0, 3);
//                if ($i == 2) $_code2[] = substr(parent::getSubjectId2(), 0, 6);
//                if ($i == 3) $_code2[] = substr(parent::getSubjectId2(), 0, 8);
//            }
//            $addWhere = "`code` IN ('" . implode("','", $_code2) . "') and type = 1 ";
//            $trees = sf::getModel("Subjects")->selectAll($addWhere, "ORDER BY code ASC");
//            while ($tree = $trees->getObject()) {
//                $_tree2[$tree->getCode()] = trim($tree->getSubject());
//            }
//            $data[] = implode($dv, $_tree2);
            return implode('<br>', $data);
        } else return parent::getSubjectName();
    }

    function getRealStartAt($format = '')
    {
        if ($format) return date($format, strtotime(parent::getRealStartAt()));
        else return parent::getRealStartAt();
    }

    function getRealEndAt($format = '')
    {
        if ($format) return date($format, strtotime(parent::getRealEndAt()));
        else return parent::getRealEndAt();
    }

    function getStartAt($format = 'Y-m-d')
    {
        if ($this->getIsSubsidy()) return '/';
        if (parent::getRealStartAt() != '0000-00-00') return $this->getRealStartAt();
        else {
            if (strtotime(parent::getStartAt()) < strtotime("2000-01-01 00:00:00")) return '/';
            else return parent::getStartAt();
        }
    }

    function getEndAt($format = 'Y-m-d')
    {
        if ($this->getIsSubsidy()) return '/';
        if (parent::getRealEndAt() != '0000-00-00') return $this->getRealEndAt();
        else {
            if (strtotime(parent::getEndAt()) < strtotime("2000-01-01 00:00:00")) return '/';
            else return parent::getEndAt();
        }
    }

    function isSubsidy()
    {
        if (parent::getIsSubsidy() > 0) return true;//特别项目，特别处理
        return isSubsidy($this->getTypeId(), $this->getIsShift());
    }

    /*function getSubject($len=0)
	{
		return parent::getSubject($len);
	}*/

    function getGuideName($len = 0)
    {
        if (!$this->getGuideId()) return $this->getTypeSubject();
        //暂时用改字段代替，原本字段应该是guide_name
        if ($guide_name = parent::getGuideName()) return $guide_name;
        //直接读取
        $guide = $this->getGuide(true);
        if ($guide->isNew()) return '/';
        $path_name = $guide->getPathName();
        //保存到数据库
        parent::setGuideName($path_name);
        $this->save();
        return $path_name;
    }

    function checkAttachment()
    {
        $msg = array();
        if (in_array($this->getTypeId(), array('78', '109', '124', '132'))) return $msg;
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `project_attachments` WHERE `item_id` = '" . $this->getProjectId() . "' AND item_type = 'content' ");
        if ($db->num_rows($query) == 0) $msg[] = "项目还没有上传正文内容";
        if (in_array($this->getTypeId(), [88, 94])) {//院所项目必须上传经费预算
            $query = $db->query("SELECT * FROM `project_attachments` WHERE `item_id` = '" . $this->getProjectId() . "' AND item_type = 'budget' ");
            if ($db->num_rows($query) == 0) $msg[] = "项目还没有上传预算(绩效目标)内容";
        }
        return $msg;
    }

    function getPdf($type = 'apply')
    {
        if ($file_name = $this->getConfigs("file.apply")) return site_path("up_files/" . $file_name);
        else return site_url($this->getConfigs("path.apply") . "/output/id/" . $this->getProjectId());
    }

    function isWarning()
    {
        return '';
        //排除2007年以前的项目
        if ($this->getRadicateYear() < 2007) return '';
        if ($this->getStatement() != 29) return '';
        if ($this->isSubsidy()) return '';
        //判断验收书
        if (strtotime($this->getRealEndAt()) < time()) return ' class="tr-danger"';
        if (strtotime($this->getRealEndAt()) < (time() + 2592000) && strtotime($this->getRealEndAt()) > time()) return ' class="tr-warning"';
        //判断任务书
        if (($this->getStateForPlanBook() != 10) && (strtotime($this->getRadicateAt()) < (time() - 5184000))) return ' class="tr-success"';
        return '';
    }

    public function getRadicateAt($fromat="Y-m-d H:i:s")
    {
        if(empty(parent::getRadicateAt())) return '';
        return parent::getRadicateAt($fromat);
    }

    function getManagerName($len = 0, $link = true)
    {
        if (!$link) return parent::getManagerName();
        if (parent::getManagerName()) $link = ' <i id="phone_' . $this->getId() . '" class="glyphicon glyphicon-phone-alt text-danger" title="点击显示电话" style="cursor:pointer" onclick="$.get(\'' . site_url("ajax/getManagerPhone") . '\',{userid:\'' . $this->getManagerId() . '\'},function(json){$(\'#phone_' . $this->getId() . '\').html(json);});"></i>';
        else $link = '';
        return parent::getManagerName() . $link;
    }

    /**
     * 取得TAG标记
     */
    function hasTag()
    {
        if ($this->getHasTag()) return true;
        else return false;
    }

    /**
     * 是否有重复
     */
    function hasRepeat()
    {
        if ($this->getStatement() >= 29) return false;
        if (parent::getHasRepeat()) return true;
        else return false;
    }

    /**
     * 是否有重复汉字
     */
    function getHasRepeat($len = 0)
    {
        if ($this->hasRepeat()) return '是';
        else return '否';
    }

    /**
     * 检查项目相似性
     */
    function similarity()
    {
        return sf::getLib("check", $this)->similarity();
    }

    /**
     * 获取窄表属性，返回数组
     * @param string $mark [description]
     * @return [type]       [description]
     */
    function getAttributes($mark = '')
    {
        $subject = $result = [];
        $addWhere = '';

        if ($mark && !is_array($mark)) $subject[0] = $mark;

        if (count($subject) > 0)
            $addWhere = " AND subject IN('" . implode("','", $subject) . "') ";

        $attributes = sf::getModel('ProjectAttributes')->selectAll(" project_id='" . $this->getProjectId() . "' " . $addWhere);

        while ($attribute = $attributes->getObject()) {
            $result[$attribute->getSubject()] = $attribute->getContent();
        }
        return $result;
    }

    /**
     * 获取项目扩展信息
     * @param string $mark [description]
     * @return [type]       [description]
     */
    function getExtends($mark = '')
    {
        if ($this->expands['project_id'] != $this->getProjectId()) {
            $extends = sf::getModel('ProjectExtends')->selectAll(" project_id='" . $this->getProjectId() . "' ");
            while ($extend = $extends->getObject()) {
                $this->expands[$extend->getSubject()] = $extend->getContent();
            }
            $this->expands['project_id'] = $this->getProjectId();
        }
        if ($mark) return $this->expands[$mark];
        else return $this->expands;
    }

    /**
     * 设置项目扩展信息
     * @param string $mark [description]
     * @return [type]       [description]
     */
    function setExtends($subject = '', $content = '')
    {
        if ($subject == '') return false;
        $extends = sf::getModel("ProjectExtends")->selectBySubject($subject, $this->getProjectId());
        $extends->setContent($content);
        return $extends->save();
    }

    /**
     * 国际合作国别，国际合作使用
     */
    function getCountryName()
    {
        if (in_array($this->getTypeId(), array('136', '176'))) {
            return $this->getBaseinfo(true)->getData('cooperation_nation') . $this->getBaseinfo(true)->getData('cooperation_area');
        }
        if (!in_array($this->getTypeId(), array('91', '125'))) return '-';
        if ($this->getExtends('getCountryName')) return $this->getExtends('getCountryName');
        else {
            $Nationals = sf::getModel("ProjectNationals")->selectByProjectId($this->getProjectId());
            if ($Nationals->getCooperativeCountry()) $this->setExtends('getCountryName', $Nationals->getCooperativeCountry());
            return $Nationals->getCooperativeCountry();
        }
    }

    /**
     * 国外的专家名，国际合作使用
     */
    function getForeignCompanyName()
    {
        if (in_array($this->getTypeId(), array('136', '176'))) {
            return $this->getBaseinfo(true)->getData('foreign_company_name');
        }
        if (!in_array($this->getTypeId(), array('91', '125'))) return '-';
        if ($this->getExtends('getForeignCompanyNam')) return $this->getExtends('getForeignCompanyNam');
        else {
            $Nationals = sf::getModel("ProjectNationals")->selectByProjectId($this->getProjectId());
            if ($Nationals->getForeignUnit()) $this->setExtends('getForeignCompanyNam', $Nationals->getForeignUnit());
            return $Nationals->getForeignUnit();
        }
    }

    /**
     * 国际合作项目的合作基地
     */
    function getBaseName()
    {
        if (in_array($this->getTypeId(), array('136', '176'))) {
            return $this->getBaseinfo(true)->getData('is_nation_project') == '否' ? '否' : $this->getBaseinfo(true)->getData('base_name');
        }
        return '-';
    }

    /**
     * 国和5+1导出使用
     */
    function get5and1()
    {
        if ($this->getTypeId() == 176) {
            return $this->getBaseinfo(true)->getProduceDomain('5and1');
        }
        return '-';
    }

    /**
     * 国和4+6导出使用
     */
    function get4and6()
    {
        if ($this->getTypeId() == 176) {
            return $this->getBaseinfo(true)->getProduceDomain('4and6');
        }
        return '-';
    }

    /**
     * 国和10+3导出使用
     */
    function get10and3()
    {
        if ($this->getTypeId() == 176) {
            return $this->getBaseinfo(true)->getProduceDomain('10and3');
        }
        return '-';
    }

    /**
     * 是否军民融合
     */
    function getDualUse()
    {
        if ($this->getExtends('getDualUse')) return $this->getExtends('getDualUse');
        else {
            $datas = sf::getModel("ProjectBaseinfos")->selectByProjectId($this->getProjectId())->getDatas();
            if ($datas['jmrh']) $this->setExtends('getDualUse', $datas['jmrh']);//保存到窄表
            return $datas['jmrh'];
        }
    }

    /**
     * <AUTHOR>
     * @DateTime  2018-07-13
     * @return    [type]      [description]
     * @license   [license]
     * @version   [version]
     * @copyright 人才计划类别
     */
    function getTalentsPlanning()
    {
        if ($this->getExtends('getTalentsPlanning')) return $this->getExtends('getTalentsPlanning');
        else return '';
    }

    /**
     * 3年经济效益
     */
    function getProfit()
    {
        if ($this->getExtends('getProfit')) return $this->getExtends('getProfit');
        else {
            $datas = sf::getModel("ProjectBaseinfos")->selectByProjectId($this->getProjectId())->getDatas();
            if ($datas['snljsr']) $this->setExtends('getProfit', $datas['snljsr']);//保存到窄表
            return $datas['snljsr'];
        }
    }

    /**
     * 申请类型
     */
    function getSblx()
    {
        if ($this->getExtends('getSblx')) return $this->getExtends('getSblx');
        else {
            $datas = sf::getModel("ProjectBaseinfos")->selectByProjectId($this->getProjectId())->getDatas();
            if ($datas['sblx']) $this->setExtends('getSblx', $datas['sblx']);//保存到窄表
            return $datas['sblx'];
        }
    }

    /**
     * 判断是否可以打印
     */
    function enablePrint($type = 'apply')
    {
        switch ($type) {
            case 'apply':
                if (!in_array($this->getStatement(), array('0', '1', '3', '6'))) return true;
                else return false;
                break;
            case 'task':
                if (in_array($this->getStateForPlanBook(), array('4', '6', '10'))) return true;
                else return false;
                break;
            case 'stage':
                if (!in_array($this->getStateForStage(), array('0', '1', '3', '6', '12'))) return true;
                else return false;
                break;
            case 'complete':
                if (in_array($this->getStateForCompleteBook(), array('4', '6', '8', '10', '12', '14'))) return true;
                else return false;
                break;
            default:
                return false;
                break;
        }
        return false;
    }

    /**
     * 判断是否可以填写
     */
    function enableWrite($type = 'apply')
    {
        if($this->isNew()) return false;
        if(input::session('roleuserid') != $this->getUserId()) return false;
        switch ($type) {
            case 'apply':
                if (in_array($this->getStatement(), array('0', '1', '3', '6','12'))) return true;
                if ($this->getStatement() == 20 && $this->getCatId() == 271) return true;
                else return false;
                break;
            case 'task':
                if (in_array($this->getStateForPlanBook(), array('0', '1', '3', '5', '7', '9'))) return true;
                else return false;
                break;
            case 'stage':
                if (in_array($this->getStateForPlanBook(), array('0', '1', '3', '6', '12'))) return true;
                else return false;
                break;
            case 'complete':
                if (in_array($this->getStateForCompleteBook(), array('4', '6', '8', '10', '12', '14'))) return true;
                else return false;
                break;
            default:
                return false;
                break;
        }
        return false;
    }

    /**
     * 判断是否可以转化为ZIP压缩包
     */
    function enableZip($type = 'task')
    {
        switch ($type) {
            case 'apply':
                if (!$file_name = $this->getConfigs("file.apply")) return false;
                break;
            case 'task':
                if (!$file_name = $this->getConfigs("file.task")) return false;
                break;
            case 'complete':
                if (!$file_name = $this->getConfigs("file.complete")) return false;
                break;
            default:
                return false;
                break;
        }
        //判断是否可以转换
        $info = pathinfo($file_name);
        $ext = strtolower($info['extension']);
        if (in_array($ext, array('pdf'))) return true;
        else return false;
    }

    /**
     * 判断是否需要回避
     */
    function checkSuspicion($str, $type = "company")
    {
        if (!$str) return false;//空的放回不避嫌
        if ($type == 'company') {//单位避嫌,只判断申报单位，不判断合作单位20181022
            if ($str == $this->getCorporationName()) return true;
        } else if ($type == 'user') {//申报人避嫌
            //return false;
        }
        return false;
    }

    /**
     * 贷款利息
     */
    function getInterest()
    {
        if ($this->getTypeId() != 132 || $this->getStatement() < 20) return '-';
        $db = sf::getLib("db");
        $row = $db->fetch_first("select custom,mark,rule_id from project_grade where project_id = '" . $this->getProjectId() . "' and role='CW'");
        if (in_array($row['rule_id'], array('52', '53'))) {
            $json = json_decode($row['custom'], true);
            return $json['heji'];
        } else {
            $mark = explode('$', $row['mark']);
            return array_pop($mark);
        }
    }

    /**
     * 计算得分
     */
    function scoring()
    {

    }

    /**
     * 获取相关文件
     */
    function getFile($type, $branch = 1)
    {
        if (!$type) return false;
        $branch = $branch - 1;
        if ($branch < 0) $branch = 0;
        $db = sf::getLib("db");
        $row = $db->fetch_first("select file_path from `project_attachments` where item_id = '" . $this->getProjectId() . "' and item_type='" . $type . "' LIMIT " . $branch . ",1");
        if (is_array($row)) return $row['file_path'];
        else return false;
    }

    function enableArchive($type = '')
    {
        if ($this->isNew()) return false;
        switch ($type) {
            case 'apply':
                if ($this->getStatement() >= 20) return true;
                break;
            case 'task':
                if ($this->getStateForPlanBook() == 10) return true;
                break;
            case 'budget':
                if ($this->getStateForBudgetBook() == 30) return true;
                break;
            case 'assess':
                if ($this->getStatement() >= 20) return true;
                break;
            case 'audit':
                if ($this->audits(true)->getStatement() == 30) return true;
                break;
            case 'history':
                if ($this->getIsSubsidy() && $this->getStatement() == 29) return true;
                else if (in_array($this->getStatement(), array('30', '31', '35', '40'))) return true;
                break;
            default:
                if (in_array($this->getStatement(), array('29', '30', '31', '35', '40'))) return true;
                break;
        }
        return false;
    }

    /**
     * 评审时间
     * @param string $format [返回时间格式]
     * @param string $type [评审类型]
     * @return string       [返回评审时间]
     */
    function getAssessedAt($type = "project", $format = "Y年m月")
    {
        $db = sf::getLib("Db");
        if ($type == 'project') {
            $row = $db->fetch_first("SELECT updated_at FROM `project_grade` WHERE project_id = '" . $this->getProjectId() . "' ORDER BY updated_at DESC LIMIT 0,1");
            return date($format, strtotime($row['updated_at']));
        } else {
            return '-';
        }
    }

    /**
     * 材料上报前提
     * @param string $type [description]
     * @return [type]       [description]
     */
    function premise($type = 'complete')
    {
        $msg = array();
        if ($this->getStateForPlanBook() < 10) $msg[] = '提交报告之前，必须完成该项目任务合同签署';
        if ($this->getIsReport() > 0 && $this->getIsReport() < 5) $msg[] = '必须完成科技报告填写后才能填写验收报告';
        $Achievement = $this->getAchievement();
        if ($Achievement->isNew()) $msg[] = '必须在线完成科技成果登记后才能上报验收报告';
        else if ($Achievement->getStatement() < 10) $msg[] = '科技成果登记必须经过档案馆确认后才能上报验收报告';
        if ($this->getRadicateMoney() >= 100) {
            $audit = $this->audits();
            if ($audit->isNew()) $msg[] = '必须在线完成项目验收（结题）财务审计报告!项目验收（结题）财务审计申请<a href="' . site_url("user/report/apply/id/" . $this->getProjectId()) . '">请点击这里</a>快速处理。';
            if ($audit->getStatement() < 30) $msg[] = '项目验收（结题）财务审计报告必须审定后才能上报验收报告';
        }
        return $msg;
    }

    /**
     * 获取引擎工作器Id
     * @param string $type 解析器ID类型
     * @return [type]       [description]
     */
    function getWorkerId($type = 'apply')
    {
        switch ($type) {
            case 'stage'://中期进度报告
                //根据项目设置返回
                if ($stageId = $this->getConfigs('worker.stage')) return $stageId;
                //从指南返回
                $guide = $this->getGuide(true);
                if ($stageId = $guide->getStageMapId()) return $stageId;
                return 0;
                break;
            case 'task'://任务书
                //根据项目设置返回
                if ($task_id = $this->getConfigs('worker.task')) return $task_id;
                //国家级项目直接返回
                if($this->getCatId()==230) return 855;
                //从指南返回
                $guide = $this->getGuide(true);
                if ($task_id = $guide->getTaskMapId()) return $task_id;
                //从type表查找
                $type = $this->getType(true);
                if ($task_id = $type->getTaskWorkerId()) return $task_id;
                return 0;
                break;
            case 'complete'://验收报告
                return 834;
                //根据项目设置返回
                if ($complete_id = $this->getConfigs('worker.complete')) return $complete_id;
                //从指南返回
                $guide = $this->getGuide(true);
                if ($complete_id = $guide->getCompleteMapId()) return $complete_id;
                //从type表查找
                $type = $this->getType(true);
                if ($complete_id = $type->getCompleteWorkerId()) return $complete_id;
                return 0;
                break;
            default://申报书ID
                return parent::getWorkerId();
                break;
        }
    }

    /**
     * 获取引擎工作器
     * @param string $type [description]
     * @return [type]       [description]
     */
    function worker($type = 'apply', $f = false)
    {
        if (!in_array($type, array('apply', 'task', 'stage', 'complete'))) $type = 'apply';
        if ($this->worker[$type] === NULL || $f) {
            $this->worker[$type] = sf::getModel("EngineWorkers", $this->getWorkerId($type));
        }
        return $this->worker[$type];
    }

    function clearWorker($type = 'apply')
    {
        $this->worker[$type] = null;
    }

    /**
     * 取得部件配置信息
     */
    function getWidgetConfigs($widget_name = '', $type = 'apply')
    {
//		if($type == 'apply'){
//			//指南配置
//			$guide_configs = $this->getGuide()->getConfigs($type);
//			//解析器配置
//			$worker_configs = $this->worker($type)->getConfigs();
//			//合并
//			$_configs = array_merge($worker_configs,$guide_configs);
//		}else{
//			$_configs = $this->worker($type)->getConfigs();
//		}
        $_configs = $this->worker($type)->getConfigs();
        if ($widget_name) return $_configs[$widget_name];
        else return $_configs;
    }

    /**
     * 获取项目部件
     * @param string $type [description]
     * @return [type]       [description]
     */
    function widgets($_widget = '', $type = 'apply')
    {
        if (router::getMethod()!='read' && $this->isNew()) return false;
        $_widget = strtolower($_widget);
        if (in_array($_widget, array_keys((array)$this->widgets[$type]))) return $this->widgets[$type][$_widget]->setProject($this);
        else {
            //读取指南附件配置
            $configs = $this->getWidgetConfigs('', $type);
            $widget_configs = $this->widget_configs;
            $pager = $this->worker($type)->widgets();
            while ($w = $pager->getObject()) {
                if (count($configs[$w->getWidgetName()]) > 0) {
                    $_configs = array_merge($configs[$w->getWidgetName()], $widget_configs);
                } else $_configs = $widget_configs;

                $this->widgets[$type][$w->getWidgetName()] = $w->getWidget($_configs);
            }
            if (in_array($_widget, array_keys($this->widgets[$type]))) return $this->widgets[$type][$_widget]->setProject($this);
        }
        return '部件不存在！';
    }

    /**
     * 获取项目部件
     * @param string $type [description]
     * @return [type]       [description]
     */
    function getWidget($widgetName, $type = 'apply')
    {
        $widget = $this->worker($type)->getWidgetByName($widgetName);
        if($widget->isNew()){
            return false;
        }
        return $widget;
    }

    /**
     * 获取项目部件
     * @param string $type [description]
     * @return [type]       [description]
     */
    function widget_validate($type = 'apply')
    {
        $message = array();
        $configs = $this->getWidgetConfigs('',$type);
        $widget_configs = $this->widget_configs;
        $pager = $this->worker($type)->widgets();
        while ($w = $pager->getObject()) {
            $_msg = array();
            $_widget = NULL;
            if (count($configs[$w->getWidgetName()]) > 0) {
                $_configs = array_merge($configs[$w->getWidgetName()], $widget_configs);
            } else $_configs = $widget_configs;
            $_widget = $w->getWidget($_configs);
            $_msg = $_widget->setProject($this)->validate();
            if (count($_msg) > 0) $message = array_merge($message, $_msg);
        }
        return $message;
    }

    /**
     * 按照项目设置部件配置信息
     * @param string $type [description]
     * @return [type]       [description]
     */
    function setWidgetConfigs($key, $val)
    {
        $this->widget_configs[$key] = $val;
        return $this;
    }

    /**
     * <AUTHOR>
     * @DateTime  2019-08-06
     * @return    [type]      [description]
     * @license   [license]
     * @version   [version]
     * @copyright 根据类型获取项目附件
     */
    function attachements($item_type = '', $single_model = false)
    {
        if (!$item_type) return array();

        if ($single_model)
            $sql = ' limit 0,1';

        $files = sf::getModel("Filemanager")->selectAll(" item_id = '" . parent::getProjectId() . "' AND item_type = '" . $item_type . "' ", ' order by id desc' . $sql);
        return $files->toArray();
    }

    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachment($itemType = 'attachement', $limit = 0, $orders='order by item_type asc,no asc,id asc')
    {
        $addwhere = "`item_id` = '".$this->getProjectId()."'";
        if(is_array($itemType)) {
            $addwhere .= " and item_type in ('".implode("','", $itemType)."')";
        }elseif(strstr($itemType,'%')!==false){
            $addwhere.=" and item_type like '{$itemType}'";
        }else{
            $addwhere.=" and item_type = '{$itemType}'";
        }
        return sf::getModel("Filemanager")->selectAll($addwhere, $orders, $limit);
    }

    public function getAttachementCount($itemType = 'attachement')
    {
        $addwhere = "item_id = '".$this->getProjectId()."'";
        if(strstr($itemType,'%')!==false){
            $addwhere.=" and item_type like '{$itemType}'";
        }else{
            $addwhere.=" and item_type = '{$itemType}'";
        }
        return (int)sf::getLib('db')->result_first("select count(*) c from filemanager where {$addwhere}");
    }

    /**
     * 按照挂件获取内容
     */
    function getContentByWidget($widget_name)
    {
        return sf::getModel("ProjectContents")->selectByProjectId($this->getProjectId(), $widget_name);
    }

    /**
     * <AUTHOR>
     * @DateTime  2019-08-06
     * @return    [type]      [description]
     * @license   [license]
     * @version   [version]
     * @copyright 根据类型获取项目正文
     */
    function contents($item_type = '')
    {
        if (!$item_type) return array();

        $sql = ' limit 0,1';

        $files = sf::getModel("ProjectContents")->selectAll(" project_id = '" . parent::getProjectId() . "' AND widget_name = '" . $item_type . "' ", ' order by id desc' . $sql);
        $arr = $files->toArray();
        return $arr[0];
    }

    /**
     * <AUTHOR>
     * @DateTime  2019-08-07
     * @return    [type]      [description]
     * @license   [license]
     * @version   [version]
     * @copyright 获取申报人信息
     */
    function researcher($type = 'apply', $f = false)
    {
        if ($this->researcher[$type] === NULL || $f) $this->researcher[$type] = sf::getModel("ProjectResearchers")->selectByProjectId(parent::getProjectId(), $type);
        return $this->researcher[$type];
    }

    function money($type = 'apply', $f = false)
    {
        if ($this->money[$type] === NULL || $f) $this->money[$type] = sf::getModel("ProjectMoneys")->selectByProjectId(parent::getProjectId(), $type);
        return $this->money[$type];
    }

    function fund($f = false)
    {
        if ($this->fund === NULL || $f) $this->fund = sf::getModel("ProjectFunds")->selectByProjectId(parent::getProjectId());
        return $this->fund;
    }

    function debt($f = false)
    {
        if ($this->debt === NULL || $f) $this->debt = sf::getModel("ProjectDebts")->selectByProjectId(parent::getProjectId());
        return $this->debt;
    }

    function repair($f = false)
    {
        if ($this->repair === NULL || $f) $this->repair = sf::getModel("ProjectRepairs")->selectByProjectId(parent::getProjectId());
        return $this->repair;
    }

    function techPerson($f = false)
    {
        if ($this->techPersons === NULL || $f) $this->techPersons = sf::getModel("CorporationTechPersons")->selectByProjectId(parent::getProjectId());
        return $this->techPersons;
    }

    function techProjects($year = '')
    {
        $addWhere = "`project_id` = '" . $this->getProjectId() . "' ";
        if ($year != '') $addWhere .= " AND year = '" . $year . "' ";
        return sf::getModel('ProjectMemos')->selectAll($addWhere, "ORDER BY `no` ASC,`id` ASC");
    }

    function record($f = false)
    {
        if ($this->record === NULL || $f) $this->record = sf::getModel("ProjectKeepOnRecords")->selectByProjectId(parent::getProjectId());
        return $this->record;
    }

    function gazelleExtra($f = false)
    {
        if ($this->gazelleExtra === NULL || $f) $this->gazelleExtra = sf::getModel("ProjectGazelleExtras")->selectByProjectId($this->getProjectId());
        return $this->gazelleExtra;
    }

    /**
     * 技术领域名称
     */
    function setSkillName($v)
    {
        return parent::setSkillDomain($v);
    }

    /**
     * 获取技术领域名称
     */
    function getSkillName()
    {
        if (!$this->getSkillId()) return '';
        return parent::getSkillDomain();
    }

    /**
     * 增加项目负责人到参与人员
     */
    function setResearcherToMembers()
    {
        $members = sf::getModel('ProjectMembers')->selectAll("`user_id` = '{$this->getUserId()}' and `project_id` = '{$this->getProjectId()}'");
        if ($members->getTotal() == 0) {
            $user = $this->user();
            $member = sf::getModel('ProjectMembers');
            $member->setNo(0);
            $member->setProjectId($this->getProjectId());
            $member->setUserId($this->getUserId());
            $member->setCardType($user->getCardType());
            $member->setCertificate($user->getUserIdcard());
            $member->setName($user->getPersonname());
            $member->setSex($user->getUserSex());
            $member->setBirthday($user->getUserBirthday());
            $member->setDegree($user->getUserDegree());
            $member->setProfessionalTitle($user->getUserHonor());
            $member->setTitle($user->getUserHonor());
            $member->setMarjor($user->getUserWork());
            $member->setDuty($user->getUserOccupation());
            $member->setCompanyId($user->getCorporationId());
            $member->setCompanyName($user->getCorporationName());
            $member->setCompanyId($user->getCorporationId());
            $member->setYear($this->getDeclareYear());
            $member->setType('apply');
            $member->setCreatedAt(date("Y-m-d H:i:s"));
            $member->save();
            return true;
        }
        return false;
    }

    /**
     * 增加单位资料到项目
     */
    function setCompany()
    {
        $companys = sf::getModel('ProjectCompanys')->selectAll("`project_id` = '{$this->getProjectId()}'");
        if ($companys->getTotal() == 0) {
            $corporation = $this->getCorporation();
            $company = sf::getModel('ProjectCompanys')->selectByProjectId($this->getProjectId());
            $company->setCode($corporation->getCode());
            $company->setSubject($corporation->getSubject());
            $company->setDepartmentName($corporation->getDepartmentName());
            $company->setAddress($corporation->getAddress());
            $company->setPostcode($corporation->getPostalcode());
            $company->setAreaCode($corporation->getAreaCode());
            $company->setAreaSubject($corporation->getArea());
            //法人
            $principal['name'] = $corporation->getPrincipal();
            $principal['idcard'] = $corporation->getPrincipalCardid();
            $company->setPrincipal($principal);
            //联系人
            $linkman['name'] = $corporation->getLinkman();
            $linkman['tel'] = $corporation->getPhone();
            $linkman['mobile'] = $corporation->getMobile();
            $linkman['email'] = $corporation->getLinkmanEmail();
            $company->setLinkman($linkman);
            //财务信息
            $finances = $this->getCorporation()->finances();
            if ($finances->getTotal() > 0) {
                $financeModel = $finances->getObject();
            } else {
                $financeModel = sf::getModel('CorporationFinances')->selectByYear(date('Y', strtotime('-1 year')), $this->getCorporation()->getUserId());
            }
            $finance['name'] = $financeModel->getFinanceLeaderFullName();
            $finance['mobile'] = $financeModel->getFinanceLeaderMobile();
            $finance['tel'] = $financeModel->getFinanceLeaderPhone();
            $finance['email'] = $financeModel->getFinanceLeaderEmail();
            $totalLiabilities = $financeModel->getTotalLiabilities() ?: '0.00';   //负债总额
            $totalAssets = $financeModel->getTotalAssets() ?: '0.00';             //资产总额
            $finance['lastyear_assets'] = $totalAssets;
            $finance['lastyear_asset_all'] = $totalAssets;
            $finance['lastyear_asset_ratio'] = bcdiv($totalLiabilities, $totalAssets, 4) * 100;        //资产负债率
            $finance['lastyear_liability'] = $totalLiabilities;
            $finance['lastyear_owner'] = $financeModel->getOwnerEquity() ?: '0.00';
            $finance['lastyear_flow'] = $financeModel->getCurrentLiabilities() ?: '0.00';
            $finance['lastyear_profit'] = $financeModel->getNetProfit() ?: '0.00';
            $finance['lastyear_sales'] = $financeModel->getSalesRevenue() ?: '0.00';
            $finance['lastyear_rd'] = $financeModel->getRD() ?: '0.00';
            $finance['lastyear_main_revenue'] = $financeModel->getMainRevenue() ?: '0.00';
            $finance['lastyear_income'] = $financeModel->getSalesRevenue() ?: '0.00';
            $company->setFinance($finance);
            //职工人数
            $staff['count'] = $financeModel->getEmployeesNumber() ?: 0;
            $staff['developer'] = $financeModel->getResearcherNumber() ?: 0;
            $company->setStaff($staff);
            $company->save();
            return true;
        }
        return false;
    }

    /**
     * 增加申报单位到合作单位
     */
    function setCompanyToCooperations()
    {
        $cooperations = sf::getModel('ProjectCooperations')->selectAll("`company_id` = '{$this->getCorporationId()}' and `project_id` = '{$this->getProjectId()}'");
        if ($cooperations->getTotal() == 0) {
            $projectCooperation = sf::getModel('ProjectCooperations');
            $projectCooperation->setNo(0);
            $projectCooperation->setProjectId($this->getProjectId());
            $projectCooperation->setProjectType('apply');
            $projectCooperation->setType('A');
            $projectCooperation->setCompanyId($this->getCorporationId());
            $projectCooperation->setCompanyName($this->getCorporationName());
            $projectCooperation->setCode($this->getCorporation()->getCode());
            $projectCooperation->setCreatedAt(date('Y-m-d H:i:s'));
            $projectCooperation->setUpdatedAt(date('Y-m-d H:i:s'));
            $projectCooperation->save();
            return true;
        }
        return false;
    }

    /**
     * 取得小标题
     */
    function getSubtitle($len = 0)
    {
        if (parent::getSubtitle($len)) return parent::getSubtitle($len);
        //重构
        $subtitle = '';
        if (in_array($this->getWorkerId(), array('2', '17', '18', '24'))) {
            $jichu_note = trim($this->getBaseinfo()->getData('jichu_note'));
            switch ($jichu_note) {
                case '国家重点实验室固定人员':
                    $subtitle .= '(国重)';
                    break;
                case '四川省重点实验室固定人员':
                    $subtitle .= '(省重)';
                    break;
                case '四川省杰出青年科技人':
                    $subtitle .= '(杰青)';
                    break;
                case '四川省青年科技创新研究团队':
                    $subtitle .= '(团队)';
                    break;
                default:
                    break;
            }
        }
        return $subtitle;
    }

    /**
     * <AUTHOR>
     * @DateTime  2019-09-30
     * @return    boolean     [description]
     * @license   [license]
     * @version   [version]
     * @copyright 是否需要财务专家
     */
    function isNeedFinance()
    {
        //138：科技成果转移转化示范，142：创新产品
        if (in_array(parent::getTypeId(), [138, 153, 142, 155, 161, 162, 163, 164, 171, 208]))
            return true;
        else return false;
    }

    /**
     * 获取项目的评审指标
     */
    function getRule()
    {
        if ($rule_id = $this->getConfigs("maps.assess")) {//从项目获取
            $rule = sf::getModel("Rules", $rule_id);
            return $rule;
        }
        //从评审分组获取
        $group = $this->getGroup();
        if($group->getAssessMap()){
            return sf::getModel("Rules", $group->getAssessMap());
        }
        //从指南获取评审指标
        $guide = $this->getGuide(true);
        if($guide->getAssessRuleId()){
            return sf::getModel("Rules", $guide->getAssessRuleId());
        }
        return sf::getModel("Rules")->selectByCode($this->getSubjectCode());
    }

    /**
     * 是否是中央引导地方项目
     */
    function isCentre()
    {
        return in_array($this->getTypeId(), array('113', '114', '115', '116', '117', '118', '119', '120'));
    }

    /**
     * 按年度获取进度报告
     */
    function getStageByYear($year = '')
    {
        if ($year == '') $year = date("Y");
        return sf::getModel("ProjectStages")->selectByProjectId($this->getProjectId(), $year);
    }

    /**
     * 按年度获取中期报告
     */
    function getMidstageByYear($year = '')
    {
        if ($year == '') $year = date("Y");
        return sf::getModel("ProjectMidstages")->selectByProjectId($this->getProjectId(), $year);
    }

    /**
     * 获取进度报告数据集
     */
    function stages($year = '')
    {
        return sf::getModel("ProjectStages")->selectAll("project_id = '" . $this->getProjectId() . "'", "ORDER BY year DESC");
    }

    /**
     * 获取季报报告数据集
     */
    function quarters($year = '')
    {
        return sf::getModel("ProjectQuarters")->selectAll("project_id = '" . $this->getProjectId() . "'", "ORDER BY `year` DESC,quarter DESC");
    }

    /**
     * 获取季报报告
     */
    function getQuarter($year,$quarter)
    {
        return sf::getModel("ProjectQuarters")->selectByProjectId($this->getProjectId(),$year,$quarter);
    }

    /**
     * 获取最近一次的季报报告
     */
    function getLastQuarter()
    {
        $quarters =  sf::getModel("ProjectQuarters")->selectAll("project_id = '" . $this->getProjectId() . "'", "ORDER BY `year` DESC,quarter DESC");
        if($quarters->getTotal()==0){
            return false;
        }
        return $quarters->getObject();
    }

    /**
     * 获取经费支出
     */
    function payments()
    {
        return sf::getModel("ProjectPayments")->selectAll("project_id = '" . $this->getProjectId() . "'");
    }

    /**
     * 获取企业R&D
     */
    function getRandD($f = false)
    {
        if ($this->RandD === NULL || $f) $this->RandD = sf::getModel("ProjectRandDs")->selectByProjectId($this->getProjectId());
        return $this->RandD;
    }

    /**
     * 设置成果编号
     */
    function setAchievementId($achievement_id)
    {
        if ($this->isNew()) return false;
        $achievement = sf::getModel("ProjectAchievements")->selectByProjectId($this->getProjectId());
        if ($achievement->getStatement() == 10) return false;
        $achievement->setNumber($achievement_id);
        $achievement->setStatement(10);//直接完成登记
        return $achievement->save();
    }

    /**
     * 设置评审编号
     */
    function setOrderNumber($v)
    {
        parent::setIndustryId($v);//占用该字段替代
        return $this;
    }

    /**
     * 设置评审编号
     */
    function getOrderNumber($str = 0)
    {
        return parent::getIndustryId();//占用该字段替代
    }

    /**
     * 项目类型
     */
    function getTypeSubject($len = 0)
    {
        switch ($this->getIsShift()) {
            case 2:
                return parent::getTypeSubject() . '(转移支付)';
                break;
            case 3:
                return parent::getTypeSubject() . '(苗子工程)';
                break;
            case 4:
                return parent::getTypeSubject() . '(自筹项目)';
                break;
            case 8:
                return parent::getTypeSubject() . '(评审验收)';
                break;
            case 9:
                return parent::getTypeSubject() . '(包干制试点)';
                break;
            default:
                return parent::getTypeSubject();
                break;
        }
        return parent::getTypeSubject();
    }

    /**
     * 获取省卫生健康委联系人
     */
    function getManagerLinkman()
    {
        if ($this->getIsShift() == 2) {
            $pager = sf::getModel("ProjectManagerHistorys")->selectAll("type = 'linkman' and project_id = '" . $this->getProjectId() . "' ", "ORDER BY created_at DESC", 1);
            if ($manager = $pager->getObject()) return $manager->getManagerName() . ' <i id="linkman_phone_' . $this->getId() . '" class="glyphicon glyphicon-phone-alt text-danger" title="点击显示电话" style="cursor:pointer" onclick="$.get(\'' . site_url("ajax/getManagerPhone") . '\',{userid:\'' . $manager->getManagerId() . '\'},function(json){$(\'#linkman_phone_' . $this->getId() . '\').html(json);});"></i>';
            else return '-';
        } else return parent::getManagerName();
    }

    /**
     * 获取最后一条操作记录,主要用于终止项目查看原因
     */
    function getLastMessage()
    {
        $message = sf::getLib("Db")->fetch_first("SELECT content FROM project_historys WHERE type = 'complete' and project_id = '" . $this->getProjectId() . "' ORDER BY updated_at DESC LIMIT 0,1 ");
        return $message['content'];
    }

    //外协申请表 正文
    function getOutContent()
    {
        $files = sf::getModel("Filemanager")->selectAll("`item_id`='" . $this->getProjectId() . "' and `item_type` = 'out_content'", "order by id desc");
        if ($files->getTotal() > 0) {
            return $files->getObject();
        } else return;
    }

    /**
     * 上级单位
     * @return [type] [description]
     */
    public function getParent()
    {
        $corporation = sf::getModel("corporations")->selectByUserId($this->getCorporationId());
        return sf::getModel("corporations")->selectByUserId($corporation->getParentId());
    }

    /**
     * 是否有钱，拨付总和减去支出总和
     */
    function hasMoney()
    {
        return $this->getPayforMoney() - $this->getPaymentMoney();
    }

    /**
     * 取得项目收入（拨付）总和
     */
    function getPayforMoney()
    {
        $db = sf::getLib("db");
        $sql = "SELECT SUM(money) as money,do_type FROM payfors WHERE project_id = '" . $this->getProjectId() . "' GROUP BY do_type ORDER BY do_type ASC";
        $query = $db->query($sql);
        $money = 0;
        while ($row = $db->fetch_array($query)) {
            if ($row['do_type'] == 1) $money += $row['money'];
            else $money -= $row['money'];
        }
        return $money;
    }

    /**
     * 取得项目支出总和
     */
    function getPaymentMoney()
    {
        $db = sf::getLib("db");
        $row = $db->fetch_first("SELECT SUM(money) as money FROM project_payments WHERE project_id = '" . $this->getProjectId() . "'");
        return $row['money'];
    }

    function moneys()
    {
        return sf::getModel('ProjectMoney')->selectAll("`project_id` = '" . parent::getProjectId() . "' AND `type` = 'apply'", "ORDER BY `id` ASC");
    }

    function getMoneyByMark($mark,$type='apply')
    {
        if ($this->getStateForPlanBook() == 10) {
            $type = 'task';
        }
        return sf::getModel('ProjectMoney')->selectByProjectIdAndMark($this->getProjectId(),$mark,$type);
    }

    function taskMoneys()
    {
        if ($this->getStateForPlanBook() != 10) {
            return $this->moneys();
        }
        return sf::getModel('ProjectMoney')->selectAll("`project_id` = '" . parent::getProjectId() . "' AND `type` = 'task'", "ORDER BY `id` ASC");
    }

    function paymentMoneys()
    {
        $type = 'apply';
        if ($this->getStateForPlanBook() == 10) {
            //任务书已签署
            $type = 'task';
        }
        $rows = sf::getModel('ProjectMoney')->selectAll("`project_id` = '" . parent::getProjectId() . "' AND `type` = '{$type}'", "ORDER BY `id` ASC");
        if ($rows->getTotal() == 0) {
            openPayment(parent::getProjectId());
        }
        return sf::getModel('ProjectMoney')->selectAll("`project_id` = '" . parent::getProjectId() . "' AND `type` = '{$type}'", "ORDER BY `id` ASC");
    }

    function updatePaymentMoneys()
    {
        $type = 'apply';
        if ($this->getStateForPlanBook() == 10) {
            //任务书已签署
            $type = 'task';
        }
        $rows = sf::getModel('ProjectMoney')->selectAll("`project_id` = '" . parent::getProjectId() . "' AND `type` = '{$type}'", "ORDER BY `id` ASC");
        if ($rows->getTotal() > 0) {
            updatePayment(parent::getProjectId());
        }
    }

    /**
     * 取得项目支出总和
     */
    function getPaymentTotal()
    {
        $db = sf::getLib("db");
        $row = $db->fetch_first("SELECT SUM(cast(payment_total as  decimal(20,5))) as money FROM project_money WHERE project_id = '" . $this->getProjectId() . "'");
        return (float) $row['money'];
    }

    /**
     * 取得项目申请总和
     */
    function getApplyTotal()
    {
        $db = sf::getLib("db");
        $row = $db->fetch_first("SELECT SUM(cast(apply_total as  decimal(20,5))) as money FROM project_money WHERE project_id = '" . $this->getProjectId() . "'");
        return (float)$row['money'];
    }

    //项目类别
    function getFlowTyp()
    {
        return;
        switch ($this->getFlowType()) {
            case '1':
                return '集团审批类';
                break;
            case '2':
                return '二级备案类';
                break;
            case '3':
                return '三级备案类';
                break;
            default:
                return '<s style="color:red">无</s>';
                break;
        }
    }

    public function setPaymentMoney()
    {
        $db = sf::getLib("db");
        //从申报书读取经费信息
        $query = $db->query("SELECT device1,device2,device3,material,test,power,reference,manpower,consultancy,cooperation,indirect,other FROM `project_moneys` WHERE project_id = '" . $this->getProjectId() . "' AND project_type = 'project'");
        $items = $db->fetch_array($query);
        foreach ($items as $mark => $value) {
            $item = json_decode($value, true);
            $projectMoney = sf::getModel("ProjectMoney")->selectByProjectIdAndMark($this->getProjectId(), $mark, 'apply');
            $projectMoney->setMarkSubject(moneyMark($mark));
            $projectMoney->setApplySpecial($item['cz']);
            $projectMoney->setApplyOwn($item['zc']);
            $projectMoney->setApplyTotal($item['hj']);
            $projectMoney->save();
        }
    }

    function shouldCheckProject()
    {
        $now = date('Y-m-d');
        $db = sf::getLib("db");
        // $query1 = $db->query("SELECT * FROM `".$this->table."` WHERE is_subsidy = 0 AND user_id = '".parent::getUserId()."' AND real_end_at >= '".$now."' AND real_end_at != '' ");
        $query2 = $db->query("SELECT * FROM `" . $this->table . "` WHERE is_subsidy = 0 AND user_id = '" . parent::getUserId() . "' AND real_end_at <= '" . $now . "' AND real_end_at != '' AND statement = 29");
        $query3 = $db->query("SELECT * FROM `" . $this->table . "` WHERE is_subsidy = 0 AND user_id = '" . parent::getUserId() . "' AND statement = 30 ");
        // if($db->num_rows($query1)) return '否';
        if ($db->num_rows($query2)) return "<span class='red'>是</span>";
        if ($db->num_rows($query3)) return '已验收';
        return '否';
    }

    public function getResearchTyp()
    {
        return $this->getProjectTypeSubject();
    }

    public function getProjectTypeSubject()
    {
        return parent::getProjectTypeSubject()?:$this->getCatSubject();
    }

    public function getCatSubject()
    {
        return sf::getModel('Categorys',parent::getCatId())->getSubject();
    }

    public function getCatLevel()
    {
        switch (parent::getCatId()) {
            case 174:
            case 233:
            case 235:
                return '省级';
            case 225:
                return '市州级';
            case 230:
                return '国家级';
            case 231:
                return '县级';
            default:
                return '省级';
        }
    }

    public function getCatTag()
    {
        switch (parent::getCatId()) {
            case 174:
                return 'S';
            case 235:
                return 'QY';
            case 233:
                return 'CY';
            case 225:
                return $this->getDepartment()->getLabel().'S';
            case 230:
                return 'G';
            case 231:
                return $this->getDepartment()->getLabel().'X';
            default:
                return 'S';
        }
    }

    public function getTopGuideSubject()
    {
        $guide = $this->getGuide();
        return $guide->getParent()->getSubject();
    }

    public function getSponsor()
    {
        if(in_array($this->getCatId(),[225,231])){
            $departmentId = $this->getGuide()->getDepartmentIds();
            return sf::getModel('Departments')->selectByUserId($departmentId)->getSubject();
        }
        return '四川省卫生健康委员会';
    }

    public function getStampAt()
    {
        $stampAt = $this->getGuide()->getStampAt();
        if(!$stampAt)  return '年&nbsp;&nbsp;月&nbsp;&nbsp;日';
        if($stampAt=='[当天时间]')  return date('Y年m月d日');
        return date('Y年m月d日',strtotime($stampAt));
    }

    public function copyToTask()
    {
        sf::getModel('ProjectBaseinfos')->copyToTask($this->getProjectId());
        sf::getModel('ProjectCooperations')->copyToTask($this->getProjectId());
        sf::getModel('ProjectResearchers')->copyToTask($this->getProjectId());
        sf::getModel('ProjectMembers')->copyToTask($this->getProjectId());
        sf::getModel('ProjectTeams')->copyToTask($this->getProjectId());
        sf::getModel('ProjectSchedules')->copyToTask($this->getProjectId());
        sf::getModel('ProjectTargets')->copyToTask($this->getProjectId());
        sf::getModel('ProjectMoneys')->copyToTask($this->getProjectId());
        sf::getModel('Budgets')->copyToTask($this->getProjectId());
        sf::getModel('BudgetDevices')->copyToTask($this->getProjectId());
        sf::getModel('BudgetMaterials')->copyToTask($this->getProjectId());
        sf::getModel('BudgetTests')->copyToTask($this->getProjectId());
        sf::getModel('BudgetPowers')->copyToTask($this->getProjectId());
        sf::getModel('BudgetReferences')->copyToTask($this->getProjectId());
        sf::getModel('BudgetManpowers')->copyToTask($this->getProjectId());
        sf::getModel('BudgetConsultancys')->copyToTask($this->getProjectId());
        sf::getModel('BudgetOthers')->copyToTask($this->getProjectId());
        sf::getModel('ProjectAttachments')->copyToTask($this->getProjectId());
    }

    //html、pdf保存路径,本地路径
    private function getSavePath()
    {
        $path = config::get('document_dir', WEBROOT . '/Documents') . '/' . substr($this->getProjectId(), 0, 1) . '/' . $this->getProjectId();
        @mkdir($path, 0777, true);
        return $path;
    }

    //md5上传的文件id
    function getKey()
    {
        array_forget($data, 'data.baseinfo.file_path');
        return substr(md5(strip_tags(serialize($data))), 0, 20);;
    }

    //获取文档存放路径，url方式获取
    public function getDocumentPath()
    {
        return site_path('/') . 'Documents/' . substr($this->getProjectId(), 0, 1) . '/' . $this->getProjectId();;
    }

    //外协申请表
    function loadOutTemplates()
    {
        $dir = 'user/project/';
        $templates = [
            'print_readme' => 'print_readme',
            'header'       => 'common_header',
            'headerE'      => 'common_headerE',
            'footer'       => 'common_footer',
            'footerE'      => 'common_footerE',
            'content_head' => 'wxsqb',
            'content_end'  => 'wxsqb_end',
        ];

        $_temp = [];
        $data['project'] = $this;
        View::set($data);
        foreach ($templates as $key => $template) {
            $_temp[$key] = View::getContent($dir . $template);
        }
        //正文
        $_temp['content'] = site_path('/up_files/') . '/' . $this->getOutContent()->getFilePath();
        //预览版提示打印方式
        if ($this->getStatement() == 10) {
            $_temp['water'] = '科研项目外协申请表';
        } else {
            $_temp['water'] = '科研项目外协申请表';
        }
        //模板样式
        $_temp['style'] = WEBROOT . '/css/template.css';
        return $_temp;
    }

    //外协申请表
    public function makeOutPDF()
    {
        $templetes = $this->loadOutTemplates();
        $pdf_name = time() . mt_rand(1000, 9999) . '.pdf';
        $pdf = PDF::setHeader($templetes['header'])
            ->setFooter(' ')
            ->setContent($templetes['content_head'], 'mix')
            ->setContent($templetes['content'], 'file')
            ->setContent($templetes['content_end'], 'mix')
            ->setStyle($templetes['style'])
            ->setWaterMark($templetes['water'], 0.1)
            ->setTitle($this->getSubject())
            ->setSubject($this->getSubject())
            ->setCreator('admin')
            ->setAuthor($this->getUserName())
            ->hasCover(false);
        $pdf = $pdf->save($this->getSavePath() . '/' . $pdf_name);
        //保存pdf名
        $data['key'] = $this->getKey();
        $data['pdf'] = $pdf_name;

        $this->setShortUrl($data['key'], 'project');
        $path = $this->getSavePath() . '/' . $pdf_name;
        return $this->getDocumentPath() . '/' . $pdf_name;
    }

    //外协申请
    function getStateForOut()
    {
        switch (parent::getStateForOut()) {
            case 1:
                return '填写中';
            case 2:
                if ($this->getWaitForCompany() < $this->getCompanyLevel()) {
                    return '待上级单位审核';
                }
                return '待承担单位审核';
            case 3:
                if ($this->getWaitForCompany() < $this->getCompanyLevel()) {
                    return '上级单位退回';
                }
                return '<span style="color:red">承担单位退回</span>';
            case 9:
                return '<span style="color:blue">待省卫生健康委审核</span>';
            case 10:
                return '<span style="color:green">已审批</span>';
            case 12:
                return '<s style="color:red">省卫生健康委退回</s>';
            default:
                return '<s style="color:red">未知状态</s>';
        }
    }

    public function setIndustry($arr = [])
    {
        parent::setIndustryIds(implode(',', $arr));
        return $this;
    }

    public function setSubjectIds($arr = [])
    {
        parent::setSubjectIds(implode(',', $arr));
        return $this;
    }

    public function setSubjectIds2($arr = [])
    {
        parent::setSubjectIds2(implode(',', $arr));
        return $this;
    }

    public function getIndustryIds($k = '')
    {
        $ids = parent::getIndustryIds();
        $idArr = explode(',', $ids);
        if ($k) {
            return $idArr[$k - 1];
        }
        return $idArr;
    }

    public function getSubjectIds($k = '')
    {
        $ids = parent::getSubjectIds();
        $idArr = explode(',', $ids);
        if ($k) {
            return $idArr[$k - 1];
        }
        return $idArr;
    }

//    public function getSubjectIds2($k = '')
//    {
//        $ids = parent::getSubjectIds2();
//        $idArr = explode(',', $ids);
//        if ($k) {
//            return $idArr[$k - 1];
//        }
//        return $idArr;
//    }

    /**
     * 获取项目的评分情况
     * @return mixed
     */
    function getAssessGrade()
    {
        return sf::getModel('AssessGrade')->selectAll(" project_id = '" . parent::getProjectId() . "'");
    }

    /**
     * 是否是计划内项目
     */
    public function isInPlan()
    {
        return in_array(parent::getProjectType(),[3783,3784,3785]);
    }

    /**
     * 是否是一般项目
     */
    public function isGeneralProject()
    {
        return parent::getProjectType() == 3785;
    }

    /**
     * 是否是资助类项目
     */
    public function isSupportProject()
    {
        return parent::getProjectType() == 3782;
    }

    /**
     * 是否是政府项目
     */
    public function isGovernmentProject()
    {
        return parent::getCatId() == 16;
    }

    public function getIsPublicStr()
    {
        return parent::getIsPublic() == 1 ? '是' : '否';
    }

    public function needTask()
    {
        return parent::getProjectType() == 3782;
    }

    /*
     获取项目的评分情况
    */
    function getGrade()
    {
        return sf::getModel('ProjectGrade')->selectAll(" project_id = '" . parent::getProjectId() . "'");
    }

    /**
     * 获取上一级待审核的单位id
     * @return false|string
     */
    function getWaitForCompanyId()
    {
        $level = $this->getWaitForCompany();
        switch ($level){
            case 1:
                return $this->getFirstId();
            case 2:
                return $this->getSecondId();
            case 3:
                return $this->getThirdId();
        }
        return false;
    }

    function getLinkmanMobile()
    {
        $baseModel = $this->getBaseinfo();
        if($baseModel->getLinkmanMobile()) return $baseModel->getLinkmanMobile();
        $linkman = sf::getModel('Declarers')->selectByUserId($this->getLinkmanId());
        return $linkman->getUserMobile();
    }

    function getSeasonUse($year,$season)
    {
        $db = sf::getLib('db');
        switch ($season){
            case 1:
                $payStartAt = $year.'-01-01';
                $payEndAt = date('Y-03-31',strtotime($year.'-01-01'));
                break;
            case 2:
                $payStartAt = $year.'-04-01';
                $payEndAt = date('Y-06-30',strtotime($year.'-01-01'));
                break;
            case 3:
                $payStartAt = $year.'-07-01';
                $payEndAt = date('Y-09-30',strtotime($year.'-01-01'));
                break;
            case 4:
                $payStartAt = $year.'-10-01';
                $payEndAt = date('Y-12-31',strtotime($year.'-01-01'));
                break;
        }
        $total =  $db->result_first("select sum(money) from project_payments where project_id = '".$this->getProjectId()."' and (pay_at>='{$payStartAt}' and pay_at<= '{$payEndAt}')");
        return (float)$total;
    }

    function getYearUse($year)
    {
        $db = sf::getLib('db');
        $total =  $db->result_first("select sum(money) from project_payments where project_id = '".$this->getProjectId()."' and (pay_at>='{$year}-01-01' and pay_at<= '{$year}-12-31')");
        return (float)$total;
    }

    function getHalfYearTarget($year='',$season='')
    {
        if(empty($year)) $year = date('Y');
        if(empty($season)) $season = (int)ceil((date('n'))/3);
        switch ($season){
            case 1:
                $startAt = $year.'-01-01';
                $endAt = date('Y-03-31',strtotime($year.'-01-01'));
                break;
            case 2:
                $startAt = $year.'-04-01';
                $endAt = date('Y-06-30',strtotime($year.'-01-01'));
                break;
            case 3:
                $startAt = $year.'-07-01';
                $endAt = date('Y-09-30',strtotime($year.'-01-01'));
                break;
            case 4:
                $startAt = $year.'-10-01';
                $endAt = date('Y-12-31',strtotime($year.'-01-01'));
                break;
        }

        $schedules = $this->schedules('apply')->toArray();
        foreach ($schedules as $schedule){
            if($startAt<$schedule['start_at'] || $endAt>$schedule['end_at']) continue;
            return $schedule['content'];
        }
        return false;
    }

    public function doAssignSector($sectorIds)
    {
        $reviews = sf::getModel('ProjectReviews')->selectAll("project_id = '".$this->getProjectId()."'");
        while($review = $reviews->getObject()){
            if(!in_array($review->getSectorId(),$sectorIds)){
                $review->delete();
            }
        }
        foreach ($sectorIds as $sectorId){
            $sector = sf::getModel('Sectors')->selectByUserId($sectorId);
            if($sector->isNew()) continue;
            $review = sf::getModel('ProjectReviews')->selectByProjectId($this->getProjectId(),$sectorId);
            $review->setSectorName($sector->getSubject());
            $review->setStatement(1);
            $review->setUpdatedAt(date('Y-m-d H:i:s'));
            $review->save();
        }
    }

    public function getAssignSectors()
    {
        $reviews = sf::getModel('ProjectReviews')->selectAll("project_id = '".$this->getProjectId()."'");
        $names = [];
        while($review = $reviews->getObject()){
            $names[] = $review->getSectorName();
        }
        return $names ? implode('、',$names) : '无';
    }

    public function getAssignSectorIds()
    {
        $reviews = sf::getModel('ProjectReviews')->selectAll("project_id = '".$this->getProjectId()."'");
        $ids = [];
        while($review = $reviews->getObject()){
            $ids[] = $review->getSectorId();
        }
        return $ids;
    }

	public function getCountryRanks()
	{
	  return sf::getModel('ProjectDatas')->selectAll("project_id = '".$this->getProjectId()."' and index_code = 'plus_fudan_nation'","order by `index_year` desc");
	}

	public function getSouthwestRanks()
	{
        return sf::getModel('ProjectDatas')->selectAll("project_id = '".$this->getProjectId()."' and index_code = 'plus_fudan_southwest'","order by `index_year` desc");
	}

	public function getHuaxiRank()
	{
        $projectDatas =  sf::getModel('ProjectDatas')->selectAll("project_id = '".$this->getProjectId()."' and index_code = 'plus_huaxi'","order by `index_year` desc");
        if($projectDatas->getTotal()==0) return '-';
        $projectData = $projectDatas->getObject();
        return $projectData->getData();
	}

    function addAcceptHistorys()
    {
        sf::getModel('AcceptHistorys')->add($this->getProjectId(),input::getInput('session.roleuserid'),$this->getTypeId());
    }

    function getContent($mark)
    {
        $markInfo = explode('_',$mark);
        $folder = $markInfo[0];
        array_shift($markInfo);
        $tpl =  implode('_',$markInfo);
        $worker = $this->worker();
        if($folder=='talent') {
            $configs = $worker->getConfigs('member');
        }else $configs = $worker->getConfigs($folder);
        if(empty($tpl)) $tpl = 'common';
        View::set("widget_name",$folder);
        View::set("project",$this);
        View::set("configs",$configs);
        $output = View::getContent("assess/project/{$folder}/{$tpl}");
        $output = str_replace('<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">','<table class="table table-bordered">',$output);
        $output = str_replace('width: 85px','width: 95px',$output);
        $output = str_replace('width: 86px','width: 95px',$output);
        return $output;
    }

    public function getScoreByMark($mark)
    {
        $index = sf::getModel('ProjectIndexs')->selectByProjectId($this->getProjectId(),$mark);
        if($index->isNew()){
            $rule = sf::getModel('Rules')->selectByCode($this->getSubjectCode());
            if(!$rule->isNew()){
                $rule->setProject($this);
                return $rule->getScoreByMark($mark);
            }
        }
        return !in_array($index->getScore(),['是','否']) ? (float)$index->getScore() : $index->getScore();
    }

    public function setLabel($v)
    {
        return parent::setSubjectCode($v);
    }

    public function getLabel($v)
    {
        return parent::getSubjectCode($v);
    }

    public function getSubjectName()
    {
        return getSubjectName(parent::getSubjectCode(),0,0);
    }

    public function getData($code,$indexYear='')
    {
        $projectData = sf::getModel('ProjectDatas')->selectByProjectId($this->getProjectId(),$code,$indexYear);
        if($projectData->isNew()){
            $projectData->setDeclareYear($this->getDeclareYear());
            $projectData->setCompanyId($this->getCorporationId());
            $projectData->setSubjectCode($this->getSubjectCode());
            $projectData->setSubjectName($this->getSubject());
        }
        return $projectData;
    }

    public function getText($code,$indexYear='')
    {
        $projectData = sf::getModel('ProjectTexts')->selectByProjectId($this->getProjectId(),$code,$indexYear);
        if($projectData->isNew()){
            $projectData->setCompanyId($this->getCorporationId());
            $projectData->setSubjectCode($this->getSubjectCode());
            $projectData->setSubjectName($this->getSubject());
        }
        return $projectData;
    }

    public function setData($data,$code,$indexYear='')
    {
        $projectData = sf::getModel('ProjectDatas')->selectByProjectId($this->getProjectId(),$code,$indexYear);
        if($projectData->isNew()){
            $projectData->setCompanyId($this->getCorporationId());
            $projectData->setSubjectCode($this->getSubjectCode());
            $projectData->setSubjectName($this->getSubject());
            $projectData->setCreatedAt(date('Y-m-d H:i:s'));
        }
        $projectData->setData($data);
        $projectData->save();
        return $projectData;
    }

    public function setDatas($datas,$indexYear='')
    {
        foreach ($datas as $code=>$data){
            $this->setData($data,$code,$indexYear);
        }
    }

    public function setText($content,$code,$indexYear='')
    {
        $projectData = sf::getModel('ProjectTexts')->selectByProjectId($this->getProjectId(),$code,$indexYear);
        if($projectData->isNew()){
            $projectData->setCompanyId($this->getCorporationId());
            $projectData->setSubjectCode($this->getSubjectCode());
            $projectData->setSubjectName($this->getSubject());
            $projectData->setCreatedAt(date('Y-m-d H:i:s'));
        }
        if(is_array($content)) $content = json_encode($content,JSON_UNESCAPED_UNICODE);
        $projectData->setContent($content);
        $projectData->save();
        return $projectData;
    }

    public function setTexts($contents,$indexYear='')
    {
        foreach ($contents as $code=>$content){
            $this->setText($content,$code,$indexYear);
        }
    }

    public function getAvgDataByIndexCode($indexCode,$indexYears=[],$source='system')
    {
        if($source=='system' && $this->getConfigs('data.source')=='user') $source = 'user';
        if(!in_array(input::session('userlevel'),[1,6,10,99]) && $source=='system') return '-';
        if(empty($indexYears)) $indexYears=$this->getIndexYears();
        $data = sf::getLib('db')->result_first("SELECT ROUND(AVG(data),2) FROM `project_datas` where index_code = '{$indexCode}' and project_id = '{$this->getProjectId()}' and index_year in (".implode(',',$indexYears).") and data != '无'");
        return strlen($data)>0 ? $data : '无';
    }

    public function getSumDataByIndexCode($indexCode,$indexYears=[])
    {
        if(empty($indexYears)) $indexYears=$this->getIndexYears();
        return (float) sf::getLib('db')->result_first("SELECT ROUND(SUM(data),2) FROM `project_datas` where index_code = '{$indexCode}' and project_id = '{$this->getProjectId()}' and index_year in (".implode(',',$indexYears).")");
    }

    public function getAvgDataByIndexCodes($indexCodes=[],$indexYears=[],$source='system')
    {
        if($source=='system' && $this->getConfigs('data.source')=='user') $source = 'user';
        if(!in_array(input::session('userlevel'),[1,6,10,99]) && $source=='system') return '-';
        if(empty($indexYears)) $indexYears=$this->getIndexYears();
        $indexCodesArr = [];
        foreach ($indexCodes as $indexCode){
            $indexCodesArr[] = "'".$indexCode."'";
        }
        $indexCodesStr = implode(',',$indexCodesArr);
        $data = sf::getLib('db')->result_first("SELECT ROUND(AVG(data),2) FROM `project_datas` where index_code IN ({$indexCodesStr}) and project_id = '{$this->getProjectId()}' and index_year in (".implode(',',$indexYears).") and data!='无'");
        return strlen($data)>0 ? $data : '无';
    }

    public function getScoreByIndexCode($indexCode)
    {
        return sf::getModel('ProjectScores')->selectByProjectId($this->getProjectId(),$indexCode);
    }

    public function getScoreByIndexCodes($indexCodes)
    {
        return (float)sf::getLib('db')->result_first("SELECT ROUND(SUM(score),2) FROM `project_scores` where index_code IN ('".implode("','",$indexCodes)."') and project_id = '{$this->getProjectId()}'");
    }

    public function updateObjectiveScore()
    {
        $projectScores = sf::getModel('ProjectScores')->selectAll("project_id = '".$this->getProjectId()."' and index_type = 'objective' and pid = 0");
        $score = 0;
        $_score = $this->getObjectiveScore(true);

        while($projectScore = $projectScores->getObject()){
            $itemScore = $projectScore->getIsEdit() ? $projectScore->getRealScore() : $projectScore->getScore();
            $score+=$itemScore;
        }
        if($score!=$_score){
            $this->setObjectiveScore($score);
            $this->setScore($score+$this->getSubjectiveScore(true));
            $this->save();
        }

    }

    public function updateSystemScore()
    {
        $projectScores = sf::getModel('ProjectScores')->selectAll("project_id = '".$this->getProjectId()."' and score_type = 'system' and pid = 0");
        $score = 0;
        $_score = $this->getSystemScore(true);

        while($projectScore = $projectScores->getObject()){
            $itemScore = $projectScore->getIsEdit() ? $projectScore->getRealScore() : $projectScore->getScore();
            $score+=$itemScore;
        }
        if($score!=$_score){
            $this->setSystemScore($score);
            $this->setScore($score+$this->getExpertScore(true));
            $this->save();
        }
    }

    public function updateScore()
    {
        $projectScores = sf::getModel('ProjectScores')->selectAll("project_id = '".$this->getProjectId()."' and pid = 0");
        $score = 0;
        $systemScore = 0;
        $expertScore = 0;
        $objectiveScore = 0;
        $subjectiveScore = 0;
        while($projectScore = $projectScores->getObject()){
            $itemScore = $projectScore->getIsEdit() ? $projectScore->getRealScore() : $projectScore->getScore();
            $score+=$itemScore;
            if($projectScore->getScoreType()=='system'){
                $systemScore+=$itemScore;
            }
            if($projectScore->getScoreType()=='expert'){
                $expertScore+=$itemScore;
            }
            if($projectScore->getIndexType()=='objective'){
                $objectiveScore+=$itemScore;
            }
            if($projectScore->getIndexType()=='subjective'){
                $subjectiveScore+=$itemScore;
            }
        }
        $this->setScore($score);
        $this->setSystemScore($systemScore);
        $this->setExpertScore($expertScore);
        $this->setObjectiveScore($objectiveScore);
        $this->setSubjectiveScore($subjectiveScore);
        $this->save();
    }

    public function getAssessId()
    {
        return $this->getGuide()->getAssessId();
    }
    public function getProvinceAvgData($indexCode,$indexYears=[])
    {
        $indexYears = $indexYears ?: $this->getIndexYears();
//        return sf::getModel('ProvinceIndexs')->getAvgData($this->getSubjectCode(),$indexCode,$indexYears);
        if(strstr($indexCode,'safety_ylzl')!==false){
            return sf::getModel('ProvinceIndexs')->getAvgData($this->getSubjectCode(),$indexCode,$indexYears);
        }
        return sf::getModel('ProvinceDatas')->getAvgData($this->getAssessId(),$this->getSubjectCode(),$indexCode,$indexYears);
    }


    public function getNationAvgData($indexCode)
    {
        $indexYears=$this->getIndexYears();
        return sf::getModel('NationIndexs')->getAvgData($this->getSubjectCode(),$indexCode,$indexYears);
    }

    public function getCityAvgData($indexCode)
    {
        $indexYears=$this->getIndexYears();
        return sf::getModel('CityIndexs')->getAvgData($this->getDepartmentId(),$this->getSubjectCode(),$indexCode,$indexYears);
    }

    /**
     * 判断项目是否是市县级临床重点专科
     * @return bool
     */
    public function isCityCountryLevel()
    {
        return in_array($this->getCatId(),[225,231]);
    }

    public function getChildIndexCodes($indexCode,$module='specialty')
    {
        $indexCodeArr = explode('_',$indexCode);
        $key = array_pop($indexCodeArr);
        $configs = $this->getWorkerConfigs($module);
        $indexCodes = [];
        foreach ($configs['zdbz_'.$key] as $k=>$v){
            $indexCodes[] = $indexCode.'_'.$v;
        }
        return $indexCodes;
    }

    public function getWorkerConfigs($module='specialty')
    {
        $worker = $this->worker();
        return $worker->getConfigs($module);
    }

    /**
     * 是否需要归口部门审核
     * @param $project object
     * @return bool true:需要 false:不需要
     */
    function isNeedDepartmentCheck()
    {
        if(isDirectlyUnit($this->getDepartmentId())){
            //是省直属单位 无须归口审核
            return false;
        }
        return true;

    }
    function isRead()
    {
        return $this->getStatement()===999;
    }

    function isThreeState()
    {
        $departmentIds = [
            '76C7A246-B01D-42D1-A2E9-6404B21543EC',
            'A0D54141-5256-458F-836D-8A2E98EDD8D5',
            'EE342405-52BB-4D75-959D-E3324C3FFB1B',
        ];
        return in_array($this->getDepartmentId(),$departmentIds);
    }
    
    function getUnitId()
    {
        return $this->getCorporation(true)->getUnitId();
    }

    function getProperty()
    {
        return $this->getCorporation(true)->getProperty();
    }

    function getCompanyType()
    {
        return $this->getCorporation(true)->getType();
    }

    /**
     * 取得评审指标
     */
    function getAssessUrl($url='',$type='index')
    {
        return site_url("assess/rule".strtolower($this->getSubjectCode())."/".$type."/".trim($url,'/'));
    }

    /**
     * 专业组排名
     * @return void
     */
    function getGroupRank()
    {
        if($this->getIsAccept()==0) return '';
        if(!in_array($this->getStatement(),[20,29])) return '';
        $count = sf::getLib('db')->result_first("select count(*) c from projects where subject_code = '".$this->getSubjectCode()."' and score > '".$this->getScore(true)."' and project_id !='".$this->getProjectId()."' and declare_year = '".$this->getDeclareYear()."' and type_current_group = '".$this->getTypeCurrentGroup()."' and cat_id = '".$this->getCatId()."'");
        return $count+1;
    }

    /**
     * 专业组项目数
     * @return void
     */
    function getGroupProjectCount()
    {
        if($this->getIsAccept()==0) return '';
        return $this->getGroup()->getProjectCount();
    }

    /**
     * 片区排名
     * @return void
     */
    function getDistrictRank()
    {
        if($this->getIsAccept()==0) return '';
        if(!in_array($this->getStatement(),[20,29])) return '-';
        $count = sf::getLib('db')->result_first("select count(*) c from projects where subject_code = '".$this->getSubjectCode()."' and score > '".$this->getScore(true)."' and district = '".$this->getDistrict()."' and project_id !='".$this->getProjectId()."' and declare_year = '".$this->getDeclareYear()."' and type_current_group = '".$this->getTypeCurrentGroup()."' and cat_id = '".$this->getCatId()."' and statement in (20,29)");
        return $count+1;
    }

    /**
     * 市州排名
     * @return void
     */
    function getProvinceRank()
    {
        if($this->getIsAccept()==0) return '';
        if(!in_array($this->getStatement(),[20,29])) return '-';
        $count = sf::getLib('db')->result_first("select count(*) c from projects where score > '".$this->getScore(true)."' and department_id = '".$this->getDepartmentId()."' and project_id !='".$this->getProjectId()."' and declare_year = '".$this->getDeclareYear()."' and type_current_group = '".$this->getTypeCurrentGroup()."' and cat_id = '".$this->getCatId()."' and statement in (20,29)");
        return $count+1;
    }

    /**
     * 片区项目数
     * @return void
     */
    function getDistrictProjectCount()
    {
        if($this->getIsAccept()==0) return '';
        if(!in_array($this->getStatement(),[20,29])) return '-';
        $count = sf::getLib('db')->result_first("select count(*) c from projects where subject_code = '".$this->getSubjectCode()."' and district = '".$this->getDistrict()."' and declare_year = '".$this->getDeclareYear()."' and type_current_group = '".$this->getTypeCurrentGroup()."' and cat_id = '".$this->getCatId()."' and statement in (20,29)");
        return $count;
    }

    /**
     * 主观分数全省排名
     * @return void
     */
    function getSubjectiveRank()
    {
        if($this->getIsAccept()==0) return '';
        if(!in_array($this->getStatement(),[20,29])) return '-';
        $count = sf::getLib('db')->result_first("select count(*) c from projects where subject_code = '".$this->getSubjectCode()."' and subjective_score > '".$this->getSubjectiveScore(true)."' and project_id !='".$this->getProjectId()."' and declare_year = '".$this->getDeclareYear()."' and type_current_group = '".$this->getTypeCurrentGroup()."' and cat_id = '".$this->getCatId()."'");
        return $count+1;
    }

    /**
     * 客观分数全省排名
     * @return void
     */
    function getObjectiveRank()
    {
        if($this->getIsAccept()==0) return '';
        if(!in_array($this->getStatement(),[20,29])) return '-';
        $count = sf::getLib('db')->result_first("select count(*) c from projects where subject_code = '".$this->getSubjectCode()."' and objective_score > '".$this->getObjectiveScore(true)."' and project_id !='".$this->getProjectId()."' and declare_year = '".$this->getDeclareYear()."' and type_current_group = '".$this->getTypeCurrentGroup()."' and cat_id = '".$this->getCatId()."'");
        return $count+1;
    }

    /**
     * 是否有国重项目
     * @return string
     */
    function haveNationProject()
    {
        $count = (int)sf::getLib('db')->result_first("select count(*) c from projects where diagnosis_code = '".$this->getDiagnosisCode()."' and corporation_id = '".$this->getCorporationId()."' and statement in (29,30) and level = '国家级'");
        return $count>0 ? '是' : '否';
    }

    /**
     * 是否有省重项目
     * @return string
     */
    function haveProvinceProject()
    {
        $count = (int)sf::getLib('db')->result_first("select count(*) c from projects where diagnosis_code = '".$this->getDiagnosisCode()."' and corporation_id = '".$this->getCorporationId()."' and statement in (29,30) and level = '省级'");
        return $count>0 ? '是' : '否';
    }

    /**
     * 专科声誉西南榜排名
     * @return string
     */
    public function getRankSouthwest()
    {
        $datas = [];
        $ranks = sf::getModel('IndexRanks')->selectAll("company_id = '".$this->getCorporationId()."' and index_code = 'plus_fudan_southwest' and diagnosis_code = '".$this->getDiagnosisCode()."'","order by index_year desc");
        while($rank = $ranks->getObject()){
            $datas[] = $rank->getDataNumber().'('.$rank->getIndexYear().')';
        }
        return empty($datas) ? '未上榜' : implode('、',$datas);
    }

    /**
     * 专科声誉全国榜排名
     * @return string
     */
    public function getRankNation()
    {
        $datas = [];
        $ranks = sf::getModel('IndexRanks')->selectAll("company_id = '".$this->getCorporationId()."' and index_code = 'plus_fudan_nation' and diagnosis_code = '".$this->getDiagnosisCode()."'","order by index_year desc");
        while($rank = $ranks->getObject()){
            $datas[] = $rank->getDataNumber().'('.$rank->getIndexYear().')';
        }
        return empty($datas) ? '未上榜' : implode('、',$datas);
    }

    /**
     * 医疗服务能力和质量排名
     * @return string
     */
    public function getRankReport()
    {
        $datas = [];
        $ranks = sf::getModel('IndexReports')->selectAll("company_id = '".$this->getCorporationId()."' and diagnosis_code = '".$this->getDiagnosisCode()."'","order by index_year desc");
        while($rank = $ranks->getObject()){
            $datas[] = $rank->getData().'('.$rank->getIndexYear().')';
        }
        return empty($datas) ? '未上榜' : implode('、',$datas);
    }

    public function setCatId($v)
    {
        if($v==174 && input::session('userlevel')==4){
            $uri = $_SERVER['REQUEST_URI'];
            \Sofast\Core\Log::write(PHP_EOL."【message】gather insert 174".PHP_EOL."【Uri】:{$uri}".PHP_EOL);
            echo file_get_contents(WEBROOT.'/500.html');exit();
        }
        return parent::setCatId($v);
    }

    public function getMonth($declareYear,$declareMonth)
    {
        $month = sf::getModel('Months')->selectByMonthAndCompanyId($declareYear,$declareMonth,$this->getCorporationId());
        return $month;
    }

    public function getWeek($declareYear,$declareWeek)
    {
        $week = sf::getModel('Weeks')->selectByWeekAndCompanyId($declareYear,$declareWeek,$this->getCorporationId());
        return $week;
    }

    public function stage()
    {
        return sf::getModel('Stages')->selectByProjectId($this->getProjectId());
    }

    public function complete()
    {
        return sf::getModel('Completes')->selectByProjectId($this->getProjectId());
    }

    public function getShortDepartmentName()
    {
        return str_replace('卫生健康委员会','',$this->getDepartmentName());
    }

    public function getStageState()
    {
        $stage = sf::getModel('Stages')->selectByProjectId($this->getProjectId());
        return $stage->isNew() ? '未生成' : $stage->getState();
    }

    public function getCompleteState()
    {
        $stage = sf::getModel('Completes')->selectByProjectId($this->getProjectId());
        return $stage->isNew() ? '未生成' : $stage->getState();
    }

    public function getInspectState()
    {
        $state = [];
        $guide = sf::getModel('Guides',174);
        $inspect = sf::getModel('Inspects')->selectByProjectId($this->getProjectId(),$guide->getId());
        if(!$inspect->isNew()){
            $state[] = '绩效评价表'.$inspect->getState();
        }
        $inspect = sf::getModel('Inspects')->selectByCompanyId($this->getCorporationId(),$this->getCatId(),$guide->getId(),$this->getDeclareYear());
        if(!$inspect->isNew()){
            $state[] = '绩效评价报告'.$inspect->getState();
        }
        return $state ? implode('<br>',$state) : '未生成';
    }

    public function getReviewState()
    {
        $review = sf::getModel('Reviews')->selectByProjectId($this->getProjectId());
        return $review->isNew() ? '未生成' : $review->getState();
    }

    public function getSzxWzbzCount($type='special')
    {
        $db = sf::getLib('db');
        $count = (int)$db->result_first("select count(*) c from szx_diseases where type= '{$type}' and subject_code = '".$this->getSubjectCode()."' and is_leaf = 1");
        return $count;
    }


    public function getSzxHxjsCount()
    {
        $db = sf::getLib('db');
        $count = (int)$db->result_first("select count(*) c from szx_technologys where subject_code = '".$this->getSubjectCode()."' and is_leaf = 1");
        return $count;
    }

    /**
     * 生成绩效评价表
     * @return false|void
     */
    public function createUserinspects()
    {
//        if($this->getCatId()==230) {
//            //国家级不需要填绩效评价表
//            return false;
//        }
        $inspect = sf::getModel('Inspects')->selectByProjectId($this->getProjectId());
        if($inspect->isNew()){
            $inspect->setWorkerId(1027);
            $inspect->setSubjectCode($this->getSubjectCode());
            $inspect->setCatId($this->getCatId());
            $inspect->setSubject($this->getSubject());
            $inspect->setDeclareYear($this->getDeclareYear());
            $inspect->setDistrict($this->getDistrict());
            $inspect->setTypeCurrentGroup($this->getTypeCurrentGroup());
            $inspect->setUserId($this->getUserId());
            $inspect->setUserName($this->getUserName());
            $inspect->setCompanyId($this->getCorporationId());
            $inspect->setCompanyName($this->getCorporationName());
            $inspect->setDepartmentId($this->getDepartmentId());
            $inspect->setDepartmentName($this->getDepartmentName());
            $inspect->setCreatedAt(date('Y-m-d H:i:s'));
            $inspect->save();
            return true;
        }
        return false;
    }

    /**
     * 生成年度考核
     * @return false|void
     */
    public function createReviews()
    {
        $guides = sf::getModel('Guides')->selectAll("`type` = 'review' and `cat_id` = '{$this->getCatId()}'");
        if($guides->getTotal()==0){
            exit("还未设置".date('Y')."年度的年度重点工作清单指南");
        }
        $guide = $guides->getObject();
        $workerId = $guide->types()->getWorkerId();
        if(!$workerId){
            exit("还未设置工作引擎");
        }
        $review = sf::getModel('Reviews')->selectByProjectId($this->getProjectId(),$guide->getId());
        if($review->isNew()){
            $review->setSubjectCode($this->getSubjectCode());
            $review->setGuideId($guide->getId());
            $review->setCatId($this->getCatId());
            $review->setSubject($this->getSubject());
//            $review->setWorkerId(945);
//            if($this->getCatId()==244) $review->setWorkerId(948);
            $review->setWorkerId($workerId);
            $review->setDeclareYear($this->getDeclareYear());
            $review->setReviewYear(date('Y'));
            $review->setDistrict($this->getDistrict());
            $review->setTypeCurrentGroup($this->getTypeCurrentGroup());
            $review->setUserId($this->getUserId());
            $review->setUserName($this->getUserName());
            $review->setCompanyId($this->getCorporationId());
            $review->setCompanyName($this->getCorporationName());
            $review->setDepartmentId($this->getDepartmentId());
            $review->setDepartmentName($this->getDepartmentName());
            $review->setCreatedAt(date('Y-m-d H:i:s'));
            $review->save();
            $review->setConfigs('path.apply',"engine/reviewer");
            return true;
        }
        return false;
    }

    /**
     * 生成绩效评价报告
     * @return void
     */
    function createUnitReport()
    {
        $unitInspect = sf::getModel("Inspects")->selectByInspectId('');
        $unitInspect->setDeclareYear($this->getDeclareYear());
        $unitInspect->setUserRole(3);
        $unitInspect->setWorkerId(1028);
//        if($this->getCatId()==230){
//            $unitInspect->setWorkerId(924);
//        }
        $catId = in_array($this->getCatId(),[174,233]) ? 174 : $this->getCatId();
        $subject = in_array($this->getCatId(),[174,233]) ? '四川省临床重点专科绩效评价报告' : $this->getCatSubject().'绩效评价报告';
        $unitInspect->setCatId($catId);
        $company = sf::getModel('Corporations')->selectByUserId($this->getCorporationId());
        $unitInspect->setUserId($company->getUserId());
        $unitInspect->setCompanyId($company->getUserId());
        if($unitInspect->isRepeat()) return false;
        $unitInspect->setCompanyName($company->getSubject());
        $unitInspect->setDepartmentId($company->getDepartmentId());
        $unitInspect->setDepartmentName($company->getDepartmentName());
        $unitInspect->setSubject($subject);
        $unitInspect->setCreatedAt(date('Y-m-d H:i:s'));
        $unitInspect->save();
        return true;
    }

    public function createApplyArchives()
    {
        $projectId = $this->getProjectId();
        $project = sf::getModel("Projects")->selectByProjectId($projectId);
        $project->setWidgetConfigs('action',array('show'=>true,'worker'=>'worker'));
        $worker = $project->worker('apply',true);
        $saveDir = WEBROOT.'/Documents/'.$project->getDeclareYear().'/apply/'.$project->getProjectId();
        if(!is_dir($saveDir)) mkdir($saveDir,0777,true);
        $userlevel = input::session('userlevel');
        //生成管理员看的版本
        $_SESSION['userlevel'] = 6;
        $html = $this->getArchiveHtml($project,$worker);
        file_put_contents($saveDir.'/manager.html',$html);
        //生成单位看的版本
        $_SESSION['userlevel'] = 3;
        $project->cleanObject();
        $project = sf::getModel("Projects")->selectByProjectId($projectId);
        $html = $this->getArchiveHtml($project,$worker);
        file_put_contents($saveDir.'/company.html',$html);
        //生成导出pdf的版本
        $project->cleanObject();
        $project = sf::getModel("Projects")->selectByProjectId($projectId);
        $project->setWidgetConfigs('action',array('download'=>'yes','worker'=>'worker'));
        $html = $this->getArchiveHtml($project,$worker,'output');
        file_put_contents($saveDir.'/output.html',$html);
        $_SESSION['userlevel'] = $userlevel;
    }

    public function createTaskArchives()
    {
        $projectId = $this->getProjectId();
        $project = sf::getModel("Projects")->selectByProjectId($projectId);
        $project->setWidgetConfigs('action',array('show'=>true,'worker'=>'tasker'));
        $worker = $project->worker('task',true);
        $saveDir = WEBROOT.'/Documents/'.$project->getDeclareYear().'/task/'.$project->getProjectId();
        if(!is_dir($saveDir)) mkdir($saveDir,0777,true);
        $userlevel = input::session('userlevel');
        //生成管理员看的版本
//        $_SESSION['userlevel'] = 6;
//        $html = $this->getArchiveHtml($project,$worker);
//        file_put_contents($saveDir.'/manager.html',$html);
        //生成单位看的版本
        $_SESSION['userlevel'] = 3;
        $project->cleanObject();
        $project = sf::getModel("Projects")->selectByProjectId($projectId);
        $html = $this->getArchiveHtml($project,$worker);
        file_put_contents($saveDir.'/company.html',$html);
        //生成导出pdf的版本
        $project->cleanObject();
        $project = sf::getModel("Projects")->selectByProjectId($projectId);
        $project->setWidgetConfigs('action',array('download'=>'yes','worker'=>'tasker'));
        $html = $this->getTaskArchiveHtml($project,$worker,'output');
        file_put_contents($saveDir.'/output.html',$html);
        $_SESSION['userlevel'] = $userlevel;
    }

    public function getArchiveHtml($project,$worker,$type='show')
    {
        $view = new Template(realpath(APPPATH).'/controller/Engine/View/');
        if($type=='output') $view->set("download",'yes');
        $view->set("project",$project);
        $view->set("configs",$worker->getConfigs());
        $htmlStr = $view->getContent("Worker/".$worker->template());
        if($type=='show'){
            $pattern = '/\<!-- Page Content -->(.*?)\<!-- END Page Content -->/s';
            preg_match_all($pattern,$htmlStr,$matches);
            return $matches[1][0];
        }
        return $htmlStr;
    }

    public function getTaskArchiveHtml($project,$worker,$type='show')
    {
        $view = new Template(realpath(APPPATH).'/controller/Engine/View/');
        if($type=='output') $view->set("download",'yes');
        $view->set("project",$project);
        $view->set("configs",$worker->getConfigs());
        $htmlStr = $view->getContent("Worker/".$worker->template());
        if($type=='show'){
            $pattern = '/\<!-- Page Content -->(.*?)\<!-- END Page Content -->/s';
            preg_match_all($pattern,$htmlStr,$matches);
            return $matches[1][0];
        }
        return $htmlStr;
    }

    /**
     * 生成项目立项编号
     * @return mixed|string
     */
    function generateRadicateId()
    {
        if($this->getRadicateId()) return $this->getRadicateId();
        $suffix = $this->getGuide(true)->getMark();
        $str = $this->getDeclareYear().$suffix;
        $num = sf::getModel("Projects")->selectAll("radicate_id like '".$str."%' and cat_id = '".$this->getCatId()."' and id != '".$this->getId()."'")->getTotal()+1;
        $radicateId = $str.str_pad($num,3,'0',STR_PAD_LEFT);
        return strtoupper($radicateId);
    }

    /**
     * 是否符合前置条件
     * @return string
     */
    public function getIsConformPremise()
    {
        if($this->getCatId()!=174 || $this->getDeclareYear()<2024) return '-';
        $_company = sf::getModel('Companys')->selectByCode($this->getCorporation(true)->getCode());
        if($_company->isNew() || $_company->getStatement()>0){
            return '否';
        }
        return '是';
    }

    public function getDepartmentCityRadicateNum($radicateYear='')
    {
        if(!in_array(input::session('userlevel'),[1,6,10,99])) return '-';
        $department = $this->getDepartment(true);
        if(in_array($department->getSubject(),['绵阳市卫生健康委员会','科学城卫生健康委员会'])){
            $department1 = sf::getModel('Departments')->selectBySubject('绵阳市卫生健康委员会');
            $department2 = sf::getModel('Departments')->selectBySubject('科学城卫生健康委员会');
            $count1 = $department1->getCityRadicateNum($radicateYear);
            $count2 = $department2->getCityRadicateNum($radicateYear);
            return $count1+$count2;
        }
        return $department->getCityRadicateNum($radicateYear);
    }

    public function getDepartmentCountryRadicateNum($radicateYear='')
    {
        if(!in_array(input::session('userlevel'),[1,6,10,99])) return '-';
        $department = $this->getDepartment();
        if(in_array($department->getSubject(),['绵阳市卫生健康委员会','科学城卫生健康委员会'])){
            $department1 = sf::getModel('Departments')->selectBySubject('绵阳市卫生健康委员会');
            $department2 = sf::getModel('Departments')->selectBySubject('科学城卫生健康委员会');
            $count1 = $department1->getCountryRadicateNum($radicateYear);
            $count2 = $department2->getCountryRadicateNum($radicateYear);
            return $count1+$count2;
        }
        return $department->getCountryRadicateNum($radicateYear);
    }

    /**
     * 是否属于市级临床重点专科
     * @return string
     */
    public function getIsCityZdzk()
    {
        if(!in_array(input::session('userlevel'),[1,6,10,99])) return '-';
        $subject = $this->getSubject();
        $subjectCode = $this->getSubjectCode();
        $db = sf::getLib('db');
        $total = $db->result_first("select count(*) c from projects where (subject_code = '{$subjectCode}' or subject = '{$subject}') and corporation_id = '".$this->getCorporationId()."' and cat_id = 225 and statement in (29,30)");
        if($total>0) return '是';
        return '否';
    }

    public function getTaskTargetModel()
    {
        return $this->getRadicateYear()<2024 ? sf::getModel('Targets') : sf::getModel('Targets'.$this->getRadicateYear());
    }

    public function getTaskTargetModelName()
    {
        return $this->getRadicateYear()<2024 ? 'Targets' : 'Targets'.$this->getRadicateYear();
    }

    public function cleanObject()
    {
        parent::cleanObject();
        $this->awards = NULL;
        $this->patents = NULL;
        $this->devices = NULL;
        $this->diseases = NULL;
        $this->researchProjects = NULL;
        $this->papers = NULL;
        $this->supports = NULL;
        $this->populars = NULL;
        $this->remotes = NULL;
        $this->standards = NULL;
        $this->missions = NULL;
        $this->eduprojects = NULL;
        $this->syjsprojects = NULL;
        $this->members = NULL;
        $this->memberStructures = NULL;
        $this->memberSeniors = NULL;
        $this->memberChs = NULL;
        $this->memberHxs = NULL;
        $this->works = NULL;
        $this->part = NULL;
        $this->target = NULL;
        $this->szbzs = NULL;
        $this->guidelines = NULL;
        $this->centers = NULL;
        $this->academics = NULL;
        $this->magazines = NULL;
        $this->worker = NULL;
        $this->researcher = NULL;
        $this->money = NULL;
        $this->fund = NULL;
        $this->widgets = NULL;
    }

    public function getIndexYears()
    {
        if($this->index_years) return $this->index_years;
        return $this->getIndexYearsDefault();
    }

    public function setIndexYears($years=[])
    {
        $this->index_years = $years;
        return $this;
    }

    public function getIndexYearsDefault()
    {
        $startYear = $this->getStartYear();
        $endYear = $this->getEndYear();
        $years = [];
        for($year = $startYear;$year<=$endYear;$year++){
            $years[] = (int)$year;
        }
        return $years;
    }

    /**
     * 重点实验室是否属于修正状态
     * @return bool
     */
    function isModify()
    {
        return $this->getCatId()==271 && $this->getStatement()>=20 ? true : false;
    }

}

