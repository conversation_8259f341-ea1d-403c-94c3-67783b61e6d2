<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseSummarys extends BaseModel
{
	private $id;
	private $cat_id;
	private $guide_id  = '0';
	private $summary_id;
	private $platform_id;
	private $worker_id  = '0';
	private $type_current_group  = '1';
	private $declare_year;
	private $start_at  = '2020';
	private $end_at  = '2022';
	private $subject;
	private $company_id;
	private $company_name;
	private $department_id;
	private $department_name;
	private $principal_name;
	private $linkman;
	private $linkman_mobile;
	private $score;
	private $statement  = '-1';
	private $configs;
	private $tags;
	private $subject_id;
	private $subject_name;
	private $subject_ids;
	private $subject_names;
	private $declare_at;
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "summarys";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getCatId()
	{
		return $this->cat_id;
	}

	public function getGuideId()
	{
		return $this->guide_id;
	}

	public function getSummaryId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->summary_id,0,$len,"utf-8");
			else return substr($this->summary_id,0,$len);
		}
		return $this->summary_id;
	}

	public function getPlatformId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->platform_id,0,$len,"utf-8");
			else return substr($this->platform_id,0,$len);
		}
		return $this->platform_id;
	}

	public function getWorkerId()
	{
		return $this->worker_id;
	}

	public function getTypeCurrentGroup()
	{
		return $this->type_current_group;
	}

	public function getDeclareYear($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->declare_year,0,$len,"utf-8");
			else return substr($this->declare_year,0,$len);
		}
		return $this->declare_year;
	}

	public function getStartAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->start_at,0,$len,"utf-8");
			else return substr($this->start_at,0,$len);
		}
		return $this->start_at;
	}

	public function getEndAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->end_at,0,$len,"utf-8");
			else return substr($this->end_at,0,$len);
		}
		return $this->end_at;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getCompanyId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->company_id,0,$len,"utf-8");
			else return substr($this->company_id,0,$len);
		}
		return $this->company_id;
	}

	public function getCompanyName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->company_name,0,$len,"utf-8");
			else return substr($this->company_name,0,$len);
		}
		return $this->company_name;
	}

	public function getDepartmentId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department_id,0,$len,"utf-8");
			else return substr($this->department_id,0,$len);
		}
		return $this->department_id;
	}

	public function getDepartmentName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department_name,0,$len,"utf-8");
			else return substr($this->department_name,0,$len);
		}
		return $this->department_name;
	}

	public function getPrincipalName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->principal_name,0,$len,"utf-8");
			else return substr($this->principal_name,0,$len);
		}
		return $this->principal_name;
	}

	public function getLinkman($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman,0,$len,"utf-8");
			else return substr($this->linkman,0,$len);
		}
		return $this->linkman;
	}

	public function getLinkmanMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman_mobile,0,$len,"utf-8");
			else return substr($this->linkman_mobile,0,$len);
		}
		return $this->linkman_mobile;
	}

	public function getScore($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->score,0,$len,"utf-8");
			else return substr($this->score,0,$len);
		}
		return $this->score;
	}

	public function getStatement()
	{
		return $this->statement;
	}

	public function getConfigs($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->configs,0,$len,"utf-8");
			else return substr($this->configs,0,$len);
		}
		return $this->configs;
	}

	public function getTags($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->tags,0,$len,"utf-8");
			else return substr($this->tags,0,$len);
		}
		return $this->tags;
	}

	public function getSubjectId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_id,0,$len,"utf-8");
			else return substr($this->subject_id,0,$len);
		}
		return $this->subject_id;
	}

	public function getSubjectName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_name,0,$len,"utf-8");
			else return substr($this->subject_name,0,$len);
		}
		return $this->subject_name;
	}

	public function getSubjectIds($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_ids,0,$len,"utf-8");
			else return substr($this->subject_ids,0,$len);
		}
		return $this->subject_ids;
	}

	public function getSubjectNames($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_names,0,$len,"utf-8");
			else return substr($this->subject_names,0,$len);
		}
		return $this->subject_names;
	}

	public function getDeclareAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->declare_at,0,$len,"utf-8");
			else return substr($this->declare_at,0,$len);
		}
		return $this->declare_at;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setCatId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->cat_id !== $v)
		{
			$this->cat_id = $v;
			$this->fieldData["cat_id"] = $v;
		}
		return $this;

	}

	public function setGuideId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->guide_id !== $v)
		{
			$this->guide_id = $v;
			$this->fieldData["guide_id"] = $v;
		}
		return $this;

	}

	public function setSummaryId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->summary_id !== $v)
		{
			$this->summary_id = $v;
			$this->fieldData["summary_id"] = $v;
		}
		return $this;

	}

	public function setPlatformId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->platform_id !== $v)
		{
			$this->platform_id = $v;
			$this->fieldData["platform_id"] = $v;
		}
		return $this;

	}

	public function setWorkerId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->worker_id !== $v)
		{
			$this->worker_id = $v;
			$this->fieldData["worker_id"] = $v;
		}
		return $this;

	}

	public function setTypeCurrentGroup($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->type_current_group !== $v)
		{
			$this->type_current_group = $v;
			$this->fieldData["type_current_group"] = $v;
		}
		return $this;

	}

	public function setDeclareYear($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->declare_year !== $v)
		{
			$this->declare_year = $v;
			$this->fieldData["declare_year"] = $v;
		}
		return $this;

	}

	public function setStartAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->start_at !== $v)
		{
			$this->start_at = $v;
			$this->fieldData["start_at"] = $v;
		}
		return $this;

	}

	public function setEndAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->end_at !== $v)
		{
			$this->end_at = $v;
			$this->fieldData["end_at"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setCompanyId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->company_id !== $v)
		{
			$this->company_id = $v;
			$this->fieldData["company_id"] = $v;
		}
		return $this;

	}

	public function setCompanyName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->company_name !== $v)
		{
			$this->company_name = $v;
			$this->fieldData["company_name"] = $v;
		}
		return $this;

	}

	public function setDepartmentId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_id !== $v)
		{
			$this->department_id = $v;
			$this->fieldData["department_id"] = $v;
		}
		return $this;

	}

	public function setDepartmentName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_name !== $v)
		{
			$this->department_name = $v;
			$this->fieldData["department_name"] = $v;
		}
		return $this;

	}

	public function setPrincipalName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->principal_name !== $v)
		{
			$this->principal_name = $v;
			$this->fieldData["principal_name"] = $v;
		}
		return $this;

	}

	public function setLinkman($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman !== $v)
		{
			$this->linkman = $v;
			$this->fieldData["linkman"] = $v;
		}
		return $this;

	}

	public function setLinkmanMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman_mobile !== $v)
		{
			$this->linkman_mobile = $v;
			$this->fieldData["linkman_mobile"] = $v;
		}
		return $this;

	}

	public function setScore($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->score !== $v)
		{
			$this->score = $v;
			$this->fieldData["score"] = $v;
		}
		return $this;

	}

	public function setStatement($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->statement !== $v)
		{
			$this->statement = $v;
			$this->fieldData["statement"] = $v;
		}
		return $this;

	}

	public function setConfigs($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->configs !== $v)
		{
			$this->configs = $v;
			$this->fieldData["configs"] = $v;
		}
		return $this;

	}

	public function setTags($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->tags !== $v)
		{
			$this->tags = $v;
			$this->fieldData["tags"] = $v;
		}
		return $this;

	}

	public function setSubjectId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_id !== $v)
		{
			$this->subject_id = $v;
			$this->fieldData["subject_id"] = $v;
		}
		return $this;

	}

	public function setSubjectName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_name !== $v)
		{
			$this->subject_name = $v;
			$this->fieldData["subject_name"] = $v;
		}
		return $this;

	}

	public function setSubjectIds($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_ids !== $v)
		{
			$this->subject_ids = $v;
			$this->fieldData["subject_ids"] = $v;
		}
		return $this;

	}

	public function setSubjectNames($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_names !== $v)
		{
			$this->subject_names = $v;
			$this->fieldData["subject_names"] = $v;
		}
		return $this;

	}

	public function setDeclareAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->declare_at !== $v)
		{
			$this->declare_at = $v;
			$this->fieldData["declare_at"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `summarys` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"cat_id" => $this->getCatId(),
			"guide_id" => $this->getGuideId(),
			"summary_id" => $this->getSummaryId(),
			"platform_id" => $this->getPlatformId(),
			"worker_id" => $this->getWorkerId(),
			"type_current_group" => $this->getTypeCurrentGroup(),
			"declare_year" => $this->getDeclareYear(),
			"start_at" => $this->getStartAt(),
			"end_at" => $this->getEndAt(),
			"subject" => $this->getSubject(),
			"company_id" => $this->getCompanyId(),
			"company_name" => $this->getCompanyName(),
			"department_id" => $this->getDepartmentId(),
			"department_name" => $this->getDepartmentName(),
			"principal_name" => $this->getPrincipalName(),
			"linkman" => $this->getLinkman(),
			"linkman_mobile" => $this->getLinkmanMobile(),
			"score" => $this->getScore(),
			"statement" => $this->getStatement(),
			"configs" => $this->getConfigs(),
			"tags" => $this->getTags(),
			"subject_id" => $this->getSubjectId(),
			"subject_name" => $this->getSubjectName(),
			"subject_ids" => $this->getSubjectIds(),
			"subject_names" => $this->getSubjectNames(),
			"declare_at" => $this->getDeclareAt(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->cat_id = '';
		$this->guide_id = '';
		$this->summary_id = '';
		$this->platform_id = '';
		$this->worker_id = '';
		$this->type_current_group = '';
		$this->declare_year = '';
		$this->start_at = '';
		$this->end_at = '';
		$this->subject = '';
		$this->company_id = '';
		$this->company_name = '';
		$this->department_id = '';
		$this->department_name = '';
		$this->principal_name = '';
		$this->linkman = '';
		$this->linkman_mobile = '';
		$this->score = '';
		$this->statement = '';
		$this->configs = '';
		$this->tags = '';
		$this->subject_id = '';
		$this->subject_name = '';
		$this->subject_ids = '';
		$this->subject_names = '';
		$this->declare_at = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["cat_id"]) && $this->cat_id = $data["cat_id"];
		isset($data["guide_id"]) && $this->guide_id = $data["guide_id"];
		isset($data["summary_id"]) && $this->summary_id = $data["summary_id"];
		isset($data["platform_id"]) && $this->platform_id = $data["platform_id"];
		isset($data["worker_id"]) && $this->worker_id = $data["worker_id"];
		isset($data["type_current_group"]) && $this->type_current_group = $data["type_current_group"];
		isset($data["declare_year"]) && $this->declare_year = $data["declare_year"];
		isset($data["start_at"]) && $this->start_at = $data["start_at"];
		isset($data["end_at"]) && $this->end_at = $data["end_at"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["company_id"]) && $this->company_id = $data["company_id"];
		isset($data["company_name"]) && $this->company_name = $data["company_name"];
		isset($data["department_id"]) && $this->department_id = $data["department_id"];
		isset($data["department_name"]) && $this->department_name = $data["department_name"];
		isset($data["principal_name"]) && $this->principal_name = $data["principal_name"];
		isset($data["linkman"]) && $this->linkman = $data["linkman"];
		isset($data["linkman_mobile"]) && $this->linkman_mobile = $data["linkman_mobile"];
		isset($data["score"]) && $this->score = $data["score"];
		isset($data["statement"]) && $this->statement = $data["statement"];
		isset($data["configs"]) && $this->configs = $data["configs"];
		isset($data["tags"]) && $this->tags = $data["tags"];
		isset($data["subject_id"]) && $this->subject_id = $data["subject_id"];
		isset($data["subject_name"]) && $this->subject_name = $data["subject_name"];
		isset($data["subject_ids"]) && $this->subject_ids = $data["subject_ids"];
		isset($data["subject_names"]) && $this->subject_names = $data["subject_names"];
		isset($data["declare_at"]) && $this->declare_at = $data["declare_at"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}