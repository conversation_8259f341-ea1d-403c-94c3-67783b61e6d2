<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use Sofast\Core\lang;
use App\Model\BaseUsers;

class Users extends BaseUsers
{
    function encode_password($password='')
    {
        return password_hash($password,PASSWORD_DEFAULT);
    }

	function setUserPassword($v)
	{
		parent::setUserPassword($this->encode_password($v));
	}

	function setUserPasswordNoEncrypt($v)
	{
		parent::setUserPassword($v);
	}

    function setPassword($v)
    {
        parent::setUserPassword($v);
    }

	function selectByName($userName='')
	{
		$db = sf::getLib("db");
		$result = $db->fetch_first("SELECT * FROM ".$this->table." WHERE `user_name` = '".$userName."'");
		if($result){
			$this->fillObject($result);
			return $this;
		}else return false;
	}
	
	function selectByHash($hash='')
	{
		$db = sf::getLib("db");
		$result = $db->fetch_first("SELECT * FROM ".$this->table." WHERE `hash` = '".$hash."'");
		if($result){
			$this->fillObject($result);
			return $this;
		}else return false;
	}
	
	function selectByUserId($user_id='')
	{
		if($user_id == '') $user_id = sf::getLib("MyString")->getRandString();
		$db = sf::getLib("db");
		$addWhere ="`user_id` = '".$user_id."' ";
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE ".$addWhere."");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else{
			$this->setUserId($user_id);
			if($_SESSION['user_info']) $this->fillObject($_SESSION['user_info']);
		}
		return $this;
	}

    public function selectByOpenId($openId)
    {
        $db = sf::getLib("db");
        $addWhere ="`open_id` = '".$openId."' ";
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE ".$addWhere."");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setOpenId($openId);
        return $this;
    }

	function selectByTrueName($name)
	{
		$db = sf::getLib("db");
		$addWhere ="`user_username` = '".$name."' ";
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE ".$addWhere."");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else{
			$this->setUserUsername($name);
		}
		return $this;
	}


	function selectByIdcard($idcard='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_idcard` = '".$idcard."' ");

		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else {
            $this->cleanObject();
            $this->setUserIdcard($idcard);
            $this->setUserId(sf::getLib("MyString")->getRandString());
        }
		return $this;
	}

    function selectByUserName($userName='')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_name` = '".$userName."' ");

        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->cleanObject();
            $this->setUserName($userName);
            $this->setUserId(sf::getLib("MyString")->getRandString());
        }
        return $this;
    }
	
	function setLoginNum($v=1)
	{
		parent::setLoginNum($this->getLoginNum() + 1);
	}
	
	function hasByUserName($userName='')
	{
		if($userName == '') return true;
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_name` = '".$userName."' AND `user_name` <> '".$this->getUserName()."' ");
		if($db->num_rows($query)) return true;
		else return false;
	}
	
	function check($password='',$oldpass='')
	{
        $oldpass == '' && $oldpass = $this->getUserPassword();
        if(substr($oldpass,0,4) == '$2y$'){//采用password_hash加密
            return password_verify($password,$oldpass);
        }else{//老方式加密验证
            $pass_array = explode(":",$oldpass);
            if(count($pass_array) > 1){
                $oldpass = $pass_array[0];
                $tempstr = $pass_array[1];
                if($oldpass == md5($password.$tempstr)) return true;
                else return false;
            }else{//明文验证
                if($oldpass == $password) return true;
                else return false;
            }
        }
	}

    /**
     * 创建用户账号
     * @param  [type] $user_name   姓名
     * @param  [type] $user_idcard 身份证号码
     * @param  [type] $user_mobile 手机号码
     * @param  [type] $user_phone  座机号码
     * @param  string $user_email  电子邮箱
     * @return [type]              [description]
     */
//    function create($user_name,$password,$user_idcard,$user_mobile,$user_email='',$user_phone='',$idcard_type='身份证')
//    {
//        $user = $this->selectByIdcard(strtoupper(trim($user_idcard)));
//        if($user->isNew()){
//            $user->setUserName($user_mobile);
//            $user->setUserPasswordNoEncrypt($password);
//            $user->setUserIdcardType($idcard_type);
//        }
//        $user->setUserUsername(trim($user_name));
//        $user->setUserMobile($user_mobile);
//        if($user_email) $user->setUserEmail($user_email);
//        if($user_phone) $user->setUserPhone($user_phone);
//        $user->setCreatedAt(date('Y-m-d H:i:s'));
//        $user->save();
//        //将其他使用该手机号码的账号冻结
//        return $user;
//    }

    /**
     * 创建用户账号
     * @param  [type] $full_name   姓名
     * @param  [type] $user_idcard 身份证号码
     * @param  [type] $user_mobile 手机号码
     * @param  [type] $user_phone  座机号码
     * @param  string $user_email  电子邮箱
     * @return [type]              [description]
     */
    public function create($full_name, $user_idcard, $user_mobile, $user_email = '', $user_phone = '', $idcard_type = '身份证',$open_id='')
    {
        if(empty($open_id)) $open_id = getOpenId(trim($user_idcard));
        $user = $this->selectByIdcard(strtoupper(trim($user_idcard)));
        if ($user->isNew()) {
            $user->setUserName($user_mobile);
            $user->setUserIdcardType($idcard_type);
        }
        $user->setUserUsername(trim($full_name));
        $user->setUserMobile($user_mobile);
        if ($user_email) {
            $user->setUserEmail($user_email);
        }

        if ($user_phone) {
            $user->setUserPhone($user_phone);
        }
        $user->setOpenId($open_id);
        $user->setUserId($open_id);
        $user->setUserGroupId(2);

        $user->save();
        //将其他使用该手机号码的账号冻结
        return $user;
    }
	
	function getUserGroupName()
	{
		return sf::getModel("UserGroups",parent::getUserGroupId())->getUserGroupName();
	}
	
	function getState()
	{
		if(parent::getIsLock() == 4) return lang::get("Is lock!");
		else return lang::get("Is normal!");
	}
	
	function getOfficeId()
	{
		if(in_array(input::getInput("session.userlevel"),[1,5,6])){
            $manager = sf::getModel("Managers")->selectByUserId($this->getRoleUserId(input::getInput("session.userlevel")));
            if($manager->isNew()) return '-1';
            return $manager->getOfficeId();
		}else return '-1';
	}

	function getAuths($key='')
	{
        if(in_array(input::getInput("session.userlevel"),[1,6])){
            $manager = sf::getModel("Managers")->selectByUserId($this->getRoleUserId(input::getInput("session.userlevel")));
            if($manager->isNew()) return '-1';
            return $manager->getAuths($key);
        }else return '-1';
	}
	
	function selectByFind($userName='',$idcard='')
	{
		$db = sf::getLib("db");
		$result = $db->fetch_first("SELECT * FROM ".$this->table." WHERE user_username = '".$userName."' AND user_idcard = '".$idcard."'");
		if($result){
			$this->fillObject($result);
			return $this;
		}else return false;
	}

	function hasByCardId($card_id='')
	{
		if($card_id == '') return true;
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_idcard` = '".$card_id."' AND `user_idcard` <> '".$this->getUserIdcard()."' ");
		if($db->num_rows($query)) return true;
		else return false;
	}
	
	function hasByEmail($email='')
	{
		if($email == '') return true;
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_email` = '".$email."' AND `user_email` <> '".$this->getUserEmail()."' ");
		if($db->num_rows($query)) return true;
		else return false;
	}
	
	function hasByMobile($mobile='')
	{
		if($mobile == '') return true;
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_mobile` = '".$mobile."' AND `user_mobile` <> '".$this->getUserMobile()."'");
		if($db->num_rows($query)) return true;
		else return false;
	}
	
	public function sendMessage($message='',$send_at='')
	{
		if(!isMobile($this->getUserMobile())) return false;
		return sf::getModel("ShortMessages")->sendSms($this->getUserMobile(),$message,$this->getUserId(),'users',$send_at);
	}
	
	public function setRole($group_id,$user_id)
	{
		if(!$group_id || !$user_id) return false;
		if($role = sf::getModel("UserRoles")->selectByRole($this->getUserId(),$group_id)){
			$role->setUserRoleId($user_id);
			$role->setUserRoleType($this->_getRoleType($group_id));
			$role->save();
			$this->setUserGroupId($group_id);
			return $this->save();
		}
	}
	
	function delete()
	{
		if(!$this->isEmpty()) return false;
		return parent::delete();	
	}
	
	public function addRole($group_id,$user_id)
	{
		if(!$group_id || !$user_id) return false;
		if($role = sf::getModel("UserRoles")->selectByRole($this->getUserId(),$group_id)){
			$role->setUserRoleId($user_id);
			$role->setUserRoleType($this->_getRoleType($group_id));
			return $role->save();
		}
	}
	
	public function removeRole($group_id)
	{
		if(!$group_id) return false;
		if($role = sf::getModel("UserRoles")->selectByRole($this->getUserId(),$group_id)){
			return $role->delete();
		}
		return false;
	}
	
	public function hasRole($role_id)
	{
		if(!$role_id) return false;
		if(sf::getModel("UserRoles")->selectAll("user_id = '".$this->getUserId()."' AND role_id = '".$role_id."' ")->getTotal()) return true;
		else return false;	
	}
	
	/**
	 * 是否已经是专家
	 */
	public function isExpert()
	{
		if(sf::getModel("UserRoles")->selectAll("user_id = '".$this->getUserId()."' AND role_id = '10' ")->getTotal()) return true;
		else{
			$experts = sf::getModel("Experts")->selectAll("card_id = '".$this->getUserIdcard()."' and card_id <> '' ");
			if($experts->getTotal() == 0) return false;
			while($expert = $experts->getObject())
			{
				$this->addRole(10,$expert->getUserId());//将角色关联进来
			}
			return true;
		}
	}
	
	/**
	 * 是否已经是负责人
	 */
	public function isResearcher()
	{
		if(sf::getModel("UserRoles")->selectAll("user_id = '".$this->getUserId()."' AND role_id = '2' ")->getTotal()) return true;
		else{
			$researchers = sf::getModel("Declarers")->selectAll("user_idcard = '".$this->getUserIdcard()."' and user_idcard <> '' ");
			if($researchers->getTotal() == 0) return false;
			while($researcher = $researchers->getObject())
			{
				$this->addRole(2,$researcher->getUserId());//将角色关联进来
			}
			return true;
		}
	}
	
	/**
	 * 是否已经是申报单位
	 */
	public function isCompany()
	{
		if(sf::getModel("UserRoles")->selectAll("user_id = '".$this->getUserId()."' AND role_id = '3' ")->getTotal()) return true;
		else{
			return false;
			$corporations = sf::getModel("Corporations")->selectAll("linkman_email = '".$this->getUserEmail()."' AND linkman='".$this->getUserUsername()."'");
			if($corporations->getTotal() == 0) return false;
			while($corporation = $corporations->getObject())
			{
				$this->addRole(3,$corporation->getUserId());//将角色关联进来
			}
			return true;
		}
	}
	
	/**
	 * 是否是空账号
	 */
	public function isEmpty()
	{
		if(sf::getModel("UserRoles")->selectAll("user_id = '".$this->getUserId()."'")->getTotal()) return false;
		else return true;
	}
	
	public function getDefultRoleName()
	{
		return sf::getModel("UserGroups",$this->getUserGroupId())->getUserGroupName();	
	}
	
	public function getDefultRoleId()
	{
		return $this->getUserGroupId();	
	}
	
	/**
	 * 取得用户当前角色的ID,一般这个ID等于USER_ID,多角色时允许不同
	 */
	public function getRoleUserId($role_id=0)
	{
		$role_id = $role_id ? $role_id : $this->getDefultRoleId();
		$db = sf::getLib("db");
		$row = $db->fetch_first("select user_role_id from user_roles where user_id = '".$this->getUserId()."' and role_id = '".$role_id."' ");
		return $row['user_role_id'] ? $row['user_role_id'] : $this->getUserId();
	}

	public function getRoleId($role_id=0)
	{
		$role_id = $role_id ? $role_id : $this->getDefultRoleId();
		$db = sf::getLib("db");
		$row = $db->fetch_first("select id from user_roles where user_id = '".$this->getUserId()."' and role_id = '".$role_id."' ");
		return $row['id'];
	}
	
	public function selectRoles()
	{
		return sf::getModel("UserRoles")->selectAll("user_id = '".$this->getUserId()."' ","ORDER BY role_id ASC");
	}
	
	/**
	 * 取得用户所有分组，与selectRoles相似
	 */
	public function selectGroups()
	{
		return sf::getModel("UserGroups")->selectAll("id IN (SELECT role_id FROM user_roles WHERE user_id = '".$this->getUserId()."')","ORDER BY id DESC");	
	}
	
	function _getRoleType($group_id)
	{
		switch($group_id)
		{
			case 2:
			return 'App\Models\Researcher';
			break;
			case 3:
			return 'App\Models\Company';
			break;
			case 4:
			return 'App\Models\Department';
			break;
			default:
			return 'App\Models\Manager';
			break;
		}	
	}
	
	/**
	 * 检查身份证是否正确
	 */
	function isBadIdCard()
	{
		if($this->getUserIdcardType() == '身份证'){
			if(!isIdcard($this->getUserIdcard())) return true;	
			else return false;
		}
		return true;
	}
	
	/**
	 * 查找相识的用户
	 */
	public function selectSameUser()
	{
		return $this->selectAll("user_id != '".parent::getUserId()."' AND ((user_mobile = '".parent::getUserMobile()."' AND user_mobile <> '') OR (user_idcard = '".parent::getUserIdcard()."' AND user_idcard <> '') OR (user_email = '".parent::getUserEmail()."' AND user_email <> ''))","ORDER BY id ASC");	
	}
	
	public function getMark()
	{
		$htmlStr = '';
        if($this->getIsLock() == 4) $htmlStr .= '[禁]';
        if($this->getErrorNumber() >= 5) $htmlStr .= '[<a href="'.site_url("admin/account/resetError/userid/".$this->getUserId()).'">解</a>]</mark>';
        if($this->getQqOpenid()) $htmlStr .= '[<a href="'.site_url("admin/account/unbindqq/userid/".$this->getUserId()).'">QQ</a>]</mark>';
		$log = sf::getModel("LogGetpwds");
		if($log->getCount(parent::getUserId()) > 5){
			$htmlStr .= '<a href="'.site_url('admin/account/delLogGetpwd/id/'.parent::getUserId()).'" title="自助找回密码超过5次" onclick="return confirm(\'确定要解除此账号的锁定状态吗？\')" class="red">[锁]</a>';
		}

		//if($this->isEmpty()) $htmlStr .= '<mark>空</mark>';
		return $htmlStr;	
	}

	function getGroupId()
	{
		//不在用户组内则执行
		if(!in_array(input::getInput("session.userlevel"),[2,3,4,9,10,16,37])){
			$manager = sf::getModel("Managers")->selectByUserId($this->getRoleUserId(input::getInput("session.userlevel")));
			if($manager->isNew()) return '-1';
			return $manager->getUserGroupId();
		}else return '-1';	
	}

	function getUnitLevel()
	{
		if($this->isCompany()){
			$company = sf::getModel('Corporations')->selectByUserId(input::session('roleuserid'));
			return intval($company->getLevel())+1;
		}
		return 0;
	}

    public function checkFingerPrint($printStr)
    {
        $prints = $this->getFingerprints();
        if($prints->getTotal()==0){
            $Fingerprint = sf::getModel('Fingerprints');
            $Fingerprint->setUserId($this->getUserId());
            $Fingerprint->setFingerprint($printStr);
            $Fingerprint->setUserIp(input::getIp());
            $Fingerprint->save();
            return true;
        }
        while($print = $prints->getObject()){
            if($print->getFingerprint()==$printStr || $print->getUserIp()==input::getIp())
                return true;
        }
        return false;
    }

    public function getFingerprints()
    {
        return  sf::getModel('Fingerprints')->selectAll("`user_id` = '".$this->getUserId()."'");
    }

    public function setFingerprints($str)
    {
        $Fingerprint = sf::getModel('Fingerprints');
        $Fingerprint->setUserId($this->getUserId());
        $Fingerprint->setFingerprint($str);
        $Fingerprint->setUserIp(input::getIp());
        $Fingerprint->save();
        return true;
    }

    function getMobileCount($mobile)
    {
        $db = sf::getLib("db");
        return $db->result_first("SELECT count(*) c FROM ".$this->table." WHERE `user_mobile` = '".$mobile."'");
    }

    function selectByMobile($mobile)
    {
        $db = sf::getLib("db");
        $result = $db->fetch_first("SELECT * FROM ".$this->table." WHERE `user_mobile` = '".$mobile."'");
        if($result){
            $this->fillObject($result);
            return $this;
        }else return false;
    }

    public function getFullName()
    {
        return parent::getUserUsername();
    }
    /**
     * 账号登陆,权限如何控制，严格执行调用审查,仅仅单点登录调用
     * @return [type] [description]
     */
    public function login()
    {
        if ($this->isNew()) {
            return false;
        }
        //基础信息
        $_SESSION['id'] = $this->getId();
        $_SESSION['userid']   = $this->getUserId();
        $_SESSION['roleuserid'] =  $this->getRoleUserId();
        $_SESSION['openid']   = $this->getOpenId();
        $_SESSION['username'] = $this->getUserName();
        $_SESSION['fullname'] = $_SESSION['nickname'] = $this->getFullName();
        $_SESSION['userlevel'] = $this->getDefultRoleId();
        $_SESSION['groupname'] = $this->getUserGroupName();
        //登录统计
        $_SESSION['login_num'] = $this->getLoginNum();
        $_SESSION['lastlogin'] = $this->getLastloginAt();
        $_SESSION['userip']    = $this->getUserIp();
        $_SESSION['group_id']   =  $this->getGroupId();
        $_SESSION['auth'] =  $this->getAuths();

        //记录登录信息
        $this->setLoginNum();
        $this->setSessionId(session_id());
        $this->setLastloginAt(date("Y-m-d H:i:s"));
        $this->setUserIp(input::getIp());
        $this->setErrorNumber(0);
        $this->save();

        return true;
    }

    public function handelRoles($data)
    {
        $roles = $data['role'];
        $roleArr = array_column($roles, 'role_user_id', 'role_id');
        $roleNames = array_column($roles, 'role_name');
        $roleIds = array_column($roles, 'role_id');
        $allowRoleId = [2,3,4,5,6,10,11];
        $existRoleIds = array_intersect($allowRoleId, $roleIds);
        if(empty($existRoleIds)){
            return ['code'=>403,'data'=>'当前账号所属角色（'.implode('、',$roleNames).'）没有权限访问该系统！','url'=>site_url('login/index')];
        }
        rsort($existRoleIds);
        //当前账号已有的角色
        $rolesObjects = $this->selectRoles();
        $currentRoles = [];
        while($rolesObject = $rolesObjects->getObject()){
            $currentRoles[] = $rolesObject->getRoleId();
        }
        //移除账号角色
        foreach ($currentRoles as $currentRoleId){
            if($currentRoleId==5) continue;
            if(in_array($currentRoleId,$allowRoleId) && !in_array($currentRoleId,$existRoleIds)){
                $this->removeRole($currentRoleId);
            }
        }
        //关联角色
        foreach ($existRoleIds as $existRoleId){
            if(in_array($existRoleId,$currentRoles)) continue;
            if(in_array($existRoleId,[5,6])){
                //处长、处室工作人员
                $result = $this->bindManager($data,$roleArr[$existRoleId]);
                if($result!==true) return $result;
            }
            if($existRoleId==4){
                //主管部门
                $result = $this->bindGather($roleArr[$existRoleId]);
                if($result!==true) return $result;
            }
            if($existRoleId==3){
                //依托单位
                $result = $this->bindCompany($data['company'],$roleArr[$existRoleId]);
                if($result!==true) return $result;
            }
            if($existRoleId==2){
                //项目负责人
                $result = $this->bindResearcher($data,$roleArr[$existRoleId]);
                if($result!==true) return $result;
            }
        }
        return true;

    }

    private function bindManager($data,$userId)
    {
        $manager = sf::getModel('Managers')->selectByUserId($userId);
        if($manager->isNew()) {
            $manager->setUserGroupId(6);
            $manager->setOfficeId(300);
            $manager->setUserName($data['account']['user_name']);
            $manager->setUserUsername($data['full_name']);
            $manager->setCreatedAt(date('Y-m-d H:i:s'));
            $manager->setUpdatedAt(date('Y-m-d H:i:s'));
            $manager->save();
        }
        //关联角色
        $this->addRole(6,$manager->getUserId());
        return true;
    }

    private function bindGather($userId)
    {
        $department = sf::getModel('Departments')->selectByUserId($userId);
        if($department->isNew()) return ['code'=>404,'data'=>'没有找到该主管部门'];
        //关联角色
        $this->addRole(4,$department->getUserId());
        return true;
    }

    private function bindCompany($data,$userId)
    {
        if(empty($data)) return ['code'=>403,'data'=>'缺少单位数据'];
        if(empty($data['code']) || empty($data['company_name']) || empty($data['department_id'])) return ['code'=>403,'data'=>'缺少单位关键信息'];
        $company = sf::getModel('Corporations')->selectByUserId($userId);
        if($company->isNew()) {
            //注册单位
            $company->setCode($data['code']);
            $company->setSubject($data['company_name']);
            $company->setDepartmentId($data['department_id']);
            $company->setProperty($data['property']);
            $company->setPrincipal($data['legal_person']);
            //保存联系人信息
            $company->setAreaCode($data['area_code']);
            $company->setArea($data['area']);
            $company->setLinkman($data['linkman']);
            $company->setMobile($data['linkman_mobile']);
            $company->setPhone($data['linkman_phone']);
            $company->setAddress($data['address']);
            $company->setBankName($data['bank_name']);
            $company->setBankId($data['bank_card_id']);
            $company->setManagerUserId($data['manager_user_id']);
            $isLock = $data['state']==='T' ? 0 : 9;
            $company->setIsLock($isLock);	//注册完成
            $company->save();
        }
        //关联角色
        $this->addRole(3,$company->getUserId());
        return true;
    }

    private function bindResearcher($data,$userId)
    {
        $account = $data['account'];
        $base = $data['base'];
        if(empty($account)) return ['code'=>403,'data'=>'缺少人员账号数据'];
        if(empty($base)) return ['code'=>403,'data'=>'缺少人员基础数据'];
        if(empty($account['idcard_number'])) return ['code'=>403,'data'=>'缺少人员身份证信息'];
        if(empty($account['full_name'])) return ['code'=>403,'data'=>'缺少人员姓名'];
        $declarer = sf::getModel('Declarers')->selectByUserId($userId);
        if($declarer->isNew()) {
            //注册项目负责人
            $declarer->setPersonname($account['full_name']);
            $declarer->setCardType($account['idcard_type']);
            $declarer->setUserIdcard($account['idcard_number']);
            $declarer->setUserBirthday($base['birthday']);
            $declarer->setUserSex($base['sex']);
            $declarer->setUserEmail($account['email']);
            $declarer->setUserMobile($account['mobile']);
            $declarer->setEducation($data['school']['education']);
            $declarer->setTitleType($data['title']['level']);
            $declarer->setTitle($data['title']['name']);
            $declarer->setMajor($data['school']['major']);
            //保存单位信息
            $declarer->setCorporationId($data['work']['company_id']);
            $declarer->setCorporationName($data['work']['company_name']);
            $isLock = $base['state']==='T' ? 0 : 9;
            $declarer->setIsLock($isLock);	//注册完成
            $declarer->save();
        }
        //关联角色
        $this->addRole(2,$declarer->getUserId());
        return true;
    }
}
