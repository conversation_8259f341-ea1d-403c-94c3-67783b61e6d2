<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use Sofast\Core\Config;
use App\Model\BaseMenus;

class UserMenus extends BaseUserMenus
{
	private $category_type = 'default';
	
	public function __construct($data='',$type='default')
  	{
    	$type && $this->category_type = $type;;
    	parent::__construct($data);
  	}
	
	function selectAll($addWhere='',$orderBy='',$showMax=0,$fields='')
	 {
	 	!$orderBy && $orderBy = 'ORDER BY `left` ASC,orders asc';
		if($addWhere) $addWhere .= " AND type = '".$this->category_type."' ";
		else $addWhere .= "type = '".$this->category_type."' ";
		return parent::selectAll($addWhere,$orderBy,$showMax,$fields);
	 }
	 
	 function getPager($addWhere='',$orderBy='',$showMax=20,$fields='')
	 {
	 	!$orderBy && $orderBy = 'ORDER BY `left` ASC,orders ASC';
		if($addWhere) $addWhere .= " AND type = '".$this->category_type."' ";
		else $addWhere .= "type = '".$this->category_type."' ";
		return parent::getPager($addWhere,$orderBy,$showMax,$fields);
	 }
	 
	 function rebuildTree()
	 {
		$data = $this->getFormatList();
		$db = sf::getLib("Db");
		$count = count($data);
        $lefts = [];
        $rights = [];
        $headStrs = [];
        $levels = [];
        $ids = [];
		for ($i = 0, $n = $count; $i < $n; $i++ )
		{
            $lefts[$data[$i]['id']] = $data[$i]['left'];
            $rights[$data[$i]['id']] = $data[$i]['right'];
            $headStrs[$data[$i]['id']] = $data[$i]['HeadStr'];
            $levels[$data[$i]['id']] = $data[$i]['level'];
            $ids[] = $data[$i]['id'];
		}
		$sql = "UPDATE `".$this->table."` SET `left` = CASE id ";
         foreach ($lefts as $id => $left) {
             $sql .= sprintf("WHEN %d THEN %d ", $id, $left);
         }
         $sql .= "END,`right` = CASE id ";
         foreach ($rights as $id => $right) {
             $sql .= sprintf("WHEN %d THEN %d ", $id, $right);
         }
         $sql .= "END,`head_str` = CASE id ";
         foreach ($headStrs as $id => $headStr) {
             $sql .= sprintf("WHEN %d THEN '%s' ", $id, $headStr);
         }
         $sql .= "END,`level` = CASE id ";
         foreach ($levels as $id => $level) {
             $sql .= sprintf("WHEN %d THEN %d ", $id, $level);
         }
         $sql .= "END WHERE id IN (".implode(',',$ids).")";
		$db->exec($sql);
	 }
	 
	 function save()
	 {
		 $result = parent::save();
		 $this->rebuildTree();
		 return $result;
	 }
	 
	 function getFormatList($pid = 0)
	 {
		$result = $this->selectAll("type = '".$this->category_type."' ",'ORDER BY orders ASC')->toArray();
		foreach((array)$result as $row){
			$num = count($tmpData[$row['parent_id']]);
			$tmpData[$row['parent_id']][$num] = $row;
		}
		if (count($tmpData) == 0) return;
		$showData = array();
		$this->getFormatList_c($tmpData, $showData, $pid, '',1);
		return $showData;
	 }
	 
	 function getFormatList_c(&$tmpData, &$showData, $pid, $headstr = '', $level = 1, $left = 1)
	 {
		$num = count($tmpData[$pid]);
		$i = 0;
		if(!is_array($tmpData[$pid])) return false;
		foreach ($tmpData[$pid] as $key => $val)
		{
		  	$id     = $val['id'];
		  	$tmplen = count($showData);
		  	$showData[$tmplen] = $val;
		  	$showData[$tmplen]['left'] = $left;
		  	$right = $left + 1;
	
		  	if (!empty($headstr)) $showData[$tmplen]['HeadStr'] = $headstr;
	
		  	if ($i == $num-1){
				$showData[$tmplen]['HeadStr'] .= '└';
				$headstr_1 =  $headstr."&nbsp;&nbsp;";
				$level_1   =  $level + 1;
		  	}else{
				$showData[$tmplen]['HeadStr'] .= '├';
				$headstr_1 = $headstr.'│';
				$level_1   =  $level + 1;
		  	}
		  	$showData[$tmplen]['level'] = $level;
		  	$i++;
	
		  	if ((count($tmpData[$id]) > 0)) $right = $this->getFormatList_c($tmpData, $showData, $id, $headstr_1, $level_1, $right);
				
		  	$showData[$tmplen]['right'] = $right;
		  	$left = $right + 1;
		}
		return $right + 1;
	 }
	 
	 function remove()
	 {
		 $result = $this->selectSonTree($this);
		 while($object = $result->getObject()){
			 $object->delete();
		 }
		 return $this->delete();
	 }
	 
	 function selectSonTree($object)
	 {
		 if(!is_object($object)){
			$this->selectByPk((int)$object);
			$object = & $this;
		}
		 return $this->selectAll("`left` > '".$object->getLeft()."' AND `left` < '".$object->getRight()."'",'ORDER BY `left` ASC');
	 }
	 
	 function getUserGroupName($dv=',',$is_array = false)
	{
		$ids = explode(",",parent::getUserGroupIds());
		
		foreach((array)$ids as $id){
			if($id == '-1') $result[] = '<s style="color:red">禁止使用</s>';
			else $result[] = sf::getModel("UserGroups",$id)->getUserGroupName();
		}
		
		if($is_array) return $result;
		else return implode($dv,$result);
	}
	
	function setUserGroupIds($actions=array())
	{
		$actions = (array)$actions;
		if(in_array('-1',$actions)) return parent::setUserGroupIds('-1');
		return parent::setUserGroupIds(implode(",",$actions));
	}

    function setOfficeIds($actions=array())
    {
        $actions = (array)$actions;
        if(in_array('-1',$actions)) return parent::setOfficeIds('-1');
        return parent::setOfficeIds(implode(",",$actions));
    }
	
	function getUserGroupIds($is_array=false)
	{
		if($is_array) return explode(",",parent::getUserGroupIds());
		else return parent::getUserGroupIds();
	}

    function getOfficeIds($is_array=false)
    {
        if($is_array) return array_filter(explode(",",parent::getOfficeIds()));
        else return parent::getOfficeIds();
    }
	
	function getUserGroupIdArray()
	{
		return explode(",",parent::getUserGroupIds());
	}
	
	function getMenuStr()
	{
        $cache = sf::getLib("cache",APPPATH . "../cache/menus");
        $menuType = sf::getModel('UserMenuTypes')->selectByUserId(input::session('userid'));
        if(!$_SESSION['navigation_id']){
            if($_SESSION['userlevel']==10) $_SESSION['navigation_id'] = 47;
            else $_SESSION['navigation_id'] = 7;
        }

        $cacheName = 'menu_'.$menuType->getType().$_SESSION['userlevel'].$this->getId();
        if($_SESSION['userlevel']==6){
            $cacheName = 'menu_'.$menuType->getType().$_SESSION['userlevel'].$_SESSION['id'].$this->getId();
        }
        if($html = $cache->getCache($cacheName)){
            return $html;
        }
        $html ="<li class=\"nav-main-heading\">".$this->getSubject()."</li>";
        if($this->hasSon()){
            $html.=$this->getChildMenuHtml($this);
        }
        $cache->setCache($cacheName,$html);
        return $html;
	}
	
	function hasAuth()
	{
        if(!in_array(input::getInput("session.userlevel"),$this->getUserGroupIds(true))){
            return false;
        }
        if($officeIds = $this->getOfficeIds(true)){
            if(input::session('userlevel')==6 && !in_array(input::session('office_id'),$officeIds)) return false;
        }
        if(input::session("userlevel")==6){
            $auth = (array)input::session('auth');
            $authMenus = $auth['menu'];
            if($authMenus && !in_array($this->getId(),$authMenus)){
                return false;
            }
        }
        return true;
	}
	
	function hasSon()
	{
		if($this->getRight() - $this->getLeft() > 2) return true;
		else return false;	
	}

	function getNavbars($start=0,$showMax=7)
	{
		$navbars = [];

		$all = $this->selectAll("level = 1","ORDER BY orders ASC",0);
		
		while($navbar = $all->getObject())
		{
			$self = new static();
			$self = $self->fillObject($navbar->toArray());
			if($self->hasAuth())
				$navbars[] = $self;
		}

		if(count($navbars)==0) return $navbars;
		return array_slice($navbars,$start,$showMax);
	}
	
	function getCurrentNavbarId()
	{
		if($_SESSION['navigation_id']) $_CurrentNavbarId = $_SESSION['navigation_id'];
		else{
			$navbars = $this->selectAll("level = 1","ORDER BY orders ASC",0);
			while($navbar = $navbars->getObject())
			{
				if($navbar->hasAuth()){
					$_CurrentNavbarId = $_SESSION['navigation_id'] = $navbar->getId();
					return $_CurrentNavbarId;
				}
			}
		}
		return $_CurrentNavbarId;
	}
	
	function getIconHtml()
	{
		if(parent::getIcon()) return '<i class="'.parent::getIcon().'"></i> ';
		else return '';	
	}

	function getLink()
	{
		if(strtolower(substr($this->getUrl(),0,4)) == 'http') return $this->getUrl();
		return site_url($this->getUrl());
	}
	
	private function getAuthId(){
		if($this->getUrl()=='#') return 0;

		$urlArr = explode('/',$this->getUrl());
		$method = array_pop($urlArr);
		$controller = implode('/',$urlArr);

		$db = sf::getLib("db");
		$result = $db->fetch_first("SELECT id FROM authorizations WHERE controller = '".$controller."' AND method = '".$method."'");
		if($result){
			return $result['id'];
		}else return 0;
	}
	
    public function getTip()
    {
        return;
        if($this->getUrl()=='office/apply/wait_list'){
            $total = waitApplyNum();
            if($total>0){
                return '<span class="badge badge-primary">'.$total.'</span>';
            }
        }
    }

    function getMenuHtml()
    {
        $cache = sf::getLib("cache",APPPATH . "../cache/menus");
        $menuType = sf::getModel('UserMenuTypes')->selectByUserId(input::session('userid'));
        if(!$_SESSION['navigation_id']){
            $menus = sf::getModel('UserMenus')->selectAll("`level` = 1 and CONCAT(',', user_group_ids, ',') LIKE '%,".input::session('userlevel').",%'","ORDER BY orders asc,id asc",1);
            if($menus->getTotal()>0){
                $_SESSION['navigation_id'] = $menus->getObject()->getId();
            }
            else $_SESSION['navigation_id'] = 670;
        }

        $cacheName = 'menu_'.$menuType->getType().$_SESSION['userlevel'].$_SESSION['navigation_id'];
        if($_SESSION['userlevel']==6){
            $cacheName = 'menu_'.$menuType->getType().$_SESSION['userlevel'].$_SESSION['id'].$_SESSION['navigation_id'];
        }

        if(!$html = $cache->getCache($cacheName)){
            $addwhere = "`level` = 1";
            if($menuType->getType()=='complex') {
                $_SESSION['navigation_id'] = $_SESSION['navigation_id'];
                if(!$_SESSION['navigation_id']){
                    $_SESSION['navigation_id'] = $this->getTopMenuId();
                }
                if($_SESSION['navigation_id']) $addwhere .= " and id = {$_SESSION['navigation_id']}";
            }
            $topMenus = $this->selectAll($addwhere,"order by `orders` asc,id asc");
            while($topMenu = $topMenus->getObject()){
                if(!$topMenu->hasAuth()) continue;
                $html.="<li class=\"nav-main-heading\">".$topMenu->getSubject()."</li>";
                if($topMenu->hasSon()){
                    $html.=$this->getChildMenuHtml($topMenu);
                }
            }
            $cache->setCache($cacheName,$html);
        }
        return $html;
    }

    function getTopMenuId()
    {
        $topMenus = $this->selectAll("`level` = 1","order by `orders` asc,id asc");
        while($topMenu = $topMenus->getObject()){
            if($topMenu->hasAuth()) {
                return $topMenu->getId();
            }
        }
    }

    function getChildMenu($menuObj)
    {
        return $this->selectAll("`left` > '".$menuObj->getLeft()."' AND `left` < '".$menuObj->getRight()."' AND parent_id = '".$menuObj->getId()."'",'ORDER BY `orders` ASC,`id` ASC');
    }

    function getChildMenuHtml($menuObj,$i=0)
    {
        $i++;
        $html = '';
        if($menuObj->hasSon()){
            if($i>1) $html.='<ul class="nav-main-submenu">';
            $menus = $this->getChildMenu($menuObj);
            while($menu = $menus->getObject()){
                if(!$menu->hasAuth()) continue;
                $childAttr = $menu->hasSon() ? 'class="nav-main-link nav-main-link-submenu" data-toggle="submenu" aria-haspopup="true" aria-expanded="false"' : 'class="nav-main-link"';
                $childMenuHtml = $menu->hasSon() ? $this->getChildMenuHtml($menu,$i) : '';
                $icon = $menu->getIcon() ? '<i class="nav-main-link-icon '.$menu->getIcon().'"></i> ' : ($i>1 ? '' : '<i class="nav-main-link-icon nav-main-link-icon fa fa-sticky-note"></i> ');
                $url = $menu->getUrl()&&$menu->getUrl()!='#' ? site_url($menu->getUrl()) : '#';
                $html.='<li class="nav-main-item level-'.$i.'"><a '.$childAttr.' href="'.$url.'">'.$icon.'<span class="nav-main-link-name">'.$menu->getSubject().'</span></a>'.$childMenuHtml.'</li>';
            }
            if($i>1) $html.='</ul>';
        }
        return $html;
    }

    function getTopNavHtml()
    {
        $cache = sf::getLib("cache",APPPATH . "../cache/menus");
        $cacheName = 'topnavbtn_'.$_SESSION['userlevel'];
        if($_SESSION['userlevel']==6){
            $cacheName = 'topnavbtn_'.$_SESSION['userlevel'].$_SESSION['id'];
        }
        if(!$html = $cache->getCache($cacheName)){
            $topMenus = $this->selectAll("`level` = 1","order by `orders` asc,id asc");
            while($topMenu = $topMenus->getObject()){
                if(!$topMenu->hasAuth()) continue;
                $html.='<button class="mynav-item mymav-item-buttons mynav-button-style">'.$topMenu->getSubject().'</button>';
            }
            $cache->setCache($cacheName,$html);
        }
        return $html;
    }

    function getTopMenus()
    {
        $cache = sf::getLib("cache",APPPATH . "../cache/menus");
        $cacheName = 'topnav_'.$_SESSION['userlevel'];
        if($_SESSION['userlevel']==6){
            $cacheName = 'topnav_'.$_SESSION['userlevel'].$_SESSION['id'];
        }
        if(!$html = $cache->getCache($cacheName)){
            $topMenus = $this->selectAll("`level` = 1","order by `orders` asc,id asc");
            while($topMenu = $topMenus->getObject()){
                if(!$topMenu->hasAuth()) continue;
                $html.='<button type="button" class="btn btn-dual mr-1" onclick="return changeMenu('.$topMenu->getId().')" id="nav'.$topMenu->getId().'"><i class="'.$topMenu->getIcon().'"></i> <span class="ml-1 d-none d-sm-inline-block">'.$topMenu->getSubject().'</span></button>';
            }
            $cache->setCache($cacheName,$html);
        }
        return $html;
    }

    function getNavHtml()
    {
        $cache = sf::getLib("cache",APPPATH . "../cache/menus");
        $cacheName = 'nav_'.$_SESSION['userlevel'];
        if($_SESSION['userlevel']==6){
            $cacheName = 'nav_'.$_SESSION['userlevel'].$_SESSION['id'];
        }
        if(!$html = $cache->getCache($cacheName)){
            $topMenus = $this->selectAll("`level` = 1","order by `orders` asc,id asc");
            while($topMenu = $topMenus->getObject()){
                if(!$topMenu->hasAuth()) continue;
                $html.='<div class="nav-group"><h3 class="mynav-h3">'.$topMenu->getSubject().'</h3>';
                if($topMenu->hasSon()){
                    $html.=$this->getChildNavHtml($topMenu);
                }
                $html.='</div>';
            }
            $cache->setCache($cacheName,$html);
        }
        return $html;
    }

    function getChildNavHtml($menuObj)
    {
        $html = '';
        if($menuObj->hasSon()){
            $menus = $this->getChildMenu($menuObj);
            while($menu = $menus->getObject()){
                if(!$menu->hasAuth()) continue;
                $url = $menu->getUrl()&&$menu->getUrl()!='#' ? site_url($menu->getUrl()) : '#';
                $html .= $menu->hasSon() ? '<h3 class="mynav-h3">'.$menu->getSubject().'</h3>'.$this->getChildNavHtml($menu) : '<div class="mynav-transition"><a href="'.$url.'" class="mynav-item mynav-link mynav-link-padding">'.$menu->getSubject().'</a></div>';
            }
        }
        return $html;
    }

    function hasAuthByGroupId($groupId)
    {
        if(!in_array($groupId,$this->getUserGroupIds(true))){
            return false;
        }
        return true;
    }

    function hasAuthByUserId($userId)
    {
        if($userId==1) return true;
        if(!in_array($userId,$this->getUserIdArray())){
            return false;
        }
        return true;
    }

    function getUserIdArray()
    {
        return explode(",",parent::getUserIds());
    }

    function setUserIds($actions=array())
    {
        $actions = (array)$actions;
        return parent::setUserIds(implode(",",$actions));
    }
}