<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseProjectMembers extends BaseModel
{
	private $id;
	private $no  = '0';
	private $project_id;
	private $type;
	private $user_id;
	private $user_type  = '普通';
	private $personnel_type;
	private $card_type  = '身份证';
	private $certificate;
	private $name;
	private $sex;
	private $birthday;
	private $school;
	private $graduate_at;
	private $age;
	private $education;
	private $degree;
	private $professional_title;
	private $title_type;
	private $honor;
	private $honor_level;
	private $honor_type;
	private $honor_at;
	private $honor_file;
	private $pay;
	private $duty;
	private $title;
	private $time;
	private $major;
	private $submajor;
	private $project_type;
	private $year;
	private $work_at;
	private $company_name;
	private $mobile;
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "project_members";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getNo()
	{
		return $this->no;
	}

	public function getProjectId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->project_id,0,$len,"utf-8");
			else return substr($this->project_id,0,$len);
		}
		return $this->project_id;
	}

	public function getType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->type,0,$len,"utf-8");
			else return substr($this->type,0,$len);
		}
		return $this->type;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getUserType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_type,0,$len,"utf-8");
			else return substr($this->user_type,0,$len);
		}
		return $this->user_type;
	}

	public function getPersonnelType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->personnel_type,0,$len,"utf-8");
			else return substr($this->personnel_type,0,$len);
		}
		return $this->personnel_type;
	}

	public function getCardType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->card_type,0,$len,"utf-8");
			else return substr($this->card_type,0,$len);
		}
		return $this->card_type;
	}

	public function getCertificate($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->certificate,0,$len,"utf-8");
			else return substr($this->certificate,0,$len);
		}
		return $this->certificate;
	}

	public function getName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->name,0,$len,"utf-8");
			else return substr($this->name,0,$len);
		}
		return $this->name;
	}

	public function getSex($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->sex,0,$len,"utf-8");
			else return substr($this->sex,0,$len);
		}
		return $this->sex;
	}

	public function getBirthday($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->birthday,0,$len,"utf-8");
			else return substr($this->birthday,0,$len);
		}
		return $this->birthday;
	}

	public function getSchool($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->school,0,$len,"utf-8");
			else return substr($this->school,0,$len);
		}
		return $this->school;
	}

	public function getGraduateAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->graduate_at,0,$len,"utf-8");
			else return substr($this->graduate_at,0,$len);
		}
		return $this->graduate_at;
	}

	public function getAge($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->age,0,$len,"utf-8");
			else return substr($this->age,0,$len);
		}
		return $this->age;
	}

	public function getEducation($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->education,0,$len,"utf-8");
			else return substr($this->education,0,$len);
		}
		return $this->education;
	}

	public function getDegree($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->degree,0,$len,"utf-8");
			else return substr($this->degree,0,$len);
		}
		return $this->degree;
	}

	public function getProfessionalTitle($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->professional_title,0,$len,"utf-8");
			else return substr($this->professional_title,0,$len);
		}
		return $this->professional_title;
	}

	public function getTitleType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->title_type,0,$len,"utf-8");
			else return substr($this->title_type,0,$len);
		}
		return $this->title_type;
	}

	public function getHonor($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->honor,0,$len,"utf-8");
			else return substr($this->honor,0,$len);
		}
		return $this->honor;
	}

	public function getHonorLevel($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->honor_level,0,$len,"utf-8");
			else return substr($this->honor_level,0,$len);
		}
		return $this->honor_level;
	}

	public function getHonorType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->honor_type,0,$len,"utf-8");
			else return substr($this->honor_type,0,$len);
		}
		return $this->honor_type;
	}

	public function getHonorAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->honor_at,0,$len,"utf-8");
			else return substr($this->honor_at,0,$len);
		}
		return $this->honor_at;
	}

	public function getHonorFile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->honor_file,0,$len,"utf-8");
			else return substr($this->honor_file,0,$len);
		}
		return $this->honor_file;
	}

	public function getPay($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->pay,0,$len,"utf-8");
			else return substr($this->pay,0,$len);
		}
		return $this->pay;
	}

	public function getDuty($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->duty,0,$len,"utf-8");
			else return substr($this->duty,0,$len);
		}
		return $this->duty;
	}

	public function getTitle($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->title,0,$len,"utf-8");
			else return substr($this->title,0,$len);
		}
		return $this->title;
	}

	public function getTime()
	{
		return $this->time;
	}

	public function getMajor($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->major,0,$len,"utf-8");
			else return substr($this->major,0,$len);
		}
		return $this->major;
	}

	public function getSubmajor($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->submajor,0,$len,"utf-8");
			else return substr($this->submajor,0,$len);
		}
		return $this->submajor;
	}

	public function getProjectType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->project_type,0,$len,"utf-8");
			else return substr($this->project_type,0,$len);
		}
		return $this->project_type;
	}

	public function getYear($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->year,0,$len,"utf-8");
			else return substr($this->year,0,$len);
		}
		return $this->year;
	}

	public function getWorkAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->work_at,0,$len,"utf-8");
			else return substr($this->work_at,0,$len);
		}
		return $this->work_at;
	}

	public function getCompanyName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->company_name,0,$len,"utf-8");
			else return substr($this->company_name,0,$len);
		}
		return $this->company_name;
	}

	public function getMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->mobile,0,$len,"utf-8");
			else return substr($this->mobile,0,$len);
		}
		return $this->mobile;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setNo($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->no !== $v)
		{
			$this->no = $v;
			$this->fieldData["no"] = $v;
		}
		return $this;

	}

	public function setProjectId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->project_id !== $v)
		{
			$this->project_id = $v;
			$this->fieldData["project_id"] = $v;
		}
		return $this;

	}

	public function setType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->type !== $v)
		{
			$this->type = $v;
			$this->fieldData["type"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setUserType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_type !== $v)
		{
			$this->user_type = $v;
			$this->fieldData["user_type"] = $v;
		}
		return $this;

	}

	public function setPersonnelType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->personnel_type !== $v)
		{
			$this->personnel_type = $v;
			$this->fieldData["personnel_type"] = $v;
		}
		return $this;

	}

	public function setCardType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->card_type !== $v)
		{
			$this->card_type = $v;
			$this->fieldData["card_type"] = $v;
		}
		return $this;

	}

	public function setCertificate($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->certificate !== $v)
		{
			$this->certificate = $v;
			$this->fieldData["certificate"] = $v;
		}
		return $this;

	}

	public function setName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->name !== $v)
		{
			$this->name = $v;
			$this->fieldData["name"] = $v;
		}
		return $this;

	}

	public function setSex($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->sex !== $v)
		{
			$this->sex = $v;
			$this->fieldData["sex"] = $v;
		}
		return $this;

	}

	public function setBirthday($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->birthday !== $v)
		{
			$this->birthday = $v;
			$this->fieldData["birthday"] = $v;
		}
		return $this;

	}

	public function setSchool($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->school !== $v)
		{
			$this->school = $v;
			$this->fieldData["school"] = $v;
		}
		return $this;

	}

	public function setGraduateAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->graduate_at !== $v)
		{
			$this->graduate_at = $v;
			$this->fieldData["graduate_at"] = $v;
		}
		return $this;

	}

	public function setAge($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->age !== $v)
		{
			$this->age = $v;
			$this->fieldData["age"] = $v;
		}
		return $this;

	}

	public function setEducation($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->education !== $v)
		{
			$this->education = $v;
			$this->fieldData["education"] = $v;
		}
		return $this;

	}

	public function setDegree($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->degree !== $v)
		{
			$this->degree = $v;
			$this->fieldData["degree"] = $v;
		}
		return $this;

	}

	public function setProfessionalTitle($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->professional_title !== $v)
		{
			$this->professional_title = $v;
			$this->fieldData["professional_title"] = $v;
		}
		return $this;

	}

	public function setTitleType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->title_type !== $v)
		{
			$this->title_type = $v;
			$this->fieldData["title_type"] = $v;
		}
		return $this;

	}

	public function setHonor($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->honor !== $v)
		{
			$this->honor = $v;
			$this->fieldData["honor"] = $v;
		}
		return $this;

	}

	public function setHonorLevel($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->honor_level !== $v)
		{
			$this->honor_level = $v;
			$this->fieldData["honor_level"] = $v;
		}
		return $this;

	}

	public function setHonorType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->honor_type !== $v)
		{
			$this->honor_type = $v;
			$this->fieldData["honor_type"] = $v;
		}
		return $this;

	}

	public function setHonorAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->honor_at !== $v)
		{
			$this->honor_at = $v;
			$this->fieldData["honor_at"] = $v;
		}
		return $this;

	}

	public function setHonorFile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->honor_file !== $v)
		{
			$this->honor_file = $v;
			$this->fieldData["honor_file"] = $v;
		}
		return $this;

	}

	public function setPay($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->pay !== $v)
		{
			$this->pay = $v;
			$this->fieldData["pay"] = $v;
		}
		return $this;

	}

	public function setDuty($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->duty !== $v)
		{
			$this->duty = $v;
			$this->fieldData["duty"] = $v;
		}
		return $this;

	}

	public function setTitle($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->title !== $v)
		{
			$this->title = $v;
			$this->fieldData["title"] = $v;
		}
		return $this;

	}

	public function setTime($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->time !== $v)
		{
			$this->time = $v;
			$this->fieldData["time"] = $v;
		}
		return $this;

	}

	public function setMajor($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->major !== $v)
		{
			$this->major = $v;
			$this->fieldData["major"] = $v;
		}
		return $this;

	}

	public function setSubmajor($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->submajor !== $v)
		{
			$this->submajor = $v;
			$this->fieldData["submajor"] = $v;
		}
		return $this;

	}

	public function setProjectType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->project_type !== $v)
		{
			$this->project_type = $v;
			$this->fieldData["project_type"] = $v;
		}
		return $this;

	}

	public function setYear($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->year !== $v)
		{
			$this->year = $v;
			$this->fieldData["year"] = $v;
		}
		return $this;

	}

	public function setWorkAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->work_at !== $v)
		{
			$this->work_at = $v;
			$this->fieldData["work_at"] = $v;
		}
		return $this;

	}

	public function setCompanyName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->company_name !== $v)
		{
			$this->company_name = $v;
			$this->fieldData["company_name"] = $v;
		}
		return $this;

	}

	public function setMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->mobile !== $v)
		{
			$this->mobile = $v;
			$this->fieldData["mobile"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `project_members` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"no" => $this->getNo(),
			"project_id" => $this->getProjectId(),
			"type" => $this->getType(),
			"user_id" => $this->getUserId(),
			"user_type" => $this->getUserType(),
			"personnel_type" => $this->getPersonnelType(),
			"card_type" => $this->getCardType(),
			"certificate" => $this->getCertificate(),
			"name" => $this->getName(),
			"sex" => $this->getSex(),
			"birthday" => $this->getBirthday(),
			"school" => $this->getSchool(),
			"graduate_at" => $this->getGraduateAt(),
			"age" => $this->getAge(),
			"education" => $this->getEducation(),
			"degree" => $this->getDegree(),
			"professional_title" => $this->getProfessionalTitle(),
			"title_type" => $this->getTitleType(),
			"honor" => $this->getHonor(),
			"honor_level" => $this->getHonorLevel(),
			"honor_type" => $this->getHonorType(),
			"honor_at" => $this->getHonorAt(),
			"honor_file" => $this->getHonorFile(),
			"pay" => $this->getPay(),
			"duty" => $this->getDuty(),
			"title" => $this->getTitle(),
			"time" => $this->getTime(),
			"major" => $this->getMajor(),
			"submajor" => $this->getSubmajor(),
			"project_type" => $this->getProjectType(),
			"year" => $this->getYear(),
			"work_at" => $this->getWorkAt(),
			"company_name" => $this->getCompanyName(),
			"mobile" => $this->getMobile(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->no = '';
		$this->project_id = '';
		$this->type = '';
		$this->user_id = '';
		$this->user_type = '';
		$this->personnel_type = '';
		$this->card_type = '';
		$this->certificate = '';
		$this->name = '';
		$this->sex = '';
		$this->birthday = '';
		$this->school = '';
		$this->graduate_at = '';
		$this->age = '';
		$this->education = '';
		$this->degree = '';
		$this->professional_title = '';
		$this->title_type = '';
		$this->honor = '';
		$this->honor_level = '';
		$this->honor_type = '';
		$this->honor_at = '';
		$this->honor_file = '';
		$this->pay = '';
		$this->duty = '';
		$this->title = '';
		$this->time = '';
		$this->major = '';
		$this->submajor = '';
		$this->project_type = '';
		$this->year = '';
		$this->work_at = '';
		$this->company_name = '';
		$this->mobile = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["no"]) && $this->no = $data["no"];
		isset($data["project_id"]) && $this->project_id = $data["project_id"];
		isset($data["type"]) && $this->type = $data["type"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["user_type"]) && $this->user_type = $data["user_type"];
		isset($data["personnel_type"]) && $this->personnel_type = $data["personnel_type"];
		isset($data["card_type"]) && $this->card_type = $data["card_type"];
		isset($data["certificate"]) && $this->certificate = $data["certificate"];
		isset($data["name"]) && $this->name = $data["name"];
		isset($data["sex"]) && $this->sex = $data["sex"];
		isset($data["birthday"]) && $this->birthday = $data["birthday"];
		isset($data["school"]) && $this->school = $data["school"];
		isset($data["graduate_at"]) && $this->graduate_at = $data["graduate_at"];
		isset($data["age"]) && $this->age = $data["age"];
		isset($data["education"]) && $this->education = $data["education"];
		isset($data["degree"]) && $this->degree = $data["degree"];
		isset($data["professional_title"]) && $this->professional_title = $data["professional_title"];
		isset($data["title_type"]) && $this->title_type = $data["title_type"];
		isset($data["honor"]) && $this->honor = $data["honor"];
		isset($data["honor_level"]) && $this->honor_level = $data["honor_level"];
		isset($data["honor_type"]) && $this->honor_type = $data["honor_type"];
		isset($data["honor_at"]) && $this->honor_at = $data["honor_at"];
		isset($data["honor_file"]) && $this->honor_file = $data["honor_file"];
		isset($data["pay"]) && $this->pay = $data["pay"];
		isset($data["duty"]) && $this->duty = $data["duty"];
		isset($data["title"]) && $this->title = $data["title"];
		isset($data["time"]) && $this->time = $data["time"];
		isset($data["major"]) && $this->major = $data["major"];
		isset($data["submajor"]) && $this->submajor = $data["submajor"];
		isset($data["project_type"]) && $this->project_type = $data["project_type"];
		isset($data["year"]) && $this->year = $data["year"];
		isset($data["work_at"]) && $this->work_at = $data["work_at"];
		isset($data["company_name"]) && $this->company_name = $data["company_name"];
		isset($data["mobile"]) && $this->mobile = $data["mobile"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}