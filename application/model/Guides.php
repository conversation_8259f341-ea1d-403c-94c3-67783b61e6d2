<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseGuides;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class Guides extends BaseGuides
{
	private $year = '2021';
	//禁止申报人员清单
	private $forbidden_list = NULL;
	
	public function __construct($data='',$year='')
  	{
    	if($year=='') $year = config::get("current_declare_year",date("Y"));
		$this->year = $year;
		
		if(!$data) return $this;
		
		if(is_array($data)) $this->fillObject($data);
		else $this->selectByPk($data);
		
		if(!$this->isNew()) $this->year = $this->getYear();
		return $this;
  	}
	
	function selectAll($addWhere='',$orderBy='',$showMax=0,$fields='')
	 {
	 	!$orderBy && $orderBy = 'ORDER BY `lft` ASC,orders asc';
//		if($addWhere) $addWhere .= " AND year = '".$this->year."' ";
//		else $addWhere .= "year = '".$this->year."' ";
		return parent::selectAll($addWhere,$orderBy,$showMax,$fields);
	 } 
	 
	 function getPager($addWhere='',$orderBy='',$showMax=20,$fields='', $key = '', $form_vars = [])
	 {
	 	!$orderBy && $orderBy = 'ORDER BY `lft` ASC,orders ASC';
		if($addWhere) $addWhere .= " AND year = '".$this->year."' ";
		else $addWhere .= "year = '".$this->year."' ";
		return parent::getPager($addWhere,$orderBy,$showMax,$fields);
	 }
	 
	 function rebuildTree()
	 {
		$data = $this->getFormatList();
		$db = sf::getLib("db");
		for ($i = 0, $n = count($data); $i < $n; $i++ )
		{
		  	$result = array("lft"=>$data[$i]['lft'],
						 	"rgt"=>$data[$i]['rgt'],
						 	"head_str"=>$data[$i]['HeadStr'],
						 	"level"=>$data[$i]['level']);
		  	$db->update($result,"id = '".$data[$i]['id']."'",$this->table);
		}
        $this->makeGuideJson();
	 }

     function makeGuideJson()
     {
         $year = config::get('current_declare_year',date('Y'));
         $years = [];
         for($i=2022;$i<=$year;$i++){
             $years[$i] = $i.'年';
         }
         krsort($years);
         $i = 0 ;
         //所有申报指南
         foreach ($years as $y=>$year){
             $datas[$i] = [
                 'n'=>$year,
                 'v'=>$y,
             ];
             $guides = sf::getModel('Guides')->selectAll("level = 1 and `year` = '{$y}' and is_show = 1 and `type` = 'apply'","order by orders asc,id asc");
             $j=0;
             while($guide = $guides->getObject()){
                 $datas[$i]['s'][$j] = [
                     'n'=>$guide->getSubject(),
                     'v'=>$guide->getId(),
                 ];
                 if(!$guide->getIsLastnode()){
                     $k=0;
                     $childGuides = sf::getModel('Guides')->selectAll("parent_id ='".$guide->getId()."' and `year` = '{$y}' and is_show = 1 and `type` = 'apply'","order by orders asc,id asc");
                     while($childGuide = $childGuides->getObject()){
                         $datas[$i]['s'][$j]['s'][$k] = [
                             'n'=>$childGuide->getSubject(),
                             'v'=>$childGuide->getId(),
                         ];
                         $k++;
                     }
                 }
                 $j++;
             }
             $i++;
         }
         file_put_contents(WEBROOT.'/json/guides.json',json_encode($datas,JSON_UNESCAPED_UNICODE));
         //所有中期指南
         $datas = [];
         $i = 0 ;
         foreach ($years as $y=>$year){
             $datas[$i] = [
                 'n'=>$year,
                 'v'=>$y,
             ];
             $guides = sf::getModel('Guides')->selectAll("level = 1 and `year` = '{$y}' and is_show = 1 and `type` = 'stage'","order by orders asc,id asc");
             $j=0;
             while($guide = $guides->getObject()){
                 $datas[$i]['s'][$j] = [
                     'n'=>$guide->getSubject(),
                     'v'=>$guide->getId(),
                 ];
                 if(!$guide->getIsLastnode()){
                     $k=0;
                     $childGuides = sf::getModel('Guides')->selectAll("parent_id ='".$guide->getId()."' and `year` = '{$y}' and is_show = 1 and `type` = 'stage'","order by orders asc,id asc");
                     while($childGuide = $childGuides->getObject()){
                         $datas[$i]['s'][$j]['s'][$k] = [
                             'n'=>$childGuide->getSubject(),
                             'v'=>$childGuide->getId(),
                         ];
                         $k++;
                     }
                 }
                 $j++;
             }
             $i++;
         }
         file_put_contents(WEBROOT.'/json/guide_stages.json',json_encode($datas,JSON_UNESCAPED_UNICODE));
         //所有验收指南
         $datas = [];
         $i = 0 ;
         foreach ($years as $y=>$year){
             $datas[$i] = [
                 'n'=>$year,
                 'v'=>$y,
             ];
             $guides = sf::getModel('Guides')->selectAll("level = 1 and `year` = '{$y}' and is_show = 1 and `type` = 'complete'","order by orders asc,id asc");
             $j=0;
             while($guide = $guides->getObject()){
                 $datas[$i]['s'][$j] = [
                     'n'=>$guide->getSubject(),
                     'v'=>$guide->getId(),
                 ];
                 if(!$guide->getIsLastnode()){
                     $k=0;
                     $childGuides = sf::getModel('Guides')->selectAll("parent_id ='".$guide->getId()."' and `year` = '{$y}' and is_show = 1 and `type` = 'complete'","order by orders asc,id asc");
                     while($childGuide = $childGuides->getObject()){
                         $datas[$i]['s'][$j]['s'][$k] = [
                             'n'=>$childGuide->getSubject(),
                             'v'=>$childGuide->getId(),
                         ];
                         $k++;
                     }
                 }
                 $j++;
             }
             $i++;
         }
         file_put_contents(WEBROOT.'/json/guide_completes.json',json_encode($datas,JSON_UNESCAPED_UNICODE));
         //省级指南
         $datas = [];
         $i = 0 ;
         foreach ($years as $y=>$year){
             if($y<2023) continue;
             $datas[$i] = [
                 'n'=>$year,
                 'v'=>$y,
             ];
             $guides = sf::getModel('Guides')->selectAll("level = 1 and `year` = '{$y}' and is_show = 1 and cat_id = 174 and `type` = 'apply'","order by orders asc,id asc");
             $j=0;
             if($guides->getTotal()==0){
                 continue;
             }
             while($guide = $guides->getObject()){
                 $datas[$i]['s'][$j] = [
                     'n'=>$guide->getSubject(),
                     'v'=>$guide->getId(),
                 ];
                 if(!$guide->getIsLastnode()){
                     $k=0;
                     $childGuides = sf::getModel('Guides')->selectAll("parent_id ='".$guide->getId()."' and `year` = '{$y}' and is_show = 1 and `type` = 'apply'","order by orders asc,id asc");
                     while($childGuide = $childGuides->getObject()){
                         $datas[$i]['s'][$j]['s'][$k] = [
                             'n'=>$childGuide->getSubject(),
                             'v'=>$childGuide->getId(),
                         ];
                         $k++;
                     }
                 }
                 $j++;
             }
             $i++;
         }
         file_put_contents(WEBROOT.'/json/guide_174.json',json_encode($datas,JSON_UNESCAPED_UNICODE));

         //国重指南
         $datas = [];
         $i = 0 ;
         foreach ($years as $y=>$year){
             if($y<2023) continue;
             $datas[$i] = [
                 'n'=>$year,
                 'v'=>$y,
             ];
             $guides = sf::getModel('Guides')->selectAll("level = 1 and `year` = '{$y}' and is_show = 1 and cat_id = 230 and `type` = 'apply'","order by orders asc,id asc");
             $j=0;
             if($guides->getTotal()==0){
                 continue;
             }
             while($guide = $guides->getObject()){
                 $datas[$i]['s'][$j] = [
                     'n'=>$guide->getSubject(),
                     'v'=>$guide->getId(),
                 ];
                 if(!$guide->getIsLastnode()){
                     $k=0;
                     $childGuides = sf::getModel('Guides')->selectAll("parent_id ='".$guide->getId()."' and `year` = '{$y}' and is_show = 1 and `type` = 'apply'","order by orders asc,id asc");
                     while($childGuide = $childGuides->getObject()){
                         $datas[$i]['s'][$j]['s'][$k] = [
                             'n'=>$childGuide->getSubject(),
                             'v'=>$childGuide->getId(),
                         ];
                         $k++;
                     }
                 }
                 $j++;
             }
             $i++;
         }
         file_put_contents(WEBROOT.'/json/guide_230.json',json_encode($datas,JSON_UNESCAPED_UNICODE));

         //省级区域医疗中心
         $datas = [];
         $i = 0 ;
         foreach ($years as $y=>$year){
             if($y<2023) continue;
             $datas[$i] = [
                 'n'=>$year,
                 'v'=>$y,
             ];
             $guides = sf::getModel('Guides')->selectAll("level = 1 and `year` = '{$y}' and is_show = 1 and cat_id = 235 and `type` = 'apply'","order by orders asc,id asc");
             $j=0;
             if($guides->getTotal()==0){
                 continue;
             }
             while($guide = $guides->getObject()){
                 $datas[$i]['s'][$j] = [
                     'n'=>$guide->getSubject(),
                     'v'=>$guide->getId(),
                 ];
                 if(!$guide->getIsLastnode()){
                     $k=0;
                     $childGuides = sf::getModel('Guides')->selectAll("parent_id ='".$guide->getId()."' and `year` = '{$y}' and is_show = 1 and `type` = 'apply'","order by orders asc,id asc");
                     while($childGuide = $childGuides->getObject()){
                         $datas[$i]['s'][$j]['s'][$k] = [
                             'n'=>$childGuide->getSubject(),
                             'v'=>$childGuide->getId(),
                         ];
                         $k++;
                     }
                 }
                 $j++;
             }
             $i++;
         }
         file_put_contents(WEBROOT.'/json/guide_235.json',json_encode($datas,JSON_UNESCAPED_UNICODE));
     }
	
	/**
	 * 申报书类型
	 */
	function types()
	{
		 return sf::getModel("Types",$this->getDeclareMapId());
	}
	 
	 function save()
	 {
		 $result = parent::save();
		 //$this->rebuildTree();//单独构建，免得影响保存速度
		 return $result;
	 }
	 
	 function getFormatList($pid = 0)
	 {
		$result = $this->selectAll("`year` = '".$this->year."' ",'ORDER BY orders ASC')->toArray();
		foreach((array)$result as $row){
			$num = count($tmpData[$row['parent_id']]);
			$tmpData[$row['parent_id']][$num] = $row;
		}
		if (count($tmpData) == 0) return;
		$showData = array();
		$this->getFormatList_c($tmpData, $showData, $pid, '',1);
		return $showData;
	 }
	 
	 function getFormatList_c(&$tmpData, &$showData, $pid, $headstr = '', $level = 1, $left = 1)
	 {
		$num = count($tmpData[$pid]);
		$i = 0;
		if(!is_array($tmpData[$pid])) return false;
		foreach ($tmpData[$pid] as $key => $val)
		{
		  	$id     = $val['id'];
		  	$tmplen = count($showData);
		  	$showData[$tmplen] = $val;
		  	$showData[$tmplen]['lft'] = $left;
		  	$right = $left + 1;
	
		  	if (!empty($headstr)) $showData[$tmplen]['HeadStr'] = $headstr;
	
		  	if ($i == $num-1){
				$showData[$tmplen]['HeadStr'] .= '└';
				$headstr_1 =  $headstr."&nbsp;&nbsp;";
				$level_1   =  $level + 1;
		  	}else{
				$showData[$tmplen]['HeadStr'] .= '├';
				$headstr_1 = $headstr.'│';
				$level_1   =  $level + 1;
		  	}
		  	$showData[$tmplen]['level'] = $level;
		  	$i++;
	
		  	if ((count($tmpData[$id]) > 0)) $right = $this->getFormatList_c($tmpData, $showData, $id, $headstr_1, $level_1, $right);
				
		  	$showData[$tmplen]['rgt'] = $right;
		  	$left = $right + 1;
		}
		return $right + 1;
	 }
	 
	 function remove($addWhere = '')
	 {
		 $result = $this->selectSonTree($this);
		 while($object = $result->getObject()){
			 $object->delete();
		 }
		 return $this->delete();
	 }
	 
	 function selectSonTree($object = NULL)
	 {
		 if($object === NULL) $object = & $this;
		 else if(!is_object($object)){
			$this->selectByPk((int)$object);
			$object = & $this;
		}
		 return $this->selectAll("`lft` > '".$object->getLft()."' AND `rgt` < '".$object->getRgt()."'",'ORDER BY `lft` ASC');
	 }
	
	/**
	 * 将指南复制到指定的ID下面
	 */ 
	function copyto($to=0)
	{
		if($this->isNew()) return false;
		//整理复制内容
		$pager = $this->selectAll("`lft` > '".$this->getLft()."' AND `rgt` < '".$this->getRgt()."'",'ORDER BY `lft` ASC');
		$from = array();
		while($g = $pager->getObject())
		{
			$_a = array();
			$_a['id'] = $g->getId();
			$_a['subject'] = $g->getSubject();
			$_a['project_name'] = $g->getProjectName();
			$_a['mark'] = $g->getMark();
			$_a['is_special'] = $g->getIsSpecial();
			$_a['district'] = $g->getDistrict();
			$_a['department_ids'] = $g->getDepartmentIdArr();
			$_a['start_year'] = $g->getStartYear();
			$_a['end_year'] = $g->getEndYear();
			$_a['user_level'] = $g->getUserLevel();
			$_a['declare_map_id'] = $g->getDeclareMapId();
			$_a['task_map_id'] = $g->getTaskMapId();
			$_a['assess_rule_id'] = $g->getAssessRuleId();
			$_a['content'] = $g->getContent();
			$_a['year'] = $g->getYear();
			$_a['current_group'] = $g->getCurrentGroup();
			$_a['parent_id'] = $g->getParentId();
			$_a['orders'] = $g->getOrders();
			$from[] = $_a;
		}
		//开始复制
		$from_to_array = array();
		$from_to_array[$this->getId()] = $to;
		for($i=0,$n=count($from);$i<$n;$i++)
		{
			$g = sf::getModel("Guides",0,$from[$i]['year'])->selectByGuideId('');
			$g->setSubject($from[$i]['subject']);
			$g->setProjectName($from[$i]['project_name']);
			$g->setMark($from[$i]['mark']);
			$g->setIsSpecial($from[$i]['is_special']);
			$g->setDistrict($from[$i]['district']);
			$g->setDepartmentIds($from[$i]['department_ids']);
			$g->setStartYear($from[$i]['start_year']);
			$g->setEndYear($from[$i]['end_year']);
			$g->setUserLevel($from[$i]['user_level']);
			$g->setDeclareMapId($from[$i]['declare_map_id']);
			$g->setTaskMapId($from[$i]['task_map_id']);
			$g->setAssessId($from[$i]['assess_id']);
			$g->setContent($from[$i]['content']);
			$g->setYear($from[$i]['year']);
			$g->setCurrentGroup($from[$i]['current_group']);
			$g->setParentId($from_to_array[$from[$i]['parent_id']]);
			$g->setOrders($from[$i]['orders']);
			$g->save();
			$from_to_array[$from[$i]['id']] = $g->getId();
		}
		return true;	 
	}
	 
	 function getPathTree($object)
	 {
		 if(!is_object($object)){
			$this->selectByPk((int)$object);
			$object = & $this;
		}
		 return $this->selectAll("`lft` <= '".$object->getLft()."' AND `rgt` >= '".$object->getRgt()."' and `year` = '".$object->getYear()."'",'ORDER BY `lft` ASC');
	 }
	
	/**
	 * 取得同级对象
	 */
	function getSiblings($user_level = '-1',$is_active=false)
	{
		$addWhere = "`lft` > '".$this->getLft()."' AND `rgt` < '".$this->getRgt()."' and level = '".($this->getLevel()+1)."' ";
		if($user_level > 0) $addWhere .= " AND user_level = '".$user_level."' ";
		if($is_active && !isTester()) $addWhere .= " AND DATE(end_at) >= '".date("Y-m-d")."' ";
		return $this->selectAll($addWhere,'ORDER BY `lft` ASC'); 
	}
	 
	function selectByGuideId($guideid)
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `guideid` = '".$guideid."' ");
		if($db->num_rows($query)){
			$this->fillObject($db->fetch_array($query));
			$this->year = $this->getYear();
		}else $this->setGuideid($guideid ? $guideid : sf::getLib("MyString")->getRandString());
		return $this;	
	}

	function selectByMark($mark)
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `mark` = '".$mark."' ");
		if($db->num_rows($query)){
			$this->fillObject($db->fetch_array($query));
			$this->year = $this->getYear();
		}else {
            $this->setGuideid(sf::getLib("MyString")->getRandString());
            $this->setMark($mark);
        }
		return $this;
	}

	function selectLastSummaryGuide($catId)
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `cat_id` = '".$catId."' and `type` = 'summary' ORDER BY `id` DESC LIMIT 1");
		if($db->num_rows($query)){
			$this->fillObject($db->fetch_array($query));
		}else {
            $this->setGuideid(sf::getLib("MyString")->getRandString());
        }
		return $this;
	}

	function setAllowGrade($v)
	{
		if(is_array($v)) parent::setAllowGrade(implode('|',$v));
		else parent::setAllowGrade($v);
	}

    function setDistrict($v)
    {
        if(is_array($v)) parent::setDistrict(implode('|',$v));
        else parent::setDistrict($v);
    }

    function setDepartmentIds($v)
    {
        if(is_array($v)) parent::setDepartmentIds(implode('|',$v));
        else parent::setDepartmentIds($v);
    }
	
	function getAllowGrade($num=0)
	{
		$result = explode('|',parent::getAllowGrade());
		if($num > 0) return $result[($num - 1)];
		else return $result;	
	}

	function getDistrict($num=0)
	{
		$result = explode('|',parent::getDistrict());
		if($num > 0) return $result[($num - 1)];
		else return $result;
	}

	function getDepartmentIdArr($num=0)
	{
		$result = explode('|',parent::getDepartmentIds());
		if($num > 0) return $result[($num - 1)];
		else return $result;
	}

	function getDistrictStr()
	{
		$result = explode('|',parent::getDistrict());
        $result = array_filter($result);
		if(!$result) return '不限';
		return implode('、',$result);
	}

    function getApplyRange()
    {
        $range = [];
        $districts = $this->getDistrict();
        foreach ($districts as $district){
            $range[] = $district;
        }
        $departmentIds = $this->getDepartmentIdArr();
        foreach ($departmentIds as $departmentId){
            $department = sf::getModel('Departments')->selectByUserId($departmentId);
            $range[] = $department->getShortName();
        }
        $range = array_filter($range);
        if(!$range) return '不限';
        return implode('、',$range);
    }
	
	function setAllowMaterial($v)
	{
		if(is_array($v)) parent::setAllowMaterial(implode('|',$v));
		else parent::setAllowMaterial($v);
	}
	
	function getAllowMaterial($num=0)
	{
		$result = explode('|',parent::getAllowMaterial());
		if($num > 0) return $result[($num - 1)];
		else return $result;	
	}
	
	function setAllowCompany($v)
	{
		if(is_array($v)) parent::setAllowCompany(implode('|',$v));
		else parent::setAllowCompany($v);
	}
	
	function getAllowCompany($num=0)
	{
		if(!parent::getAllowCompany()) return array();
		$result = explode('|',parent::getAllowCompany());
		if($num > 0) return $result[($num - 1)];
		else return $result;	
	}
	
	function setProperty($v)
	{
		if(is_array($v)) parent::setProperty(implode('|',$v));
		else parent::setProperty($v);
	}
	
	function getProperty($num=0)
	{
		$result = explode('|',parent::getProperty());
		if($num > 0) return $result[($num - 1)];
		else return $result;	
	}
	
	function setSubjectIds($v)
	{
		if(is_array($v)) parent::setSubjectIds(implode('|',$v));
		else parent::setSubjectIds($v);
	}
	
	function getSubjectIds($num=0)
	{
		$result = explode('|',parent::getSubjectIds());
		if($num > 0) return $result[($num - 1)];
		else return $result;	
	}
	
	function setIndustryIds($v)
	{
		if(is_array($v)) parent::setIndustryIds(implode('|',$v));
		else parent::setIndustryIds($v);
	}
	
	function getIndustryIds($num=0)
	{
		$result = explode('|',parent::getIndustryIds());
		if($num > 0) return $result[($num - 1)];
		else return $result;	
	}
	
	/**
	 * 取得路径名称
	 */
	function getPathName($div=' > ')
	{
		$pager = $this->getPathTree($this);
		while($tree = $pager->getObject()){
			$_tree[] = $tree->getSubject();	
		}
		return implode($div,$_tree);
	}
	
	/**
	 * 设置路径上的所有节点某一属性
	 */
	function setPathAttribute($attrs=array())
	{
		if($this->isNew()) return false;
		$_data = array();
		$_attr = ['year','current_group','start_at','end_at','gather_end_at','declare_map_id','task_map_id','budget_map_id','complete_map_id','assess_id','budget_path','task_path','complete_path','assess_path','office_id','allow_grade','allow_company','property','month_min','month_max','user_level','skill_branch','skill_id','configs'];
		foreach($attrs as $_key => $_val){
			if(in_array($_key,$_attr))
				$_data[$_key] = $_val;
		}
		if(count($_data)==0) return false;
		return sf::getLib("db")->update($_data,"year = '".$this->year."' AND lft < '".$this->getLft()."' AND `rgt` > '".$this->getRgt()."'",$this->table);
	}
	
	/**
	 * 设置子树的所有节点某一属性
	 */
	function setTreeAttribute($attrs=array())
	{
		if($this->isNew()) return false;
		$_data = array();
		$_attr = ['year','current_group','start_at','end_at','project_start_at','project_end_at','gather_end_at','declare_map_id','task_map_id','budget_map_id','complete_map_id','assess_id','budget_path','task_path','complete_path','assess_path','office_id','allow_grade','allow_company','property','month_min','month_max','user_level','skill_branch','skill_id','district','department_ids','configs'];
		foreach($attrs as $_key => $_val){
			if(in_array($_key,$_attr))
				$_data[$_key] = $_val;
		}
		if(count($_data)==0) return false;
		return sf::getLib("db")->update($_data,"year = '".$this->year."' AND lft > '".$this->getLft()."' AND rgt < '".$this->getRgt()."'",$this->table);
	}
	
	
	function getFromName()
	{
		return sf::getModel("Types",$this->getDeclareMapId())->getSubject();
	}

    function getExceptionCompanys()
    {
        $exceptions = sf::getModel('GuideExceptions')->selectAll("guide_id = '".$this->getId()."' and unix_timestamp(start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') AND unix_timestamp(end_at) > unix_timestamp('".date("Y-m-d H:i:s")."')");
        $companyNames = [];
        while($exception = $exceptions->getObject()){
            $companyNames[] = $exception->getCompanyName();
        }
        return $companyNames;
    }
	
	/**
	 * 验证指南是否符合
	 */
	function validate()
	{
		$message = array();
        $user = sf::getModel('Declarers')->selectByUserId(input::session('roleuserid'));
        if($user->getIsTest()==1) return $message;
        if(in_array($user->getCorporationName(),$this->getExceptionCompanys())!==false) return $message;
		if(strtotime($this->getStartAt()) > time()) $message[] = '申报时间还未开始！';
		if(strtotime($this->getEndAt()) < time()) $message[] = '申报已经截止！';
		return $message;
	}

	/**
	 * 验证指南是否符合
	 */
	function getState()
	{
		if(strtotime($this->getStartAt()) > time()) {
		    return '<span class="badge badge-warning font-size-base">申报通道还未开启</span>';
        }
		if(strtotime($this->getEndAt()) < time()){
		    return '<span class="badge badge-danger font-size-base">申报通道已关闭</span>';
        }
        return '<span class="badge badge-success font-size-base">已开始申报</span>';
	}

    /**
     * 获取指南所有子类下的项目数量
     * @param string $addwhere
     * @return mixed
     */
	public function getProjectCount($addwhere ='')
    {
        $db = sf::getLib('db');
        if($this->getIsLastnode() || !$this->getLft() || !$this->getRgt()){
            $sql = "select count(*) c from projects WHERE guide_id = ".$this->getId()." {$addwhere}";
        }else{
            $sql = "select count(*) c from projects WHERE guide_id IN (SELECT id FROM guides WHERE `lft`>=".$this->getLft()." and `rgt`<=".$this->getRgt()." and `year` = ".$this->getYear().") {$addwhere}";
        }
        return $db->result_first($sql);
    }
	
	/**
	 * 判断是否为最后一级
	 */
	function getIsLastnode($len=0)
	{
		if($this->getRgt() - $this->getLft() == 1) return true;
		else return false; 	
	}

    /**
     * 获取指定指南id下的所有子类
     * @param int $guide_id
     * @param bool $is_top
     * @param array $son_id
     * @return array|mixed
     */
    function getChildren()
    {
        if(false && $cache = sf::getLib('cache',APPPATH.'../cache/guide/',3600*24*30)->getCache('children_'.$this->getId())){
            return json_decode($cache,true);
        }else{
            $son_id = [];
            if($this->getIsLastnode() || !$this->getLft() || !$this->getRgt()){
                $son_id[] = $this->getId();
            }else{
                $db = sf::getLib('db');
                $sql = "SELECT id FROM guides WHERE `lft`>=".$this->getLft()." and `rgt`<=".$this->getRgt()." and `year` = '".$this->getYear()."' ";
                $result = $db->result_array($db->query($sql));
                $son_id = array_column($result,'id');
            }
            sf::getLib('cache',APPPATH.'../cache/guide/',3600*24*30)->setCache('children_'.$this->getId(),json_encode($son_id));
            return $son_id;
        }
    }
	
	function hasSon()
	{
		if($this->getRgt() - $this->getLft() > 1) return true;
		else return false;
	}
	
	function hasParent()
	{
		if($this->getLevel() > 1) return true;
		else return false;
	}
	
	/**
	 * 单位是否满足条件，暂时不用
	 *
	 */
	function allowCompany()
	{
		return true;
	}
	
	/**                                                         
	 * 取得评审指标
	 */
	function getAssessUrl($url='',$type='index')
	{
		if($assess_path = $this->getAssessPath()) return site_url($assess_path.'/'.$type.'/'.trim($url,'/'));
		else return false;
	}
	
	/**                                                         
	 * 取得预算书路径
	 */
	function getApplyUrl($url='',$type='index')
	{
		if($apply_path = $this->getApplyPath()) return site_url($apply_path.'/'.$type.'/'.trim($url,'/'));	
		else return site_url("engine/worker/".$type."/".trim($url,'/'));	
	}
	
	/**                                                         
	 * 取得预算书路径
	 */
	function getBudgetUrl($url='',$type='index')
	{
		if($budget_path = $this->getBudgetPath()) return site_url($budget_path.'/'.$type.'/'.trim($url,'/'));	
		else return site_url("apply/bzdyf2019/".$type."/".trim($url,'/'));	
	}
	
	/**                                                         
	 * 取得任务书路径
	 */
	function getTaskUrl($url='',$type='index')
	{
		if($task_path = $this->getTaskPath()) return site_url($task_path.'/'.$type.'/'.trim($url,'/'));
		else return false;
	}

	/**
	 * 取得中期报告路径
	 */
	function getStageUrl($url='',$type='index')
	{
		if($stagePath = $this->getStagePath()) return site_url($stagePath.'/'.$type.'/'.trim($url,'/'));
		else return false;
	}
	
	/**                                                         
	 * 取得验收报告路径
	 */
	function getCompleteUrl($url='',$type='index')
	{
		if($complete_path = $this->getCompletePath()) return site_url($complete_path.'/'.$type.'/'.trim($url,'/'));	
		else return site_url("engine/completer/".$type."/".trim($url,'/'));
	}
	
	/**
	 * 项目结束日期
	 */
	function getProjectEndAt($format='')
	{
		//没有区间就直接返回设置的结束时间
		if(parent::getProjectEndAt() && $this->getMonthMin() == 0 && $this->getMonthMax() == 0) return parent::getProjectEndAt();
		//返回最小时间
		if($this->getMonthMin() > 0) return date("Y-m-d",strtotime("+".$this->getMonthMin()." month",(strtotime($this->getProjectStartAt())-3600*24)));
		//返回最小时间
		else if($this->getMonthMin() == 0 && $this->getMonthMax() > 0) return date("Y-m-d",strtotime("+".$this->getMonthMax()." month",(strtotime($this->getProjectStartAt())-3600*24)));
		else return '';
	}
	
	/**
	 * 项目结束日期
	 */
	function getProjectEndRange()
	{
		if($this->getMonthMin() > 0 && $this->getMonthMax() > 0) return date("Y-m-d",strtotime("+".$this->getMonthMin()." month",(strtotime($this->getProjectStartAt())-3600*24))).'~'.date("Y-m-d",strtotime("+".$this->getMonthMax()." month",(strtotime($this->getProjectStartAt())-3600*24)));
		else if($this->getMonthMin() > 0 && $this->getMonthMax() == 0) return "时间应晚于".date("Y-m-d",strtotime("+".$this->getMonthMin()." month",(strtotime($this->getProjectStartAt())-3600*24)));
		else if($this->getMonthMax() > 0 && $this->getMonthMin() == 0) return "时间应早于".date("Y-m-d",strtotime("+".$this->getMonthMax()." month",(strtotime($this->getProjectStartAt())-3600*24)));
		else return parent::getProjectEndAt();
	}
	
	/**
	 * 是否为区间
	 */
	function isRange()
	{
		if($this->getMonthMin() > 0 || $this->getMonthMax() > 0) return true;
		else return false;
	}

	/**
	 * 检查结束时间是否正确
	 */
	function checkEndAt($end_at)
	{
		$_end_time = strtotime($end_at);
		if($this->getMonthMin() || $this->getMonthMax()){
			//判断最小值
			if($this->getMonthMin()){
				$_min_time = strtotime("+".$this->getMonthMin()." month",(strtotime($this->getProjectStartAt())-3600*24));
				if($_end_time < $_min_time) return false;
			}

			if($this->getMonthMax()){
				$_max_time = strtotime("+".$this->getMonthMax()." month",(strtotime($this->getProjectStartAt())-3600*24));
				if($_end_time > $_max_time) return false;
			}
		}else if(parent::getProjectEndAt()){
			if(parent::getProjectEndAt() != $end_at) return false;
		}

		return true;
	}
	
	/**
	 * 是否过期
	 */
	function isLost()
	{
		if(strtotime($this->getEndAt()) < time() || strtotime($this->getEndAt()) <= strtotime($this->getStartAt())) return true;
		else return false;	
	}
	
	/**
	 * 子类个数
	 */
	function getChildCount()
	{
		return ($this->getRgt() - $this->getLft() - 1)/2;
	}


	function getNewOrders()
	{
        if($this->isNew()) return 1;
		$count = (int)sf::getLib('db')->result_first("select count(*) c from guides where parent_id = '".$this->getId()."'");
        return $count+1;
	}
	
	/**
	 * 统计信息
	 */
	function getGuideInfo()
	{
		return "开始时间：".$this->getStartAt("Y年m月d日 H时")." <br>截止时间：".$this->getEndAt("Y年m月d日 H时");
	}
	
	/**
	 * 返回申报主体
	 */
	function getUserRole()
	{
		if($this->getUserLevel() == 3) return "申报单位";	
		else return "项目负责人";
	}
	
	/**
	 * 设置配置信息
	 */
	function setConfigs($key,$val=array())
	{
		$configs = array();
		$configs = $this->getConfigs();
		$configs[$key] = $val;
		return parent::setConfigs(json_encode($configs,JSON_UNESCAPED_UNICODE));
	}
	
	/**
	 * 获取配置信息
	 */
	function getConfigs($key=NULL)
	{
		if($key == 'apply') $key = NULL;
		$configs = array();
		if($_json = parent::getConfigs()){
			$configs = json_decode($_json,true);
		}else{
			$configs = array();	
		}
		if($key) return $configs[$key];
		else return $configs;	
	}

	function getSkillBranch($key='')
	{
		return explode("|",parent::getSkillBranch());	
	}
	
	function setSkillBranch($v)
	{
		if(is_array($v)) $v = implode('|',$v);
		return parent::setSkillBranch($v);
	}
	
	/**
	 * 是否是候补项目
	 */
	function isSubsidy()
	{
		$property = $this->getProperty();
		if(in_array('is_subsidy',$property)) return true;
		else return false;
	}
	
	/**
	 * 是否是转移支付项目
	 */
	function isShift()
	{
		$property = $this->getProperty();
		if(in_array('is_shift',$property)) return true;
		else return false;
	}
	
	/**
	 * 是否是软件类项目
	 */
	function isSoftware()
	{
		$property = $this->getProperty();
		if(in_array('is_software',$property)) return true;
		else return false;	
	}
	
	/**
	 * 单位是否限项
	 */
	function isCompanyLimit()
	{
		$property = $this->getProperty();
		if(in_array('company_limit',$property)) return true;
		else return false;	
	}
	
	/**
	 * 检查单位是否超出限制
	 */
	function checkCompanyLimit($company_id,$user_id='')
	{
		if(!$this->getRuleId()) return false;
		$msg = array();
		$db = sf::getLib("Db");
		//不限制的人员直接上报
		//$row = $db->fetch_first("select user_id from user_filters where user_id = '".$user_id."' ");
		//if(strlen($row['user_id']) >30) return false;
		//判断单位信息
		switch($this->getRuleId())
		{
			case 1://应用基础
				$row = $db->fetch_first("SELECT count(*) as number FROM projects WHERE corporation_id = '".$company_id."' and user_id not in (select user_id from user_filters where user_id <> '') and statement > 3 and guide_id IN (select id from guides where year='".$this->getYear()."' and rule_id = 1) ");
				if($row['number'] >= 5){//一般单位不超过5项目
					//看看是否有增量
					$_row = $db->fetch_first("SELECT number FROM guide_controls WHERE company_id = '".$company_id."' and rule_id = 1 ");
					if($_row['number'] > 0){
						if($row['number'] >= $_row['number']) $msg[] = '申报数量已经超过'.$_row['number'].'项';
						else return false;
					}else $msg[] = '申报数量已经超过5项';
					return $msg;
				}
			break;
			case 2://青年基金
				$row = $db->fetch_first("SELECT count(*) as number FROM projects WHERE corporation_id = '".$company_id."' and user_id not in (select user_id from user_filters where user_id <> '') and statement > 3 and guide_id IN (select id from guides where year='".$this->getYear()."' and rule_id = 2) ");
				if($row['number'] >= 3){
					//看看是否有增量
					$_row = $db->fetch_first("SELECT number FROM guide_controls WHERE company_id = '".$company_id."' and rule_id = 2 ");
					if($_row['number'] > 0){
						if($row['number'] >= $_row['number']) $msg[] = '申报数量已经超过'.$_row['number'].'项';
						else return false;
					}else $msg[] = '申报数量已经超过3项';
					return $msg;
				}
			break;
			case 3://软科学
				$row = $db->fetch_first("SELECT count(*) as number FROM projects WHERE corporation_id = '".$company_id."' and statement > 3 and guide_id IN (select id from guides where year='".$this->getYear()."' and rule_id = 3) ");
				if($row['number'] >= 5){
					//看看是否有增量
					$_row = $db->fetch_first("SELECT number FROM guide_controls WHERE company_id = '".$company_id."' and rule_id = 3 ");
					if($_row['number'] > 0){
						if($row['number'] >= $_row['number']) $msg[] = '申报数量已经超过'.$_row['number'].'项';
						else return false;
					}else $msg[] = '申报数量已经超过5项';
					return $msg;
				}
			break;
			case 4://创新人才
				$row = $db->fetch_first("SELECT count(*) as number FROM projects WHERE corporation_id = '".$company_id."' and statement > 3 and guide_id IN (select id from guides where year='".$this->getYear()."' and rule_id = 4) ");
				if($row['number'] >= 2){
					$company = sf::getModel("Corporations")->selectByUserId($company_id);
					if($company->isNew()) return array('申报单位信息读取错误！');
					
					if($company->isUniversity()){
						if($company->isDoubleOne()){
							if($row['number'] >= 5) array('"双一流"高校限报5个');
							else return false;	
						}else{
							if($row['number'] >= 3) return array('高校和科研院所每家限报3个');
							else return false;
						}
					}else if(!$company->isBusiness()){
						if($row['number'] >= 3) return array('高校和科研院所每家限报3个');
						else return false;	
					}else return array('企业每家限报2个');
				}else return false;
			break;
			case 5://创业人才
				$row = $db->fetch_first("SELECT count(*) as number FROM projects WHERE corporation_id = '".$company_id."' and statement > 3 and guide_id IN (select id from guides where year='".$this->getYear()."' and rule_id = 5) ");
				if($row['number'] >= 1) return array('申报数量已经超过1项');
				else return false;
			break;
			case 6://苗子工程
				$row = $db->fetch_first("SELECT count(*) as number FROM projects WHERE corporation_id = '".$company_id."' and statement > 3 and guide_id IN (select id from guides where year='".$this->getYear()."' and rule_id = 6) ");
				if($row['number'] >= 1){
					$company = sf::getModel("Corporations")->selectByUserId($company_id);
					if($company->isNew()) return array('申报单位信息读取错误！');
					
					if($company->isUniversity()){
						if($company->isDoubleOne()){
							if($row['number'] >= 10) array('"双一流"高校限报10个');
							else return false;	
						}else{
							if($row['number'] >= 5) return array('高校和科研院所每家限报5个');
							else return false;
						}
					}else if(!$company->isBusiness()){
						if($row['number'] >= 5) return array('高校、科研院所和事业单位每家限报5个');
						else return false;
					}else return array('企业每家限报1个');
				}else return false;
			break;
			default:
				return false;
			break;	
		}
		return false;
	}
	
	/**
	 * 返回技术领域名称
	 */
	function getSkillName()
	{
		if(!$this->getSkillId()) return '未设置';
		return sf::getModel("Subjects")->selectByCode($this->getSkillId(),4)->getSubject();	
	}
	
	/**
	 * 返回学科方向名称
	 */
	function getSubjectName()
	{
		if(!$this->getSubjectId()) return '未设置';
		return sf::getModel("Subjects")->selectByCode($this->getSubjectId(),1)->getSubject();	
	}
	
	/**
	 * 返回分管处室名称
	 */
	function getOfficeName()
	{
		if(!$this->getOfficeId()) return '未设置';
		return sf::getModel("Categorys",$this->getOfficeId(),'office')->getSubject();	
	}
	
	/**
	 * 返回评审表名称
	 */
	function getAssessName()
	{
		if(!$this->getAssessRuleId()) return '未设置';
		return sf::getModel("Rules",$this->getAssessRuleId())->getSubject();
	}
	
	/**
	 * 路径以前的指南内容
	 */
	function getPathContent()
	{
		if($this->isNew()) return '';
		if($this->getContent()) return $this->getContent();
		$htmlStr = '';
		$pager = $this->selectAll("`lft` <= '".$this->getLft()."' AND `rgt` >= '".$this->getRgt()."'",'ORDER BY `lft` ASC');
		while($g = $pager->getObject()){
			if($g->getId() == $this->getId()) $htmlStr .= '<h2 style="color:red;">'.$g->getSubject().'(当前指南)</h2>';
			else $htmlStr .= '<h2>'.$g->getSubject().'</h2>';
			$htmlStr .= $g->getContent();	
		}
		return $htmlStr;	
	}

	/**
	 * 指南配置概要
	 */
	function getOutline()
	{
		$configs = $this->getConfigs();
		$htmlStr = '标记：'.$this->getMark();
		$htmlStr .= '<br />平台类别：'.$this->getCatSubject();
		$htmlStr .= '<br />填报周期：'.$this->getStartAt().'到'.$this->getEndAt();
		$htmlStr .= '<br />主管部门审核截止日期：'.$this->getGatherEndAt();
		return $htmlStr;
	}

    public function getCatSubject()
    {
        return sf::getModel('Categorys',parent::getCatId())->getSubject();
    }

    function checkMarkRepeat($mark,$pid=0)
    {
        $count = (int)sf::getLib('db')->result_first("select count(*) c from guides where mark = '{$mark}' and id!='".$this->getId()."' and parent_id = {$pid}");
        return $count==0;
    }
	
	/**
	 * 获取指南编制专家（排除人员）清单
	 */
	function getForbiddenList()
	{
		if($this->isNew()) return array();
		$pager = $this->selectAll("`lft` <= '".$this->getLft()."' AND `rgt` >= '".$this->getRgt()."'",'ORDER BY `lft` DESC');
		while($tree = $pager->getObject()){
			$configs = $tree->getConfigs('researcher');
			if($configs['validate']['forbidden']) return explode('|',$configs['validate']['forbidden']);
		}
		return array();
	}
	
	/**
	 * 是否为指南编辑人员
	 */
	function isForbidden($idcard='')
	{
		if($this->forbidden_list === NULL) $this->forbidden_list = $this->getForbiddenList();
		if(in_array(trim($idcard),$this->forbidden_list)) return true;
		else return false;
	}

	function getParent()
    {
        return sf::getModel('Guides',$this->getParentId());
    }

//    function getType()
//    {
//        return $this->getIsSpecial() ? '特色专科' : '普惠专科';
//    }


    function getDiseasesCount()
    {
        return (int) sf::getLib('db')->result_first("select count(*) c from diseases where subject_code = '".$this->getMark()."'");
    }

    function getForbidCatIdArr()
    {
        if(empty(parent::getForbidCatId())) return [];
        return explode(',',parent::getForbidCatId());
    }

    /**
     * 获取评审指标
     */
    function getRule()
    {
        if (parent::getAssessRuleId()) {
            return sf::getModel("Rules", parent::getAssessRuleId());
        }
        return sf::getModel("Rules")->selectByCode($this->getMark());
    }

    /**
     * 获取顶级指南
     * @return void
     */
    function getTop()
    {
        if($this->getParentId()==0) return $this;
        return sf::getModel('Guides',$this->getParentId());
    }

    public function getIndexYears()
    {
        $startYear = $this->getStartYear();
        $endYear = $this->getEndYear();
        $years = [];
        for($year = $startYear;$year<=$endYear;$year++){
            $years[] = (int)$year;
        }
        return $years;
    }
}