<?php
namespace App\Model;
use Sofast\Core\config;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use App\Model\BaseDepartments;
use App\Lib\Button;

class Departments extends BaseDepartments
{
	private $user = NULL;
	
	function selectByUserId($user_id='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_id` = '".$user_id."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else $this->setUserId(sf::getLib("MyString")->getRandString());
		return $this;
	}

    function selectBySubject($subject = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `subject` = '" . $subject . "' ");

        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setSubject($subject);
        return $this;
    }
	
	function selectById($department_id='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `id` = '".$department_id."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else $this->setUserId(sf::getLib("MyString")->getRandString());
		return $this;
	}
	
	function getTypeName()
	{
		return sf::getModel("categorys",parent::getType(),'department')->getSubject();
	}
	
	function getUser($f=false)
	{
	    $users = $this->users();
	    if($users->getTotal()>0){
            $user = $users->getObject();
            if($this->user === NULL || $f) $this->user = sf::getModel("Users")->selectByUserId($user->getUserId());
            return $this->user;
        }
	    return sf::getModel("Users")->selectByUserId(parent::getUserId());

	}
	
	function users()
	{
		return sf::getModel("Users")->selectAll("user_id IN (select user_id from user_roles where user_role_id = '".parent::getUserId()."')");	
	}
	
	public function remove($ids='')
	{
		return false;//注销该方法
		return parent::remove("user_id in('".$ids."')");
	}
	
	/**
	 * 取得剩余的配额
	 */
	public function hasQuota()
	{
		return parent::getQuota() - parent::getUseQuota();
	}
	/**
	*等待审核的项目数
	*/
	function getWaitNum()
	{
		return sf::getModel("projects")->selectAll("department_id = '".$this->getUserId()."' and statement = '".config::get("GATHER_WAIT")."'")->getTotal();
		
	}
	/**
	*待立项的市县级项目数
	*/
	function getCityWaitNum()
	{
		return sf::getModel("projects")->selectAll("department_id = '".$this->getUserId()."' and statement = '20' and cat_id in (225,231)")->getTotal();

	}

    /**
     *已立项的项目数
     */
    function getRadicateNum($catId,$radicateYear='')
    {
        $addwhere = "department_id = '".$this->getUserId()."' and statement IN (29,30) and cat_id = {$catId}";
        if($radicateYear && is_array($radicateYear))  {
            $years = [];
            foreach ($radicateYear as $y){
                $years[] = $y;
            }
            $addwhere .= " and radicate_year IN (".implode(',',$years).") ";
        }elseif($radicateYear){
            $addwhere .= " and radicate_year = '{$radicateYear}'";
        }
        return sf::getModel("projects")->selectAll($addwhere)->getTotal();
    }

    /**
     *已立项的市级项目数
     */
    function getCityRadicateNum($radicateYear='')
    {
        $addwhere = "department_id = '".$this->getUserId()."' and statement IN (29,30) and cat_id = 225";
        if($radicateYear && is_array($radicateYear))  {
            $years = [];
            foreach ($radicateYear as $y){
                $years[] = $y;
            }
            $addwhere .= " and radicate_year IN (".implode(',',$years).") ";
        }elseif($radicateYear){
            $addwhere .= " and radicate_year = '{$radicateYear}'";
        }
        return sf::getModel("projects")->selectAll($addwhere)->getTotal();
    }

    /**
     *已立项的县级项目数
     */
    function getCountryRadicateNum($radicateYear='')
    {
        $addwhere = "department_id = '".$this->getUserId()."' and statement IN (29,30) and cat_id = 231";
        if($radicateYear && is_array($radicateYear))  {
            $years = [];
            foreach ($radicateYear as $y){
                $years[] = $y;
            }
            $addwhere .= " and radicate_year IN (".implode(',',$years).") ";
        }elseif($radicateYear){
            $addwhere .= " and radicate_year = '{$radicateYear}'";
        }
        return sf::getModel("projects")->selectAll($addwhere)->getTotal();
    }
	
	/**
	*等待审核的项目数
	*/
	function getTaskWaitnum()
	{
		return sf::getModel("projects")->selectAll("department_id = '".$this->getUserId()."' and state_for_plan_book = '4'")->getTotal();
	}

	/**
	*等待审核的年度考核数
	*/
	function getSummaryWaitnum()
	{
		return sf::getModel("Summarys")->selectAll("department_id = '".$this->getUserId()."' and statement = 5")->getTotal();
	}

    /**
     *等待审核的中期报告数
     */
    function getStageWaitnum()
    {
        $addWhere = "statement = 5 and department_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Stages")->selectAll($addWhere)->getTotal();
    }

    /**
     *等待审核的年度考核数
     */
    function getReviewWaitnum()
    {
        $addWhere = "statement = 5 and department_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Reviews")->selectAll($addWhere)->getTotal();
    }

    /**
     *等待审核的季度监测数
     */
    function getReviewquarterWaitnum()
    {
        $addWhere = "statement = 5 and department_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Reviewquarters")->selectAll($addWhere)->getTotal();
    }
	
	/**
	*已经驳回的项目数
	*/
	function getRejectedNum()
	{
		return sf::getModel("projects")->selectAll("department_id = '".$this->getUserId()."' and statement = '".config::get("GATHER_BACK")."'")->getTotal();
		
	}
	/**
	*待审核的验收书数
	*/
	function getCompleteWaitnum()
	{
		return sf::getModel("projects")->selectAll("department_id = '".$this->getUserId()."' and statement = 29 and `state_for_complete_book` = 4")->getTotal();
		
	}
	/**
	*已驳回的验收书数
	*/
	function getCompleteRejectednum()
	{
		return sf::getModel("projects")->selectAll("department_id = '".$this->getUserId()."' and statement = 29 and `state_for_complete_book` = 5")->getTotal();
		
	}
	/**
	*未审核申报单位数
	*/
	function getUnitWaitnum()
	{
		return sf::getModel("corporations")->selectAll("department_id = '".$this->getUserId()."' and `is_lock` = 1")->getTotal();
		
	}
	
	function getParentName()
	{
		if(!parent::getParentId() || parent::getParentId() == parent::getUserId()) return '-';
		return sf::getModel("Departments")->selectByUserId(parent::getParentId())->getSubject();
	}
	
	function getCountyIds()
	{
		$ids = array();
		$db = sf::getLib("db");
		$query = $db->query("SELECT user_id FROM `".$this->table."` WHERE `parent_id` = '".$this->getUserId()."' ");
		while($row = $db->fetch_array($query)){
			$ids[] = $row['user_id'];
		}
		return $ids;
	}
	
	function getState()
	{
		if(parent::getIsDelete()) return '停用';
		else return '正常';	
	}
	
	/**
	 * 验证信息是否完整
	 */
	public function validate()
	{
		$message = array();
		
		if(!$this->getSubject()) 		$message[] = '归口部门名称必须填写';
		if(!$this->getLinkman()) 		$message[] = '联系人必须填写';
		if(!$this->getLinkmanPhone()) 	$message[] = '联系人电话必须填写';
		if(!$this->getLinkmanEmail()) 	$message[] = '联系人电子邮箱必须填写';
		
		return $message;
	}
	
	/**
	 * 根据权限定义操作菜单
	 */
	function getCustomButton($type='show')
	{
		$htmlStr  = Button::back();
		$htmlStr .= Button::window('查看记录',site_url("admin/history/index/id/".$this->getUserId()),'time',600,460);
		switch(input::getInput("session.userlevel"))
		{
			case 1:
				$htmlStr .= Button::window("发送短消息",site_url("message/short/edit/item_type/experts/item_id/".$this->getUserId().'/mobile/'.$this->getLinkmanMobile()),"send",400,300);
				$htmlStr .= Button::window("编辑用户",site_url("admin/corporation/edit/userid/".$this->getUserId()),"edit",600,460);
				$htmlStr .= Button::window("重置密码",site_url("admin/corporation/users/userid/".$this->getUserId()),"list",600,460);
			break;
			default:
			break;	
		}
		return $htmlStr;
	}
	
	public function decoupling($userid='')
	{
		if(!$userid) return false;
		return sf::getModel("UserRoles")->remove("user_role_id = '".$this->getUserId()."' and role_id = '4' AND user_id = '".$userid."' ");
	}
	
	public function coupling($userid='')
	{
		if(!$userid) return false;
		$userrole = sf::getModel("UserRoles")->selectByRole($userid,4);
		$userrole->setUserRoleId($this->getUserId());
		return $userrole->save();
	}
	
	/**
	 * 设置默认管理员
	 */
	public function setManager($userid='')
	{
		if(!$userid) return false;
		$user = sf::getModel("Users")->selectByUserId($userid);
		if($user->isNew()) return false;
		//更新联系人信息
		$this->setLinkman($user->getUserUsername());
		$this->setLinkmanMobile($user->getUserMobile());
		$this->setLinkmanEmail($user->getUserEmail());
		$this->setLinkmanPhone($user->getUserPhone());
		$this->setManagerUserId($user->getUserId());
		$this->setUpdatedAt(date('Y-m-d H:i:s'));
		$this->save();//保存结果
		return true;
	}
	
	public function isManager($userid='')
	{
		if(!$userid) return false;
		if($this->getManagerUserId() == $userid) return true;
		else return false;
	}
	
	public function getManagerUserId()
	{
		if(parent::getManagerUserId()) return parent::getManagerUserId();
		else return $this->getUserId();
	}
	
	public function selectUsers()
	{
		return sf::getModel("Users")->selectAll("user_id IN (SELECT user_id FROM user_roles WHERE user_role_id = '".$this->getUserId()."' AND role_id = '4')");
	}
	
	public function getDepartmentName()
	{
		return sf::getModel("departments")->selectByUserId(parent::getDepartmentId())->getSubject();
	}
	
	/**
	 * 所有没有验收的项目数
	 */
	function getCompleteAlert()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE declare_year > 2006 AND is_subsidy = 0 AND department_id = '".$this->getUserId()."' and statement = 29 and `state_for_complete_book` < 14 AND unix_timestamp(real_end_at) < unix_timestamp('".date("Y-m-d H:i:s",(time() + 2592000))."') AND type_id IN (select type_id from types_switch WHERE has_complete = 1 AND is_subsidy = 0) ");
		return $row['num'];
	}
	
	/**
	 * 没签署任务书的项目数
	 */
	function getTaskAlert()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE declare_year > 2006 AND is_subsidy = 0 AND department_id = '".$this->getUserId()."' and statement = 29 and `state_for_plan_book` < 10 AND unix_timestamp(radicate_at) < unix_timestamp('".date("Y-m-d H:i:s",time() - 5184000)."') AND type_id IN (select type_id from types_switch WHERE has_task = 1 AND is_subsidy = 0) ");
		return $row['num'];
	}
	
	/**
	 * 预警提示信息
	 */
	function getAlert()
	{
		$msg = array();
		if($cnum = $this->getCompleteAlert()) $msg[] = '系统中你的下属单位还有 <a href="'.site_url("gather/complete/alert").'" class="text-success" target="_blank">'.$cnum.'</a> 个项目还没有完成结题手续（或执行期即将结束）';
				if($tnum = $this->getTaskAlert()) $msg[] = '系统中你的下属还有 <a href="'.site_url("gather/task/alert").'" class="text-success" target="_blank">'.$tnum.'</a> 个项目立项已经超过60天，但是还没有完成任务书签订';
		return $msg;
	}

	    /**
     * 获取外地企业归口部门的department_id
     */
	function getForeignDepartmentId()
    {
        $result = $this->selectAll("`subject` = '外地企业'");
        if($result->getTotal()>0){
            return $result->getObject()->getUserId();
        }else{
            return '';
        }
    }

    function getList()
    {
        $this->selectAll("","");
    }

    function getMark()
    {
        if(input::session('userlevel')!=1){
            return '';
        }
        $html = '';
        $this->getIgnoreEndat() && $html .= '<span style="color:red;">[时]</span>';
        $this->getIgnoreCountLimit() && $html .= '<span style="color:red;">[数]</span>';
        return $html;
    }

    function checkRadicateFiles()
    {
        $count = sf::getLib('db')->result_first("select count(*) c from filemanager where item_id = '".$this->getUserId()."' and item_type = 'gather_radicate'");
        return $count>0;
    }

    function checkRadicateProjects()
    {
        $count = sf::getLib('db')->result_first("select count(*) c from projects where department_id = '".$this->getUserId()."' and cat_id = '225'");
        return $count>0;
    }

    function getShortName()
    {
        $name = parent::getSubject();
        $name = str_replace('经济信息化和商务科技局','',$name);
        $name = str_replace('经济信息化和科学技术局','',$name);
        $name = str_replace('科技和经济信息化局','',$name);
        $name = str_replace('科学技术和人才工作局','',$name);
        $name = str_replace('经济信息科学技术局','',$name);
        $name = str_replace('经济信息化和科技局','',$name);
        $name = str_replace('经济商务科学技术局','',$name);
        $name = str_replace('发展和改革局','',$name);
        $name = str_replace('发展改革与科学技术局','',$name);
        $name = str_replace('教育体育和科学技术局','',$name);
        $name = str_replace('科学技术和农业畜牧局','',$name);
        $name = str_replace('科学技术和知识产权局','',$name);
        $name = str_replace('科技和知识产权局','',$name);
        $name = str_replace('教育科技和体育局','',$name);
        $name = str_replace('教育和科学技术局','',$name);
        $name = str_replace('发展和政策研究局','',$name);
        $name = str_replace('财政金融局','',$name);
        $name = str_replace('教育科技局','',$name);
        $name = str_replace('经济发展局','',$name);
        $name = str_replace('科学技术局','',$name);
        return $name;
    }

    /**
     *等待依托单位审核的变更申请
     */
    function getChangeWaitnum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM platform_changes WHERE department_id = '".$this->getUserId()."' and statement = 4");
        return $row['num'];
    }
}
?>