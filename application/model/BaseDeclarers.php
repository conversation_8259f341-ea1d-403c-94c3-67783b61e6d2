<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseDeclarers extends BaseModel
{
	private $id;
	private $user_id;
	private $zdsys_user_id;
	private $corporation_id;
	private $corporation_name;
	private $department_id;
	private $personname;
	private $subject_code;
	private $subject_name;
	private $user_grade  = 'D';
	private $user_sex;
	private $nation;
	private $user_birthday;
	private $age;
	private $card_type  = '身份证';
	private $user_idcard;
	private $user_degree;
	private $education;
	private $title_type;
	private $title;
	private $user_occupation;
	private $user_work;
	private $foreign;
	private $subject_id;
	private $user_graduate;
	private $user_graduate_at;
	private $user_home_address;
	private $postal_code;
	private $user_phone;
	private $user_mobile;
	private $user_email;
	private $user_summary;
	private $specialty  = '';
	private $major;
	private $branch  = '';
	private $user_academichonor;
	private $is_lock  = '9';
	private $orde  = '1';
	private $field;
	private $full_time;
	private $account_validate;
	private $ignore_declare  = '0';
	private $ignore_complete  = '0';
	private $ignore_endat  = '0';
	private $deleted_at;
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	private $is_multiple  = '0';
	private $is_test  = '0';
	public $table = "declarers";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getZdsysUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->zdsys_user_id,0,$len,"utf-8");
			else return substr($this->zdsys_user_id,0,$len);
		}
		return $this->zdsys_user_id;
	}

	public function getCorporationId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->corporation_id,0,$len,"utf-8");
			else return substr($this->corporation_id,0,$len);
		}
		return $this->corporation_id;
	}

	public function getCorporationName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->corporation_name,0,$len,"utf-8");
			else return substr($this->corporation_name,0,$len);
		}
		return $this->corporation_name;
	}

	public function getDepartmentId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department_id,0,$len,"utf-8");
			else return substr($this->department_id,0,$len);
		}
		return $this->department_id;
	}

	public function getPersonname($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->personname,0,$len,"utf-8");
			else return substr($this->personname,0,$len);
		}
		return $this->personname;
	}

	public function getSubjectCode($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_code,0,$len,"utf-8");
			else return substr($this->subject_code,0,$len);
		}
		return $this->subject_code;
	}

	public function getSubjectName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_name,0,$len,"utf-8");
			else return substr($this->subject_name,0,$len);
		}
		return $this->subject_name;
	}

	public function getUserGrade($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_grade,0,$len,"utf-8");
			else return substr($this->user_grade,0,$len);
		}
		return $this->user_grade;
	}

	public function getUserSex($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_sex,0,$len,"utf-8");
			else return substr($this->user_sex,0,$len);
		}
		return $this->user_sex;
	}

	public function getNation($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->nation,0,$len,"utf-8");
			else return substr($this->nation,0,$len);
		}
		return $this->nation;
	}

	public function getUserBirthday($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_birthday,0,$len,"utf-8");
			else return substr($this->user_birthday,0,$len);
		}
		return $this->user_birthday;
	}

	public function getAge()
	{
		return $this->age;
	}

	public function getCardType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->card_type,0,$len,"utf-8");
			else return substr($this->card_type,0,$len);
		}
		return $this->card_type;
	}

	public function getUserIdcard($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_idcard,0,$len,"utf-8");
			else return substr($this->user_idcard,0,$len);
		}
		return $this->user_idcard;
	}

	public function getUserDegree($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_degree,0,$len,"utf-8");
			else return substr($this->user_degree,0,$len);
		}
		return $this->user_degree;
	}

	public function getEducation($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->education,0,$len,"utf-8");
			else return substr($this->education,0,$len);
		}
		return $this->education;
	}

	public function getTitleType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->title_type,0,$len,"utf-8");
			else return substr($this->title_type,0,$len);
		}
		return $this->title_type;
	}

	public function getTitle($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->title,0,$len,"utf-8");
			else return substr($this->title,0,$len);
		}
		return $this->title;
	}

	public function getUserOccupation($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_occupation,0,$len,"utf-8");
			else return substr($this->user_occupation,0,$len);
		}
		return $this->user_occupation;
	}

	public function getUserWork($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_work,0,$len,"utf-8");
			else return substr($this->user_work,0,$len);
		}
		return $this->user_work;
	}

	public function getForeign($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->foreign,0,$len,"utf-8");
			else return substr($this->foreign,0,$len);
		}
		return $this->foreign;
	}

	public function getSubjectId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_id,0,$len,"utf-8");
			else return substr($this->subject_id,0,$len);
		}
		return $this->subject_id;
	}

	public function getUserGraduate($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_graduate,0,$len,"utf-8");
			else return substr($this->user_graduate,0,$len);
		}
		return $this->user_graduate;
	}

	public function getUserGraduateAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_graduate_at,0,$len,"utf-8");
			else return substr($this->user_graduate_at,0,$len);
		}
		return $this->user_graduate_at;
	}

	public function getUserHomeAddress($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_home_address,0,$len,"utf-8");
			else return substr($this->user_home_address,0,$len);
		}
		return $this->user_home_address;
	}

	public function getPostalCode()
	{
		return $this->postal_code;
	}

	public function getUserPhone($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_phone,0,$len,"utf-8");
			else return substr($this->user_phone,0,$len);
		}
		return $this->user_phone;
	}

	public function getUserMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_mobile,0,$len,"utf-8");
			else return substr($this->user_mobile,0,$len);
		}
		return $this->user_mobile;
	}

	public function getUserEmail($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_email,0,$len,"utf-8");
			else return substr($this->user_email,0,$len);
		}
		return $this->user_email;
	}

	public function getUserSummary($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_summary,0,$len,"utf-8");
			else return substr($this->user_summary,0,$len);
		}
		return $this->user_summary;
	}

	public function getSpecialty($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->specialty,0,$len,"utf-8");
			else return substr($this->specialty,0,$len);
		}
		return $this->specialty;
	}

	public function getMajor($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->major,0,$len,"utf-8");
			else return substr($this->major,0,$len);
		}
		return $this->major;
	}

	public function getBranch($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->branch,0,$len,"utf-8");
			else return substr($this->branch,0,$len);
		}
		return $this->branch;
	}

	public function getUserAcademichonor($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_academichonor,0,$len,"utf-8");
			else return substr($this->user_academichonor,0,$len);
		}
		return $this->user_academichonor;
	}

	public function getIsLock()
	{
		return $this->is_lock;
	}

	public function getOrde()
	{
		return $this->orde;
	}

	public function getField($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->field,0,$len,"utf-8");
			else return substr($this->field,0,$len);
		}
		return $this->field;
	}

	public function getFullTime($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->full_time,0,$len,"utf-8");
			else return substr($this->full_time,0,$len);
		}
		return $this->full_time;
	}

	public function getAccountValidate()
	{
		return $this->account_validate;
	}

	public function getIgnoreDeclare()
	{
		return $this->ignore_declare;
	}

	public function getIgnoreComplete()
	{
		return $this->ignore_complete;
	}

	public function getIgnoreEndat()
	{
		return $this->ignore_endat;
	}

	public function getDeletedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->deleted_at));
		else return $this->deleted_at;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function getIsMultiple()
	{
		return $this->is_multiple;
	}

	public function getIsTest()
	{
		return $this->is_test;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setZdsysUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->zdsys_user_id !== $v)
		{
			$this->zdsys_user_id = $v;
			$this->fieldData["zdsys_user_id"] = $v;
		}
		return $this;

	}

	public function setCorporationId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_id !== $v)
		{
			$this->corporation_id = $v;
			$this->fieldData["corporation_id"] = $v;
		}
		return $this;

	}

	public function setCorporationName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_name !== $v)
		{
			$this->corporation_name = $v;
			$this->fieldData["corporation_name"] = $v;
		}
		return $this;

	}

	public function setDepartmentId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_id !== $v)
		{
			$this->department_id = $v;
			$this->fieldData["department_id"] = $v;
		}
		return $this;

	}

	public function setPersonname($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->personname !== $v)
		{
			$this->personname = $v;
			$this->fieldData["personname"] = $v;
		}
		return $this;

	}

	public function setSubjectCode($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_code !== $v)
		{
			$this->subject_code = $v;
			$this->fieldData["subject_code"] = $v;
		}
		return $this;

	}

	public function setSubjectName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_name !== $v)
		{
			$this->subject_name = $v;
			$this->fieldData["subject_name"] = $v;
		}
		return $this;

	}

	public function setUserGrade($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_grade !== $v)
		{
			$this->user_grade = $v;
			$this->fieldData["user_grade"] = $v;
		}
		return $this;

	}

	public function setUserSex($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_sex !== $v)
		{
			$this->user_sex = $v;
			$this->fieldData["user_sex"] = $v;
		}
		return $this;

	}

	public function setNation($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->nation !== $v)
		{
			$this->nation = $v;
			$this->fieldData["nation"] = $v;
		}
		return $this;

	}

	public function setUserBirthday($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_birthday !== $v)
		{
			$this->user_birthday = $v;
			$this->fieldData["user_birthday"] = $v;
		}
		return $this;

	}

	public function setAge($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->age !== $v)
		{
			$this->age = $v;
			$this->fieldData["age"] = $v;
		}
		return $this;

	}

	public function setCardType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->card_type !== $v)
		{
			$this->card_type = $v;
			$this->fieldData["card_type"] = $v;
		}
		return $this;

	}

	public function setUserIdcard($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_idcard !== $v)
		{
			$this->user_idcard = $v;
			$this->fieldData["user_idcard"] = $v;
		}
		return $this;

	}

	public function setUserDegree($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_degree !== $v)
		{
			$this->user_degree = $v;
			$this->fieldData["user_degree"] = $v;
		}
		return $this;

	}

	public function setEducation($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->education !== $v)
		{
			$this->education = $v;
			$this->fieldData["education"] = $v;
		}
		return $this;

	}

	public function setTitleType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->title_type !== $v)
		{
			$this->title_type = $v;
			$this->fieldData["title_type"] = $v;
		}
		return $this;

	}

	public function setTitle($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->title !== $v)
		{
			$this->title = $v;
			$this->fieldData["title"] = $v;
		}
		return $this;

	}

	public function setUserOccupation($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_occupation !== $v)
		{
			$this->user_occupation = $v;
			$this->fieldData["user_occupation"] = $v;
		}
		return $this;

	}

	public function setUserWork($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_work !== $v)
		{
			$this->user_work = $v;
			$this->fieldData["user_work"] = $v;
		}
		return $this;

	}

	public function setForeign($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->foreign !== $v)
		{
			$this->foreign = $v;
			$this->fieldData["foreign"] = $v;
		}
		return $this;

	}

	public function setSubjectId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_id !== $v)
		{
			$this->subject_id = $v;
			$this->fieldData["subject_id"] = $v;
		}
		return $this;

	}

	public function setUserGraduate($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_graduate !== $v)
		{
			$this->user_graduate = $v;
			$this->fieldData["user_graduate"] = $v;
		}
		return $this;

	}

	public function setUserGraduateAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_graduate_at !== $v)
		{
			$this->user_graduate_at = $v;
			$this->fieldData["user_graduate_at"] = $v;
		}
		return $this;

	}

	public function setUserHomeAddress($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_home_address !== $v)
		{
			$this->user_home_address = $v;
			$this->fieldData["user_home_address"] = $v;
		}
		return $this;

	}

	public function setPostalCode($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->postal_code !== $v)
		{
			$this->postal_code = $v;
			$this->fieldData["postal_code"] = $v;
		}
		return $this;

	}

	public function setUserPhone($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_phone !== $v)
		{
			$this->user_phone = $v;
			$this->fieldData["user_phone"] = $v;
		}
		return $this;

	}

	public function setUserMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_mobile !== $v)
		{
			$this->user_mobile = $v;
			$this->fieldData["user_mobile"] = $v;
		}
		return $this;

	}

	public function setUserEmail($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_email !== $v)
		{
			$this->user_email = $v;
			$this->fieldData["user_email"] = $v;
		}
		return $this;

	}

	public function setUserSummary($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_summary !== $v)
		{
			$this->user_summary = $v;
			$this->fieldData["user_summary"] = $v;
		}
		return $this;

	}

	public function setSpecialty($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->specialty !== $v)
		{
			$this->specialty = $v;
			$this->fieldData["specialty"] = $v;
		}
		return $this;

	}

	public function setMajor($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->major !== $v)
		{
			$this->major = $v;
			$this->fieldData["major"] = $v;
		}
		return $this;

	}

	public function setBranch($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->branch !== $v)
		{
			$this->branch = $v;
			$this->fieldData["branch"] = $v;
		}
		return $this;

	}

	public function setUserAcademichonor($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_academichonor !== $v)
		{
			$this->user_academichonor = $v;
			$this->fieldData["user_academichonor"] = $v;
		}
		return $this;

	}

	public function setIsLock($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_lock !== $v)
		{
			$this->is_lock = $v;
			$this->fieldData["is_lock"] = $v;
		}
		return $this;

	}

	public function setOrde($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->orde !== $v)
		{
			$this->orde = $v;
			$this->fieldData["orde"] = $v;
		}
		return $this;

	}

	public function setField($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->field !== $v)
		{
			$this->field = $v;
			$this->fieldData["field"] = $v;
		}
		return $this;

	}

	public function setFullTime($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->full_time !== $v)
		{
			$this->full_time = $v;
			$this->fieldData["full_time"] = $v;
		}
		return $this;

	}

	public function setAccountValidate($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->account_validate !== $v)
		{
			$this->account_validate = $v;
			$this->fieldData["account_validate"] = $v;
		}
		return $this;

	}

	public function setIgnoreDeclare($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->ignore_declare !== $v)
		{
			$this->ignore_declare = $v;
			$this->fieldData["ignore_declare"] = $v;
		}
		return $this;

	}

	public function setIgnoreComplete($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->ignore_complete !== $v)
		{
			$this->ignore_complete = $v;
			$this->fieldData["ignore_complete"] = $v;
		}
		return $this;

	}

	public function setIgnoreEndat($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->ignore_endat !== $v)
		{
			$this->ignore_endat = $v;
			$this->fieldData["ignore_endat"] = $v;
		}
		return $this;

	}

	public function setDeletedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->deleted_at !== $v)
		{
			$this->deleted_at = $v;
			$this->fieldData["deleted_at"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function setIsMultiple($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_multiple !== $v)
		{
			$this->is_multiple = $v;
			$this->fieldData["is_multiple"] = $v;
		}
		return $this;

	}

	public function setIsTest($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_test !== $v)
		{
			$this->is_test = $v;
			$this->fieldData["is_test"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `declarers` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"user_id" => $this->getUserId(),
			"zdsys_user_id" => $this->getZdsysUserId(),
			"corporation_id" => $this->getCorporationId(),
			"corporation_name" => $this->getCorporationName(),
			"department_id" => $this->getDepartmentId(),
			"personname" => $this->getPersonname(),
			"subject_code" => $this->getSubjectCode(),
			"subject_name" => $this->getSubjectName(),
			"user_grade" => $this->getUserGrade(),
			"user_sex" => $this->getUserSex(),
			"nation" => $this->getNation(),
			"user_birthday" => $this->getUserBirthday(),
			"age" => $this->getAge(),
			"card_type" => $this->getCardType(),
			"user_idcard" => $this->getUserIdcard(),
			"user_degree" => $this->getUserDegree(),
			"education" => $this->getEducation(),
			"title_type" => $this->getTitleType(),
			"title" => $this->getTitle(),
			"user_occupation" => $this->getUserOccupation(),
			"user_work" => $this->getUserWork(),
			"foreign" => $this->getForeign(),
			"subject_id" => $this->getSubjectId(),
			"user_graduate" => $this->getUserGraduate(),
			"user_graduate_at" => $this->getUserGraduateAt(),
			"user_home_address" => $this->getUserHomeAddress(),
			"postal_code" => $this->getPostalCode(),
			"user_phone" => $this->getUserPhone(),
			"user_mobile" => $this->getUserMobile(),
			"user_email" => $this->getUserEmail(),
			"user_summary" => $this->getUserSummary(),
			"specialty" => $this->getSpecialty(),
			"major" => $this->getMajor(),
			"branch" => $this->getBranch(),
			"user_academichonor" => $this->getUserAcademichonor(),
			"is_lock" => $this->getIsLock(),
			"orde" => $this->getOrde(),
			"field" => $this->getField(),
			"full_time" => $this->getFullTime(),
			"account_validate" => $this->getAccountValidate(),
			"ignore_declare" => $this->getIgnoreDeclare(),
			"ignore_complete" => $this->getIgnoreComplete(),
			"ignore_endat" => $this->getIgnoreEndat(),
			"deleted_at" => $this->getDeletedAt(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			"is_multiple" => $this->getIsMultiple(),
			"is_test" => $this->getIsTest(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->user_id = '';
		$this->zdsys_user_id = '';
		$this->corporation_id = '';
		$this->corporation_name = '';
		$this->department_id = '';
		$this->personname = '';
		$this->subject_code = '';
		$this->subject_name = '';
		$this->user_grade = '';
		$this->user_sex = '';
		$this->nation = '';
		$this->user_birthday = '';
		$this->age = '';
		$this->card_type = '';
		$this->user_idcard = '';
		$this->user_degree = '';
		$this->education = '';
		$this->title_type = '';
		$this->title = '';
		$this->user_occupation = '';
		$this->user_work = '';
		$this->foreign = '';
		$this->subject_id = '';
		$this->user_graduate = '';
		$this->user_graduate_at = '';
		$this->user_home_address = '';
		$this->postal_code = '';
		$this->user_phone = '';
		$this->user_mobile = '';
		$this->user_email = '';
		$this->user_summary = '';
		$this->specialty = '';
		$this->major = '';
		$this->branch = '';
		$this->user_academichonor = '';
		$this->is_lock = '';
		$this->orde = '';
		$this->field = '';
		$this->full_time = '';
		$this->account_validate = '';
		$this->ignore_declare = '';
		$this->ignore_complete = '';
		$this->ignore_endat = '';
		$this->deleted_at = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->is_multiple = '';
		$this->is_test = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["zdsys_user_id"]) && $this->zdsys_user_id = $data["zdsys_user_id"];
		isset($data["corporation_id"]) && $this->corporation_id = $data["corporation_id"];
		isset($data["corporation_name"]) && $this->corporation_name = $data["corporation_name"];
		isset($data["department_id"]) && $this->department_id = $data["department_id"];
		isset($data["personname"]) && $this->personname = $data["personname"];
		isset($data["subject_code"]) && $this->subject_code = $data["subject_code"];
		isset($data["subject_name"]) && $this->subject_name = $data["subject_name"];
		isset($data["user_grade"]) && $this->user_grade = $data["user_grade"];
		isset($data["user_sex"]) && $this->user_sex = $data["user_sex"];
		isset($data["nation"]) && $this->nation = $data["nation"];
		isset($data["user_birthday"]) && $this->user_birthday = $data["user_birthday"];
		isset($data["age"]) && $this->age = $data["age"];
		isset($data["card_type"]) && $this->card_type = $data["card_type"];
		isset($data["user_idcard"]) && $this->user_idcard = $data["user_idcard"];
		isset($data["user_degree"]) && $this->user_degree = $data["user_degree"];
		isset($data["education"]) && $this->education = $data["education"];
		isset($data["title_type"]) && $this->title_type = $data["title_type"];
		isset($data["title"]) && $this->title = $data["title"];
		isset($data["user_occupation"]) && $this->user_occupation = $data["user_occupation"];
		isset($data["user_work"]) && $this->user_work = $data["user_work"];
		isset($data["foreign"]) && $this->foreign = $data["foreign"];
		isset($data["subject_id"]) && $this->subject_id = $data["subject_id"];
		isset($data["user_graduate"]) && $this->user_graduate = $data["user_graduate"];
		isset($data["user_graduate_at"]) && $this->user_graduate_at = $data["user_graduate_at"];
		isset($data["user_home_address"]) && $this->user_home_address = $data["user_home_address"];
		isset($data["postal_code"]) && $this->postal_code = $data["postal_code"];
		isset($data["user_phone"]) && $this->user_phone = $data["user_phone"];
		isset($data["user_mobile"]) && $this->user_mobile = $data["user_mobile"];
		isset($data["user_email"]) && $this->user_email = $data["user_email"];
		isset($data["user_summary"]) && $this->user_summary = $data["user_summary"];
		isset($data["specialty"]) && $this->specialty = $data["specialty"];
		isset($data["major"]) && $this->major = $data["major"];
		isset($data["branch"]) && $this->branch = $data["branch"];
		isset($data["user_academichonor"]) && $this->user_academichonor = $data["user_academichonor"];
		isset($data["is_lock"]) && $this->is_lock = $data["is_lock"];
		isset($data["orde"]) && $this->orde = $data["orde"];
		isset($data["field"]) && $this->field = $data["field"];
		isset($data["full_time"]) && $this->full_time = $data["full_time"];
		isset($data["account_validate"]) && $this->account_validate = $data["account_validate"];
		isset($data["ignore_declare"]) && $this->ignore_declare = $data["ignore_declare"];
		isset($data["ignore_complete"]) && $this->ignore_complete = $data["ignore_complete"];
		isset($data["ignore_endat"]) && $this->ignore_endat = $data["ignore_endat"];
		isset($data["deleted_at"]) && $this->deleted_at = $data["deleted_at"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		isset($data["is_multiple"]) && $this->is_multiple = $data["is_multiple"];
		isset($data["is_test"]) && $this->is_test = $data["is_test"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}