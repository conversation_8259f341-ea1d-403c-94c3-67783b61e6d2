<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BasePlatformDatas;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class PlatformDatas extends BasePlatformDatas
{
    private $platform = NULL;

    public function platform($f=false)
    {
        if($this->platform === NULL || $f) $this->platform = sf::getModel("Platforms")->selectByPlatformId($this->getPlatformId());
        return $this->platform;
    }

    function selectByPlatformId($platformId,$code)
    {
        $addwhere = "`platform_id` = '{$platformId}' and code = '{$code}'";
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE {$addwhere} order by id desc");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setPlatformId($platformId);
            $this->setcode($code);
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }

    public function __toString()
    {
        return (string)$this->getData();
    }
}