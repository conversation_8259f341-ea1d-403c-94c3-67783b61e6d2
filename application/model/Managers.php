<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use App\Model\BaseManagers;
class Managers extends BaseManagers
{
    private $user = NULL;

	function encode_password($password='')
	{
		$str = substr(md5(uniqid()),-3);
		return md5($password.$str).":".$str;
	}
	
	function setUserPassword($v)
	{
		parent::setUserPassword($this->encode_password($v));
	}
	
	function selectByName($userName='')
	{
		$db = sf::getLib("db");
		$result = $db->fetch_first("SELECT * FROM ".$this->table." WHERE `user_name` = '".$userName."'");
		if($result){
			$this->fillObject($result);
			return $this;
		}else return false;
	}
	
	function selectByUserId($user_id='')
	{
		if($user_id == '') $user_id = sf::getLib("MyString")->getRandString();
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_id` = '".$user_id."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else $this->setUserId($user_id);
		return $this;
	}

    public function selectUsers()
    {
        return sf::getModel("Users")->selectAll("user_id IN (SELECT user_id FROM user_roles WHERE user_role_id = '" . $this->getUserId() . "' AND role_id = '6')");
    }

    public function users()
    {
        return $this->selectUsers();
    }
	
	function setLoginNum($v)
	{
		parent::setLoginNum($this->getLoginNum() + 1);
	}
	
	function hasUser($userName='')
	{
		if($this->selectByName($userName)) return true;
		else return false;
	}
	
	function check($password='',$oldpass='')
	{
		$oldpass == '' && $oldpass = $this->getUserPassword();
		$pass_array = explode(":",$oldpass);
		if(count($pass_array) > 1){
			$oldpass = $pass_array[0];
			$tempstr = $pass_array[1];
			if($oldpass == md5($password.$tempstr)) return true;
			else return false;
		}else{
			if($oldpass == $password) return true;
			else return false;
		}
	}
	
	function getUserGroupName()
	{
		return sf::getModel("UserGroups",parent::getUserGroupId())->getUserGroupName();
	}
	
	
	function getState()
	{
		if(parent::getIsLock() == 4) return lang::get("Is lock!");
		else return lang::get("Is normal!");
	}
	
	function getOfficeSubject()
	{
		$office_id = parent::getOfficeId();
		if($office_id == -1 || $office_id == 0 ) return '<s style="color:red">未知</s>';
		return sf::getModel("Categorys",$office_id,'office')->getSubject();
	}
	
	/**
	 * 配合其他模型使用
	 */
	public function getUser($f=false)
	{
        if ($this->user === NULL || $f)
            $this->user = sf::getModel("Users")->selectByUserId($this->getUserId());
        return $this->user;
	}
	
	/**
	*等待审核的科技著作数
	*/
	function getWorkNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works WHERE statement = 9 ");
		return $row['num'];
	}

    function getProductNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM products WHERE `statement` = 9 ");
        return $row['num'];
    }

    function getArticleNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM science_articles WHERE `statement` = 9 ");
        return $row['num'];
    }

    function getTransformNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM `transforms` WHERE `statement` = 9 ");
        return $row['num'];
    }
	/**
	*等待审核的科技论文数
	*/
	function getPaperNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers WHERE statement = 9 ");
		return $row['num'];
	}	
	/**
	*等待审核的专利成果数
	*/
	function getPatentNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents WHERE statement = 8 ");
		return $row['num'];
	}	
	/**
	*等待科研部审核的项目计划数
	*/
	function getPlanWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_plans WHERE statement = 9");
		return $row['num'];
	}
	/**
	*等待科研部审核的项目数
	*/
	function getAcceptWaitNum()
	{
        $addWhere = "statement = 9 AND tags = ''";
        if(input::session('auth')!=-1 && input::session('auth.cat')){
            $addWhere .= " AND `cat_id` IN (".implode(',',input::session('auth.cat')).") ";
        }
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE {$addWhere}");
		return $row['num'];
	}

    /**
     *未审核申报单位数
     */
    function getUnitWaitnum()
    {
        return sf::getModel("corporations")->selectAll("`is_lock` = 5")->getTotal();

    }

	/**
	*等待科研部审核的年度执行报告
	*/
	function getStageWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_stages WHERE statement = 9");
		return $row['num'];
	}
	/**
	*等待科研部审核的季度执行报告
	*/
	function getQuarterWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_quarters WHERE statement = 9");
		return $row['num'];
	}
	/**
	*等待科研部审核的变更申请
	*/
	function getChangeWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM platform_changes WHERE statement = 9");
		return $row['num'];
	}
	/**
	*等待科研部审核的外协申请
	*/
	function getOutWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE state_for_out = 9");
		return $row['num'];
	}

    /**
     *等待审核的奖励数
     */
    function getRewardNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM rewards WHERE statement = 8 ");
        return $row['num'];
    }

    function getMethodNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods WHERE `statement` = 9 ");
        return $row['num'];
    }
	/**
     * 已注册的项目负责人数量
     */
	function getResearcherNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM declarers");
        return $row['num'];
    }
	/**
     * 已认证的项目负责人数量
     */
	function getVerifiedResearcherNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM declarers where is_lock = 0");
        return $row['num'];
    }
	/**
     * 已注册的专家数量
     */
	function getExpertNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM experts");
        return $row['num'];
    }
	/**
     * 已注册的单位数量
     */
	function getCompanyNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM corporations");
        return $row['num'];
    }
	/**
     * 已注册的归口数量
     */
	function getDepartmentNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM departments where user_id not in ('BF7900E0-D81F-D9D6-8C83-4B5EA8AC9EFF')");
        return $row['num'];
    }
	/**
     * 已认证的专家数量
     */
	function getVerifiedExpertNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM experts where is_lock = 0");
        return $row['num'];
    }
	/**
     * 已认证的单位数量
     */
	function getVerifiedCompanyNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM corporations where is_lock = 0");
        return $row['num'];
    }
	/**
     * 已立项
     */
	function getRadicateNum($catId='',$year='')
    {
        $addwhere = "statement = 29 and is_test = 0";
        if($year) $addwhere .= " and declare_year = {$year}";
        if($catId) $addwhere .= " and cat_id = {$catId}";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects where {$addwhere}");
        return $row['num'];
    }
	/**
     * 平台数
     */
	function getPlatformNum($level='')
    {
        $addwhere = "statement = 0";
        if($level) $addwhere .= " and `level` = '{$level}'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM platforms where {$addwhere}");
        return $row['num'];
    }
	/**
     * 已填报
     */
	function getReportNum($year='')
    {
        $addwhere = "is_report = 1 and department_id != 'BF7900E0-D81F-D9D6-8C83-4B5EA8AC9EFF'";
        if($year) $addwhere .= " and declare_year = {$year}";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects where {$addwhere}");
        return $row['num'];
    }
	/**
     * 已手里
     */
	function getAcceptNum($year='')
    {
        $addwhere = "is_accept = 1 and department_id != 'BF7900E0-D81F-D9D6-8C83-4B5EA8AC9EFF'";
        if($year) $addwhere .= " and declare_year = {$year}";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects where {$addwhere}");
        return $row['num'];
    }
	/**
     * 集团已结题
     */
	function getCompleteNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects where statement = 30");
        return $row['num'];
    }
	/**
     * 政府已立项
     */
	function getGovRadicateNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects where cat_id = 16 and statement = 29 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 政府已结题
     */
	function getGovCompleteNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects where cat_id = 16 and statement = 30 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 已审核专利数量
     */
	function getAcceptPatentNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents where statement = 10 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 已审核著作数量
     */
	function getAcceptBookNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works where statement = 10 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 已审核论文数量
     */
	function getAcceptPaperNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers where statement = 10 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 已审核软著数量
     */
	function getAcceptSoftNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM softs where statement = 10 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 已审核科技奖励数量
     */
	function getAcceptRewardNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM rewards where statement = 10 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 已审核工法数量
     */
	function getAcceptMethodNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods where statement = 10 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 已审核新产品数量
     */
	function getAcceptProductNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM products where statement = 10 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 已审核科普文章数量
     */
	function getAcceptArticleNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM science_articles where statement = 10 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
	*等待总工程师审核的项目数
	*/
	function getZgcsWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE (flow_type = 1 and statement = 20) or (flow_type > 1 and statement = 9)");
		return $row['num'];
	}
	/**
	*等待分组的项目数
	*/
	function getdevideWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement = 10  and type_group < 2 ");
		return $row['num'];
	}
	/**
	*等待分配专家的组数
	*/
	function getgroupWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_group WHERE `group_subject` LIKE '".substr(date('Y'),-2)."%' and  is_lock = 0 ");
		return $row['num'];
	}
	/**
	*等待分流的项目数
	*/
	function getpartitionWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement >= 10 AND statement < 20 AND statement <> 12 ");
		return $row['num'];
	}
	/**
	*等待立项的项目数
	*/
	function getRadicateWaitNum()
	{
        $addWhere = "statement = 20";
        if(input::session('auth')!=-1 && input::session('auth.cat')){
            $addWhere .= " AND `cat_id` IN (".implode(',',input::session('auth.cat')).") ";
        }
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE {$addWhere}");
		return $row['num'];
	}
	
	/**
	*等待推荐的项目数
	*/
	function getWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE office_id = '".input::getInput('session.office_id')."' and statement = 20 and declare_year = ".date('Y'));
		return $row['num'];
	}
	/**
	*等待审定的项目预算数
	*/
	function getBudgetWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE office_id = '".input::getInput('session.office_id')."' and `state_for_budget_book` = 15  and budget_open = 2 AND type_id IN (select type_id from types_switch WHERE has_budget = 1) ");
		return $row['num'];
	}
	/**
	*等待审定的项目预算数(审定人员)
	*/
	function getsdBudgetWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE `state_for_budget_book` IN ('15','20','25')  and budget_open = 1 AND type_id IN (select type_id from types_switch WHERE has_budget = 1) ");
		return $row['num'];
	}
	/**
	*等待审定的项目预算数(复审人员)
	*/
	function getfsBudgetWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE `state_for_budget_book` IN ('15','20')  and budget_open = 1 AND type_id IN (select type_id from types_switch WHERE has_budget = 1) ");
		return $row['num'];
	}
	/**
	*等待审定的项目预算数(初审人员)
	*/
	function getcsBudgetWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE `state_for_budget_book` = 15  and budget_open = 1  AND type_id IN (select type_id from types_switch WHERE has_budget = 1) ");
		return $row['num'];
		
	}
	/**
	*等待审核的预算申报书数
	*/
	function getBudgetNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE `state_for_budget_book` = 6 AND type_id IN (select type_id from types_switch WHERE has_budget = 1) ");
		return $row['num'];
	}

	/**
	*待审核的计划任务书数
	*/
	function getTaskWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE  (`statement` = 29 OR task_open > 0) and `state_for_plan_book` = 6");
		return $row['num'];
	}

    /**
     *等待审核的年度考核
     */
    function getSummaryWaitnum()
    {
        $addWhere = "statement = 9 ";
        return sf::getModel("Summarys")->selectAll($addWhere)->getTotal();
    }
	/**
	*待审核的科技奖励数
	*/
	function getRewardWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM rewards WHERE `statement` = 8 ");
		return $row['num'];
	}
	/**
	*待审核的科技论文数
	*/
	function getPaperWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers WHERE `statement` = 8 ");
		return $row['num'];
	}
	/**
	*待审核的工法数
	*/
	function getMethodWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods WHERE `statement` = 8 ");
		return $row['num'];
	}
	/**
	*待审核的专利数
	*/
	function getPatentWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents WHERE `statement` = 8 ");
		return $row['num'];
	}
	/**
	*待审核的著作数
	*/
	function getWorkWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works WHERE `statement` = 8 ");
		return $row['num'];
	}
	/**
	*待审核的科协会员
	*/
	function getMemberWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM declarers WHERE `talent` = 3 and  is_lock = 5 ");
		return $row['num'];
	}
	/**
	*待审核的中期报告数
	*/
	function getZqbgWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE office_id = '".input::getInput('session.office_id')."' and (`statement` = 29 OR task_open > 0) and `state_for_interimreport` = 5 ");
		return $row['num'];
	}
	/**
	*待审核的验收书数
	*/
	function getCompleteWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE  statement = 29 and `state_for_complete_book` = 6");
		return $row['num'];
		
	}
	/**
	*等待结题的项目数
	*/
	function getfinishWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement = 29 AND `state_for_complete_book` IN ('8','10')  AND type_id IN (select type_id from types_switch WHERE has_complete = 1) ");
		return $row['num'];
	}
	/**
	*需要复议的项目数
	*/
	function getreconsiderWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement = 29 AND `state_for_complete_book` = 13 ");
		return $row['num'];
	}
	/**
	*等待受理的国家项目数
	*/
	function getnationWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_memo WHERE statement = 1 AND `office_id` = '".input::getInput('session.office_id')."' ");
		return $row['num'];
	}

	//等待受理的老项目
	function getOldWaitnum(){
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement = 29 AND state_for_datum = 5 AND office_id = '".input::getInput('session.office_id')."' AND declare_year < 2016 ");
		return $row['num'];
	}
	
	/**
	 * 发送短消息给用户
	 */
	public function sendMessage($message='',$item_id='',$item_type='managers',$send_at='')
	{
		if(!isMobile($this->getUserMobile())) return false;
		return sf::getModel("ShortMessages")->sendSms($this->getUserMobile(),$message,$item_id,$item_type,$send_at);
	}

    public function coupling($userid = '')
    {
        if (!$userid) return false;
        $userrole = sf::getModel("UserRoles")->selectByRole($userid, 6);
        $userrole->setUserRoleId($this->getUserId());
        return $userrole->save();
    }

    public function isManager($userid = '')
    {
        if (!$userid) return false;
        $manager = sf::getModel('Managers')->selectByUserId($userid);
        if ($manager->getIsManager()) return true;
        else return false;
    }

    function setAuths($key, $val = '')
    {
        $configs = (array)json_decode(parent::getAuths(), true);

        if (!is_array($key)) $key = array($key => $val);
        foreach ($key as $_key => $_val) {
            $_ks = explode('.', $_key);
            $_code = '$configs';
            for ($i = 0, $n = count($_ks); $i < $n; $i++)
                $_code .= '[\'' . $_ks[$i] . '\']';
            $_code .= '= $_val;';
            @eval($_code);//执行语句,不是最佳选择，寻找替代方法
        }

        parent::setAuths(json_encode($configs));
        return parent::save();
    }

    function getAuths($key = '')
    {
        $configs = json_decode(parent::getAuths(), true);
        if ($key) {
            $_key = explode('.', $key);
            foreach ($_key as $k) {
                $configs = $configs[$k];
            }
        }
        return $configs;
    }

    function getSubject()
    {
        return $this->getUserUsername();
    }


    function getOfficeName()
    {
        if(!$this->getOfficeId()) return '未设置';
        return sf::getModel("Categorys",$this->getOfficeId(),'office')->getSubject();
    }
}