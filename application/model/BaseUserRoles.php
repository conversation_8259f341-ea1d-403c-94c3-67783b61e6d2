<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseUserRoles extends BaseModel
{
	private $id;
	private $user_id;
	private $role_id;
	private $user_role_id;
	private $user_role_type;
	private $created_at;
	private $updated_at;
	public $table = "user_roles";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getRoleId()
	{
		return $this->role_id;
	}

	public function getUserRoleId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_role_id,0,$len,"utf-8");
			else return substr($this->user_role_id,0,$len);
		}
		return $this->user_role_id;
	}

	public function getUserRoleType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_role_type,0,$len,"utf-8");
			else return substr($this->user_role_type,0,$len);
		}
		return $this->user_role_type;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setRoleId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->role_id !== $v)
		{
			$this->role_id = $v;
			$this->fieldData["role_id"] = $v;
		}
		return $this;

	}

	public function setUserRoleId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_role_id !== $v)
		{
			$this->user_role_id = $v;
			$this->fieldData["user_role_id"] = $v;
		}
		return $this;

	}

	public function setUserRoleType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_role_type !== $v)
		{
			$this->user_role_type = $v;
			$this->fieldData["user_role_type"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `user_roles` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"user_id" => $this->getUserId(),
			"role_id" => $this->getRoleId(),
			"user_role_id" => $this->getUserRoleId(),
			"user_role_type" => $this->getUserRoleType(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->user_id = '';
		$this->role_id = '';
		$this->user_role_id = '';
		$this->user_role_type = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["role_id"]) && $this->role_id = $data["role_id"];
		isset($data["user_role_id"]) && $this->user_role_id = $data["user_role_id"];
		isset($data["user_role_type"]) && $this->user_role_type = $data["user_role_type"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}