<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BasePlatforms extends BaseModel
{
	private $id;
	private $platform_id;
	private $subject;
	private $cat_id  = '0';
	private $level;
	private $platform_type;
	private $user_id;
	private $user_name;
	private $corporation_id;
	private $corporation_name;
	private $corporation_property;
	private $department_id;
	private $cooperatation;
	private $industry;
	private $industry_other;
	private $principal_name;
	private $principal_mobile;
	private $linkman_name;
	private $linkman_mobile;
	private $area;
	private $area_code;
	private $address;
	private $statement  = '0';
	private $manager_user_id;
	private $radicate_year;
	private $research_direction;
	private $declare_at;
	private $created_at  = 'CURRENT_TIMESTAMP';
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "platforms";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getPlatformId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->platform_id,0,$len,"utf-8");
			else return substr($this->platform_id,0,$len);
		}
		return $this->platform_id;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getCatId()
	{
		return $this->cat_id;
	}

	public function getLevel($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->level,0,$len,"utf-8");
			else return substr($this->level,0,$len);
		}
		return $this->level;
	}

	public function getPlatformType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->platform_type,0,$len,"utf-8");
			else return substr($this->platform_type,0,$len);
		}
		return $this->platform_type;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getUserName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_name,0,$len,"utf-8");
			else return substr($this->user_name,0,$len);
		}
		return $this->user_name;
	}

	public function getCorporationId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->corporation_id,0,$len,"utf-8");
			else return substr($this->corporation_id,0,$len);
		}
		return $this->corporation_id;
	}

	public function getCorporationName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->corporation_name,0,$len,"utf-8");
			else return substr($this->corporation_name,0,$len);
		}
		return $this->corporation_name;
	}

	public function getCorporationProperty($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->corporation_property,0,$len,"utf-8");
			else return substr($this->corporation_property,0,$len);
		}
		return $this->corporation_property;
	}

	public function getDepartmentId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department_id,0,$len,"utf-8");
			else return substr($this->department_id,0,$len);
		}
		return $this->department_id;
	}

	public function getCooperatation($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->cooperatation,0,$len,"utf-8");
			else return substr($this->cooperatation,0,$len);
		}
		return $this->cooperatation;
	}

	public function getIndustry($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->industry,0,$len,"utf-8");
			else return substr($this->industry,0,$len);
		}
		return $this->industry;
	}

	public function getIndustryOther($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->industry_other,0,$len,"utf-8");
			else return substr($this->industry_other,0,$len);
		}
		return $this->industry_other;
	}

	public function getPrincipalName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->principal_name,0,$len,"utf-8");
			else return substr($this->principal_name,0,$len);
		}
		return $this->principal_name;
	}

	public function getPrincipalMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->principal_mobile,0,$len,"utf-8");
			else return substr($this->principal_mobile,0,$len);
		}
		return $this->principal_mobile;
	}

	public function getLinkmanName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman_name,0,$len,"utf-8");
			else return substr($this->linkman_name,0,$len);
		}
		return $this->linkman_name;
	}

	public function getLinkmanMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->linkman_mobile,0,$len,"utf-8");
			else return substr($this->linkman_mobile,0,$len);
		}
		return $this->linkman_mobile;
	}

	public function getArea($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->area,0,$len,"utf-8");
			else return substr($this->area,0,$len);
		}
		return $this->area;
	}

	public function getAreaCode($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->area_code,0,$len,"utf-8");
			else return substr($this->area_code,0,$len);
		}
		return $this->area_code;
	}

	public function getAddress($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->address,0,$len,"utf-8");
			else return substr($this->address,0,$len);
		}
		return $this->address;
	}

	public function getStatement()
	{
		return $this->statement;
	}

	public function getManagerUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->manager_user_id,0,$len,"utf-8");
			else return substr($this->manager_user_id,0,$len);
		}
		return $this->manager_user_id;
	}

	public function getRadicateYear($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->radicate_year,0,$len,"utf-8");
			else return substr($this->radicate_year,0,$len);
		}
		return $this->radicate_year;
	}

	public function getResearchDirection($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->research_direction,0,$len,"utf-8");
			else return substr($this->research_direction,0,$len);
		}
		return $this->research_direction;
	}

	public function getDeclareAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->declare_at));
		else return $this->declare_at;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setPlatformId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->platform_id !== $v)
		{
			$this->platform_id = $v;
			$this->fieldData["platform_id"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setCatId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->cat_id !== $v)
		{
			$this->cat_id = $v;
			$this->fieldData["cat_id"] = $v;
		}
		return $this;

	}

	public function setLevel($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->level !== $v)
		{
			$this->level = $v;
			$this->fieldData["level"] = $v;
		}
		return $this;

	}

	public function setPlatformType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->platform_type !== $v)
		{
			$this->platform_type = $v;
			$this->fieldData["platform_type"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setUserName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_name !== $v)
		{
			$this->user_name = $v;
			$this->fieldData["user_name"] = $v;
		}
		return $this;

	}

	public function setCorporationId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_id !== $v)
		{
			$this->corporation_id = $v;
			$this->fieldData["corporation_id"] = $v;
		}
		return $this;

	}

	public function setCorporationName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_name !== $v)
		{
			$this->corporation_name = $v;
			$this->fieldData["corporation_name"] = $v;
		}
		return $this;

	}

	public function setCorporationProperty($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_property !== $v)
		{
			$this->corporation_property = $v;
			$this->fieldData["corporation_property"] = $v;
		}
		return $this;

	}

	public function setDepartmentId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_id !== $v)
		{
			$this->department_id = $v;
			$this->fieldData["department_id"] = $v;
		}
		return $this;

	}

	public function setCooperatation($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->cooperatation !== $v)
		{
			$this->cooperatation = $v;
			$this->fieldData["cooperatation"] = $v;
		}
		return $this;

	}

	public function setIndustry($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->industry !== $v)
		{
			$this->industry = $v;
			$this->fieldData["industry"] = $v;
		}
		return $this;

	}

	public function setIndustryOther($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->industry_other !== $v)
		{
			$this->industry_other = $v;
			$this->fieldData["industry_other"] = $v;
		}
		return $this;

	}

	public function setPrincipalName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->principal_name !== $v)
		{
			$this->principal_name = $v;
			$this->fieldData["principal_name"] = $v;
		}
		return $this;

	}

	public function setPrincipalMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->principal_mobile !== $v)
		{
			$this->principal_mobile = $v;
			$this->fieldData["principal_mobile"] = $v;
		}
		return $this;

	}

	public function setLinkmanName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman_name !== $v)
		{
			$this->linkman_name = $v;
			$this->fieldData["linkman_name"] = $v;
		}
		return $this;

	}

	public function setLinkmanMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman_mobile !== $v)
		{
			$this->linkman_mobile = $v;
			$this->fieldData["linkman_mobile"] = $v;
		}
		return $this;

	}

	public function setArea($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->area !== $v)
		{
			$this->area = $v;
			$this->fieldData["area"] = $v;
		}
		return $this;

	}

	public function setAreaCode($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->area_code !== $v)
		{
			$this->area_code = $v;
			$this->fieldData["area_code"] = $v;
		}
		return $this;

	}

	public function setAddress($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->address !== $v)
		{
			$this->address = $v;
			$this->fieldData["address"] = $v;
		}
		return $this;

	}

	public function setStatement($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->statement !== $v)
		{
			$this->statement = $v;
			$this->fieldData["statement"] = $v;
		}
		return $this;

	}

	public function setManagerUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->manager_user_id !== $v)
		{
			$this->manager_user_id = $v;
			$this->fieldData["manager_user_id"] = $v;
		}
		return $this;

	}

	public function setRadicateYear($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->radicate_year !== $v)
		{
			$this->radicate_year = $v;
			$this->fieldData["radicate_year"] = $v;
		}
		return $this;

	}

	public function setResearchDirection($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->research_direction !== $v)
		{
			$this->research_direction = $v;
			$this->fieldData["research_direction"] = $v;
		}
		return $this;

	}

	public function setDeclareAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->declare_at !== $v)
		{
			$this->declare_at = $v;
			$this->fieldData["declare_at"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `platforms` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"platform_id" => $this->getPlatformId(),
			"subject" => $this->getSubject(),
			"cat_id" => $this->getCatId(),
			"level" => $this->getLevel(),
			"platform_type" => $this->getPlatformType(),
			"user_id" => $this->getUserId(),
			"user_name" => $this->getUserName(),
			"corporation_id" => $this->getCorporationId(),
			"corporation_name" => $this->getCorporationName(),
			"corporation_property" => $this->getCorporationProperty(),
			"department_id" => $this->getDepartmentId(),
			"cooperatation" => $this->getCooperatation(),
			"industry" => $this->getIndustry(),
			"industry_other" => $this->getIndustryOther(),
			"principal_name" => $this->getPrincipalName(),
			"principal_mobile" => $this->getPrincipalMobile(),
			"linkman_name" => $this->getLinkmanName(),
			"linkman_mobile" => $this->getLinkmanMobile(),
			"area" => $this->getArea(),
			"area_code" => $this->getAreaCode(),
			"address" => $this->getAddress(),
			"statement" => $this->getStatement(),
			"manager_user_id" => $this->getManagerUserId(),
			"radicate_year" => $this->getRadicateYear(),
			"research_direction" => $this->getResearchDirection(),
			"declare_at" => $this->getDeclareAt(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->platform_id = '';
		$this->subject = '';
		$this->cat_id = '';
		$this->level = '';
		$this->platform_type = '';
		$this->user_id = '';
		$this->user_name = '';
		$this->corporation_id = '';
		$this->corporation_name = '';
		$this->corporation_property = '';
		$this->department_id = '';
		$this->cooperatation = '';
		$this->industry = '';
		$this->industry_other = '';
		$this->principal_name = '';
		$this->principal_mobile = '';
		$this->linkman_name = '';
		$this->linkman_mobile = '';
		$this->area = '';
		$this->area_code = '';
		$this->address = '';
		$this->statement = '';
		$this->manager_user_id = '';
		$this->radicate_year = '';
		$this->research_direction = '';
		$this->declare_at = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["platform_id"]) && $this->platform_id = $data["platform_id"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["cat_id"]) && $this->cat_id = $data["cat_id"];
		isset($data["level"]) && $this->level = $data["level"];
		isset($data["platform_type"]) && $this->platform_type = $data["platform_type"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["user_name"]) && $this->user_name = $data["user_name"];
		isset($data["corporation_id"]) && $this->corporation_id = $data["corporation_id"];
		isset($data["corporation_name"]) && $this->corporation_name = $data["corporation_name"];
		isset($data["corporation_property"]) && $this->corporation_property = $data["corporation_property"];
		isset($data["department_id"]) && $this->department_id = $data["department_id"];
		isset($data["cooperatation"]) && $this->cooperatation = $data["cooperatation"];
		isset($data["industry"]) && $this->industry = $data["industry"];
		isset($data["industry_other"]) && $this->industry_other = $data["industry_other"];
		isset($data["principal_name"]) && $this->principal_name = $data["principal_name"];
		isset($data["principal_mobile"]) && $this->principal_mobile = $data["principal_mobile"];
		isset($data["linkman_name"]) && $this->linkman_name = $data["linkman_name"];
		isset($data["linkman_mobile"]) && $this->linkman_mobile = $data["linkman_mobile"];
		isset($data["area"]) && $this->area = $data["area"];
		isset($data["area_code"]) && $this->area_code = $data["area_code"];
		isset($data["address"]) && $this->address = $data["address"];
		isset($data["statement"]) && $this->statement = $data["statement"];
		isset($data["manager_user_id"]) && $this->manager_user_id = $data["manager_user_id"];
		isset($data["radicate_year"]) && $this->radicate_year = $data["radicate_year"];
		isset($data["research_direction"]) && $this->research_direction = $data["research_direction"];
		isset($data["declare_at"]) && $this->declare_at = $data["declare_at"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}