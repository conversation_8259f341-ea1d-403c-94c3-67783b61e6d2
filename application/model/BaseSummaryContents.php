<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseSummaryContents extends BaseModel
{
	private $id;
	private $summary_id;
	private $widget_name;
	private $file_name;
	private $content;
	private $server_ip;
	private $file_path;
	private $file_ext;
	private $pdf_path;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "summary_contents";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getSummaryId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->summary_id,0,$len,"utf-8");
			else return substr($this->summary_id,0,$len);
		}
		return $this->summary_id;
	}

	public function getWidgetName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->widget_name,0,$len,"utf-8");
			else return substr($this->widget_name,0,$len);
		}
		return $this->widget_name;
	}

	public function getFileName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->file_name,0,$len,"utf-8");
			else return substr($this->file_name,0,$len);
		}
		return $this->file_name;
	}

	public function getContent($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->content,0,$len,"utf-8");
			else return substr($this->content,0,$len);
		}
		return $this->content;
	}

	public function getServerIp($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->server_ip,0,$len,"utf-8");
			else return substr($this->server_ip,0,$len);
		}
		return $this->server_ip;
	}

	public function getFilePath($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->file_path,0,$len,"utf-8");
			else return substr($this->file_path,0,$len);
		}
		return $this->file_path;
	}

	public function getFileExt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->file_ext,0,$len,"utf-8");
			else return substr($this->file_ext,0,$len);
		}
		return $this->file_ext;
	}

	public function getPdfPath($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->pdf_path,0,$len,"utf-8");
			else return substr($this->pdf_path,0,$len);
		}
		return $this->pdf_path;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setSummaryId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->summary_id !== $v)
		{
			$this->summary_id = $v;
			$this->fieldData["summary_id"] = $v;
		}
		return $this;

	}

	public function setWidgetName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->widget_name !== $v)
		{
			$this->widget_name = $v;
			$this->fieldData["widget_name"] = $v;
		}
		return $this;

	}

	public function setFileName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->file_name !== $v)
		{
			$this->file_name = $v;
			$this->fieldData["file_name"] = $v;
		}
		return $this;

	}

	public function setContent($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->content !== $v)
		{
			$this->content = $v;
			$this->fieldData["content"] = $v;
		}
		return $this;

	}

	public function setServerIp($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->server_ip !== $v)
		{
			$this->server_ip = $v;
			$this->fieldData["server_ip"] = $v;
		}
		return $this;

	}

	public function setFilePath($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->file_path !== $v)
		{
			$this->file_path = $v;
			$this->fieldData["file_path"] = $v;
		}
		return $this;

	}

	public function setFileExt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->file_ext !== $v)
		{
			$this->file_ext = $v;
			$this->fieldData["file_ext"] = $v;
		}
		return $this;

	}

	public function setPdfPath($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->pdf_path !== $v)
		{
			$this->pdf_path = $v;
			$this->fieldData["pdf_path"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `summary_contents` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"summary_id" => $this->getSummaryId(),
			"widget_name" => $this->getWidgetName(),
			"file_name" => $this->getFileName(),
			"content" => $this->getContent(),
			"server_ip" => $this->getServerIp(),
			"file_path" => $this->getFilePath(),
			"file_ext" => $this->getFileExt(),
			"pdf_path" => $this->getPdfPath(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->summary_id = '';
		$this->widget_name = '';
		$this->file_name = '';
		$this->content = '';
		$this->server_ip = '';
		$this->file_path = '';
		$this->file_ext = '';
		$this->pdf_path = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["summary_id"]) && $this->summary_id = $data["summary_id"];
		isset($data["widget_name"]) && $this->widget_name = $data["widget_name"];
		isset($data["file_name"]) && $this->file_name = $data["file_name"];
		isset($data["content"]) && $this->content = $data["content"];
		isset($data["server_ip"]) && $this->server_ip = $data["server_ip"];
		isset($data["file_path"]) && $this->file_path = $data["file_path"];
		isset($data["file_ext"]) && $this->file_ext = $data["file_ext"];
		isset($data["pdf_path"]) && $this->pdf_path = $data["pdf_path"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}