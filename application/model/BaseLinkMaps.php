<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseLinkMaps extends BaseModel
{
	private $id;
	private $url_name;
	private $subject;
	private $note;
	private $need_roles;
	private $target_url;
	private $created_at;
	private $updated_at;
	public $table = "link_maps";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getUrlName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->url_name,0,$len,"utf-8");
			else return substr($this->url_name,0,$len);
		}
		return $this->url_name;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getNote($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->note,0,$len,"utf-8");
			else return substr($this->note,0,$len);
		}
		return $this->note;
	}

	public function getNeedRoles($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->need_roles,0,$len,"utf-8");
			else return substr($this->need_roles,0,$len);
		}
		return $this->need_roles;
	}

	public function getTargetUrl($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->target_url,0,$len,"utf-8");
			else return substr($this->target_url,0,$len);
		}
		return $this->target_url;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setUrlName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->url_name !== $v)
		{
			$this->url_name = $v;
			$this->fieldData["url_name"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setNote($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->note !== $v)
		{
			$this->note = $v;
			$this->fieldData["note"] = $v;
		}
		return $this;

	}

	public function setNeedRoles($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->need_roles !== $v)
		{
			$this->need_roles = $v;
			$this->fieldData["need_roles"] = $v;
		}
		return $this;

	}

	public function setTargetUrl($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->target_url !== $v)
		{
			$this->target_url = $v;
			$this->fieldData["target_url"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `link_maps` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"url_name" => $this->getUrlName(),
			"subject" => $this->getSubject(),
			"note" => $this->getNote(),
			"need_roles" => $this->getNeedRoles(),
			"target_url" => $this->getTargetUrl(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->url_name = '';
		$this->subject = '';
		$this->note = '';
		$this->need_roles = '';
		$this->target_url = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["url_name"]) && $this->url_name = $data["url_name"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["note"]) && $this->note = $data["note"];
		isset($data["need_roles"]) && $this->need_roles = $data["need_roles"];
		isset($data["target_url"]) && $this->target_url = $data["target_url"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}