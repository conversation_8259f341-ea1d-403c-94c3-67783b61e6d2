<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseGuides extends BaseModel
{
	private $id;
	private $guideid;
	private $type  = 'apply';
	private $cat_id;
	private $subject;
	private $mark;
	private $content;
	private $year;
	private $current_group;
	private $report_start_at;
	private $report_end_at;
	private $start_at;
	private $end_at;
	private $submit_at;
	private $gather_end_at;
	private $project_name;
	private $project_start_at;
	private $project_end_at;
	private $month_min  = '0';
	private $month_max  = '0';
	private $parent_id  = '0';
	private $user_level  = '2';
	private $lft;
	private $rgt;
	private $level  = '1';
	private $orders  = '0';
	private $head_str;
	private $root_id  = '0';
	private $is_lastnode  = '1';
	private $process_id;
	private $declare_map_id;
	private $task_map_id;
	private $budget_map_id;
	private $stage_map_id;
	private $complete_map_id;
	private $apply_path  = 'engine/worker';
	private $budget_path  = 'apply/outlay/zdyf2019';
	private $task_path  = 'engine/tasker';
	private $stage_path;
	private $complete_path;
	private $assess_id;
	private $assess_rule_id;
	private $assess_path  = 'assess/rulezdyf';
	private $office_id;
	private $allow_grade;
	private $allow_material;
	private $allow_company;
	private $property;
	private $template;
	private $is_show  = '1';
	private $marker;
	private $path_name;
	private $configs;
	private $rule_id  = '0';
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "guides";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getGuideid($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->guideid,0,$len,"utf-8");
			else return substr($this->guideid,0,$len);
		}
		return $this->guideid;
	}

	public function getType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->type,0,$len,"utf-8");
			else return substr($this->type,0,$len);
		}
		return $this->type;
	}

	public function getCatId()
	{
		return $this->cat_id;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getMark($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->mark,0,$len,"utf-8");
			else return substr($this->mark,0,$len);
		}
		return $this->mark;
	}

	public function getContent($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->content,0,$len,"utf-8");
			else return substr($this->content,0,$len);
		}
		return $this->content;
	}

	public function getYear($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->year,0,$len,"utf-8");
			else return substr($this->year,0,$len);
		}
		return $this->year;
	}

	public function getCurrentGroup()
	{
		return $this->current_group;
	}

	public function getReportStartAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->report_start_at,0,$len,"utf-8");
			else return substr($this->report_start_at,0,$len);
		}
		return $this->report_start_at;
	}

	public function getReportEndAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->report_end_at,0,$len,"utf-8");
			else return substr($this->report_end_at,0,$len);
		}
		return $this->report_end_at;
	}

	public function getStartAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->start_at));
		else return $this->start_at;
	}

	public function getEndAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->end_at));
		else return $this->end_at;
	}

	public function getSubmitAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->submit_at));
		else return $this->submit_at;
	}

	public function getGatherEndAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->gather_end_at));
		else return $this->gather_end_at;
	}

	public function getProjectName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->project_name,0,$len,"utf-8");
			else return substr($this->project_name,0,$len);
		}
		return $this->project_name;
	}

	public function getProjectStartAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->project_start_at,0,$len,"utf-8");
			else return substr($this->project_start_at,0,$len);
		}
		return $this->project_start_at;
	}

	public function getProjectEndAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->project_end_at,0,$len,"utf-8");
			else return substr($this->project_end_at,0,$len);
		}
		return $this->project_end_at;
	}

	public function getMonthMin()
	{
		return $this->month_min;
	}

	public function getMonthMax()
	{
		return $this->month_max;
	}

	public function getParentId()
	{
		return $this->parent_id;
	}

	public function getUserLevel()
	{
		return $this->user_level;
	}

	public function getLft()
	{
		return $this->lft;
	}

	public function getRgt()
	{
		return $this->rgt;
	}

	public function getLevel()
	{
		return $this->level;
	}

	public function getOrders()
	{
		return $this->orders;
	}

	public function getHeadStr($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->head_str,0,$len,"utf-8");
			else return substr($this->head_str,0,$len);
		}
		return $this->head_str;
	}

	public function getRootId()
	{
		return $this->root_id;
	}

	public function getIsLastnode()
	{
		return $this->is_lastnode;
	}

	public function getProcessId()
	{
		return $this->process_id;
	}

	public function getDeclareMapId()
	{
		return $this->declare_map_id;
	}

	public function getTaskMapId()
	{
		return $this->task_map_id;
	}

	public function getBudgetMapId()
	{
		return $this->budget_map_id;
	}

	public function getStageMapId()
	{
		return $this->stage_map_id;
	}

	public function getCompleteMapId()
	{
		return $this->complete_map_id;
	}

	public function getApplyPath($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->apply_path,0,$len,"utf-8");
			else return substr($this->apply_path,0,$len);
		}
		return $this->apply_path;
	}

	public function getBudgetPath($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->budget_path,0,$len,"utf-8");
			else return substr($this->budget_path,0,$len);
		}
		return $this->budget_path;
	}

	public function getTaskPath($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->task_path,0,$len,"utf-8");
			else return substr($this->task_path,0,$len);
		}
		return $this->task_path;
	}

	public function getStagePath($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->stage_path,0,$len,"utf-8");
			else return substr($this->stage_path,0,$len);
		}
		return $this->stage_path;
	}

	public function getCompletePath($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->complete_path,0,$len,"utf-8");
			else return substr($this->complete_path,0,$len);
		}
		return $this->complete_path;
	}

	public function getAssessId()
	{
		return $this->assess_id;
	}

	public function getAssessRuleId()
	{
		return $this->assess_rule_id;
	}

	public function getAssessPath($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->assess_path,0,$len,"utf-8");
			else return substr($this->assess_path,0,$len);
		}
		return $this->assess_path;
	}

	public function getOfficeId()
	{
		return $this->office_id;
	}

	public function getAllowGrade($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->allow_grade,0,$len,"utf-8");
			else return substr($this->allow_grade,0,$len);
		}
		return $this->allow_grade;
	}

	public function getAllowMaterial($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->allow_material,0,$len,"utf-8");
			else return substr($this->allow_material,0,$len);
		}
		return $this->allow_material;
	}

	public function getAllowCompany($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->allow_company,0,$len,"utf-8");
			else return substr($this->allow_company,0,$len);
		}
		return $this->allow_company;
	}

	public function getProperty($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->property,0,$len,"utf-8");
			else return substr($this->property,0,$len);
		}
		return $this->property;
	}

	public function getTemplate($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->template,0,$len,"utf-8");
			else return substr($this->template,0,$len);
		}
		return $this->template;
	}

	public function getIsShow()
	{
		return $this->is_show;
	}

	public function getMarker($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->marker,0,$len,"utf-8");
			else return substr($this->marker,0,$len);
		}
		return $this->marker;
	}

	public function getPathName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->path_name,0,$len,"utf-8");
			else return substr($this->path_name,0,$len);
		}
		return $this->path_name;
	}

	public function getConfigs($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->configs,0,$len,"utf-8");
			else return substr($this->configs,0,$len);
		}
		return $this->configs;
	}

	public function getRuleId()
	{
		return $this->rule_id;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setGuideid($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->guideid !== $v)
		{
			$this->guideid = $v;
			$this->fieldData["guideid"] = $v;
		}
		return $this;

	}

	public function setType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->type !== $v)
		{
			$this->type = $v;
			$this->fieldData["type"] = $v;
		}
		return $this;

	}

	public function setCatId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->cat_id !== $v)
		{
			$this->cat_id = $v;
			$this->fieldData["cat_id"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setMark($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->mark !== $v)
		{
			$this->mark = $v;
			$this->fieldData["mark"] = $v;
		}
		return $this;

	}

	public function setContent($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->content !== $v)
		{
			$this->content = $v;
			$this->fieldData["content"] = $v;
		}
		return $this;

	}

	public function setYear($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->year !== $v)
		{
			$this->year = $v;
			$this->fieldData["year"] = $v;
		}
		return $this;

	}

	public function setCurrentGroup($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->current_group !== $v)
		{
			$this->current_group = $v;
			$this->fieldData["current_group"] = $v;
		}
		return $this;

	}

	public function setReportStartAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->report_start_at !== $v)
		{
			$this->report_start_at = $v;
			$this->fieldData["report_start_at"] = $v;
		}
		return $this;

	}

	public function setReportEndAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->report_end_at !== $v)
		{
			$this->report_end_at = $v;
			$this->fieldData["report_end_at"] = $v;
		}
		return $this;

	}

	public function setStartAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->start_at !== $v)
		{
			$this->start_at = $v;
			$this->fieldData["start_at"] = $v;
		}
		return $this;

	}

	public function setEndAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->end_at !== $v)
		{
			$this->end_at = $v;
			$this->fieldData["end_at"] = $v;
		}
		return $this;

	}

	public function setSubmitAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->submit_at !== $v)
		{
			$this->submit_at = $v;
			$this->fieldData["submit_at"] = $v;
		}
		return $this;

	}

	public function setGatherEndAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->gather_end_at !== $v)
		{
			$this->gather_end_at = $v;
			$this->fieldData["gather_end_at"] = $v;
		}
		return $this;

	}

	public function setProjectName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->project_name !== $v)
		{
			$this->project_name = $v;
			$this->fieldData["project_name"] = $v;
		}
		return $this;

	}

	public function setProjectStartAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->project_start_at !== $v)
		{
			$this->project_start_at = $v;
			$this->fieldData["project_start_at"] = $v;
		}
		return $this;

	}

	public function setProjectEndAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->project_end_at !== $v)
		{
			$this->project_end_at = $v;
			$this->fieldData["project_end_at"] = $v;
		}
		return $this;

	}

	public function setMonthMin($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->month_min !== $v)
		{
			$this->month_min = $v;
			$this->fieldData["month_min"] = $v;
		}
		return $this;

	}

	public function setMonthMax($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->month_max !== $v)
		{
			$this->month_max = $v;
			$this->fieldData["month_max"] = $v;
		}
		return $this;

	}

	public function setParentId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->parent_id !== $v)
		{
			$this->parent_id = $v;
			$this->fieldData["parent_id"] = $v;
		}
		return $this;

	}

	public function setUserLevel($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->user_level !== $v)
		{
			$this->user_level = $v;
			$this->fieldData["user_level"] = $v;
		}
		return $this;

	}

	public function setLft($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->lft !== $v)
		{
			$this->lft = $v;
			$this->fieldData["lft"] = $v;
		}
		return $this;

	}

	public function setRgt($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->rgt !== $v)
		{
			$this->rgt = $v;
			$this->fieldData["rgt"] = $v;
		}
		return $this;

	}

	public function setLevel($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->level !== $v)
		{
			$this->level = $v;
			$this->fieldData["level"] = $v;
		}
		return $this;

	}

	public function setOrders($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->orders !== $v)
		{
			$this->orders = $v;
			$this->fieldData["orders"] = $v;
		}
		return $this;

	}

	public function setHeadStr($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->head_str !== $v)
		{
			$this->head_str = $v;
			$this->fieldData["head_str"] = $v;
		}
		return $this;

	}

	public function setRootId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->root_id !== $v)
		{
			$this->root_id = $v;
			$this->fieldData["root_id"] = $v;
		}
		return $this;

	}

	public function setIsLastnode($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_lastnode !== $v)
		{
			$this->is_lastnode = $v;
			$this->fieldData["is_lastnode"] = $v;
		}
		return $this;

	}

	public function setProcessId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->process_id !== $v)
		{
			$this->process_id = $v;
			$this->fieldData["process_id"] = $v;
		}
		return $this;

	}

	public function setDeclareMapId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->declare_map_id !== $v)
		{
			$this->declare_map_id = $v;
			$this->fieldData["declare_map_id"] = $v;
		}
		return $this;

	}

	public function setTaskMapId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->task_map_id !== $v)
		{
			$this->task_map_id = $v;
			$this->fieldData["task_map_id"] = $v;
		}
		return $this;

	}

	public function setBudgetMapId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->budget_map_id !== $v)
		{
			$this->budget_map_id = $v;
			$this->fieldData["budget_map_id"] = $v;
		}
		return $this;

	}

	public function setStageMapId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->stage_map_id !== $v)
		{
			$this->stage_map_id = $v;
			$this->fieldData["stage_map_id"] = $v;
		}
		return $this;

	}

	public function setCompleteMapId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->complete_map_id !== $v)
		{
			$this->complete_map_id = $v;
			$this->fieldData["complete_map_id"] = $v;
		}
		return $this;

	}

	public function setApplyPath($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->apply_path !== $v)
		{
			$this->apply_path = $v;
			$this->fieldData["apply_path"] = $v;
		}
		return $this;

	}

	public function setBudgetPath($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->budget_path !== $v)
		{
			$this->budget_path = $v;
			$this->fieldData["budget_path"] = $v;
		}
		return $this;

	}

	public function setTaskPath($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->task_path !== $v)
		{
			$this->task_path = $v;
			$this->fieldData["task_path"] = $v;
		}
		return $this;

	}

	public function setStagePath($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->stage_path !== $v)
		{
			$this->stage_path = $v;
			$this->fieldData["stage_path"] = $v;
		}
		return $this;

	}

	public function setCompletePath($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->complete_path !== $v)
		{
			$this->complete_path = $v;
			$this->fieldData["complete_path"] = $v;
		}
		return $this;

	}

	public function setAssessId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->assess_id !== $v)
		{
			$this->assess_id = $v;
			$this->fieldData["assess_id"] = $v;
		}
		return $this;

	}

	public function setAssessRuleId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->assess_rule_id !== $v)
		{
			$this->assess_rule_id = $v;
			$this->fieldData["assess_rule_id"] = $v;
		}
		return $this;

	}

	public function setAssessPath($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->assess_path !== $v)
		{
			$this->assess_path = $v;
			$this->fieldData["assess_path"] = $v;
		}
		return $this;

	}

	public function setOfficeId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->office_id !== $v)
		{
			$this->office_id = $v;
			$this->fieldData["office_id"] = $v;
		}
		return $this;

	}

	public function setAllowGrade($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->allow_grade !== $v)
		{
			$this->allow_grade = $v;
			$this->fieldData["allow_grade"] = $v;
		}
		return $this;

	}

	public function setAllowMaterial($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->allow_material !== $v)
		{
			$this->allow_material = $v;
			$this->fieldData["allow_material"] = $v;
		}
		return $this;

	}

	public function setAllowCompany($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->allow_company !== $v)
		{
			$this->allow_company = $v;
			$this->fieldData["allow_company"] = $v;
		}
		return $this;

	}

	public function setProperty($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->property !== $v)
		{
			$this->property = $v;
			$this->fieldData["property"] = $v;
		}
		return $this;

	}

	public function setTemplate($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->template !== $v)
		{
			$this->template = $v;
			$this->fieldData["template"] = $v;
		}
		return $this;

	}

	public function setIsShow($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_show !== $v)
		{
			$this->is_show = $v;
			$this->fieldData["is_show"] = $v;
		}
		return $this;

	}

	public function setMarker($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->marker !== $v)
		{
			$this->marker = $v;
			$this->fieldData["marker"] = $v;
		}
		return $this;

	}

	public function setPathName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->path_name !== $v)
		{
			$this->path_name = $v;
			$this->fieldData["path_name"] = $v;
		}
		return $this;

	}

	public function setConfigs($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->configs !== $v)
		{
			$this->configs = $v;
			$this->fieldData["configs"] = $v;
		}
		return $this;

	}

	public function setRuleId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->rule_id !== $v)
		{
			$this->rule_id = $v;
			$this->fieldData["rule_id"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `guides` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"guideid" => $this->getGuideid(),
			"type" => $this->getType(),
			"cat_id" => $this->getCatId(),
			"subject" => $this->getSubject(),
			"mark" => $this->getMark(),
			"content" => $this->getContent(),
			"year" => $this->getYear(),
			"current_group" => $this->getCurrentGroup(),
			"report_start_at" => $this->getReportStartAt(),
			"report_end_at" => $this->getReportEndAt(),
			"start_at" => $this->getStartAt(),
			"end_at" => $this->getEndAt(),
			"submit_at" => $this->getSubmitAt(),
			"gather_end_at" => $this->getGatherEndAt(),
			"project_name" => $this->getProjectName(),
			"project_start_at" => $this->getProjectStartAt(),
			"project_end_at" => $this->getProjectEndAt(),
			"month_min" => $this->getMonthMin(),
			"month_max" => $this->getMonthMax(),
			"parent_id" => $this->getParentId(),
			"user_level" => $this->getUserLevel(),
			"lft" => $this->getLft(),
			"rgt" => $this->getRgt(),
			"level" => $this->getLevel(),
			"orders" => $this->getOrders(),
			"head_str" => $this->getHeadStr(),
			"root_id" => $this->getRootId(),
			"is_lastnode" => $this->getIsLastnode(),
			"process_id" => $this->getProcessId(),
			"declare_map_id" => $this->getDeclareMapId(),
			"task_map_id" => $this->getTaskMapId(),
			"budget_map_id" => $this->getBudgetMapId(),
			"stage_map_id" => $this->getStageMapId(),
			"complete_map_id" => $this->getCompleteMapId(),
			"apply_path" => $this->getApplyPath(),
			"budget_path" => $this->getBudgetPath(),
			"task_path" => $this->getTaskPath(),
			"stage_path" => $this->getStagePath(),
			"complete_path" => $this->getCompletePath(),
			"assess_id" => $this->getAssessId(),
			"assess_rule_id" => $this->getAssessRuleId(),
			"assess_path" => $this->getAssessPath(),
			"office_id" => $this->getOfficeId(),
			"allow_grade" => $this->getAllowGrade(),
			"allow_material" => $this->getAllowMaterial(),
			"allow_company" => $this->getAllowCompany(),
			"property" => $this->getProperty(),
			"template" => $this->getTemplate(),
			"is_show" => $this->getIsShow(),
			"marker" => $this->getMarker(),
			"path_name" => $this->getPathName(),
			"configs" => $this->getConfigs(),
			"rule_id" => $this->getRuleId(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->guideid = '';
		$this->type = '';
		$this->cat_id = '';
		$this->subject = '';
		$this->mark = '';
		$this->content = '';
		$this->year = '';
		$this->current_group = '';
		$this->report_start_at = '';
		$this->report_end_at = '';
		$this->start_at = '';
		$this->end_at = '';
		$this->submit_at = '';
		$this->gather_end_at = '';
		$this->project_name = '';
		$this->project_start_at = '';
		$this->project_end_at = '';
		$this->month_min = '';
		$this->month_max = '';
		$this->parent_id = '';
		$this->user_level = '';
		$this->lft = '';
		$this->rgt = '';
		$this->level = '';
		$this->orders = '';
		$this->head_str = '';
		$this->root_id = '';
		$this->is_lastnode = '';
		$this->process_id = '';
		$this->declare_map_id = '';
		$this->task_map_id = '';
		$this->budget_map_id = '';
		$this->stage_map_id = '';
		$this->complete_map_id = '';
		$this->apply_path = '';
		$this->budget_path = '';
		$this->task_path = '';
		$this->stage_path = '';
		$this->complete_path = '';
		$this->assess_id = '';
		$this->assess_rule_id = '';
		$this->assess_path = '';
		$this->office_id = '';
		$this->allow_grade = '';
		$this->allow_material = '';
		$this->allow_company = '';
		$this->property = '';
		$this->template = '';
		$this->is_show = '';
		$this->marker = '';
		$this->path_name = '';
		$this->configs = '';
		$this->rule_id = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["guideid"]) && $this->guideid = $data["guideid"];
		isset($data["type"]) && $this->type = $data["type"];
		isset($data["cat_id"]) && $this->cat_id = $data["cat_id"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["mark"]) && $this->mark = $data["mark"];
		isset($data["content"]) && $this->content = $data["content"];
		isset($data["year"]) && $this->year = $data["year"];
		isset($data["current_group"]) && $this->current_group = $data["current_group"];
		isset($data["report_start_at"]) && $this->report_start_at = $data["report_start_at"];
		isset($data["report_end_at"]) && $this->report_end_at = $data["report_end_at"];
		isset($data["start_at"]) && $this->start_at = $data["start_at"];
		isset($data["end_at"]) && $this->end_at = $data["end_at"];
		isset($data["submit_at"]) && $this->submit_at = $data["submit_at"];
		isset($data["gather_end_at"]) && $this->gather_end_at = $data["gather_end_at"];
		isset($data["project_name"]) && $this->project_name = $data["project_name"];
		isset($data["project_start_at"]) && $this->project_start_at = $data["project_start_at"];
		isset($data["project_end_at"]) && $this->project_end_at = $data["project_end_at"];
		isset($data["month_min"]) && $this->month_min = $data["month_min"];
		isset($data["month_max"]) && $this->month_max = $data["month_max"];
		isset($data["parent_id"]) && $this->parent_id = $data["parent_id"];
		isset($data["user_level"]) && $this->user_level = $data["user_level"];
		isset($data["lft"]) && $this->lft = $data["lft"];
		isset($data["rgt"]) && $this->rgt = $data["rgt"];
		isset($data["level"]) && $this->level = $data["level"];
		isset($data["orders"]) && $this->orders = $data["orders"];
		isset($data["head_str"]) && $this->head_str = $data["head_str"];
		isset($data["root_id"]) && $this->root_id = $data["root_id"];
		isset($data["is_lastnode"]) && $this->is_lastnode = $data["is_lastnode"];
		isset($data["process_id"]) && $this->process_id = $data["process_id"];
		isset($data["declare_map_id"]) && $this->declare_map_id = $data["declare_map_id"];
		isset($data["task_map_id"]) && $this->task_map_id = $data["task_map_id"];
		isset($data["budget_map_id"]) && $this->budget_map_id = $data["budget_map_id"];
		isset($data["stage_map_id"]) && $this->stage_map_id = $data["stage_map_id"];
		isset($data["complete_map_id"]) && $this->complete_map_id = $data["complete_map_id"];
		isset($data["apply_path"]) && $this->apply_path = $data["apply_path"];
		isset($data["budget_path"]) && $this->budget_path = $data["budget_path"];
		isset($data["task_path"]) && $this->task_path = $data["task_path"];
		isset($data["stage_path"]) && $this->stage_path = $data["stage_path"];
		isset($data["complete_path"]) && $this->complete_path = $data["complete_path"];
		isset($data["assess_id"]) && $this->assess_id = $data["assess_id"];
		isset($data["assess_rule_id"]) && $this->assess_rule_id = $data["assess_rule_id"];
		isset($data["assess_path"]) && $this->assess_path = $data["assess_path"];
		isset($data["office_id"]) && $this->office_id = $data["office_id"];
		isset($data["allow_grade"]) && $this->allow_grade = $data["allow_grade"];
		isset($data["allow_material"]) && $this->allow_material = $data["allow_material"];
		isset($data["allow_company"]) && $this->allow_company = $data["allow_company"];
		isset($data["property"]) && $this->property = $data["property"];
		isset($data["template"]) && $this->template = $data["template"];
		isset($data["is_show"]) && $this->is_show = $data["is_show"];
		isset($data["marker"]) && $this->marker = $data["marker"];
		isset($data["path_name"]) && $this->path_name = $data["path_name"];
		isset($data["configs"]) && $this->configs = $data["configs"];
		isset($data["rule_id"]) && $this->rule_id = $data["rule_id"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}