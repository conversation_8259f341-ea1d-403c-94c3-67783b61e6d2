<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BasePlatformDatas extends BaseModel
{
	private $id;
	private $platform_id;
	private $code;
	private $data;
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "platform_datas";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getPlatformId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->platform_id,0,$len,"utf-8");
			else return substr($this->platform_id,0,$len);
		}
		return $this->platform_id;
	}

	public function getCode($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->code,0,$len,"utf-8");
			else return substr($this->code,0,$len);
		}
		return $this->code;
	}

	public function getData($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->data,0,$len,"utf-8");
			else return substr($this->data,0,$len);
		}
		return $this->data;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setPlatformId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->platform_id !== $v)
		{
			$this->platform_id = $v;
			$this->fieldData["platform_id"] = $v;
		}
		return $this;

	}

	public function setCode($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->code !== $v)
		{
			$this->code = $v;
			$this->fieldData["code"] = $v;
		}
		return $this;

	}

	public function setData($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->data !== $v)
		{
			$this->data = $v;
			$this->fieldData["data"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `platform_datas` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"platform_id" => $this->getPlatformId(),
			"code" => $this->getCode(),
			"data" => $this->getData(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->platform_id = '';
		$this->code = '';
		$this->data = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["platform_id"]) && $this->platform_id = $data["platform_id"];
		isset($data["code"]) && $this->code = $data["code"];
		isset($data["data"]) && $this->data = $data["data"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}