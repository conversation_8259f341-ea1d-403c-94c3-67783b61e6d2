<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseEngineWorkers;

class EngineWorkers extends BaseEngineWorkers
{

    function selectBySubjectCode($subjectCode,$type='Apply',$catId='')
    {
        $addwhere = "`subject_code` = '".$subjectCode."' and `type` = '{$type}'";
        if($catId) $addwhere.= " and `cat_id` = '{$catId}'";
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE {$addwhere}");
        if($db->num_rows($query)){
            $this->fillObject($db->fetch_array($query));
        }else {
            $this->setSubjectCode($subjectCode);
            $this->setType($type);
        }
        return $this;
    }

    /**
     * 设置配置文件
     */
    function setConfigs($arr=[])
    {
//        $v = $this->builderConfigs();
//        parent::setConfigs(serialize($v));
//        return $this;
        return parent::setConfigs(json_encode($arr,JSON_UNESCAPED_UNICODE));
    }

    /**
     * 取得配置文件
     */
    function getConfigs($key='')
    {
        $configs = [];
        if(parent::getConfigs()){
            $configs = $this->dejson(parent::getConfigs(),true);
        }
        $widgetsConfigs = $this->builderConfigs();
        $configs = array_merge($configs,$widgetsConfigs);
        if($key) return $configs[$key];
        else return $configs;
    }


    private function dejson($str){
        $str = $this->replaceQuote($str);
        $str = stripslashes($str);
        $str = str_replace("\\", '\\\\', $str);
        $str = str_replace("\t", '\\t', $str);
        $str = str_replace("\r\n", '\n', $str);
        return json_decode($str, 1);
    }

    private function replaceQuote($string){
        return preg_replace('/\\\"([^"]*)\\\"/',"“$1”",$string);
    }

	/**
	 * 创建配置文件
	 */
	function builderConfigs()
	{
		$configs = array();
		$widgets = $this->widgets();
		while($widget = $widgets->getObject()){
			$configs[$widget->getWidgetName()] = $widget->getConfigs();
			$configs['widgets'][$widget->getWidgetName()] = $widget->getClassName();	
		}
		return $configs;
	}
	
	/**
	 * 取得模板
	 */
	function template()
	{
		return $this->getType()."/".$this->getTemplates();
	}
	
	/**
	 * 申报书类型
	 */
	function getTypeName()
	{
		switch($this->getType())
		{
			case 'Apply':
				return '申报材料';
			case 'Task':
				return '任务合同';
			case 'Summary':
				return '年度考核';
			case 'Week':
				return '周报';
			case 'Month':
				return '月度报告';
			case 'Budget':
				return '经费预算';
			case 'Stage':
				return '中期评估';
			case 'Inspect':
				return '绩效评价报告';
			case 'Review':
				return '年度重点工作清单';
			case 'Reviewquart':
				return '季度监测报告';
			case 'Reviewannual':
				return '年度监测报告';
			case 'Complete':
				return '验收报告';
			default:
				return '其他';
		}
	}
	
	/**
	 * 取得执行器部件数据
	 */
	function widgets()
	{
		return sf::getModel("EngineWorkerWidgets")->selectAll("engine_worker_id = '".$this->getId()."' ","ORDER BY orders asc,id ASC");
	}

	/**
	 * 取得执行器部件数据
	 */
	function getWidgetByName($widgetName)
	{
		return sf::getModel("EngineWorkerWidgets")->selectByName($widgetName,$this->getId());
	}
	
	function getPath()
	{
		return 'engine/'.$this->getParser();	
	}


    function getCatSubject()
    {
        return sf::getModel('Categorys',parent::getCatId(),'cat')->getSubject();
    }
			
}