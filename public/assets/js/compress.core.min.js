!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(x,e){"use strict";function g(e){return null!=e&&e===e.window}var t=[],n=Object.getPrototypeOf,a=t.slice,m=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},l=t.push,r=t.indexOf,i={},o=i.toString,v=i.hasOwnProperty,s=v.toString,c=s.call(Object),y={},b=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType},T=x.document,u={type:!0,src:!0,nonce:!0,noModule:!0};function _(e,t,n){var i,r,o=(n=n||T).createElement("script");if(o.text=e,t)for(i in u)(r=t[i]||t.getAttribute&&t.getAttribute(i))&&o.setAttribute(i,r);n.head.appendChild(o).parentNode.removeChild(o)}function p(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?i[o.call(e)]||"object":typeof e}var f="3.5.1",C=function(e,t){return new C.fn.init(e,t)};function d(e){var t=!!e&&"length"in e&&e.length,n=p(e);return!b(e)&&!g(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}C.fn=C.prototype={jquery:f,constructor:C,length:0,toArray:function(){return a.call(this)},get:function(e){return null==e?a.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){e=C.merge(this.constructor(),e);return e.prevObject=this,e},each:function(e){return C.each(this,e)},map:function(n){return this.pushStack(C.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(C.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(C.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:t.sort,splice:t.splice},C.extend=C.fn.extend=function(){var e,t,n,i,r,o=arguments[0]||{},s=1,a=arguments.length,l=!1;for("boolean"==typeof o&&(l=o,o=arguments[s]||{},s++),"object"==typeof o||b(o)||(o={}),s===a&&(o=this,s--);s<a;s++)if(null!=(e=arguments[s]))for(t in e)n=e[t],"__proto__"!==t&&o!==n&&(l&&n&&(C.isPlainObject(n)||(i=Array.isArray(n)))?(r=o[t],r=i&&!Array.isArray(r)?[]:i||C.isPlainObject(r)?r:{},i=!1,o[t]=C.extend(l,r,n)):void 0!==n&&(o[t]=n));return o},C.extend({expando:"jQuery"+(f+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){return!(!e||"[object Object]"!==o.call(e)||(e=n(e))&&("function"!=typeof(e=v.call(e,"constructor")&&e.constructor)||s.call(e)!==c))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t,n){_(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(d(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},makeArray:function(e,t){t=t||[];return null!=e&&(d(Object(e))?C.merge(t,"string"==typeof e?[e]:e):l.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:r.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;i<n;i++)e[r++]=t[i];return e.length=r,e},grep:function(e,t,n){for(var i=[],r=0,o=e.length,s=!n;r<o;r++)!t(e[r],r)!=s&&i.push(e[r]);return i},map:function(e,t,n){var i,r,o=0,s=[];if(d(e))for(i=e.length;o<i;o++)null!=(r=t(e[o],o,n))&&s.push(r);else for(o in e)null!=(r=t(e[o],o,n))&&s.push(r);return m(s)},guid:1,support:y}),"function"==typeof Symbol&&(C.fn[Symbol.iterator]=t[Symbol.iterator]),C.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){i["[object "+t+"]"]=t.toLowerCase()});var h=function(n){function f(e,t){return e="0x"+e.slice(1)-65536,t||(e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))}function i(){E()}var e,h,_,o,r,p,d,g,w,l,c,E,x,s,T,m,a,u,v,C="sizzle"+ +new Date,y=n.document,S=0,b=0,A=le(),k=le(),O=le(),N=le(),D=function(e,t){return e===t&&(c=!0),0},j={}.hasOwnProperty,t=[],L=t.pop,I=t.push,M=t.push,R=t.slice,P=function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1},q="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",H="[\\x20\\t\\r\\n\\f]",W="(?:\\\\[\\da-fA-F]{1,6}"+H+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",F="\\["+H+"*("+W+")(?:"+H+"*([*^$|!~]?=)"+H+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+W+"))|)"+H+"*\\]",B=":("+W+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+F+")*)|.*)\\)|)",z=new RegExp(H+"+","g"),$=new RegExp("^"+H+"+|((?:^|[^\\\\])(?:\\\\.)*)"+H+"+$","g"),V=new RegExp("^"+H+"*,"+H+"*"),U=new RegExp("^"+H+"*([>+~]|"+H+")"+H+"*"),Y=new RegExp(H+"|>"),Q=new RegExp(B),X=new RegExp("^"+W+"$"),G={ID:new RegExp("^#("+W+")"),CLASS:new RegExp("^\\.("+W+")"),TAG:new RegExp("^("+W+"|[*])"),ATTR:new RegExp("^"+F),PSEUDO:new RegExp("^"+B),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+H+"*(even|odd|(([+-]|)(\\d*)n|)"+H+"*(?:([+-]|)"+H+"*(\\d+)|))"+H+"*\\)|)","i"),bool:new RegExp("^(?:"+q+")$","i"),needsContext:new RegExp("^"+H+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+H+"*((?:-\\d)?\\d*)"+H+"*\\)|)(?=[^-]|$)","i")},K=/HTML$/i,J=/^(?:input|select|textarea|button)$/i,Z=/^h\d$/i,ee=/^[^{]+\{\s*\[native \w/,te=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ne=/[+~]/,ie=new RegExp("\\\\[\\da-fA-F]{1,6}"+H+"?|\\\\([^\\r\\n\\f])","g"),re=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,oe=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},se=ye(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{M.apply(t=R.call(y.childNodes),y.childNodes),t[y.childNodes.length].nodeType}catch(e){M={apply:t.length?function(e,t){I.apply(e,R.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function ae(e,t,n,i){var r,o,s,a,l,c,u,f=t&&t.ownerDocument,d=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==d&&9!==d&&11!==d)return n;if(!i&&(E(t),t=t||x,T)){if(11!==d&&(l=te.exec(e)))if(r=l[1]){if(9===d){if(!(s=t.getElementById(r)))return n;if(s.id===r)return n.push(s),n}else if(f&&(s=f.getElementById(r))&&v(t,s)&&s.id===r)return n.push(s),n}else{if(l[2])return M.apply(n,t.getElementsByTagName(e)),n;if((r=l[3])&&h.getElementsByClassName&&t.getElementsByClassName)return M.apply(n,t.getElementsByClassName(r)),n}if(h.qsa&&!N[e+" "]&&(!m||!m.test(e))&&(1!==d||"object"!==t.nodeName.toLowerCase())){if(u=e,f=t,1===d&&(Y.test(e)||U.test(e))){for((f=ne.test(e)&&ge(t.parentNode)||t)===t&&h.scope||((a=t.getAttribute("id"))?a=a.replace(re,oe):t.setAttribute("id",a=C)),o=(c=p(e)).length;o--;)c[o]=(a?"#"+a:":scope")+" "+ve(c[o]);u=c.join(",")}try{return M.apply(n,f.querySelectorAll(u)),n}catch(t){N(e,!0)}finally{a===C&&t.removeAttribute("id")}}}return g(e.replace($,"$1"),t,n,i)}function le(){var i=[];return function e(t,n){return i.push(t+" ")>_.cacheLength&&delete e[i.shift()],e[t+" "]=n}}function ce(e){return e[C]=!0,e}function ue(e){var t=x.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function fe(e,t){for(var n=e.split("|"),i=n.length;i--;)_.attrHandle[n[i]]=t}function de(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function he(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&se(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function pe(s){return ce(function(o){return o=+o,ce(function(e,t){for(var n,i=s([],e.length,o),r=i.length;r--;)e[n=i[r]]&&(e[n]=!(t[n]=e[n]))})})}function ge(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in h=ae.support={},r=ae.isXML=function(e){var t=e.namespaceURI,e=(e.ownerDocument||e).documentElement;return!K.test(t||e&&e.nodeName||"HTML")},E=ae.setDocument=function(e){var t,e=e?e.ownerDocument||e:y;return e!=x&&9===e.nodeType&&e.documentElement&&(s=(x=e).documentElement,T=!r(x),y!=x&&(t=x.defaultView)&&t.top!==t&&(t.addEventListener?t.addEventListener("unload",i,!1):t.attachEvent&&t.attachEvent("onunload",i)),h.scope=ue(function(e){return s.appendChild(e).appendChild(x.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),h.attributes=ue(function(e){return e.className="i",!e.getAttribute("className")}),h.getElementsByTagName=ue(function(e){return e.appendChild(x.createComment("")),!e.getElementsByTagName("*").length}),h.getElementsByClassName=ee.test(x.getElementsByClassName),h.getById=ue(function(e){return s.appendChild(e).id=C,!x.getElementsByName||!x.getElementsByName(C).length}),h.getById?(_.filter.ID=function(e){var t=e.replace(ie,f);return function(e){return e.getAttribute("id")===t}},_.find.ID=function(e,t){if(void 0!==t.getElementById&&T){e=t.getElementById(e);return e?[e]:[]}}):(_.filter.ID=function(e){var t=e.replace(ie,f);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},_.find.ID=function(e,t){if(void 0!==t.getElementById&&T){var n,i,r,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(r=t.getElementsByName(e),i=0;o=r[i++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),_.find.TAG=h.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):h.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[r++];)1===n.nodeType&&i.push(n);return i},_.find.CLASS=h.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&T)return t.getElementsByClassName(e)},a=[],m=[],(h.qsa=ee.test(x.querySelectorAll))&&(ue(function(e){var t;s.appendChild(e).innerHTML="<a id='"+C+"'></a><select id='"+C+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+H+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+H+"*(?:value|"+q+")"),e.querySelectorAll("[id~="+C+"-]").length||m.push("~="),(t=x.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||m.push("\\["+H+"*name"+H+"*="+H+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+C+"+*").length||m.push(".#.+[+~]"),e.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")}),ue(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=x.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+H+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),s.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")})),(h.matchesSelector=ee.test(u=s.matches||s.webkitMatchesSelector||s.mozMatchesSelector||s.oMatchesSelector||s.msMatchesSelector))&&ue(function(e){h.disconnectedMatch=u.call(e,"*"),u.call(e,"[s!='']:x"),a.push("!=",B)}),m=m.length&&new RegExp(m.join("|")),a=a.length&&new RegExp(a.join("|")),t=ee.test(s.compareDocumentPosition),v=t||ee.test(s.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},D=t?function(e,t){if(e===t)return c=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!h.sortDetached&&t.compareDocumentPosition(e)===n?e==x||e.ownerDocument==y&&v(y,e)?-1:t==x||t.ownerDocument==y&&v(y,t)?1:l?P(l,e)-P(l,t):0:4&n?-1:1)}:function(e,t){if(e===t)return c=!0,0;var n,i=0,r=e.parentNode,o=t.parentNode,s=[e],a=[t];if(!r||!o)return e==x?-1:t==x?1:r?-1:o?1:l?P(l,e)-P(l,t):0;if(r===o)return de(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)a.unshift(n);for(;s[i]===a[i];)i++;return i?de(s[i],a[i]):s[i]==y?-1:a[i]==y?1:0}),x},ae.matches=function(e,t){return ae(e,null,null,t)},ae.matchesSelector=function(e,t){if(E(e),h.matchesSelector&&T&&!N[t+" "]&&(!a||!a.test(t))&&(!m||!m.test(t)))try{var n=u.call(e,t);if(n||h.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){N(t,!0)}return 0<ae(t,x,null,[e]).length},ae.contains=function(e,t){return(e.ownerDocument||e)!=x&&E(e),v(e,t)},ae.attr=function(e,t){(e.ownerDocument||e)!=x&&E(e);var n=_.attrHandle[t.toLowerCase()],n=n&&j.call(_.attrHandle,t.toLowerCase())?n(e,t,!T):void 0;return void 0!==n?n:h.attributes||!T?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},ae.escape=function(e){return(e+"").replace(re,oe)},ae.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},ae.uniqueSort=function(e){var t,n=[],i=0,r=0;if(c=!h.detectDuplicates,l=!h.sortStable&&e.slice(0),e.sort(D),c){for(;t=e[r++];)t===e[r]&&(i=n.push(r));for(;i--;)e.splice(n[i],1)}return l=null,e},o=ae.getText=function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=o(t);return n},(_=ae.selectors={cacheLength:50,createPseudo:ce,match:G,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(ie,f),e[3]=(e[3]||e[4]||e[5]||"").replace(ie,f),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ae.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ae.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return G.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&Q.test(n)&&(t=p(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(ie,f).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=A[e+" "];return t||(t=new RegExp("(^|"+H+")"+e+"("+H+"|$)"))&&A(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,i){return function(e){e=ae.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===i:"!="===n?e!==i:"^="===n?i&&0===e.indexOf(i):"*="===n?i&&-1<e.indexOf(i):"$="===n?i&&e.slice(-i.length)===i:"~="===n?-1<(" "+e.replace(z," ")+" ").indexOf(i):"|="===n&&(e===i||e.slice(0,i.length+1)===i+"-"))}},CHILD:function(p,e,t,g,m){var v="nth"!==p.slice(0,3),y="last"!==p.slice(-4),b="of-type"===e;return 1===g&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var i,r,o,s,a,l,c=v!=y?"nextSibling":"previousSibling",u=e.parentNode,f=b&&e.nodeName.toLowerCase(),d=!n&&!b,h=!1;if(u){if(v){for(;c;){for(s=e;s=s[c];)if(b?s.nodeName.toLowerCase()===f:1===s.nodeType)return!1;l=c="only"===p&&!l&&"nextSibling"}return!0}if(l=[y?u.firstChild:u.lastChild],y&&d){for(h=(a=(i=(r=(o=(s=u)[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[p]||[])[0]===S&&i[1])&&i[2],s=a&&u.childNodes[a];s=++a&&s&&s[c]||(h=a=0)||l.pop();)if(1===s.nodeType&&++h&&s===e){r[p]=[S,a,h];break}}else if(d&&(h=a=(i=(r=(o=(s=e)[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[p]||[])[0]===S&&i[1]),!1===h)for(;(s=++a&&s&&s[c]||(h=a=0)||l.pop())&&((b?s.nodeName.toLowerCase()!==f:1!==s.nodeType)||!++h||(d&&((r=(o=s[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[p]=[S,h]),s!==e)););return(h-=m)===g||h%g==0&&0<=h/g}}},PSEUDO:function(e,o){var t,s=_.pseudos[e]||_.setFilters[e.toLowerCase()]||ae.error("unsupported pseudo: "+e);return s[C]?s(o):1<s.length?(t=[e,e,"",o],_.setFilters.hasOwnProperty(e.toLowerCase())?ce(function(e,t){for(var n,i=s(e,o),r=i.length;r--;)e[n=P(e,i[r])]=!(t[n]=i[r])}):function(e){return s(e,0,t)}):s}},pseudos:{not:ce(function(e){var i=[],r=[],a=d(e.replace($,"$1"));return a[C]?ce(function(e,t,n,i){for(var r,o=a(e,null,i,[]),s=e.length;s--;)(r=o[s])&&(e[s]=!(t[s]=r))}):function(e,t,n){return i[0]=e,a(i,null,n,r),i[0]=null,!r.pop()}}),has:ce(function(t){return function(e){return 0<ae(t,e).length}}),contains:ce(function(t){return t=t.replace(ie,f),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:ce(function(n){return X.test(n||"")||ae.error("unsupported lang: "+n),n=n.replace(ie,f).toLowerCase(),function(e){var t;do{if(t=T?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===s},focus:function(e){return e===x.activeElement&&(!x.hasFocus||x.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:he(!1),disabled:he(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!_.pseudos.empty(e)},header:function(e){return Z.test(e.nodeName)},input:function(e){return J.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:pe(function(){return[0]}),last:pe(function(e,t){return[t-1]}),eq:pe(function(e,t,n){return[n<0?n+t:n]}),even:pe(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:pe(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:pe(function(e,t,n){for(var i=n<0?n+t:t<n?t:n;0<=--i;)e.push(i);return e}),gt:pe(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=_.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})_.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})_.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function me(){}function ve(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function ye(s,e,t){var a=e.dir,l=e.next,c=l||a,u=t&&"parentNode"===c,f=b++;return e.first?function(e,t,n){for(;e=e[a];)if(1===e.nodeType||u)return s(e,t,n);return!1}:function(e,t,n){var i,r,o=[S,f];if(n){for(;e=e[a];)if((1===e.nodeType||u)&&s(e,t,n))return!0}else for(;e=e[a];)if(1===e.nodeType||u)if(i=(r=e[C]||(e[C]={}))[e.uniqueID]||(r[e.uniqueID]={}),l&&l===e.nodeName.toLowerCase())e=e[a]||e;else{if((r=i[c])&&r[0]===S&&r[1]===f)return o[2]=r[2];if((i[c]=o)[2]=s(e,t,n))return!0}return!1}}function be(r){return 1<r.length?function(e,t,n){for(var i=r.length;i--;)if(!r[i](e,t,n))return!1;return!0}:r[0]}function _e(e,t,n,i,r){for(var o,s=[],a=0,l=e.length,c=null!=t;a<l;a++)(o=e[a])&&(n&&!n(o,i,r)||(s.push(o),c&&t.push(a)));return s}function we(e){for(var i,t,n,r=e.length,o=_.relative[e[0].type],s=o||_.relative[" "],a=o?1:0,l=ye(function(e){return e===i},s,!0),c=ye(function(e){return-1<P(i,e)},s,!0),u=[function(e,t,n){n=!o&&(n||t!==w)||((i=t).nodeType?l:c)(e,t,n);return i=null,n}];a<r;a++)if(t=_.relative[e[a].type])u=[ye(be(u),t)];else{if((t=_.filter[e[a].type].apply(null,e[a].matches))[C]){for(n=++a;n<r&&!_.relative[e[n].type];n++);return function e(h,p,g,m,v,t){return m&&!m[C]&&(m=e(m)),v&&!v[C]&&(v=e(v,t)),ce(function(e,t,n,i){var r,o,s,a=[],l=[],c=t.length,u=e||function(e,t,n){for(var i=0,r=t.length;i<r;i++)ae(e,t[i],n);return n}(p||"*",n.nodeType?[n]:n,[]),f=!h||!e&&p?u:_e(u,a,h,n,i),d=g?v||(e?h:c||m)?[]:t:f;if(g&&g(f,d,n,i),m)for(r=_e(d,l),m(r,[],n,i),o=r.length;o--;)(s=r[o])&&(d[l[o]]=!(f[l[o]]=s));if(e){if(v||h){if(v){for(r=[],o=d.length;o--;)(s=d[o])&&r.push(f[o]=s);v(null,d=[],r,i)}for(o=d.length;o--;)(s=d[o])&&-1<(r=v?P(e,s):a[o])&&(e[r]=!(t[r]=s))}}else d=_e(d===t?d.splice(c,d.length):d),v?v(null,t,d,i):M.apply(t,d)})}(1<a&&be(u),1<a&&ve(e.slice(0,a-1).concat({value:" "===e[a-2].type?"*":""})).replace($,"$1"),t,a<n&&we(e.slice(a,n)),n<r&&we(e=e.slice(n)),n<r&&ve(e))}u.push(t)}return be(u)}return me.prototype=_.filters=_.pseudos,_.setFilters=new me,p=ae.tokenize=function(e,t){var n,i,r,o,s,a,l,c=k[e+" "];if(c)return t?0:c.slice(0);for(s=e,a=[],l=_.preFilter;s;){for(o in n&&!(i=V.exec(s))||(i&&(s=s.slice(i[0].length)||s),a.push(r=[])),n=!1,(i=U.exec(s))&&(n=i.shift(),r.push({value:n,type:i[0].replace($," ")}),s=s.slice(n.length)),_.filter)!(i=G[o].exec(s))||l[o]&&!(i=l[o](i))||(n=i.shift(),r.push({value:n,type:o,matches:i}),s=s.slice(n.length));if(!n)break}return t?s.length:s?ae.error(e):k(e,a).slice(0)},d=ae.compile=function(e,t){var n,m,v,y,b,i,r=[],o=[],s=O[e+" "];if(!s){for(n=(t=t||p(e)).length;n--;)((s=we(t[n]))[C]?r:o).push(s);(s=O(e,(m=o,y=0<(v=r).length,b=0<m.length,i=function(e,t,n,i,r){var o,s,a,l=0,c="0",u=e&&[],f=[],d=w,h=e||b&&_.find.TAG("*",r),p=S+=null==d?1:Math.random()||.1,g=h.length;for(r&&(w=t==x||t||r);c!==g&&null!=(o=h[c]);c++){if(b&&o){for(s=0,t||o.ownerDocument==x||(E(o),n=!T);a=m[s++];)if(a(o,t||x,n)){i.push(o);break}r&&(S=p)}y&&((o=!a&&o)&&l--,e&&u.push(o))}if(l+=c,y&&c!==l){for(s=0;a=v[s++];)a(u,f,t,n);if(e){if(0<l)for(;c--;)u[c]||f[c]||(f[c]=L.call(i));f=_e(f)}M.apply(i,f),r&&!e&&0<f.length&&1<l+v.length&&ae.uniqueSort(i)}return r&&(S=p,w=d),u},y?ce(i):i))).selector=e}return s},g=ae.select=function(e,t,n,i){var r,o,s,a,l,c="function"==typeof e&&e,u=!i&&p(e=c.selector||e);if(n=n||[],1===u.length){if(2<(o=u[0]=u[0].slice(0)).length&&"ID"===(s=o[0]).type&&9===t.nodeType&&T&&_.relative[o[1].type]){if(!(t=(_.find.ID(s.matches[0].replace(ie,f),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(r=G.needsContext.test(e)?0:o.length;r--&&(s=o[r],!_.relative[a=s.type]);)if((l=_.find[a])&&(i=l(s.matches[0].replace(ie,f),ne.test(o[0].type)&&ge(t.parentNode)||t))){if(o.splice(r,1),!(e=i.length&&ve(o)))return M.apply(n,i),n;break}}return(c||d(e,u))(i,t,!T,n,!t||ne.test(e)&&ge(t.parentNode)||t),n},h.sortStable=C.split("").sort(D).join("")===C,h.detectDuplicates=!!c,E(),h.sortDetached=ue(function(e){return 1&e.compareDocumentPosition(x.createElement("fieldset"))}),ue(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||fe("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),h.attributes&&ue(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||fe("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ue(function(e){return null==e.getAttribute("disabled")})||fe(q,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(t=e.getAttributeNode(t))&&t.specified?t.value:null}),ae}(x);C.find=h,C.expr=h.selectors,C.expr[":"]=C.expr.pseudos,C.uniqueSort=C.unique=h.uniqueSort,C.text=h.getText,C.isXMLDoc=h.isXML,C.contains=h.contains,C.escapeSelector=h.escape;function w(e,t,n){for(var i=[],r=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(r&&C(e).is(n))break;i.push(e)}return i}function E(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var S=C.expr.match.needsContext;function A(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var k=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function O(e,n,i){return b(n)?C.grep(e,function(e,t){return!!n.call(e,t,e)!==i}):n.nodeType?C.grep(e,function(e){return e===n!==i}):"string"!=typeof n?C.grep(e,function(e){return-1<r.call(n,e)!==i}):C.filter(n,e,i)}C.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?C.find.matchesSelector(i,e)?[i]:[]:C.find.matches(e,C.grep(t,function(e){return 1===e.nodeType}))},C.fn.extend({find:function(e){var t,n,i=this.length,r=this;if("string"!=typeof e)return this.pushStack(C(e).filter(function(){for(t=0;t<i;t++)if(C.contains(r[t],this))return!0}));for(n=this.pushStack([]),t=0;t<i;t++)C.find(e,r[t],n);return 1<i?C.uniqueSort(n):n},filter:function(e){return this.pushStack(O(this,e||[],!1))},not:function(e){return this.pushStack(O(this,e||[],!0))},is:function(e){return!!O(this,"string"==typeof e&&S.test(e)?C(e):e||[],!1).length}});var N,D=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(C.fn.init=function(e,t,n){if(!e)return this;if(n=n||N,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):b(e)?void 0!==n.ready?n.ready(e):e(C):C.makeArray(e,this);if(!(i="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:D.exec(e))||!i[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(i[1]){if(t=t instanceof C?t[0]:t,C.merge(this,C.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:T,!0)),k.test(i[1])&&C.isPlainObject(t))for(var i in t)b(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(e=T.getElementById(i[2]))&&(this[0]=e,this.length=1),this}).prototype=C.fn,N=C(T);var j=/^(?:parents|prev(?:Until|All))/,L={children:!0,contents:!0,next:!0,prev:!0};function I(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}C.fn.extend({has:function(e){var t=C(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(C.contains(this,t[e]))return!0})},closest:function(e,t){var n,i=0,r=this.length,o=[],s="string"!=typeof e&&C(e);if(!S.test(e))for(;i<r;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&C.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?C.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?r.call(C(e),this[0]):r.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(C.uniqueSort(C.merge(this.get(),C(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),C.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return w(e,"parentNode")},parentsUntil:function(e,t,n){return w(e,"parentNode",n)},next:function(e){return I(e,"nextSibling")},prev:function(e){return I(e,"previousSibling")},nextAll:function(e){return w(e,"nextSibling")},prevAll:function(e){return w(e,"previousSibling")},nextUntil:function(e,t,n){return w(e,"nextSibling",n)},prevUntil:function(e,t,n){return w(e,"previousSibling",n)},siblings:function(e){return E((e.parentNode||{}).firstChild,e)},children:function(e){return E(e.firstChild)},contents:function(e){return null!=e.contentDocument&&n(e.contentDocument)?e.contentDocument:(A(e,"template")&&(e=e.content||e),C.merge([],e.childNodes))}},function(i,r){C.fn[i]=function(e,t){var n=C.map(this,r,e);return"Until"!==i.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=C.filter(t,n)),1<this.length&&(L[i]||C.uniqueSort(n),j.test(i)&&n.reverse()),this.pushStack(n)}});var M=/[^\x20\t\r\n\f]+/g;function R(e){return e}function P(e){throw e}function q(e,t,n,i){var r;try{e&&b(r=e.promise)?r.call(e).done(t).fail(n):e&&b(r=e.then)?r.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}C.Callbacks=function(i){var n;i="string"==typeof i?(n={},C.each(i.match(M)||[],function(e,t){n[t]=!0}),n):C.extend({},i);function r(){for(s=s||i.once,t=o=!0;l.length;c=-1)for(e=l.shift();++c<a.length;)!1===a[c].apply(e[0],e[1])&&i.stopOnFalse&&(c=a.length,e=!1);i.memory||(e=!1),o=!1,s&&(a=e?[]:"")}var o,e,t,s,a=[],l=[],c=-1,u={add:function(){return a&&(e&&!o&&(c=a.length-1,l.push(e)),function n(e){C.each(e,function(e,t){b(t)?i.unique&&u.has(t)||a.push(t):t&&t.length&&"string"!==p(t)&&n(t)})}(arguments),e&&!o&&r()),this},remove:function(){return C.each(arguments,function(e,t){for(var n;-1<(n=C.inArray(t,a,n));)a.splice(n,1),n<=c&&c--}),this},has:function(e){return e?-1<C.inArray(e,a):0<a.length},empty:function(){return a=a&&[],this},disable:function(){return s=l=[],a=e="",this},disabled:function(){return!a},lock:function(){return s=l=[],e||o||(a=e=""),this},locked:function(){return!!s},fireWith:function(e,t){return s||(t=[e,(t=t||[]).slice?t.slice():t],l.push(t),o||r()),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!t}};return u},C.extend({Deferred:function(e){var o=[["notify","progress",C.Callbacks("memory"),C.Callbacks("memory"),2],["resolve","done",C.Callbacks("once memory"),C.Callbacks("once memory"),0,"resolved"],["reject","fail",C.Callbacks("once memory"),C.Callbacks("once memory"),1,"rejected"]],r="pending",s={state:function(){return r},always:function(){return a.done(arguments).fail(arguments),this},catch:function(e){return s.then(null,e)},pipe:function(){var r=arguments;return C.Deferred(function(i){C.each(o,function(e,t){var n=b(r[t[4]])&&r[t[4]];a[t[1]](function(){var e=n&&n.apply(this,arguments);e&&b(e.promise)?e.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[t[0]+"With"](this,n?[e]:arguments)})}),r=null}).promise()},then:function(t,n,i){var l=0;function c(r,o,s,a){return function(){function e(){var e,t;if(!(r<l)){if((e=s.apply(n,i))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,b(t)?a?t.call(e,c(l,o,R,a),c(l,o,P,a)):(l++,t.call(e,c(l,o,R,a),c(l,o,P,a),c(l,o,R,o.notifyWith))):(s!==R&&(n=void 0,i=[e]),(a||o.resolveWith)(n,i))}}var n=this,i=arguments,t=a?e:function(){try{e()}catch(e){C.Deferred.exceptionHook&&C.Deferred.exceptionHook(e,t.stackTrace),l<=r+1&&(s!==P&&(n=void 0,i=[e]),o.rejectWith(n,i))}};r?t():(C.Deferred.getStackHook&&(t.stackTrace=C.Deferred.getStackHook()),x.setTimeout(t))}}return C.Deferred(function(e){o[0][3].add(c(0,e,b(i)?i:R,e.notifyWith)),o[1][3].add(c(0,e,b(t)?t:R)),o[2][3].add(c(0,e,b(n)?n:P))}).promise()},promise:function(e){return null!=e?C.extend(e,s):s}},a={};return C.each(o,function(e,t){var n=t[2],i=t[5];s[t[1]]=n.add,i&&n.add(function(){r=i},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),a[t[0]]=function(){return a[t[0]+"With"](this===a?void 0:this,arguments),this},a[t[0]+"With"]=n.fireWith}),s.promise(a),e&&e.call(a,a),a},when:function(e){function t(t){return function(e){r[t]=this,o[t]=1<arguments.length?a.call(arguments):e,--n||s.resolveWith(r,o)}}var n=arguments.length,i=n,r=Array(i),o=a.call(arguments),s=C.Deferred();if(n<=1&&(q(e,s.done(t(i)).resolve,s.reject,!n),"pending"===s.state()||b(o[i]&&o[i].then)))return s.then();for(;i--;)q(o[i],t(i),s.reject);return s.promise()}});var H=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;C.Deferred.exceptionHook=function(e,t){x.console&&x.console.warn&&e&&H.test(e.name)&&x.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},C.readyException=function(e){x.setTimeout(function(){throw e})};var W=C.Deferred();function F(){T.removeEventListener("DOMContentLoaded",F),x.removeEventListener("load",F),C.ready()}C.fn.ready=function(e){return W.then(e).catch(function(e){C.readyException(e)}),this},C.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--C.readyWait:C.isReady)||(C.isReady=!0)!==e&&0<--C.readyWait||W.resolveWith(T,[C])}}),C.ready.then=W.then,"complete"===T.readyState||"loading"!==T.readyState&&!T.documentElement.doScroll?x.setTimeout(C.ready):(T.addEventListener("DOMContentLoaded",F),x.addEventListener("load",F));var B=function(e,t,n,i,r,o,s){var a=0,l=e.length,c=null==n;if("object"===p(n))for(a in r=!0,n)B(e,t,a,n[a],!0,o,s);else if(void 0!==i&&(r=!0,b(i)||(s=!0),c&&(t=s?(t.call(e,i),null):(c=t,function(e,t,n){return c.call(C(e),n)})),t))for(;a<l;a++)t(e[a],n,s?i:i.call(e[a],a,t(e[a],n)));return r?e:c?t.call(e):l?t(e[0],n):o},z=/^-ms-/,$=/-([a-z])/g;function V(e,t){return t.toUpperCase()}function U(e){return e.replace(z,"ms-").replace($,V)}function Y(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function Q(){this.expando=C.expando+Q.uid++}Q.uid=1,Q.prototype={cache:function(e){var t=e[this.expando];return t||(t={},Y(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,r=this.cache(e);if("string"==typeof t)r[U(t)]=n;else for(i in t)r[U(i)]=t[i];return r},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][U(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(U):(t=U(t))in i?[t]:t.match(M)||[]).length;for(;n--;)delete i[t[n]]}void 0!==t&&!C.isEmptyObject(i)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){e=e[this.expando];return void 0!==e&&!C.isEmptyObject(e)}};var X=new Q,G=new Q,K=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,J=/[A-Z]/g;function Z(e,t,n){var i,r;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(J,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n="true"===(r=n)||"false"!==r&&("null"===r?null:r===+r+""?+r:K.test(r)?JSON.parse(r):r)}catch(e){}G.set(e,t,n)}else n=void 0;return n}C.extend({hasData:function(e){return G.hasData(e)||X.hasData(e)},data:function(e,t,n){return G.access(e,t,n)},removeData:function(e,t){G.remove(e,t)},_data:function(e,t,n){return X.access(e,t,n)},_removeData:function(e,t){X.remove(e,t)}}),C.fn.extend({data:function(n,e){var t,i,r,o=this[0],s=o&&o.attributes;if(void 0!==n)return"object"==typeof n?this.each(function(){G.set(this,n)}):B(this,function(e){var t;return o&&void 0===e?void 0!==(t=G.get(o,n))||void 0!==(t=Z(o,n))?t:void 0:void this.each(function(){G.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(r=G.get(o),1===o.nodeType&&!X.get(o,"hasDataAttrs"))){for(t=s.length;t--;)s[t]&&0===(i=s[t].name).indexOf("data-")&&(i=U(i.slice(5)),Z(o,i,r[i]));X.set(o,"hasDataAttrs",!0)}return r},removeData:function(e){return this.each(function(){G.remove(this,e)})}}),C.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=X.get(e,t),n&&(!i||Array.isArray(n)?i=X.access(e,t,C.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=C.queue(e,t),i=n.length,r=n.shift(),o=C._queueHooks(e,t);"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,function(){C.dequeue(e,t)},o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return X.get(e,n)||X.access(e,n,{empty:C.Callbacks("once memory").add(function(){X.remove(e,[t+"queue",n])})})}}),C.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?C.queue(this[0],t):void 0===n?this:this.each(function(){var e=C.queue(this,t,n);C._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&C.dequeue(this,t)})},dequeue:function(e){return this.each(function(){C.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--r||o.resolveWith(s,[s])}var i,r=1,o=C.Deferred(),s=this,a=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(i=X.get(s[a],e+"queueHooks"))&&i.empty&&(r++,i.empty.add(n));return n(),o.promise(t)}});var ee=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,te=new RegExp("^(?:([+-])=|)("+ee+")([a-z%]*)$","i"),ne=["Top","Right","Bottom","Left"],ie=T.documentElement,re=function(e){return C.contains(e.ownerDocument,e)},oe={composed:!0};ie.getRootNode&&(re=function(e){return C.contains(e.ownerDocument,e)||e.getRootNode(oe)===e.ownerDocument});function se(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&re(e)&&"none"===C.css(e,"display")}function ae(e,t,n,i){var r,o,s=20,a=i?function(){return i.cur()}:function(){return C.css(e,t,"")},l=a(),c=n&&n[3]||(C.cssNumber[t]?"":"px"),u=e.nodeType&&(C.cssNumber[t]||"px"!==c&&+l)&&te.exec(C.css(e,t));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;s--;)C.style(e,t,u+c),(1-o)*(1-(o=a()/l||.5))<=0&&(s=0),u/=o;u*=2,C.style(e,t,u+c),n=n||[]}return n&&(u=+u||+l||0,r=n[1]?u+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=u,i.end=r)),r}var le={};function ce(e,t){for(var n,i,r,o,s,a,l=[],c=0,u=e.length;c<u;c++)(i=e[c]).style&&(n=i.style.display,t?("none"===n&&(l[c]=X.get(i,"display")||null,l[c]||(i.style.display="")),""===i.style.display&&se(i)&&(l[c]=(a=o=r=void 0,o=i.ownerDocument,s=i.nodeName,(a=le[s])||(r=o.body.appendChild(o.createElement(s)),a=C.css(r,"display"),r.parentNode.removeChild(r),"none"===a&&(a="block"),le[s]=a)))):"none"!==n&&(l[c]="none",X.set(i,"display",n)));for(c=0;c<u;c++)null!=l[c]&&(e[c].style.display=l[c]);return e}C.fn.extend({show:function(){return ce(this,!0)},hide:function(){return ce(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){se(this)?C(this).show():C(this).hide()})}});var ue=/^(?:checkbox|radio)$/i,fe=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,de=/^$|^module$|\/(?:java|ecma)script/i,f=T.createDocumentFragment().appendChild(T.createElement("div"));(h=T.createElement("input")).setAttribute("type","radio"),h.setAttribute("checked","checked"),h.setAttribute("name","t"),f.appendChild(h),y.checkClone=f.cloneNode(!0).cloneNode(!0).lastChild.checked,f.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!f.cloneNode(!0).lastChild.defaultValue,f.innerHTML="<option></option>",y.option=!!f.lastChild;var he={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function pe(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&A(e,t)?C.merge([e],n):n}function ge(e,t){for(var n=0,i=e.length;n<i;n++)X.set(e[n],"globalEval",!t||X.get(t[n],"globalEval"))}he.tbody=he.tfoot=he.colgroup=he.caption=he.thead,he.th=he.td,y.option||(he.optgroup=he.option=[1,"<select multiple='multiple'>","</select>"]);var me=/<|&#?\w+;/;function ve(e,t,n,i,r){for(var o,s,a,l,c,u=t.createDocumentFragment(),f=[],d=0,h=e.length;d<h;d++)if((o=e[d])||0===o)if("object"===p(o))C.merge(f,o.nodeType?[o]:o);else if(me.test(o)){for(s=s||u.appendChild(t.createElement("div")),a=(fe.exec(o)||["",""])[1].toLowerCase(),a=he[a]||he._default,s.innerHTML=a[1]+C.htmlPrefilter(o)+a[2],c=a[0];c--;)s=s.lastChild;C.merge(f,s.childNodes),(s=u.firstChild).textContent=""}else f.push(t.createTextNode(o));for(u.textContent="",d=0;o=f[d++];)if(i&&-1<C.inArray(o,i))r&&r.push(o);else if(l=re(o),s=pe(u.appendChild(o),"script"),l&&ge(s),n)for(c=0;o=s[c++];)de.test(o.type||"")&&n.push(o);return u}var ye=/^key/,be=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,_e=/^([^.]*)(?:\.(.+)|)/;function we(){return!0}function Ee(){return!1}function xe(e,t){return e===function(){try{return T.activeElement}catch(e){}}()==("focus"===t)}function Te(e,t,n,i,r,o){var s,a;if("object"==typeof t){for(a in"string"!=typeof n&&(i=i||n,n=void 0),t)Te(e,a,n,i,t[a],o);return e}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),!1===r)r=Ee;else if(!r)return e;return 1===o&&(s=r,(r=function(e){return C().off(e),s.apply(this,arguments)}).guid=s.guid||(s.guid=C.guid++)),e.each(function(){C.event.add(this,t,r,i,n)})}function Ce(e,r,o){o?(X.set(e,r,!1),C.event.add(e,r,{namespace:!1,handler:function(e){var t,n,i=X.get(this,r);if(1&e.isTrigger&&this[r]){if(i.length)(C.event.special[r]||{}).delegateType&&e.stopPropagation();else if(i=a.call(arguments),X.set(this,r,i),t=o(this,r),this[r](),i!==(n=X.get(this,r))||t?X.set(this,r,!1):n={},i!==n)return e.stopImmediatePropagation(),e.preventDefault(),n.value}else i.length&&(X.set(this,r,{value:C.event.trigger(C.extend(i[0],C.Event.prototype),i.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===X.get(e,r)&&C.event.add(e,r,we)}C.event={global:{},add:function(t,e,n,i,r){var o,s,a,l,c,u,f,d,h,p=X.get(t);if(Y(t))for(n.handler&&(n=(o=n).handler,r=o.selector),r&&C.find.matchesSelector(ie,r),n.guid||(n.guid=C.guid++),(a=p.events)||(a=p.events=Object.create(null)),(s=p.handle)||(s=p.handle=function(e){return void 0!==C&&C.event.triggered!==e.type?C.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(M)||[""]).length;l--;)f=h=(c=_e.exec(e[l])||[])[1],d=(c[2]||"").split(".").sort(),f&&(u=C.event.special[f]||{},f=(r?u.delegateType:u.bindType)||f,u=C.event.special[f]||{},c=C.extend({type:f,origType:h,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&C.expr.match.needsContext.test(r),namespace:d.join(".")},o),(h=a[f])||((h=a[f]=[]).delegateCount=0,u.setup&&!1!==u.setup.call(t,i,d,s)||t.addEventListener&&t.addEventListener(f,s)),u.add&&(u.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),r?h.splice(h.delegateCount++,0,c):h.push(c),C.event.global[f]=!0)},remove:function(e,t,n,i,r){var o,s,a,l,c,u,f,d,h,p,g,m=X.hasData(e)&&X.get(e);if(m&&(l=m.events)){for(c=(t=(t||"").match(M)||[""]).length;c--;)if(h=g=(a=_e.exec(t[c])||[])[1],p=(a[2]||"").split(".").sort(),h){for(f=C.event.special[h]||{},d=l[h=(i?f.delegateType:f.bindType)||h]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=d.length;o--;)u=d[o],!r&&g!==u.origType||n&&n.guid!==u.guid||a&&!a.test(u.namespace)||i&&i!==u.selector&&("**"!==i||!u.selector)||(d.splice(o,1),u.selector&&d.delegateCount--,f.remove&&f.remove.call(e,u));s&&!d.length&&(f.teardown&&!1!==f.teardown.call(e,p,m.handle)||C.removeEvent(e,h,m.handle),delete l[h])}else for(h in l)C.event.remove(e,h+t[c],n,i,!0);C.isEmptyObject(l)&&X.remove(e,"handle events")}},dispatch:function(e){var t,n,i,r,o,s=new Array(arguments.length),a=C.event.fix(e),l=(X.get(this,"events")||Object.create(null))[a.type]||[],e=C.event.special[a.type]||{};for(s[0]=a,t=1;t<arguments.length;t++)s[t]=arguments[t];if(a.delegateTarget=this,!e.preDispatch||!1!==e.preDispatch.call(this,a)){for(o=C.event.handlers.call(this,a,l),t=0;(i=o[t++])&&!a.isPropagationStopped();)for(a.currentTarget=i.elem,n=0;(r=i.handlers[n++])&&!a.isImmediatePropagationStopped();)a.rnamespace&&!1!==r.namespace&&!a.rnamespace.test(r.namespace)||(a.handleObj=r,a.data=r.data,void 0!==(r=((C.event.special[r.origType]||{}).handle||r.handler).apply(i.elem,s))&&!1===(a.result=r)&&(a.preventDefault(),a.stopPropagation()));return e.postDispatch&&e.postDispatch.call(this,a),a.result}},handlers:function(e,t){var n,i,r,o,s,a=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!("click"===e.type&&1<=e.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(o=[],s={},n=0;n<l;n++)void 0===s[r=(i=t[n]).selector+" "]&&(s[r]=i.needsContext?-1<C(r,this).index(c):C.find(r,this,null,[c]).length),s[r]&&o.push(i);o.length&&a.push({elem:c,handlers:o})}return c=this,l<t.length&&a.push({elem:c,handlers:t.slice(l)}),a},addProp:function(t,e){Object.defineProperty(C.Event.prototype,t,{enumerable:!0,configurable:!0,get:b(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[C.expando]?e:new C.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){e=this||e;return ue.test(e.type)&&e.click&&A(e,"input")&&Ce(e,"click",we),!1},trigger:function(e){e=this||e;return ue.test(e.type)&&e.click&&A(e,"input")&&Ce(e,"click"),!0},_default:function(e){e=e.target;return ue.test(e.type)&&e.click&&A(e,"input")&&X.get(e,"click")||A(e,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},C.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},C.Event=function(e,t){if(!(this instanceof C.Event))return new C.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?we:Ee,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&C.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[C.expando]=!0},C.Event.prototype={constructor:C.Event,isDefaultPrevented:Ee,isPropagationStopped:Ee,isImmediatePropagationStopped:Ee,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=we,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=we,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=we,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},C.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&ye.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&be.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},C.event.addProp),C.each({focus:"focusin",blur:"focusout"},function(e,t){C.event.special[e]={setup:function(){return Ce(this,e,xe),!1},trigger:function(){return Ce(this,e),!0},delegateType:t}}),C.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,r){C.event.special[e]={delegateType:r,bindType:r,handle:function(e){var t,n=e.relatedTarget,i=e.handleObj;return n&&(n===this||C.contains(this,n))||(e.type=i.origType,t=i.handler.apply(this,arguments),e.type=r),t}}}),C.fn.extend({on:function(e,t,n,i){return Te(this,e,t,n,i)},one:function(e,t,n,i){return Te(this,e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,C(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Ee),this.each(function(){C.event.remove(this,e,n,t)});for(r in e)this.off(r,t,e[r]);return this}});var Se=/<script|<style|<link/i,Ae=/checked\s*(?:[^=]|=\s*.checked.)/i,ke=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Oe(e,t){return A(e,"table")&&A(11!==t.nodeType?t:t.firstChild,"tr")&&C(e).children("tbody")[0]||e}function Ne(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function De(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function je(e,t){var n,i,r,o;if(1===t.nodeType){if(X.hasData(e)&&(o=X.get(e).events))for(r in X.remove(t,"handle events"),o)for(n=0,i=o[r].length;n<i;n++)C.event.add(t,r,o[r][n]);G.hasData(e)&&(e=G.access(e),e=C.extend({},e),G.set(t,e))}}function Le(n,i,r,o){i=m(i);var e,t,s,a,l,c,u=0,f=n.length,d=f-1,h=i[0],p=b(h);if(p||1<f&&"string"==typeof h&&!y.checkClone&&Ae.test(h))return n.each(function(e){var t=n.eq(e);p&&(i[0]=h.call(this,e,t.html())),Le(t,i,r,o)});if(f&&(t=(e=ve(i,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(a=(s=C.map(pe(e,"script"),Ne)).length;u<f;u++)l=e,u!==d&&(l=C.clone(l,!0,!0),a&&C.merge(s,pe(l,"script"))),r.call(n[u],l,u);if(a)for(c=s[s.length-1].ownerDocument,C.map(s,De),u=0;u<a;u++)l=s[u],de.test(l.type||"")&&!X.access(l,"globalEval")&&C.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?C._evalUrl&&!l.noModule&&C._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},c):_(l.textContent.replace(ke,""),l,c))}return n}function Ie(e,t,n){for(var i,r=t?C.filter(t,e):e,o=0;null!=(i=r[o]);o++)n||1!==i.nodeType||C.cleanData(pe(i)),i.parentNode&&(n&&re(i)&&ge(pe(i,"script")),i.parentNode.removeChild(i));return e}C.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,r,o,s,a,l,c,u=e.cloneNode(!0),f=re(e);if(!(y.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||C.isXMLDoc(e)))for(s=pe(u),i=0,r=(o=pe(e)).length;i<r;i++)a=o[i],"input"===(c=(l=s[i]).nodeName.toLowerCase())&&ue.test(a.type)?l.checked=a.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=a.defaultValue);if(t)if(n)for(o=o||pe(e),s=s||pe(u),i=0,r=o.length;i<r;i++)je(o[i],s[i]);else je(e,u);return 0<(s=pe(u,"script")).length&&ge(s,!f&&pe(e,"script")),u},cleanData:function(e){for(var t,n,i,r=C.event.special,o=0;void 0!==(n=e[o]);o++)if(Y(n)){if(t=n[X.expando]){if(t.events)for(i in t.events)r[i]?C.event.remove(n,i):C.removeEvent(n,i,t.handle);n[X.expando]=void 0}n[G.expando]&&(n[G.expando]=void 0)}}}),C.fn.extend({detach:function(e){return Ie(this,e,!0)},remove:function(e){return Ie(this,e)},text:function(e){return B(this,function(e){return void 0===e?C.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Le(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Oe(this,e).appendChild(e)})},prepend:function(){return Le(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=Oe(this,e)).insertBefore(e,t.firstChild)})},before:function(){return Le(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Le(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(C.cleanData(pe(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return C.clone(this,e,t)})},html:function(e){return B(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Se.test(e)&&!he[(fe.exec(e)||["",""])[1].toLowerCase()]){e=C.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(C.cleanData(pe(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Le(this,arguments,function(e){var t=this.parentNode;C.inArray(this,n)<0&&(C.cleanData(pe(this)),t&&t.replaceChild(e,this))},n)}}),C.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,s){C.fn[e]=function(e){for(var t,n=[],i=C(e),r=i.length-1,o=0;o<=r;o++)t=o===r?this:this.clone(!0),C(i[o])[s](t),l.apply(n,t.get());return this.pushStack(n)}});function Me(e,t,n){var i,r={};for(i in t)r[i]=e.style[i],e.style[i]=t[i];for(i in n=n.call(e),t)e.style[i]=r[i];return n}var Re,Pe,qe,He,We,Fe,Be,ze,$e=new RegExp("^("+ee+")(?!px)[a-z%]+$","i"),Ve=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=x),t.getComputedStyle(e)},Ue=new RegExp(ne.join("|"),"i");function Ye(e,t,n){var i,r,o=e.style;return(n=n||Ve(e))&&(""!==(r=n.getPropertyValue(t)||n[t])||re(e)||(r=C.style(e,t)),!y.pixelBoxStyles()&&$e.test(r)&&Ue.test(t)&&(i=o.width,e=o.minWidth,t=o.maxWidth,o.minWidth=o.maxWidth=o.width=r,r=n.width,o.width=i,o.minWidth=e,o.maxWidth=t)),void 0!==r?r+"":r}function Qe(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}function Xe(){var e;ze&&(Be.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",ze.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ie.appendChild(Be).appendChild(ze),e=x.getComputedStyle(ze),Re="1%"!==e.top,Fe=12===Ge(e.marginLeft),ze.style.right="60%",He=36===Ge(e.right),Pe=36===Ge(e.width),ze.style.position="absolute",qe=12===Ge(ze.offsetWidth/3),ie.removeChild(Be),ze=null)}function Ge(e){return Math.round(parseFloat(e))}Be=T.createElement("div"),(ze=T.createElement("div")).style&&(ze.style.backgroundClip="content-box",ze.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===ze.style.backgroundClip,C.extend(y,{boxSizingReliable:function(){return Xe(),Pe},pixelBoxStyles:function(){return Xe(),He},pixelPosition:function(){return Xe(),Re},reliableMarginLeft:function(){return Xe(),Fe},scrollboxSize:function(){return Xe(),qe},reliableTrDimensions:function(){var e,t,n;return null==We&&(e=T.createElement("table"),n=T.createElement("tr"),t=T.createElement("div"),e.style.cssText="position:absolute;left:-11111px",n.style.height="1px",t.style.height="9px",ie.appendChild(e).appendChild(n).appendChild(t),n=x.getComputedStyle(n),We=3<parseInt(n.height),ie.removeChild(e)),We}}));var Ke=["Webkit","Moz","ms"],Je=T.createElement("div").style,Ze={};function et(e){return C.cssProps[e]||Ze[e]||(e in Je?e:Ze[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Ke.length;n--;)if((e=Ke[n]+t)in Je)return e}(e)||e)}var tt=/^(none|table(?!-c[ea]).+)/,nt=/^--/,it={position:"absolute",visibility:"hidden",display:"block"},rt={letterSpacing:"0",fontWeight:"400"};function ot(e,t,n){var i=te.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function st(e,t,n,i,r,o){var s="width"===t?1:0,a=0,l=0;if(n===(i?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(l+=C.css(e,n+ne[s],!0,r)),i?("content"===n&&(l-=C.css(e,"padding"+ne[s],!0,r)),"margin"!==n&&(l-=C.css(e,"border"+ne[s]+"Width",!0,r))):(l+=C.css(e,"padding"+ne[s],!0,r),"padding"!==n?l+=C.css(e,"border"+ne[s]+"Width",!0,r):a+=C.css(e,"border"+ne[s]+"Width",!0,r));return!i&&0<=o&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-a-.5))||0),l}function at(e,t,n){var i=Ve(e),r=(!y.boxSizingReliable()||n)&&"border-box"===C.css(e,"boxSizing",!1,i),o=r,s=Ye(e,t,i),a="offset"+t[0].toUpperCase()+t.slice(1);if($e.test(s)){if(!n)return s;s="auto"}return(!y.boxSizingReliable()&&r||!y.reliableTrDimensions()&&A(e,"tr")||"auto"===s||!parseFloat(s)&&"inline"===C.css(e,"display",!1,i))&&e.getClientRects().length&&(r="border-box"===C.css(e,"boxSizing",!1,i),(o=a in e)&&(s=e[a])),(s=parseFloat(s)||0)+st(e,t,n||(r?"border":"content"),o,i,s)+"px"}function lt(e,t,n,i,r){return new lt.prototype.init(e,t,n,i,r)}C.extend({cssHooks:{opacity:{get:function(e,t){if(t){e=Ye(e,"opacity");return""===e?"1":e}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,s,a=U(t),l=nt.test(t),c=e.style;if(l||(t=et(a)),s=C.cssHooks[t]||C.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(r=s.get(e,!1,i))?r:c[t];"string"==(o=typeof n)&&(r=te.exec(n))&&r[1]&&(n=ae(e,t,r),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=r&&r[3]||(C.cssNumber[a]?"":"px")),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,i))||(l?c.setProperty(t,n):c[t]=n))}},css:function(e,t,n,i){var r,o=U(t);return nt.test(t)||(t=et(o)),(o=C.cssHooks[t]||C.cssHooks[o])&&"get"in o&&(r=o.get(e,!0,n)),void 0===r&&(r=Ye(e,t,i)),"normal"===r&&t in rt&&(r=rt[t]),""===n||n?(t=parseFloat(r),!0===n||isFinite(t)?t||0:r):r}}),C.each(["height","width"],function(e,a){C.cssHooks[a]={get:function(e,t,n){if(t)return!tt.test(C.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?at(e,a,n):Me(e,it,function(){return at(e,a,n)})},set:function(e,t,n){var i,r=Ve(e),o=!y.scrollboxSize()&&"absolute"===r.position,s=(o||n)&&"border-box"===C.css(e,"boxSizing",!1,r),n=n?st(e,a,n,s,r):0;return s&&o&&(n-=Math.ceil(e["offset"+a[0].toUpperCase()+a.slice(1)]-parseFloat(r[a])-st(e,a,"border",!1,r)-.5)),n&&(i=te.exec(t))&&"px"!==(i[3]||"px")&&(e.style[a]=t,t=C.css(e,a)),ot(0,t,n)}}}),C.cssHooks.marginLeft=Qe(y.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Ye(e,"marginLeft"))||e.getBoundingClientRect().left-Me(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),C.each({margin:"",padding:"",border:"Width"},function(r,o){C.cssHooks[r+o]={expand:function(e){for(var t=0,n={},i="string"==typeof e?e.split(" "):[e];t<4;t++)n[r+ne[t]+o]=i[t]||i[t-2]||i[0];return n}},"margin"!==r&&(C.cssHooks[r+o].set=ot)}),C.fn.extend({css:function(e,t){return B(this,function(e,t,n){var i,r,o={},s=0;if(Array.isArray(t)){for(i=Ve(e),r=t.length;s<r;s++)o[t[s]]=C.css(e,t[s],!1,i);return o}return void 0!==n?C.style(e,t,n):C.css(e,t)},e,t,1<arguments.length)}}),((C.Tween=lt).prototype={constructor:lt,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||C.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(C.cssNumber[n]?"":"px")},cur:function(){var e=lt.propHooks[this.prop];return(e&&e.get?e:lt.propHooks._default).get(this)},run:function(e){var t,n=lt.propHooks[this.prop];return this.options.duration?this.pos=t=C.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:lt.propHooks._default).set(this),this}}).init.prototype=lt.prototype,(lt.propHooks={_default:{get:function(e){return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(e=C.css(e.elem,e.prop,""))&&"auto"!==e?e:0},set:function(e){C.fx.step[e.prop]?C.fx.step[e.prop](e):1!==e.elem.nodeType||!C.cssHooks[e.prop]&&null==e.elem.style[et(e.prop)]?e.elem[e.prop]=e.now:C.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=lt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},C.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},C.fx=lt.prototype.init,C.fx.step={};var ct,ut,ft=/^(?:toggle|show|hide)$/,dt=/queueHooks$/;function ht(){ut&&(!1===T.hidden&&x.requestAnimationFrame?x.requestAnimationFrame(ht):x.setTimeout(ht,C.fx.interval),C.fx.tick())}function pt(){return x.setTimeout(function(){ct=void 0}),ct=Date.now()}function gt(e,t){var n,i=0,r={height:e};for(t=t?1:0;i<4;i+=2-t)r["margin"+(n=ne[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function mt(e,t,n){for(var i,r=(vt.tweeners[t]||[]).concat(vt.tweeners["*"]),o=0,s=r.length;o<s;o++)if(i=r[o].call(n,t,e))return i}function vt(r,e,t){var n,o,i=0,s=vt.prefilters.length,a=C.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var e=ct||pt(),e=Math.max(0,c.startTime+c.duration-e),t=1-(e/c.duration||0),n=0,i=c.tweens.length;n<i;n++)c.tweens[n].run(t);return a.notifyWith(r,[c,t,e]),t<1&&i?e:(i||a.notifyWith(r,[c,1,0]),a.resolveWith(r,[c]),!1)},c=a.promise({elem:r,props:C.extend({},e),opts:C.extend(!0,{specialEasing:{},easing:C.easing._default},t),originalProperties:e,originalOptions:t,startTime:ct||pt(),duration:t.duration,tweens:[],createTween:function(e,t){e=C.Tween(r,c.opts,e,t,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(e),e},stop:function(e){var t=0,n=e?c.tweens.length:0;if(o)return this;for(o=!0;t<n;t++)c.tweens[t].run(1);return e?(a.notifyWith(r,[c,1,0]),a.resolveWith(r,[c,e])):a.rejectWith(r,[c,e]),this}}),u=c.props;for(function(e,t){var n,i,r,o,s;for(n in e)if(r=t[i=U(n)],o=e[n],Array.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),(s=C.cssHooks[i])&&"expand"in s)for(n in o=s.expand(o),delete e[i],o)n in e||(e[n]=o[n],t[n]=r);else t[i]=r}(u,c.opts.specialEasing);i<s;i++)if(n=vt.prefilters[i].call(c,r,u,c.opts))return b(n.stop)&&(C._queueHooks(c.elem,c.opts.queue).stop=n.stop.bind(n)),n;return C.map(u,mt,c),b(c.opts.start)&&c.opts.start.call(r,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),C.fx.timer(C.extend(l,{elem:r,anim:c,queue:c.opts.queue})),c}C.Animation=C.extend(vt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ae(n.elem,e,te.exec(t),n),n}]},tweener:function(e,t){for(var n,i=0,r=(e=b(e)?(t=e,["*"]):e.match(M)).length;i<r;i++)n=e[i],vt.tweeners[n]=vt.tweeners[n]||[],vt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,r,o,s,a,l,c,u="width"in t||"height"in t,f=this,d={},h=e.style,p=e.nodeType&&se(e),g=X.get(e,"fxshow");for(i in n.queue||(null==(s=C._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,f.always(function(){f.always(function(){s.unqueued--,C.queue(e,"fx").length||s.empty.fire()})})),t)if(r=t[i],ft.test(r)){if(delete t[i],o=o||"toggle"===r,r===(p?"hide":"show")){if("show"!==r||!g||void 0===g[i])continue;p=!0}d[i]=g&&g[i]||C.style(e,i)}if((l=!C.isEmptyObject(t))||!C.isEmptyObject(d))for(i in u&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(c=g&&g.display)&&(c=X.get(e,"display")),"none"===(u=C.css(e,"display"))&&(c?u=c:(ce([e],!0),c=e.style.display||c,u=C.css(e,"display"),ce([e]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===C.css(e,"float")&&(l||(f.done(function(){h.display=c}),null==c&&(u=h.display,c="none"===u?"":u)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",f.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),l=!1,d)l||(g?"hidden"in g&&(p=g.hidden):g=X.access(e,"fxshow",{display:c}),o&&(g.hidden=!p),p&&ce([e],!0),f.done(function(){for(i in p||ce([e]),X.remove(e,"fxshow"),d)C.style(e,i,d[i])})),l=mt(p?g[i]:0,i,f),i in g||(g[i]=l.start,p&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?vt.prefilters.unshift(e):vt.prefilters.push(e)}}),C.speed=function(e,t,n){var i=e&&"object"==typeof e?C.extend({},e):{complete:n||!n&&t||b(e)&&e,duration:e,easing:n&&t||t&&!b(t)&&t};return C.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in C.fx.speeds?i.duration=C.fx.speeds[i.duration]:i.duration=C.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){b(i.old)&&i.old.call(this),i.queue&&C.dequeue(this,i.queue)},i},C.fn.extend({fadeTo:function(e,t,n,i){return this.filter(se).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(t,e,n,i){var r=C.isEmptyObject(t),o=C.speed(e,n,i),i=function(){var e=vt(this,C.extend({},t),o);(r||X.get(this,"finish"))&&e.stop(!0)};return i.finish=i,r||!1===o.queue?this.each(i):this.queue(o.queue,i)},stop:function(r,e,o){function s(e){var t=e.stop;delete e.stop,t(o)}return"string"!=typeof r&&(o=e,e=r,r=void 0),e&&this.queue(r||"fx",[]),this.each(function(){var e=!0,t=null!=r&&r+"queueHooks",n=C.timers,i=X.get(this);if(t)i[t]&&i[t].stop&&s(i[t]);else for(t in i)i[t]&&i[t].stop&&dt.test(t)&&s(i[t]);for(t=n.length;t--;)n[t].elem!==this||null!=r&&n[t].queue!==r||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||C.dequeue(this,r)})},finish:function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var e,t=X.get(this),n=t[s+"queue"],i=t[s+"queueHooks"],r=C.timers,o=n?n.length:0;for(t.finish=!0,C.queue(this,s,[]),i&&i.stop&&i.stop.call(this,!0),e=r.length;e--;)r[e].elem===this&&r[e].queue===s&&(r[e].anim.stop(!0),r.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),C.each(["toggle","show","hide"],function(e,i){var r=C.fn[i];C.fn[i]=function(e,t,n){return null==e||"boolean"==typeof e?r.apply(this,arguments):this.animate(gt(i,!0),e,t,n)}}),C.each({slideDown:gt("show"),slideUp:gt("hide"),slideToggle:gt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,i){C.fn[e]=function(e,t,n){return this.animate(i,e,t,n)}}),C.timers=[],C.fx.tick=function(){var e,t=0,n=C.timers;for(ct=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||C.fx.stop(),ct=void 0},C.fx.timer=function(e){C.timers.push(e),C.fx.start()},C.fx.interval=13,C.fx.start=function(){ut||(ut=!0,ht())},C.fx.stop=function(){ut=null},C.fx.speeds={slow:600,fast:200,_default:400},C.fn.delay=function(i,e){return i=C.fx&&C.fx.speeds[i]||i,e=e||"fx",this.queue(e,function(e,t){var n=x.setTimeout(e,i);t.stop=function(){x.clearTimeout(n)}})},f=T.createElement("input"),ee=T.createElement("select").appendChild(T.createElement("option")),f.type="checkbox",y.checkOn=""!==f.value,y.optSelected=ee.selected,(f=T.createElement("input")).value="t",f.type="radio",y.radioValue="t"===f.value;var yt,bt=C.expr.attrHandle;C.fn.extend({attr:function(e,t){return B(this,C.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){C.removeAttr(this,e)})}}),C.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?C.prop(e,t,n):(1===o&&C.isXMLDoc(e)||(r=C.attrHooks[t.toLowerCase()]||(C.expr.match.bool.test(t)?yt:void 0)),void 0!==n?null===n?void C.removeAttr(e,t):r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):!(r&&"get"in r&&null!==(i=r.get(e,t)))&&null==(i=C.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&"radio"===t&&A(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,r=t&&t.match(M);if(r&&1===e.nodeType)for(;n=r[i++];)e.removeAttribute(n)}}),yt={set:function(e,t,n){return!1===t?C.removeAttr(e,n):e.setAttribute(n,n),n}},C.each(C.expr.match.bool.source.match(/\w+/g),function(e,t){var s=bt[t]||C.find.attr;bt[t]=function(e,t,n){var i,r,o=t.toLowerCase();return n||(r=bt[o],bt[o]=i,i=null!=s(e,t,n)?o:null,bt[o]=r),i}});var _t=/^(?:input|select|textarea|button)$/i,wt=/^(?:a|area)$/i;function Et(e){return(e.match(M)||[]).join(" ")}function xt(e){return e.getAttribute&&e.getAttribute("class")||""}function Tt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(M)||[]}C.fn.extend({prop:function(e,t){return B(this,C.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[C.propFix[e]||e]})}}),C.extend({prop:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&C.isXMLDoc(e)||(t=C.propFix[t]||t,r=C.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=C.find.attr(e,"tabindex");return t?parseInt(t,10):_t.test(e.nodeName)||wt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),y.optSelected||(C.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){e=e.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),C.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){C.propFix[this.toLowerCase()]=this}),C.fn.extend({addClass:function(t){var e,n,i,r,o,s,a=0;if(b(t))return this.each(function(e){C(this).addClass(t.call(this,e,xt(this)))});if((e=Tt(t)).length)for(;n=this[a++];)if(s=xt(n),i=1===n.nodeType&&" "+Et(s)+" "){for(o=0;r=e[o++];)i.indexOf(" "+r+" ")<0&&(i+=r+" ");s!==(s=Et(i))&&n.setAttribute("class",s)}return this},removeClass:function(t){var e,n,i,r,o,s,a=0;if(b(t))return this.each(function(e){C(this).removeClass(t.call(this,e,xt(this)))});if(!arguments.length)return this.attr("class","");if((e=Tt(t)).length)for(;n=this[a++];)if(s=xt(n),i=1===n.nodeType&&" "+Et(s)+" "){for(o=0;r=e[o++];)for(;-1<i.indexOf(" "+r+" ");)i=i.replace(" "+r+" "," ");s!==(s=Et(i))&&n.setAttribute("class",s)}return this},toggleClass:function(r,t){var o=typeof r,s="string"==o||Array.isArray(r);return"boolean"==typeof t&&s?t?this.addClass(r):this.removeClass(r):b(r)?this.each(function(e){C(this).toggleClass(r.call(this,e,xt(this),t),t)}):this.each(function(){var e,t,n,i;if(s)for(t=0,n=C(this),i=Tt(r);e=i[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==r&&"boolean"!=o||((e=xt(this))&&X.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==r&&X.get(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,i=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+Et(xt(t))+" ").indexOf(i))return!0;return!1}});var Ct=/\r/g;C.fn.extend({val:function(t){var n,e,i,r=this[0];return arguments.length?(i=b(t),this.each(function(e){1===this.nodeType&&(null==(e=i?t.call(this,e,C(this).val()):t)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=C.map(e,function(e){return null==e?"":e+""})),(n=C.valHooks[this.type]||C.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value")||(this.value=e))})):r?(n=C.valHooks[r.type]||C.valHooks[r.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(r,"value"))?e:"string"==typeof(e=r.value)?e.replace(Ct,""):null==e?"":e:void 0}}),C.extend({valHooks:{option:{get:function(e){var t=C.find.attr(e,"value");return null!=t?t:Et(C.text(e))}},select:{get:function(e){for(var t,n=e.options,i=e.selectedIndex,r="select-one"===e.type,o=r?null:[],s=r?i+1:n.length,a=i<0?s:r?i:0;a<s;a++)if(((t=n[a]).selected||a===i)&&!t.disabled&&(!t.parentNode.disabled||!A(t.parentNode,"optgroup"))){if(t=C(t).val(),r)return t;o.push(t)}return o},set:function(e,t){for(var n,i,r=e.options,o=C.makeArray(t),s=r.length;s--;)((i=r[s]).selected=-1<C.inArray(C.valHooks.option.get(i),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),C.each(["radio","checkbox"],function(){C.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<C.inArray(C(e).val(),t)}},y.checkOn||(C.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),y.focusin="onfocusin"in x;function St(e){e.stopPropagation()}var At=/^(?:focusinfocus|focusoutblur)$/;C.extend(C.event,{trigger:function(e,t,n,i){var r,o,s,a,l,c,u,f=[n||T],d=v.call(e,"type")?e.type:e,h=v.call(e,"namespace")?e.namespace.split("."):[],p=u=o=n=n||T;if(3!==n.nodeType&&8!==n.nodeType&&!At.test(d+C.event.triggered)&&(-1<d.indexOf(".")&&(d=(h=d.split(".")).shift(),h.sort()),a=d.indexOf(":")<0&&"on"+d,(e=e[C.expando]?e:new C.Event(d,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=h.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:C.makeArray(t,[e]),c=C.event.special[d]||{},i||!c.trigger||!1!==c.trigger.apply(n,t))){if(!i&&!c.noBubble&&!g(n)){for(s=c.delegateType||d,At.test(s+d)||(p=p.parentNode);p;p=p.parentNode)f.push(p),o=p;o===(n.ownerDocument||T)&&f.push(o.defaultView||o.parentWindow||x)}for(r=0;(p=f[r++])&&!e.isPropagationStopped();)u=p,e.type=1<r?s:c.bindType||d,(l=(X.get(p,"events")||Object.create(null))[e.type]&&X.get(p,"handle"))&&l.apply(p,t),(l=a&&p[a])&&l.apply&&Y(p)&&(e.result=l.apply(p,t),!1===e.result&&e.preventDefault());return e.type=d,i||e.isDefaultPrevented()||c._default&&!1!==c._default.apply(f.pop(),t)||!Y(n)||a&&b(n[d])&&!g(n)&&((o=n[a])&&(n[a]=null),C.event.triggered=d,e.isPropagationStopped()&&u.addEventListener(d,St),n[d](),e.isPropagationStopped()&&u.removeEventListener(d,St),C.event.triggered=void 0,o&&(n[a]=o)),e.result}},simulate:function(e,t,n){e=C.extend(new C.Event,n,{type:e,isSimulated:!0});C.event.trigger(e,null,t)}}),C.fn.extend({trigger:function(e,t){return this.each(function(){C.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return C.event.trigger(e,t,n,!0)}}),y.focusin||C.each({focus:"focusin",blur:"focusout"},function(n,i){function r(e){C.event.simulate(i,e.target,C.event.fix(e))}C.event.special[i]={setup:function(){var e=this.ownerDocument||this.document||this,t=X.access(e,i);t||e.addEventListener(n,r,!0),X.access(e,i,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=X.access(e,i)-1;t?X.access(e,i,t):(e.removeEventListener(n,r,!0),X.remove(e,i))}}});var kt=x.location,Ot={guid:Date.now()},Nt=/\?/;C.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new x.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||C.error("Invalid XML: "+e),t};var Dt=/\[\]$/,jt=/\r?\n/g,Lt=/^(?:submit|button|image|reset|file)$/i,It=/^(?:input|select|textarea|keygen)/i;C.param=function(e,t){function n(e,t){t=b(t)?t():t,r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==t?"":t)}var i,r=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!C.isPlainObject(e))C.each(e,function(){n(this.name,this.value)});else for(i in e)!function n(i,e,r,o){if(Array.isArray(e))C.each(e,function(e,t){r||Dt.test(i)?o(i,t):n(i+"["+("object"==typeof t&&null!=t?e:"")+"]",t,r,o)});else if(r||"object"!==p(e))o(i,e);else for(var t in e)n(i+"["+t+"]",e[t],r,o)}(i,e[i],t,n);return r.join("&")},C.fn.extend({serialize:function(){return C.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=C.prop(this,"elements");return e?C.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!C(this).is(":disabled")&&It.test(this.nodeName)&&!Lt.test(e)&&(this.checked||!ue.test(e))}).map(function(e,t){var n=C(this).val();return null==n?null:Array.isArray(n)?C.map(n,function(e){return{name:t.name,value:e.replace(jt,"\r\n")}}):{name:t.name,value:n.replace(jt,"\r\n")}}).get()}});var Mt=/%20/g,Rt=/#.*$/,Pt=/([?&])_=[^&]*/,qt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ht=/^(?:GET|HEAD)$/,Wt=/^\/\//,Ft={},Bt={},zt="*/".concat("*"),$t=T.createElement("a");function Vt(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,i=0,r=e.toLowerCase().match(M)||[];if(b(t))for(;n=r[i++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Ut(t,i,r,o){var s={},a=t===Bt;function l(e){var n;return s[e]=!0,C.each(t[e]||[],function(e,t){t=t(i,r,o);return"string"!=typeof t||a||s[t]?a?!(n=t):void 0:(i.dataTypes.unshift(t),l(t),!1)}),n}return l(i.dataTypes[0])||!s["*"]&&l("*")}function Yt(e,t){var n,i,r=C.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((r[n]?e:i=i||{})[n]=t[n]);return i&&C.extend(!0,e,i),e}$t.href=kt.href,C.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:kt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(kt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":zt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":C.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Yt(Yt(e,C.ajaxSettings),t):Yt(C.ajaxSettings,e)},ajaxPrefilter:Vt(Ft),ajaxTransport:Vt(Bt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var l,c,u,n,f,i,d,h,r,o,p=C.ajaxSetup({},t),g=p.context||p,m=p.context&&(g.nodeType||g.jquery)?C(g):C.event,v=C.Deferred(),y=C.Callbacks("once memory"),b=p.statusCode||{},s={},a={},_="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(d){if(!n)for(n={};t=qt.exec(u);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return d?u:null},setRequestHeader:function(e,t){return null==d&&(e=a[e.toLowerCase()]=a[e.toLowerCase()]||e,s[e]=t),this},overrideMimeType:function(e){return null==d&&(p.mimeType=e),this},statusCode:function(e){if(e)if(d)w.always(e[w.status]);else for(var t in e)b[t]=[b[t],e[t]];return this},abort:function(e){e=e||_;return l&&l.abort(e),E(0,e),this}};if(v.promise(w),p.url=((e||p.url||kt.href)+"").replace(Wt,kt.protocol+"//"),p.type=t.method||t.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(M)||[""],null==p.crossDomain){i=T.createElement("a");try{i.href=p.url,i.href=i.href,p.crossDomain=$t.protocol+"//"+$t.host!=i.protocol+"//"+i.host}catch(e){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=C.param(p.data,p.traditional)),Ut(Ft,p,t,w),d)return w;for(r in(h=C.event&&p.global)&&0==C.active++&&C.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Ht.test(p.type),c=p.url.replace(Rt,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(Mt,"+")):(o=p.url.slice(c.length),p.data&&(p.processData||"string"==typeof p.data)&&(c+=(Nt.test(c)?"&":"?")+p.data,delete p.data),!1===p.cache&&(c=c.replace(Pt,"$1"),o=(Nt.test(c)?"&":"?")+"_="+Ot.guid+++o),p.url=c+o),p.ifModified&&(C.lastModified[c]&&w.setRequestHeader("If-Modified-Since",C.lastModified[c]),C.etag[c]&&w.setRequestHeader("If-None-Match",C.etag[c])),(p.data&&p.hasContent&&!1!==p.contentType||t.contentType)&&w.setRequestHeader("Content-Type",p.contentType),w.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+zt+"; q=0.01":""):p.accepts["*"]),p.headers)w.setRequestHeader(r,p.headers[r]);if(p.beforeSend&&(!1===p.beforeSend.call(g,w,p)||d))return w.abort();if(_="abort",y.add(p.complete),w.done(p.success),w.fail(p.error),l=Ut(Bt,p,t,w)){if(w.readyState=1,h&&m.trigger("ajaxSend",[w,p]),d)return w;p.async&&0<p.timeout&&(f=x.setTimeout(function(){w.abort("timeout")},p.timeout));try{d=!1,l.send(s,E)}catch(e){if(d)throw e;E(-1,e)}}else E(-1,"No Transport");function E(e,t,n,i){var r,o,s,a=t;d||(d=!0,f&&x.clearTimeout(f),l=void 0,u=i||"",w.readyState=0<e?4:0,i=200<=e&&e<300||304===e,n&&(s=function(e,t,n){for(var i,r,o,s,a=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(r in a)if(a[r]&&a[r].test(i)){l.unshift(r);break}if(l[0]in n)o=l[0];else{for(r in n){if(!l[0]||e.converters[r+" "+l[0]]){o=r;break}s=s||r}o=o||s}if(o)return o!==l[0]&&l.unshift(o),n[o]}(p,w,n)),!i&&-1<C.inArray("script",p.dataTypes)&&(p.converters["text script"]=function(){}),s=function(e,t,n,i){var r,o,s,a,l,c={},u=e.dataTypes.slice();if(u[1])for(s in e.converters)c[s.toLowerCase()]=e.converters[s];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(s=c[l+" "+o]||c["* "+o]))for(r in c)if((a=r.split(" "))[1]===o&&(s=c[l+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[r]:!0!==c[r]&&(o=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(p,s,w,i),i?(p.ifModified&&((n=w.getResponseHeader("Last-Modified"))&&(C.lastModified[c]=n),(n=w.getResponseHeader("etag"))&&(C.etag[c]=n)),204===e||"HEAD"===p.type?a="nocontent":304===e?a="notmodified":(a=s.state,r=s.data,i=!(o=s.error))):(o=a,!e&&a||(a="error",e<0&&(e=0))),w.status=e,w.statusText=(t||a)+"",i?v.resolveWith(g,[r,a,w]):v.rejectWith(g,[w,a,o]),w.statusCode(b),b=void 0,h&&m.trigger(i?"ajaxSuccess":"ajaxError",[w,p,i?r:o]),y.fireWith(g,[w,a]),h&&(m.trigger("ajaxComplete",[w,p]),--C.active||C.event.trigger("ajaxStop")))}return w},getJSON:function(e,t,n){return C.get(e,t,n,"json")},getScript:function(e,t){return C.get(e,void 0,t,"script")}}),C.each(["get","post"],function(e,r){C[r]=function(e,t,n,i){return b(t)&&(i=i||n,n=t,t=void 0),C.ajax(C.extend({url:e,type:r,dataType:i,data:t,success:n},C.isPlainObject(e)&&e))}}),C.ajaxPrefilter(function(e){for(var t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),C._evalUrl=function(e,t,n){return C.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){C.globalEval(e,t,n)}})},C.fn.extend({wrapAll:function(e){return this[0]&&(b(e)&&(e=e.call(this[0])),e=C(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return b(n)?this.each(function(e){C(this).wrapInner(n.call(this,e))}):this.each(function(){var e=C(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=b(t);return this.each(function(e){C(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){C(this).replaceWith(this.childNodes)}),this}}),C.expr.pseudos.hidden=function(e){return!C.expr.pseudos.visible(e)},C.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},C.ajaxSettings.xhr=function(){try{return new x.XMLHttpRequest}catch(e){}};var Qt={0:200,1223:204},Xt=C.ajaxSettings.xhr();y.cors=!!Xt&&"withCredentials"in Xt,y.ajax=Xt=!!Xt,C.ajaxTransport(function(r){var o,s;if(y.cors||Xt&&!r.crossDomain)return{send:function(e,t){var n,i=r.xhr();if(i.open(r.type,r.url,r.async,r.username,r.password),r.xhrFields)for(n in r.xhrFields)i[n]=r.xhrFields[n];for(n in r.mimeType&&i.overrideMimeType&&i.overrideMimeType(r.mimeType),r.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)i.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=s=i.onload=i.onerror=i.onabort=i.ontimeout=i.onreadystatechange=null,"abort"===e?i.abort():"error"===e?"number"!=typeof i.status?t(0,"error"):t(i.status,i.statusText):t(Qt[i.status]||i.status,i.statusText,"text"!==(i.responseType||"text")||"string"!=typeof i.responseText?{binary:i.response}:{text:i.responseText},i.getAllResponseHeaders()))}},i.onload=o(),s=i.onerror=i.ontimeout=o("error"),void 0!==i.onabort?i.onabort=s:i.onreadystatechange=function(){4===i.readyState&&x.setTimeout(function(){o&&s()})},o=o("abort");try{i.send(r.hasContent&&r.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),C.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),C.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return C.globalEval(e),e}}}),C.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),C.ajaxTransport("script",function(n){var i,r;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){i=C("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",r=function(e){i.remove(),r=null,e&&t("error"===e.type?404:200,e.type)}),T.head.appendChild(i[0])},abort:function(){r&&r()}}});var Gt=[],Kt=/(=)\?(?=&|$)|\?\?/;C.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Gt.pop()||C.expando+"_"+Ot.guid++;return this[e]=!0,e}}),C.ajaxPrefilter("json jsonp",function(e,t,n){var i,r,o,s=!1!==e.jsonp&&(Kt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Kt.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=b(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Kt,"$1"+i):!1!==e.jsonp&&(e.url+=(Nt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return o||C.error(i+" was not called"),o[0]},e.dataTypes[0]="json",r=x[i],x[i]=function(){o=arguments},n.always(function(){void 0===r?C(x).removeProp(i):x[i]=r,e[i]&&(e.jsonpCallback=t.jsonpCallback,Gt.push(i)),o&&b(r)&&r(o[0]),o=r=void 0}),"script"}),y.createHTMLDocument=((f=T.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===f.childNodes.length),C.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(y.createHTMLDocument?((i=(t=T.implementation.createHTMLDocument("")).createElement("base")).href=T.location.href,t.head.appendChild(i)):t=T),i=!n&&[],(n=k.exec(e))?[t.createElement(n[1])]:(n=ve([e],t,i),i&&i.length&&C(i).remove(),C.merge([],n.childNodes)));var i},C.fn.load=function(e,t,n){var i,r,o,s=this,a=e.indexOf(" ");return-1<a&&(i=Et(e.slice(a)),e=e.slice(0,a)),b(t)?(n=t,t=void 0):t&&"object"==typeof t&&(r="POST"),0<s.length&&C.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done(function(e){o=arguments,s.html(i?C("<div>").append(C.parseHTML(e)).find(i):e)}).always(n&&function(e,t){s.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},C.expr.pseudos.animated=function(t){return C.grep(C.timers,function(e){return t===e.elem}).length},C.offset={setOffset:function(e,t,n){var i,r,o,s,a=C.css(e,"position"),l=C(e),c={};"static"===a&&(e.style.position="relative"),o=l.offset(),i=C.css(e,"top"),s=C.css(e,"left"),s=("absolute"===a||"fixed"===a)&&-1<(i+s).indexOf("auto")?(r=(a=l.position()).top,a.left):(r=parseFloat(i)||0,parseFloat(s)||0),b(t)&&(t=t.call(e,n,C.extend({},o))),null!=t.top&&(c.top=t.top-o.top+r),null!=t.left&&(c.left=t.left-o.left+s),"using"in t?t.using.call(e,c):("number"==typeof c.top&&(c.top+="px"),"number"==typeof c.left&&(c.left+="px"),l.css(c))}},C.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){C.offset.setOffset(this,t,e)});var e,n=this[0];return n?n.getClientRects().length?(e=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],r={top:0,left:0};if("fixed"===C.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===C.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((r=C(e).offset()).top+=C.css(e,"borderTopWidth",!0),r.left+=C.css(e,"borderLeftWidth",!0))}return{top:t.top-r.top-C.css(i,"marginTop",!0),left:t.left-r.left-C.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===C.css(e,"position");)e=e.offsetParent;return e||ie})}}),C.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,r){var o="pageYOffset"===r;C.fn[t]=function(e){return B(this,function(e,t,n){var i;return g(e)?i=e:9===e.nodeType&&(i=e.defaultView),void 0===n?i?i[r]:e[t]:void(i?i.scrollTo(o?i.pageXOffset:n,o?n:i.pageYOffset):e[t]=n)},t,e,arguments.length)}}),C.each(["top","left"],function(e,n){C.cssHooks[n]=Qe(y.pixelPosition,function(e,t){if(t)return t=Ye(e,n),$e.test(t)?C(e).position()[n]+"px":t})}),C.each({Height:"height",Width:"width"},function(s,a){C.each({padding:"inner"+s,content:a,"":"outer"+s},function(i,o){C.fn[o]=function(e,t){var n=arguments.length&&(i||"boolean"!=typeof e),r=i||(!0===e||!0===t?"margin":"border");return B(this,function(e,t,n){var i;return g(e)?0===o.indexOf("outer")?e["inner"+s]:e.document.documentElement["client"+s]:9===e.nodeType?(i=e.documentElement,Math.max(e.body["scroll"+s],i["scroll"+s],e.body["offset"+s],i["offset"+s],i["client"+s])):void 0===n?C.css(e,t,r):C.style(e,t,n,r)},a,n?e:void 0,n)}})}),C.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){C.fn[t]=function(e){return this.on(t,e)}}),C.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),C.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){C.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var Jt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;C.proxy=function(e,t){var n,i;if("string"==typeof t&&(i=e[t],t=e,e=i),b(e))return n=a.call(arguments,2),(i=function(){return e.apply(t||this,n.concat(a.call(arguments)))}).guid=e.guid=e.guid||C.guid++,i},C.holdReady=function(e){e?C.readyWait++:C.ready(!0)},C.isArray=Array.isArray,C.parseJSON=JSON.parse,C.nodeName=A,C.isFunction=b,C.isWindow=g,C.camelCase=U,C.type=p,C.now=Date.now,C.isNumeric=function(e){var t=C.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},C.trim=function(e){return null==e?"":(e+"").replace(Jt,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return C});var Zt=x.jQuery,en=x.$;return C.noConflict=function(e){return x.$===C&&(x.$=en),e&&x.jQuery===C&&(x.jQuery=Zt),C},void 0===e&&(x.jQuery=x.$=C),C}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("jquery")):"function"==typeof define&&define.amd?define(["exports","jquery"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).bootstrap={},e.jQuery)}(this,function(e,t){"use strict";var n,u=(n=t)&&"object"==typeof n&&"default"in n?n:{default:n};function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function r(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n,i=arguments[t];for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}var f={TRANSITION_END:"bsTransitionEnd",getUID:function(e){for(;e+=~~(1e6*Math.random()),document.getElementById(e););return e},getSelectorFromElement:function(e){var t,n=e.getAttribute("data-target");n&&"#"!==n||(n=(t=e.getAttribute("href"))&&"#"!==t?t.trim():"");try{return document.querySelector(n)?n:null}catch(e){return null}},getTransitionDurationFromElement:function(e){if(!e)return 0;var t=u.default(e).css("transition-duration"),n=u.default(e).css("transition-delay"),i=parseFloat(t),e=parseFloat(n);return i||e?(t=t.split(",")[0],n=n.split(",")[0],1e3*(parseFloat(t)+parseFloat(n))):0},reflow:function(e){return e.offsetHeight},triggerTransitionEnd:function(e){u.default(e).trigger("transitionend")},supportsTransitionEnd:function(){return Boolean("transitionend")},isElement:function(e){return(e[0]||e).nodeType},typeCheckConfig:function(e,t,n){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var r=n[i],o=t[i],o=o&&f.isElement(o)?"element":null===o||void 0===o?""+o:{}.toString.call(o).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(r).test(o))throw new Error(e.toUpperCase()+': Option "'+i+'" provided type "'+o+'" but expected type "'+r+'".')}},findShadowRoot:function(e){if(!document.documentElement.attachShadow)return null;if("function"!=typeof e.getRootNode)return e instanceof ShadowRoot?e:e.parentNode?f.findShadowRoot(e.parentNode):null;e=e.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if(void 0===u.default)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=u.default.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||4<=e[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};f.jQueryDetection(),u.default.fn.emulateTransitionEnd=function(e){var t=this,n=!1;return u.default(this).one(f.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||f.triggerTransitionEnd(t)},e),this},u.default.event.special[f.TRANSITION_END]={bindType:"transitionend",delegateType:"transitionend",handle:function(e){if(u.default(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}};var s="alert",a=u.default.fn[s],l=((_e=c.prototype).close=function(e){var t=this._element;e&&(t=this._getRootElement(e)),this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},_e.dispose=function(){u.default.removeData(this._element,"bs.alert"),this._element=null},_e._getRootElement=function(e){var t=f.getSelectorFromElement(e),n=!1;return t&&(n=document.querySelector(t)),n=n||u.default(e).closest(".alert")[0]},_e._triggerCloseEvent=function(e){var t=u.default.Event("close.bs.alert");return u.default(e).trigger(t),t},_e._removeElement=function(t){var e,n=this;u.default(t).removeClass("show"),u.default(t).hasClass("fade")?(e=f.getTransitionDurationFromElement(t),u.default(t).one(f.TRANSITION_END,function(e){return n._destroyElement(t,e)}).emulateTransitionEnd(e)):this._destroyElement(t)},_e._destroyElement=function(e){u.default(e).detach().trigger("closed.bs.alert").remove()},c._jQueryInterface=function(n){return this.each(function(){var e=u.default(this),t=e.data("bs.alert");t||(t=new c(this),e.data("bs.alert",t)),"close"===n&&t[n](this)})},c._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},r(c,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),c);function c(e){this._element=e}u.default(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',l._handleDismiss(new l)),u.default.fn[s]=l._jQueryInterface,u.default.fn[s].Constructor=l,u.default.fn[s].noConflict=function(){return u.default.fn[s]=a,l._jQueryInterface};var d=u.default.fn.button,h=((Q=p.prototype).toggle=function(){var e,t=!0,n=!0,i=u.default(this._element).closest('[data-toggle="buttons"]')[0];!i||(e=this._element.querySelector('input:not([type="hidden"])'))&&("radio"===e.type&&(e.checked&&this._element.classList.contains("active")?t=!1:(i=i.querySelector(".active"))&&u.default(i).removeClass("active")),t&&("checkbox"!==e.type&&"radio"!==e.type||(e.checked=!this._element.classList.contains("active")),this.shouldAvoidTriggerChange||u.default(e).trigger("change")),e.focus(),n=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(n&&this._element.setAttribute("aria-pressed",!this._element.classList.contains("active")),t&&u.default(this._element).toggleClass("active"))},Q.dispose=function(){u.default.removeData(this._element,"bs.button"),this._element=null},p._jQueryInterface=function(n,i){return this.each(function(){var e=u.default(this),t=e.data("bs.button");t||(t=new p(this),e.data("bs.button",t)),t.shouldAvoidTriggerChange=i,"toggle"===n&&t[n]()})},r(p,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),p);function p(e){this._element=e,this.shouldAvoidTriggerChange=!1}u.default(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(e){var t,n=e.target,i=n;u.default(n).hasClass("btn")||(n=u.default(n).closest(".btn")[0]),!n||n.hasAttribute("disabled")||n.classList.contains("disabled")||(t=n.querySelector('input:not([type="hidden"])'))&&(t.hasAttribute("disabled")||t.classList.contains("disabled"))?e.preventDefault():"INPUT"!==i.tagName&&"LABEL"===n.tagName||h._jQueryInterface.call(u.default(n),"toggle","INPUT"===i.tagName)}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(e){var t=u.default(e.target).closest(".btn")[0];u.default(t).toggleClass("focus",/^focus(in)?$/.test(e.type))}),u.default(window).on("load.bs.button.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),t=0,n=e.length;t<n;t++){var i=e[t],r=i.querySelector('input:not([type="hidden"])');r.checked||r.hasAttribute("checked")?i.classList.add("active"):i.classList.remove("active")}for(var o=0,s=(e=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<s;o++){var a=e[o];"true"===a.getAttribute("aria-pressed")?a.classList.add("active"):a.classList.remove("active")}}),u.default.fn.button=h._jQueryInterface,u.default.fn.button.Constructor=h,u.default.fn.button.noConflict=function(){return u.default.fn.button=d,h._jQueryInterface};var g="carousel",m=u.default.fn[g],v={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},y={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},b={TOUCH:"touch",PEN:"pen"},_=((ve=w.prototype).next=function(){this._isSliding||this._slide("next")},ve.nextWhenVisible=function(){var e=u.default(this._element);!document.hidden&&e.is(":visible")&&"hidden"!==e.css("visibility")&&this.next()},ve.prev=function(){this._isSliding||this._slide("prev")},ve.pause=function(e){e||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(f.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},ve.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},ve.to=function(e){var t=this;this._activeElement=this._element.querySelector(".active.carousel-item");var n=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)u.default(this._element).one("slid.bs.carousel",function(){return t.to(e)});else{if(n===e)return this.pause(),void this.cycle();n=n<e?"next":"prev";this._slide(n,this._items[e])}},ve.dispose=function(){u.default(this._element).off(".bs.carousel"),u.default.removeData(this._element,"bs.carousel"),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},ve._getConfig=function(e){return e=o({},v,e),f.typeCheckConfig(g,e,y),e},ve._handleSwipe=function(){var e=Math.abs(this.touchDeltaX);e<=40||(e=e/this.touchDeltaX,(this.touchDeltaX=0)<e&&this.prev(),e<0&&this.next())},ve._addEventListeners=function(){var t=this;this._config.keyboard&&u.default(this._element).on("keydown.bs.carousel",function(e){return t._keydown(e)}),"hover"===this._config.pause&&u.default(this._element).on("mouseenter.bs.carousel",function(e){return t.pause(e)}).on("mouseleave.bs.carousel",function(e){return t.cycle(e)}),this._config.touch&&this._addTouchEventListeners()},ve._addTouchEventListeners=function(){var e,t,n=this;this._touchSupported&&(e=function(e){n._pointerEvent&&b[e.originalEvent.pointerType.toUpperCase()]?n.touchStartX=e.originalEvent.clientX:n._pointerEvent||(n.touchStartX=e.originalEvent.touches[0].clientX)},t=function(e){n._pointerEvent&&b[e.originalEvent.pointerType.toUpperCase()]&&(n.touchDeltaX=e.originalEvent.clientX-n.touchStartX),n._handleSwipe(),"hover"===n._config.pause&&(n.pause(),n.touchTimeout&&clearTimeout(n.touchTimeout),n.touchTimeout=setTimeout(function(e){return n.cycle(e)},500+n._config.interval))},u.default(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(e){return e.preventDefault()}),this._pointerEvent?(u.default(this._element).on("pointerdown.bs.carousel",e),u.default(this._element).on("pointerup.bs.carousel",t),this._element.classList.add("pointer-event")):(u.default(this._element).on("touchstart.bs.carousel",e),u.default(this._element).on("touchmove.bs.carousel",function(e){(e=e).originalEvent.touches&&1<e.originalEvent.touches.length?n.touchDeltaX=0:n.touchDeltaX=e.originalEvent.touches[0].clientX-n.touchStartX}),u.default(this._element).on("touchend.bs.carousel",t)))},ve._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next()}},ve._getItemIndex=function(e){return this._items=e&&e.parentNode?[].slice.call(e.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(e)},ve._getItemByDirection=function(e,t){var n="next"===e,i="prev"===e,r=this._getItemIndex(t),o=this._items.length-1;if((i&&0===r||n&&r===o)&&!this._config.wrap)return t;e=(r+("prev"===e?-1:1))%this._items.length;return-1==e?this._items[this._items.length-1]:this._items[e]},ve._triggerSlideEvent=function(e,t){var n=this._getItemIndex(e),i=this._getItemIndex(this._element.querySelector(".active.carousel-item")),n=u.default.Event("slide.bs.carousel",{relatedTarget:e,direction:t,from:i,to:n});return u.default(this._element).trigger(n),n},ve._setActiveIndicatorElement=function(e){var t;this._indicatorsElement&&(t=[].slice.call(this._indicatorsElement.querySelectorAll(".active")),u.default(t).removeClass("active"),(e=this._indicatorsElement.children[this._getItemIndex(e)])&&u.default(e).addClass("active"))},ve._slide=function(e,t){var n,i,r,o=this,s=this._element.querySelector(".active.carousel-item"),a=this._getItemIndex(s),l=t||s&&this._getItemByDirection(e,s),c=this._getItemIndex(l),t=Boolean(this._interval),e="next"===e?(n="carousel-item-left",i="carousel-item-next","left"):(n="carousel-item-right",i="carousel-item-prev","right");l&&u.default(l).hasClass("active")?this._isSliding=!1:!this._triggerSlideEvent(l,e).isDefaultPrevented()&&s&&l&&(this._isSliding=!0,t&&this.pause(),this._setActiveIndicatorElement(l),r=u.default.Event("slid.bs.carousel",{relatedTarget:l,direction:e,from:a,to:c}),u.default(this._element).hasClass("slide")?(u.default(l).addClass(i),f.reflow(l),u.default(s).addClass(n),u.default(l).addClass(n),(c=parseInt(l.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=c):this._config.interval=this._config.defaultInterval||this._config.interval,c=f.getTransitionDurationFromElement(s),u.default(s).one(f.TRANSITION_END,function(){u.default(l).removeClass(n+" "+i).addClass("active"),u.default(s).removeClass("active "+i+" "+n),o._isSliding=!1,setTimeout(function(){return u.default(o._element).trigger(r)},0)}).emulateTransitionEnd(c)):(u.default(s).removeClass("active"),u.default(l).addClass("active"),this._isSliding=!1,u.default(this._element).trigger(r)),t&&this.cycle())},w._jQueryInterface=function(i){return this.each(function(){var e=u.default(this).data("bs.carousel"),t=o({},v,u.default(this).data());"object"==typeof i&&(t=o({},t,i));var n="string"==typeof i?i:t.slide;if(e||(e=new w(this,t),u.default(this).data("bs.carousel",e)),"number"==typeof i)e.to(i);else if("string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}else t.interval&&t.ride&&(e.pause(),e.cycle())})},w._dataApiClickHandler=function(e){var t,n,i=f.getSelectorFromElement(this);!i||(t=u.default(i)[0])&&u.default(t).hasClass("carousel")&&(n=o({},u.default(t).data(),u.default(this).data()),(i=this.getAttribute("data-slide-to"))&&(n.interval=!1),w._jQueryInterface.call(u.default(t),n),i&&u.default(t).data("bs.carousel").to(i),e.preventDefault())},r(w,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return v}}]),w);function w(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}u.default(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",_._dataApiClickHandler),u.default(window).on("load.bs.carousel.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),t=0,n=e.length;t<n;t++){var i=u.default(e[t]);_._jQueryInterface.call(i,i.data())}}),u.default.fn[g]=_._jQueryInterface,u.default.fn[g].Constructor=_,u.default.fn[g].noConflict=function(){return u.default.fn[g]=m,_._jQueryInterface};var E="collapse",x=u.default.fn[E],T={toggle:!0,parent:""},C={toggle:"boolean",parent:"(string|element)"},S=((_e=A.prototype).toggle=function(){u.default(this._element).hasClass("show")?this.hide():this.show()},_e.show=function(){var e,t,n,i,r=this;this._isTransitioning||u.default(this._element).hasClass("show")||(this._parent&&0===(i=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(e){return"string"==typeof r._config.parent?e.getAttribute("data-parent")===r._config.parent:e.classList.contains("collapse")})).length&&(i=null),i&&(n=u.default(i).not(this._selector).data("bs.collapse"))&&n._isTransitioning)||(e=u.default.Event("show.bs.collapse"),u.default(this._element).trigger(e),e.isDefaultPrevented()||(i&&(A._jQueryInterface.call(u.default(i).not(this._selector),"hide"),n||u.default(i).data("bs.collapse",null)),t=this._getDimension(),u.default(this._element).removeClass("collapse").addClass("collapsing"),this._element.style[t]=0,this._triggerArray.length&&u.default(this._triggerArray).removeClass("collapsed").attr("aria-expanded",!0),this.setTransitioning(!0),n="scroll"+(t[0].toUpperCase()+t.slice(1)),i=f.getTransitionDurationFromElement(this._element),u.default(this._element).one(f.TRANSITION_END,function(){u.default(r._element).removeClass("collapsing").addClass("collapse show"),r._element.style[t]="",r.setTransitioning(!1),u.default(r._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(i),this._element.style[t]=this._element[n]+"px"))},_e.hide=function(){var e=this;if(!this._isTransitioning&&u.default(this._element).hasClass("show")){var t=u.default.Event("hide.bs.collapse");if(u.default(this._element).trigger(t),!t.isDefaultPrevented()){t=this._getDimension();this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",f.reflow(this._element),u.default(this._element).addClass("collapsing").removeClass("collapse show");var n=this._triggerArray.length;if(0<n)for(var i=0;i<n;i++){var r=this._triggerArray[i],o=f.getSelectorFromElement(r);null!==o&&(u.default([].slice.call(document.querySelectorAll(o))).hasClass("show")||u.default(r).addClass("collapsed").attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[t]="";t=f.getTransitionDurationFromElement(this._element);u.default(this._element).one(f.TRANSITION_END,function(){e.setTransitioning(!1),u.default(e._element).removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")}).emulateTransitionEnd(t)}}},_e.setTransitioning=function(e){this._isTransitioning=e},_e.dispose=function(){u.default.removeData(this._element,"bs.collapse"),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},_e._getConfig=function(e){return(e=o({},T,e)).toggle=Boolean(e.toggle),f.typeCheckConfig(E,e,C),e},_e._getDimension=function(){return u.default(this._element).hasClass("width")?"width":"height"},_e._getParent=function(){var e,n=this;f.isElement(this._config.parent)?(e=this._config.parent,void 0!==this._config.parent.jquery&&(e=this._config.parent[0])):e=document.querySelector(this._config.parent);var t='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',t=[].slice.call(e.querySelectorAll(t));return u.default(t).each(function(e,t){n._addAriaAndCollapsedClass(A._getTargetFromElement(t),[t])}),e},_e._addAriaAndCollapsedClass=function(e,t){e=u.default(e).hasClass("show");t.length&&u.default(t).toggleClass("collapsed",!e).attr("aria-expanded",e)},A._getTargetFromElement=function(e){e=f.getSelectorFromElement(e);return e?document.querySelector(e):null},A._jQueryInterface=function(i){return this.each(function(){var e=u.default(this),t=e.data("bs.collapse"),n=o({},T,e.data(),"object"==typeof i&&i?i:{});if(!t&&n.toggle&&"string"==typeof i&&/show|hide/.test(i)&&(n.toggle=!1),t||(t=new A(this,n),e.data("bs.collapse",t)),"string"==typeof i){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},r(A,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return T}}]),A);function A(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var n=[].slice.call(document.querySelectorAll('[data-toggle="collapse"]')),i=0,r=n.length;i<r;i++){var o=n[i],s=f.getSelectorFromElement(o),a=[].slice.call(document.querySelectorAll(s)).filter(function(e){return e===t});null!==s&&0<a.length&&(this._selector=s,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}u.default(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var n=u.default(this),e=f.getSelectorFromElement(this),e=[].slice.call(document.querySelectorAll(e));u.default(e).each(function(){var e=u.default(this),t=e.data("bs.collapse")?"toggle":n.data();S._jQueryInterface.call(e,t)})}),u.default.fn[E]=S._jQueryInterface,u.default.fn[E].Constructor=S,u.default.fn[E].noConflict=function(){return u.default.fn[E]=x,S._jQueryInterface};var k="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,O=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(k&&0<=navigator.userAgent.indexOf(e[t]))return 1;return 0}(),N=k&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},O))}};function D(e){return e&&"[object Function]"==={}.toString.call(e)}function j(e,t){if(1!==e.nodeType)return[];e=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?e[t]:e}function L(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function I(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=j(e),n=t.overflow,i=t.overflowX,t=t.overflowY;return/(auto|scroll|overlay)/.test(n+t+i)?e:I(L(e))}function M(e){return e&&e.referenceNode?e.referenceNode:e}var R=k&&!(!window.MSInputMethodContext||!document.documentMode),P=k&&/MSIE 10/.test(navigator.userAgent);function q(e){return 11===e?R:10!==e&&R||P}function H(e){if(!e)return document.documentElement;for(var t=q(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var i=n&&n.nodeName;return i&&"BODY"!==i&&"HTML"!==i?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===j(n,"position")?H(n):n:(e?e.ownerDocument:document).documentElement}function W(e){return null!==e.parentNode?W(e.parentNode):e}function F(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,i=n?e:t,r=n?t:e,n=document.createRange();n.setStart(i,0),n.setEnd(r,0);n=n.commonAncestorContainer;if(e!==n&&t!==n||i.contains(r))return"BODY"===(r=(i=n).nodeName)||"HTML"!==r&&H(i.firstElementChild)!==i?H(n):n;n=W(e);return n.host?F(n.host,t):F(e,W(t).host)}function B(e,t){var n="top"===(1<arguments.length&&void 0!==t?t:"top")?"scrollTop":"scrollLeft",t=e.nodeName;if("BODY"!==t&&"HTML"!==t)return e[n];t=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||t)[n]}function z(e,t){var n="x"===t?"Left":"Top",t="Left"==n?"Right":"Bottom";return parseFloat(e["border"+n+"Width"])+parseFloat(e["border"+t+"Width"])}function $(e,t,n,i){return Math.max(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],q(10)?parseInt(n["offset"+e])+parseInt(i["margin"+("Height"===e?"Top":"Left")])+parseInt(i["margin"+("Height"===e?"Bottom":"Right")]):0)}function V(e){var t=e.body,n=e.documentElement,e=q(10)&&getComputedStyle(n);return{height:$("Height",t,n,e),width:$("Width",t,n,e)}}function U(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Y=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},Q=function(e,t,n){return t&&G(e.prototype,t),n&&G(e,n),e},X=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n,i=arguments[t];for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function G(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function K(e){return X({},e,{right:e.left+e.width,bottom:e.top+e.height})}function J(e){var t,n,i={};try{q(10)?(i=e.getBoundingClientRect(),t=B(e,"top"),n=B(e,"left"),i.top+=t,i.left+=n,i.bottom+=t,i.right+=n):i=e.getBoundingClientRect()}catch(e){}var r={left:i.left,top:i.top,width:i.right-i.left,height:i.bottom-i.top},o="HTML"===e.nodeName?V(e.ownerDocument):{},s=o.width||e.clientWidth||r.width,a=o.height||e.clientHeight||r.height,o=e.offsetWidth-s,s=e.offsetHeight-a;return(o||s)&&(o-=z(a=j(e),"x"),s-=z(a,"y"),r.width-=o,r.height-=s),K(r)}function Z(e,t,n){var i=2<arguments.length&&void 0!==n&&n,r=q(10),o="HTML"===t.nodeName,s=J(e),a=J(t),l=I(e),c=j(t),n=parseFloat(c.borderTopWidth),e=parseFloat(c.borderLeftWidth);i&&o&&(a.top=Math.max(a.top,0),a.left=Math.max(a.left,0));s=K({top:s.top-a.top-n,left:s.left-a.left-e,width:s.width,height:s.height});return s.marginTop=0,s.marginLeft=0,!r&&o&&(o=parseFloat(c.marginTop),c=parseFloat(c.marginLeft),s.top-=n-o,s.bottom-=n-o,s.left-=e-c,s.right-=e-c,s.marginTop=o,s.marginLeft=c),(r&&!i?t.contains(l):t===l&&"BODY"!==l.nodeName)&&(s=function(e,t,n){var i=2<arguments.length&&void 0!==n&&n,n=B(t,"top"),t=B(t,"left"),i=i?-1:1;return e.top+=n*i,e.bottom+=n*i,e.left+=t*i,e.right+=t*i,e}(s,t)),s}function ee(e){if(!e||!e.parentElement||q())return document.documentElement;for(var t=e.parentElement;t&&"none"===j(t,"transform");)t=t.parentElement;return t||document.documentElement}function te(e,t,n,i,r){var o=4<arguments.length&&void 0!==r&&r,s={top:0,left:0},a=o?ee(e):F(e,M(t));"viewport"===i?s=function(e,t){var n=1<arguments.length&&void 0!==t&&t,i=e.ownerDocument.documentElement,r=Z(e,i),o=Math.max(i.clientWidth,window.innerWidth||0),t=Math.max(i.clientHeight,window.innerHeight||0),e=n?0:B(i),i=n?0:B(i,"left");return K({top:e-r.top+r.marginTop,left:i-r.left+r.marginLeft,width:o,height:t})}(a,o):(r=void 0,"scrollParent"===i?"BODY"===(r=I(L(t))).nodeName&&(r=e.ownerDocument.documentElement):r="window"===i?e.ownerDocument.documentElement:i,l=Z(r,a,o),"HTML"!==r.nodeName||function e(t){var n=t.nodeName;if("BODY"===n||"HTML"===n)return!1;if("fixed"===j(t,"position"))return!0;t=L(t);return!!t&&e(t)}(a)?s=l:(e=(a=V(e.ownerDocument)).height,a=a.width,s.top+=l.top-l.marginTop,s.bottom=e+l.top,s.left+=l.left-l.marginLeft,s.right=a+l.left));var l="number"==typeof(n=n||0);return s.left+=l?n:n.left||0,s.top+=l?n:n.top||0,s.right-=l?n:n.right||0,s.bottom-=l?n:n.bottom||0,s}function ne(e,t,n,i,r,o){o=5<arguments.length&&void 0!==o?o:0;if(-1===e.indexOf("auto"))return e;var r=te(n,i,o,r),s={top:{width:r.width,height:t.top-r.top},right:{width:r.right-t.right,height:r.height},bottom:{width:r.width,height:r.bottom-t.bottom},left:{width:t.left-r.left,height:r.height}},t=Object.keys(s).map(function(e){return X({key:e},s[e],{area:(e=s[e]).width*e.height})}).sort(function(e,t){return t.area-e.area}),r=t.filter(function(e){var t=e.width,e=e.height;return t>=n.clientWidth&&e>=n.clientHeight}),t=(0<r.length?r:t)[0].key,e=e.split("-")[1];return t+(e?"-"+e:"")}function ie(e,t,n,i){i=3<arguments.length&&void 0!==i?i:null;return Z(n,i?ee(t):F(t,M(n)),i)}function re(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),n=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),t=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+t,height:e.offsetHeight+n}}function oe(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function se(e,t,n){n=n.split("-")[0];var i=re(e),r={width:i.width,height:i.height},o=-1!==["right","left"].indexOf(n),s=o?"top":"left",a=o?"left":"top",e=o?"height":"width",o=o?"width":"height";return r[s]=t[s]+t[e]/2-i[e]/2,r[a]=n===a?t[a]-i[o]:t[oe(a)],r}function ae(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function le(e,n,t){return(void 0===t?e:e.slice(0,function(e,t){if(Array.prototype.findIndex)return e.findIndex(function(e){return e.name===t});var n=ae(e,function(e){return e.name===t});return e.indexOf(n)}(e,t))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var t=e.function||e.fn;e.enabled&&D(t)&&(n.offsets.popper=K(n.offsets.popper),n.offsets.reference=K(n.offsets.reference),n=t(n,e))}),n}function ce(e,n){return e.some(function(e){var t=e.name;return e.enabled&&t===n})}function ue(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),i=0;i<t.length;i++){var r=t[i],r=r?""+r+n:e;if(void 0!==document.body.style[r])return r}return null}function fe(e){e=e.ownerDocument;return e?e.defaultView:window}function de(){var e,t,n;this.state.eventsEnabled||(this.state=(e=this.reference,this.options,t=this.state,n=this.scheduleUpdate,t.updateBound=n,fe(e).addEventListener("resize",t.updateBound,{passive:!0}),function e(t,n,i,r){var o="BODY"===t.nodeName,t=o?t.ownerDocument.defaultView:t;t.addEventListener(n,i,{passive:!0}),o||e(I(t.parentNode),n,i,r),r.push(t)}(e=I(e),"scroll",t.updateBound,t.scrollParents),t.scrollElement=e,t.eventsEnabled=!0,t))}function he(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function pe(n,i){Object.keys(i).forEach(function(e){var t="";-1!==["width","height","top","right","bottom","left"].indexOf(e)&&he(i[e])&&(t="px"),n.style[e]=i[e]+t})}var ge=k&&/Firefox/i.test(navigator.userAgent);function me(e,t,n){var i,r=ae(e,function(e){return e.name===t}),o=!!r&&e.some(function(e){return e.name===n&&e.enabled&&e.order<r.order});return o||(i="`"+t+"`",e="`"+n+"`",console.warn(e+" modifier is required by "+i+" modifier in order to work, be sure to include it before "+i+"!")),o}var ve=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],ye=ve.slice(3);function be(e,t){t=1<arguments.length&&void 0!==t&&t,e=ye.indexOf(e),e=ye.slice(e+1).concat(ye.slice(0,e));return t?e.reverse():e}var _e={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t,n,i=e.placement,r=i.split("-")[0],o=i.split("-")[1];return o&&(t=(n=e.offsets).reference,i=n.popper,r=(n=-1!==["bottom","top"].indexOf(r))?"width":"height",r={start:U({},n=n?"left":"top",t[n]),end:U({},n,t[n]+t[r]-i[r])},e.offsets.popper=X({},i,r[o])),e}},offset:{order:200,enabled:!0,fn:function(e,t){var r,o,s,a,n=t.offset,i=e.placement,l=e.offsets,c=l.popper,u=l.reference,t=i.split("-")[0],l=he(+n)?[+n,0]:(i=n,r=c,o=u,s=[0,0],a=-1!==["right","left"].indexOf(n=t),u=i.split(/(\+|\-)/).map(function(e){return e.trim()}),n=u.indexOf(ae(u,function(e){return-1!==e.search(/,|\s/)})),u[n]&&-1===u[n].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead."),i=/\s*,\s*|\s+/,(-1!==n?[u.slice(0,n).concat([u[n].split(i)[0]]),[u[n].split(i)[1]].concat(u.slice(n+1))]:[u]).map(function(e,t){var n=(1===t?!a:a)?"height":"width",i=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,i=!0,e):i?(e[e.length-1]+=t,i=!1,e):e.concat(t)},[]).map(function(e){return function(e,t,n,i){var r=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+r[1],r=r[2];if(!o)return e;if(0!==r.indexOf("%"))return"vh"===r||"vw"===r?("vh"===r?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*o:o;e=void 0;switch(r){case"%p":e=n;break;case"%":case"%r":default:e=i}return K(e)[t]/100*o}(e,n,r,o)})}).forEach(function(n,i){n.forEach(function(e,t){he(e)&&(s[i]+=e*("-"===n[t-1]?-1:1))})}),s);return"left"===t?(c.top+=l[0],c.left-=l[1]):"right"===t?(c.top+=l[0],c.left+=l[1]):"top"===t?(c.left+=l[0],c.top-=l[1]):"bottom"===t&&(c.left+=l[0],c.top+=l[1]),e.popper=c,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,i){var t=i.boundariesElement||H(e.instance.popper);e.instance.reference===t&&(t=H(t));var n=ue("transform"),r=e.instance.popper.style,o=r.top,s=r.left,a=r[n];r.top="",r.left="",r[n]="";var l=te(e.instance.popper,e.instance.reference,i.padding,t,e.positionFixed);r.top=o,r.left=s,r[n]=a,i.boundaries=l;var a=i.priority,c=e.offsets.popper,u={primary:function(e){var t=c[e];return c[e]<l[e]&&!i.escapeWithReference&&(t=Math.max(c[e],l[e])),U({},e,t)},secondary:function(e){var t="right"===e?"left":"top",n=c[t];return c[e]>l[e]&&!i.escapeWithReference&&(n=Math.min(c[t],l[e]-("right"===e?c.width:c.height))),U({},t,n)}};return a.forEach(function(e){var t=-1!==["left","top"].indexOf(e)?"primary":"secondary";c=X({},c,u[t](e))}),e.offsets.popper=c,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,i=t.reference,r=e.placement.split("-")[0],o=Math.floor,s=-1!==["top","bottom"].indexOf(r),t=s?"right":"bottom",r=s?"left":"top",s=s?"width":"height";return n[t]<o(i[r])&&(e.offsets.popper[r]=o(i[r])-n[s]),n[r]>o(i[t])&&(e.offsets.popper[r]=o(i[t])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){if(!me(e.instance.modifiers,"arrow","keepTogether"))return e;var n=t.element;if("string"==typeof n){if(!(n=e.instance.popper.querySelector(n)))return e}else if(!e.instance.popper.contains(n))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var i=e.placement.split("-")[0],r=e.offsets,o=r.popper,s=r.reference,a=-1!==["left","right"].indexOf(i),l=a?"height":"width",c=a?"Top":"Left",u=c.toLowerCase(),t=a?"left":"top",r=a?"bottom":"right",i=re(n)[l];s[r]-i<o[u]&&(e.offsets.popper[u]-=o[u]-(s[r]-i)),s[u]+i>o[r]&&(e.offsets.popper[u]+=s[u]+i-o[r]),e.offsets.popper=K(e.offsets.popper);a=s[u]+s[l]/2-i/2,r=j(e.instance.popper),s=parseFloat(r["margin"+c]),c=parseFloat(r["border"+c+"Width"]),c=a-e.offsets.popper[u]-s-c,c=Math.max(Math.min(o[l]-i,c),0);return e.arrowElement=n,e.offsets.arrow=(U(n={},u,Math.round(c)),U(n,t,""),n),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(c,u){if(ce(c.instance.modifiers,"inner"))return c;if(c.flipped&&c.placement===c.originalPlacement)return c;var f=te(c.instance.popper,c.instance.reference,u.padding,u.boundariesElement,c.positionFixed),d=c.placement.split("-")[0],h=oe(d),p=c.placement.split("-")[1]||"",g=[];switch(u.behavior){case"flip":g=[d,h];break;case"clockwise":g=be(d);break;case"counterclockwise":g=be(d,!0);break;default:g=u.behavior}return g.forEach(function(e,t){if(d!==e||g.length===t+1)return c;d=c.placement.split("-")[0],h=oe(d);var n=c.offsets.popper,i=c.offsets.reference,r=Math.floor,o="left"===d&&r(n.right)>r(i.left)||"right"===d&&r(n.left)<r(i.right)||"top"===d&&r(n.bottom)>r(i.top)||"bottom"===d&&r(n.top)<r(i.bottom),s=r(n.left)<r(f.left),a=r(n.right)>r(f.right),l=r(n.top)<r(f.top),e=r(n.bottom)>r(f.bottom),i="left"===d&&s||"right"===d&&a||"top"===d&&l||"bottom"===d&&e,n=-1!==["top","bottom"].indexOf(d),r=!!u.flipVariations&&(n&&"start"===p&&s||n&&"end"===p&&a||!n&&"start"===p&&l||!n&&"end"===p&&e),l=!!u.flipVariationsByContent&&(n&&"start"===p&&a||n&&"end"===p&&s||!n&&"start"===p&&e||!n&&"end"===p&&l),l=r||l;(o||i||l)&&(c.flipped=!0,(o||i)&&(d=g[t+1]),l&&(p="end"===p?"start":"start"===p?"end":p),c.placement=d+(p?"-"+p:""),c.offsets.popper=X({},c.offsets.popper,se(c.instance.popper,c.offsets.reference,c.placement)),c=le(c.instance.modifiers,c,"flip"))}),c},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],i=e.offsets,r=i.popper,o=i.reference,s=-1!==["left","right"].indexOf(n),i=-1===["top","left"].indexOf(n);return r[s?"left":"top"]=o[n]-(i?r[s?"width":"height"]:0),e.placement=oe(t),e.offsets.popper=K(r),e}},hide:{order:800,enabled:!0,fn:function(e){if(!me(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=ae(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n=t.x,i=t.y,r=e.offsets.popper,o=ae(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration;void 0!==o&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var s,a,l,c,u,f,d=void 0!==o?o:t.gpuAcceleration,h=H(e.instance.popper),p=J(h),g={position:r.position},a=(s=e,a=window.devicePixelRatio<2||!ge,l=s.offsets,f=l.popper,c=l.reference,u=Math.round,o=Math.floor,t=function(e){return e},r=u(c.width),l=u(f.width),c=-1!==["left","right"].indexOf(s.placement),s=-1!==s.placement.indexOf("-"),o=a?c||s||r%2==l%2?u:o:t,t=a?u:t,{left:o(r%2==1&&l%2==1&&!s&&a?f.left-1:f.left),top:t(f.top),bottom:t(f.bottom),right:o(f.right)}),t="bottom"===n?"top":"bottom",o="right"===i?"left":"right",f=ue("transform"),i="bottom"==t?"HTML"===h.nodeName?-h.clientHeight+a.bottom:-p.height+a.bottom:a.top,n="right"==o?"HTML"===h.nodeName?-h.clientWidth+a.right:-p.width+a.right:a.left;d&&f?(g[f]="translate3d("+n+"px, "+i+"px, 0)",g[t]=0,g[o]=0,g.willChange="transform"):(d="bottom"==t?-1:1,f="right"==o?-1:1,g[t]=i*d,g[o]=n*f,g.willChange=t+", "+o);o={"x-placement":e.placement};return e.attributes=X({},o,e.attributes),e.styles=X({},g,e.styles),e.arrowStyles=X({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){var t,n;return pe(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach(function(e){!1!==n[e]?t.setAttribute(e,n[e]):t.removeAttribute(e)}),e.arrowElement&&Object.keys(e.arrowStyles).length&&pe(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,n,i,r){r=ie(r,t,e,n.positionFixed),e=ne(n.placement,r,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",e),pe(t,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},we=(Q(Ee,[{key:"update",value:function(){return function(){var e;this.state.isDestroyed||((e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=ie(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=ne(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=se(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=le(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,ce(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[ue("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return de.call(this)}},{key:"disableEventListeners",value:function(){return function(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,fe(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}.call(this)}}]),Ee);function Ee(e,t){var n=this,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};Y(this,Ee),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=N(this.update.bind(this)),this.options=X({},Ee.Defaults,i),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=t&&t.jquery?t[0]:t,this.options.modifiers={},Object.keys(X({},Ee.Defaults.modifiers,i.modifiers)).forEach(function(e){n.options.modifiers[e]=X({},Ee.Defaults.modifiers[e]||{},i.modifiers?i.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return X({name:e},n.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&D(e.onLoad)&&e.onLoad(n.reference,n.popper,n.options,e,n.state)}),this.update();t=this.options.eventsEnabled;t&&this.enableEventListeners(),this.state.eventsEnabled=t}we.Utils=("undefined"!=typeof window?window:global).PopperUtils,we.placements=ve,we.Defaults=_e;var xe="dropdown",Te=u.default.fn[xe],Ce=new RegExp("38|40|27"),Se={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},Ae={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},ke=((_e=Oe.prototype).toggle=function(){var e;this._element.disabled||u.default(this._element).hasClass("disabled")||(e=u.default(this._menu).hasClass("show"),Oe._clearMenus(),e||this.show(!0))},_e.show=function(e){if(void 0===e&&(e=!1),!(this._element.disabled||u.default(this._element).hasClass("disabled")||u.default(this._menu).hasClass("show"))){var t={relatedTarget:this._element},n=u.default.Event("show.bs.dropdown",t),i=Oe._getParentFromElement(this._element);if(u.default(i).trigger(n),!n.isDefaultPrevented()){if(!this._inNavbar&&e){if(void 0===we)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");e=this._element;"parent"===this._config.reference?e=i:f.isElement(this._config.reference)&&(e=this._config.reference,void 0!==this._config.reference.jquery&&(e=this._config.reference[0])),"scrollParent"!==this._config.boundary&&u.default(i).addClass("position-static"),this._popper=new we(e,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===u.default(i).closest(".navbar-nav").length&&u.default(document.body).children().on("mouseover",null,u.default.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),u.default(this._menu).toggleClass("show"),u.default(i).toggleClass("show").trigger(u.default.Event("shown.bs.dropdown",t))}}},_e.hide=function(){var e,t,n;this._element.disabled||u.default(this._element).hasClass("disabled")||!u.default(this._menu).hasClass("show")||(e={relatedTarget:this._element},t=u.default.Event("hide.bs.dropdown",e),n=Oe._getParentFromElement(this._element),u.default(n).trigger(t),t.isDefaultPrevented()||(this._popper&&this._popper.destroy(),u.default(this._menu).toggleClass("show"),u.default(n).toggleClass("show").trigger(u.default.Event("hidden.bs.dropdown",e))))},_e.dispose=function(){u.default.removeData(this._element,"bs.dropdown"),u.default(this._element).off(".bs.dropdown"),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},_e.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},_e._addEventListeners=function(){var t=this;u.default(this._element).on("click.bs.dropdown",function(e){e.preventDefault(),e.stopPropagation(),t.toggle()})},_e._getConfig=function(e){return e=o({},this.constructor.Default,u.default(this._element).data(),e),f.typeCheckConfig(xe,e,this.constructor.DefaultType),e},_e._getMenuElement=function(){var e;return this._menu||(e=Oe._getParentFromElement(this._element))&&(this._menu=e.querySelector(".dropdown-menu")),this._menu},_e._getPlacement=function(){var e=u.default(this._element.parentNode),t="bottom-start";return e.hasClass("dropup")?t=u.default(this._menu).hasClass("dropdown-menu-right")?"top-end":"top-start":e.hasClass("dropright")?t="right-start":e.hasClass("dropleft")?t="left-start":u.default(this._menu).hasClass("dropdown-menu-right")&&(t="bottom-end"),t},_e._detectNavbar=function(){return 0<u.default(this._element).closest(".navbar").length},_e._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=o({},e.offsets,t._config.offset(e.offsets,t._element)||{}),e}:e.offset=this._config.offset,e},_e._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(e.modifiers.applyStyle={enabled:!1}),o({},e,this._config.popperConfig)},Oe._jQueryInterface=function(t){return this.each(function(){var e=u.default(this).data("bs.dropdown");if(e||(e=new Oe(this,"object"==typeof t?t:null),u.default(this).data("bs.dropdown",e)),"string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},Oe._clearMenus=function(e){if(!e||3!==e.which&&("keyup"!==e.type||9===e.which))for(var t=[].slice.call(document.querySelectorAll('[data-toggle="dropdown"]')),n=0,i=t.length;n<i;n++){var r,o,s=Oe._getParentFromElement(t[n]),a=u.default(t[n]).data("bs.dropdown"),l={relatedTarget:t[n]};e&&"click"===e.type&&(l.clickEvent=e),a&&(r=a._menu,!u.default(s).hasClass("show")||e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&9===e.which)&&u.default.contains(s,e.target)||(o=u.default.Event("hide.bs.dropdown",l),u.default(s).trigger(o),o.isDefaultPrevented()||("ontouchstart"in document.documentElement&&u.default(document.body).children().off("mouseover",null,u.default.noop),t[n].setAttribute("aria-expanded","false"),a._popper&&a._popper.destroy(),u.default(r).removeClass("show"),u.default(s).removeClass("show").trigger(u.default.Event("hidden.bs.dropdown",l)))))}},Oe._getParentFromElement=function(e){var t,n=f.getSelectorFromElement(e);return n&&(t=document.querySelector(n)),t||e.parentNode},Oe._dataApiKeydownHandler=function(e){if(!(/input|textarea/i.test(e.target.tagName)?32===e.which||27!==e.which&&(40!==e.which&&38!==e.which||u.default(e.target).closest(".dropdown-menu").length):!Ce.test(e.which))&&!this.disabled&&!u.default(this).hasClass("disabled")){var t=Oe._getParentFromElement(this),n=u.default(t).hasClass("show");if(n||27!==e.which){if(e.preventDefault(),e.stopPropagation(),!n||27===e.which||32===e.which)return 27===e.which&&u.default(t.querySelector('[data-toggle="dropdown"]')).trigger("focus"),void u.default(this).trigger("click");n=[].slice.call(t.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(e){return u.default(e).is(":visible")});0!==n.length&&(t=n.indexOf(e.target),38===e.which&&0<t&&t--,40===e.which&&t<n.length-1&&t++,t<0&&(t=0),n[t].focus())}}},r(Oe,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return Se}},{key:"DefaultType",get:function(){return Ae}}]),Oe);function Oe(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}u.default(document).on("keydown.bs.dropdown.data-api",'[data-toggle="dropdown"]',ke._dataApiKeydownHandler).on("keydown.bs.dropdown.data-api",".dropdown-menu",ke._dataApiKeydownHandler).on("click.bs.dropdown.data-api keyup.bs.dropdown.data-api",ke._clearMenus).on("click.bs.dropdown.data-api",'[data-toggle="dropdown"]',function(e){e.preventDefault(),e.stopPropagation(),ke._jQueryInterface.call(u.default(this),"toggle")}).on("click.bs.dropdown.data-api",".dropdown form",function(e){e.stopPropagation()}),u.default.fn[xe]=ke._jQueryInterface,u.default.fn[xe].Constructor=ke,u.default.fn[xe].noConflict=function(){return u.default.fn[xe]=Te,ke._jQueryInterface};var Ne=u.default.fn.modal,De={backdrop:!0,keyboard:!0,focus:!0,show:!0},je={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},Le=((_e=Ie.prototype).toggle=function(e){return this._isShown?this.hide():this.show(e)},_e.show=function(e){var t,n=this;this._isShown||this._isTransitioning||(u.default(this._element).hasClass("fade")&&(this._isTransitioning=!0),t=u.default.Event("show.bs.modal",{relatedTarget:e}),u.default(this._element).trigger(t),this._isShown||t.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),u.default(this._element).on("click.dismiss.bs.modal",'[data-dismiss="modal"]',function(e){return n.hide(e)}),u.default(this._dialog).on("mousedown.dismiss.bs.modal",function(){u.default(n._element).one("mouseup.dismiss.bs.modal",function(e){u.default(e.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(e)})))},_e.hide=function(e){var t=this;e&&e.preventDefault(),this._isShown&&!this._isTransitioning&&(e=u.default.Event("hide.bs.modal"),u.default(this._element).trigger(e),this._isShown&&!e.isDefaultPrevented()&&(this._isShown=!1,(e=u.default(this._element).hasClass("fade"))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),u.default(document).off("focusin.bs.modal"),u.default(this._element).removeClass("show"),u.default(this._element).off("click.dismiss.bs.modal"),u.default(this._dialog).off("mousedown.dismiss.bs.modal"),e?(e=f.getTransitionDurationFromElement(this._element),u.default(this._element).one(f.TRANSITION_END,function(e){return t._hideModal(e)}).emulateTransitionEnd(e)):this._hideModal()))},_e.dispose=function(){[window,this._element,this._dialog].forEach(function(e){return u.default(e).off(".bs.modal")}),u.default(document).off("focusin.bs.modal"),u.default.removeData(this._element,"bs.modal"),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},_e.handleUpdate=function(){this._adjustDialog()},_e._getConfig=function(e){return e=o({},De,e),f.typeCheckConfig("modal",e,je),e},_e._triggerBackdropTransition=function(){var e,t,n,i=this;"static"===this._config.backdrop?(e=u.default.Event("hidePrevented.bs.modal"),u.default(this._element).trigger(e),e.isDefaultPrevented()||((t=this._element.scrollHeight>document.documentElement.clientHeight)||(this._element.style.overflowY="hidden"),this._element.classList.add("modal-static"),n=f.getTransitionDurationFromElement(this._dialog),u.default(this._element).off(f.TRANSITION_END),u.default(this._element).one(f.TRANSITION_END,function(){i._element.classList.remove("modal-static"),t||u.default(i._element).one(f.TRANSITION_END,function(){i._element.style.overflowY=""}).emulateTransitionEnd(i._element,n)}).emulateTransitionEnd(n),this._element.focus())):this.hide()},_e._showElement=function(e){var t=this,n=u.default(this._element).hasClass("fade"),i=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),u.default(this._dialog).hasClass("modal-dialog-scrollable")&&i?i.scrollTop=0:this._element.scrollTop=0,n&&f.reflow(this._element),u.default(this._element).addClass("show"),this._config.focus&&this._enforceFocus();var r=u.default.Event("shown.bs.modal",{relatedTarget:e}),e=function(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,u.default(t._element).trigger(r)};n?(n=f.getTransitionDurationFromElement(this._dialog),u.default(this._dialog).one(f.TRANSITION_END,e).emulateTransitionEnd(n)):e()},_e._enforceFocus=function(){var t=this;u.default(document).off("focusin.bs.modal").on("focusin.bs.modal",function(e){document!==e.target&&t._element!==e.target&&0===u.default(t._element).has(e.target).length&&t._element.focus()})},_e._setEscapeEvent=function(){var t=this;this._isShown?u.default(this._element).on("keydown.dismiss.bs.modal",function(e){t._config.keyboard&&27===e.which?(e.preventDefault(),t.hide()):t._config.keyboard||27!==e.which||t._triggerBackdropTransition()}):this._isShown||u.default(this._element).off("keydown.dismiss.bs.modal")},_e._setResizeEvent=function(){var t=this;this._isShown?u.default(window).on("resize.bs.modal",function(e){return t.handleUpdate(e)}):u.default(window).off("resize.bs.modal")},_e._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(function(){u.default(document.body).removeClass("modal-open"),e._resetAdjustments(),e._resetScrollbar(),u.default(e._element).trigger("hidden.bs.modal")})},_e._removeBackdrop=function(){this._backdrop&&(u.default(this._backdrop).remove(),this._backdrop=null)},_e._showBackdrop=function(e){var t,n=this,i=u.default(this._element).hasClass("fade")?"fade":"";this._isShown&&this._config.backdrop?(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",i&&this._backdrop.classList.add(i),u.default(this._backdrop).appendTo(document.body),u.default(this._element).on("click.dismiss.bs.modal",function(e){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:e.target===e.currentTarget&&n._triggerBackdropTransition()}),i&&f.reflow(this._backdrop),u.default(this._backdrop).addClass("show"),e&&(i?(t=f.getTransitionDurationFromElement(this._backdrop),u.default(this._backdrop).one(f.TRANSITION_END,e).emulateTransitionEnd(t)):e())):!this._isShown&&this._backdrop?(u.default(this._backdrop).removeClass("show"),i=function(){n._removeBackdrop(),e&&e()},u.default(this._element).hasClass("fade")?(t=f.getTransitionDurationFromElement(this._backdrop),u.default(this._backdrop).one(f.TRANSITION_END,i).emulateTransitionEnd(t)):i()):e&&e()},_e._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},_e._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},_e._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},_e._setScrollbar=function(){var e,t,r=this;this._isBodyOverflowing&&(e=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top")),t=[].slice.call(document.querySelectorAll(".sticky-top")),u.default(e).each(function(e,t){var n=t.style.paddingRight,i=u.default(t).css("padding-right");u.default(t).data("padding-right",n).css("padding-right",parseFloat(i)+r._scrollbarWidth+"px")}),u.default(t).each(function(e,t){var n=t.style.marginRight,i=u.default(t).css("margin-right");u.default(t).data("margin-right",n).css("margin-right",parseFloat(i)-r._scrollbarWidth+"px")}),e=document.body.style.paddingRight,t=u.default(document.body).css("padding-right"),u.default(document.body).data("padding-right",e).css("padding-right",parseFloat(t)+this._scrollbarWidth+"px")),u.default(document.body).addClass("modal-open")},_e._resetScrollbar=function(){var e=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top"));u.default(e).each(function(e,t){var n=u.default(t).data("padding-right");u.default(t).removeData("padding-right"),t.style.paddingRight=n||""});e=[].slice.call(document.querySelectorAll(".sticky-top"));u.default(e).each(function(e,t){var n=u.default(t).data("margin-right");void 0!==n&&u.default(t).css("margin-right",n).removeData("margin-right")});e=u.default(document.body).data("padding-right");u.default(document.body).removeData("padding-right"),document.body.style.paddingRight=e||""},_e._getScrollbarWidth=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},Ie._jQueryInterface=function(n,i){return this.each(function(){var e=u.default(this).data("bs.modal"),t=o({},De,u.default(this).data(),"object"==typeof n&&n?n:{});if(e||(e=new Ie(this,t),u.default(this).data("bs.modal",e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n](i)}else t.show&&e.show(i)})},r(Ie,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return De}}]),Ie);function Ie(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}u.default(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(e){var t,n=this,i=f.getSelectorFromElement(this);i&&(t=document.querySelector(i));i=u.default(t).data("bs.modal")?"toggle":o({},u.default(t).data(),u.default(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var r=u.default(t).one("show.bs.modal",function(e){e.isDefaultPrevented()||r.one("hidden.bs.modal",function(){u.default(n).is(":visible")&&n.focus()})});Le._jQueryInterface.call(u.default(t),i,this)}),u.default.fn.modal=Le._jQueryInterface,u.default.fn.modal.Constructor=Le,u.default.fn.modal.noConflict=function(){return u.default.fn.modal=Ne,Le._jQueryInterface};var Me=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],Re=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,Pe=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function qe(e,r,t){if(0===e.length)return e;if(t&&"function"==typeof t)return t(e);for(var e=(new window.DOMParser).parseFromString(e,"text/html"),o=Object.keys(r),s=[].slice.call(e.body.querySelectorAll("*")),n=0,i=s.length;n<i;n++)!function(e){var t=s[e],n=t.nodeName.toLowerCase();if(-1===o.indexOf(t.nodeName.toLowerCase()))return t.parentNode.removeChild(t);var e=[].slice.call(t.attributes),i=[].concat(r["*"]||[],r[n]||[]);e.forEach(function(e){!function(e,t){var n=e.nodeName.toLowerCase();if(-1!==t.indexOf(n))return-1===Me.indexOf(n)||Boolean(e.nodeValue.match(Re)||e.nodeValue.match(Pe));for(var i=t.filter(function(e){return e instanceof RegExp}),r=0,o=i.length;r<o;r++)if(n.match(i[r]))return 1}(e,i)&&t.removeAttribute(e.nodeName)})}(n);return e.body.innerHTML}var He="tooltip",We=u.default.fn[He],Fe=new RegExp("(^|\\s)bs-tooltip\\S+","g"),Be=["sanitize","whiteList","sanitizeFn"],ze={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},$e={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Ve={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},popperConfig:null},Ue={HIDE:"hide.bs.tooltip",HIDDEN:"hidden.bs.tooltip",SHOW:"show.bs.tooltip",SHOWN:"shown.bs.tooltip",INSERTED:"inserted.bs.tooltip",CLICK:"click.bs.tooltip",FOCUSIN:"focusin.bs.tooltip",FOCUSOUT:"focusout.bs.tooltip",MOUSEENTER:"mouseenter.bs.tooltip",MOUSELEAVE:"mouseleave.bs.tooltip"},Ye=((_e=Qe.prototype).enable=function(){this._isEnabled=!0},_e.disable=function(){this._isEnabled=!1},_e.toggleEnabled=function(){this._isEnabled=!this._isEnabled},_e.toggle=function(e){var t,n;this._isEnabled&&(e?(t=this.constructor.DATA_KEY,(n=u.default(e.currentTarget).data(t))||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),u.default(e.currentTarget).data(t,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)):u.default(this.getTipElement()).hasClass("show")?this._leave(null,this):this._enter(null,this))},_e.dispose=function(){clearTimeout(this._timeout),u.default.removeData(this.element,this.constructor.DATA_KEY),u.default(this.element).off(this.constructor.EVENT_KEY),u.default(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&u.default(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},_e.show=function(){var t=this;if("none"===u.default(this.element).css("display"))throw new Error("Please use show on visible elements");var e,n,i=u.default.Event(this.constructor.Event.SHOW);this.isWithContent()&&this._isEnabled&&(u.default(this.element).trigger(i),n=f.findShadowRoot(this.element),e=u.default.contains(null!==n?n:this.element.ownerDocument.documentElement,this.element),!i.isDefaultPrevented()&&e&&(n=this.getTipElement(),i=f.getUID(this.constructor.NAME),n.setAttribute("id",i),this.element.setAttribute("aria-describedby",i),this.setContent(),this.config.animation&&u.default(n).addClass("fade"),e="function"==typeof this.config.placement?this.config.placement.call(this,n,this.element):this.config.placement,i=this._getAttachment(e),this.addAttachmentClass(i),e=this._getContainer(),u.default(n).data(this.constructor.DATA_KEY,this),u.default.contains(this.element.ownerDocument.documentElement,this.tip)||u.default(n).appendTo(e),u.default(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new we(this.element,n,this._getPopperConfig(i)),u.default(n).addClass("show"),"ontouchstart"in document.documentElement&&u.default(document.body).children().on("mouseover",null,u.default.noop),i=function(){t.config.animation&&t._fixTransition();var e=t._hoverState;t._hoverState=null,u.default(t.element).trigger(t.constructor.Event.SHOWN),"out"===e&&t._leave(null,t)},u.default(this.tip).hasClass("fade")?(n=f.getTransitionDurationFromElement(this.tip),u.default(this.tip).one(f.TRANSITION_END,i).emulateTransitionEnd(n)):i()))},_e.hide=function(e){function t(){"show"!==n._hoverState&&i.parentNode&&i.parentNode.removeChild(i),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),u.default(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),e&&e()}var n=this,i=this.getTipElement(),r=u.default.Event(this.constructor.Event.HIDE);u.default(this.element).trigger(r),r.isDefaultPrevented()||(u.default(i).removeClass("show"),"ontouchstart"in document.documentElement&&u.default(document.body).children().off("mouseover",null,u.default.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,u.default(this.tip).hasClass("fade")?(r=f.getTransitionDurationFromElement(i),u.default(i).one(f.TRANSITION_END,t).emulateTransitionEnd(r)):t(),this._hoverState="")},_e.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},_e.isWithContent=function(){return Boolean(this.getTitle())},_e.addAttachmentClass=function(e){u.default(this.getTipElement()).addClass("bs-tooltip-"+e)},_e.getTipElement=function(){return this.tip=this.tip||u.default(this.config.template)[0],this.tip},_e.setContent=function(){var e=this.getTipElement();this.setElementContent(u.default(e.querySelectorAll(".tooltip-inner")),this.getTitle()),u.default(e).removeClass("fade show")},_e.setElementContent=function(e,t){"object"!=typeof t||!t.nodeType&&!t.jquery?this.config.html?(this.config.sanitize&&(t=qe(t,this.config.whiteList,this.config.sanitizeFn)),e.html(t)):e.text(t):this.config.html?u.default(t).parent().is(e)||e.empty().append(t):e.text(u.default(t).text())},_e.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},_e._getPopperConfig=function(e){var t=this;return o({},{placement:e,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){return t._handlePopperPlacementChange(e)}},this.config.popperConfig)},_e._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=o({},e.offsets,t.config.offset(e.offsets,t.element)||{}),e}:e.offset=this.config.offset,e},_e._getContainer=function(){return!1===this.config.container?document.body:f.isElement(this.config.container)?u.default(this.config.container):u.default(document).find(this.config.container)},_e._getAttachment=function(e){return $e[e.toUpperCase()]},_e._setListeners=function(){var n=this;this.config.trigger.split(" ").forEach(function(e){var t;"click"===e?u.default(n.element).on(n.constructor.Event.CLICK,n.config.selector,function(e){return n.toggle(e)}):"manual"!==e&&(t="hover"===e?n.constructor.Event.MOUSEENTER:n.constructor.Event.FOCUSIN,e="hover"===e?n.constructor.Event.MOUSELEAVE:n.constructor.Event.FOCUSOUT,u.default(n.element).on(t,n.config.selector,function(e){return n._enter(e)}).on(e,n.config.selector,function(e){return n._leave(e)}))}),this._hideModalHandler=function(){n.element&&n.hide()},u.default(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=o({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},_e._fixTitle=function(){var e=typeof this.element.getAttribute("data-original-title");!this.element.getAttribute("title")&&"string"==e||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},_e._enter=function(e,t){var n=this.constructor.DATA_KEY;(t=t||u.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),u.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusin"===e.type?"focus":"hover"]=!0),u.default(t.getTipElement()).hasClass("show")||"show"===t._hoverState?t._hoverState="show":(clearTimeout(t._timeout),t._hoverState="show",t.config.delay&&t.config.delay.show?t._timeout=setTimeout(function(){"show"===t._hoverState&&t.show()},t.config.delay.show):t.show())},_e._leave=function(e,t){var n=this.constructor.DATA_KEY;(t=t||u.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),u.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusout"===e.type?"focus":"hover"]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState="out",t.config.delay&&t.config.delay.hide?t._timeout=setTimeout(function(){"out"===t._hoverState&&t.hide()},t.config.delay.hide):t.hide())},_e._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},_e._getConfig=function(e){var t=u.default(this.element).data();return Object.keys(t).forEach(function(e){-1!==Be.indexOf(e)&&delete t[e]}),"number"==typeof(e=o({},this.constructor.Default,t,"object"==typeof e&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),f.typeCheckConfig(He,e,this.constructor.DefaultType),e.sanitize&&(e.template=qe(e.template,e.whiteList,e.sanitizeFn)),e},_e._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},_e._cleanTipClass=function(){var e=u.default(this.getTipElement()),t=e.attr("class").match(Fe);null!==t&&t.length&&e.removeClass(t.join(""))},_e._handlePopperPlacementChange=function(e){this.tip=e.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))},_e._fixTransition=function(){var e=this.getTipElement(),t=this.config.animation;null===e.getAttribute("x-placement")&&(u.default(e).removeClass("fade"),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},Qe._jQueryInterface=function(i){return this.each(function(){var e=u.default(this),t=e.data("bs.tooltip"),n="object"==typeof i&&i;if((t||!/dispose|hide/.test(i))&&(t||(t=new Qe(this,n),e.data("bs.tooltip",t)),"string"==typeof i)){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},r(Qe,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return Ve}},{key:"NAME",get:function(){return He}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return Ue}},{key:"EVENT_KEY",get:function(){return".bs.tooltip"}},{key:"DefaultType",get:function(){return ze}}]),Qe);function Qe(e,t){if(void 0===we)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners()}u.default.fn[He]=Ye._jQueryInterface,u.default.fn[He].Constructor=Ye,u.default.fn[He].noConflict=function(){return u.default.fn[He]=We,Ye._jQueryInterface};var Xe="popover",Ge=u.default.fn[Xe],Ke=new RegExp("(^|\\s)bs-popover\\S+","g"),Je=o({},Ye.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),Ze=o({},Ye.DefaultType,{content:"(string|element|function)"}),et={HIDE:"hide.bs.popover",HIDDEN:"hidden.bs.popover",SHOW:"show.bs.popover",SHOWN:"shown.bs.popover",INSERTED:"inserted.bs.popover",CLICK:"click.bs.popover",FOCUSIN:"focusin.bs.popover",FOCUSOUT:"focusout.bs.popover",MOUSEENTER:"mouseenter.bs.popover",MOUSELEAVE:"mouseleave.bs.popover"},tt=function(e){var t;function i(){return e.apply(this,arguments)||this}n=e,(t=i).prototype=Object.create(n.prototype),(t.prototype.constructor=t).__proto__=n;var n=i.prototype;return n.isWithContent=function(){return this.getTitle()||this._getContent()},n.addAttachmentClass=function(e){u.default(this.getTipElement()).addClass("bs-popover-"+e)},n.getTipElement=function(){return this.tip=this.tip||u.default(this.config.template)[0],this.tip},n.setContent=function(){var e=u.default(this.getTipElement());this.setElementContent(e.find(".popover-header"),this.getTitle());var t=this._getContent();"function"==typeof t&&(t=t.call(this.element)),this.setElementContent(e.find(".popover-body"),t),e.removeClass("fade show")},n._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},n._cleanTipClass=function(){var e=u.default(this.getTipElement()),t=e.attr("class").match(Ke);null!==t&&0<t.length&&e.removeClass(t.join(""))},i._jQueryInterface=function(n){return this.each(function(){var e=u.default(this).data("bs.popover"),t="object"==typeof n?n:null;if((e||!/dispose|hide/.test(n))&&(e||(e=new i(this,t),u.default(this).data("bs.popover",e)),"string"==typeof n)){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},r(i,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return Je}},{key:"NAME",get:function(){return Xe}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return et}},{key:"EVENT_KEY",get:function(){return".bs.popover"}},{key:"DefaultType",get:function(){return Ze}}]),i}(Ye);u.default.fn[Xe]=tt._jQueryInterface,u.default.fn[Xe].Constructor=tt,u.default.fn[Xe].noConflict=function(){return u.default.fn[Xe]=Ge,tt._jQueryInterface};var nt="scrollspy",it=u.default.fn[nt],rt={offset:10,method:"auto",target:""},ot={offset:"number",method:"string",target:"(string|element)"},st=((_e=at.prototype).refresh=function(){var t=this,e=this._scrollElement===this._scrollElement.window?"offset":"position",i="auto"===this._config.method?e:this._config.method,r="position"===i?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(e){var t,n=f.getSelectorFromElement(e);if(n&&(t=document.querySelector(n)),t){e=t.getBoundingClientRect();if(e.width||e.height)return[u.default(t)[i]().top+r,n]}return null}).filter(function(e){return e}).sort(function(e,t){return e[0]-t[0]}).forEach(function(e){t._offsets.push(e[0]),t._targets.push(e[1])})},_e.dispose=function(){u.default.removeData(this._element,"bs.scrollspy"),u.default(this._scrollElement).off(".bs.scrollspy"),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},_e._getConfig=function(e){var t;return"string"!=typeof(e=o({},rt,"object"==typeof e&&e?e:{})).target&&f.isElement(e.target)&&((t=u.default(e.target).attr("id"))||(t=f.getUID(nt),u.default(e.target).attr("id",t)),e.target="#"+t),f.typeCheckConfig(nt,e,ot),e},_e._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},_e._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},_e._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},_e._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),n<=e){n=this._targets[this._targets.length-1];this._activeTarget!==n&&this._activate(n)}else{if(this._activeTarget&&e<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var i=this._offsets.length;i--;)this._activeTarget!==this._targets[i]&&e>=this._offsets[i]&&(void 0===this._offsets[i+1]||e<this._offsets[i+1])&&this._activate(this._targets[i])}},_e._activate=function(t){this._activeTarget=t,this._clear();var e=this._selector.split(",").map(function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'}),e=u.default([].slice.call(document.querySelectorAll(e.join(","))));e.hasClass("dropdown-item")?(e.closest(".dropdown").find(".dropdown-toggle").addClass("active"),e.addClass("active")):(e.addClass("active"),e.parents(".nav, .list-group").prev(".nav-link, .list-group-item").addClass("active"),e.parents(".nav, .list-group").prev(".nav-item").children(".nav-link").addClass("active")),u.default(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:t})},_e._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(e){return e.classList.contains("active")}).forEach(function(e){return e.classList.remove("active")})},at._jQueryInterface=function(t){return this.each(function(){var e=u.default(this).data("bs.scrollspy");if(e||(e=new at(this,"object"==typeof t&&t),u.default(this).data("bs.scrollspy",e)),"string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},r(at,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"Default",get:function(){return rt}}]),at);function at(e,t){var n=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(t),this._selector=this._config.target+" .nav-link,"+this._config.target+" .list-group-item,"+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,u.default(this._scrollElement).on("scroll.bs.scrollspy",function(e){return n._process(e)}),this.refresh(),this._process()}u.default(window).on("load.bs.scrollspy.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),t=e.length;t--;){var n=u.default(e[t]);st._jQueryInterface.call(n,n.data())}}),u.default.fn[nt]=st._jQueryInterface,u.default.fn[nt].Constructor=st,u.default.fn[nt].noConflict=function(){return u.default.fn[nt]=it,st._jQueryInterface};var lt=u.default.fn.tab,ct=((_e=ut.prototype).show=function(){var e,t,n,i,r,o,s=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&u.default(this._element).hasClass("active")||u.default(this._element).hasClass("disabled")||(o=u.default(this._element).closest(".nav, .list-group")[0],t=f.getSelectorFromElement(this._element),o&&(r="UL"===o.nodeName||"OL"===o.nodeName?"> li > .active":".active",n=(n=u.default.makeArray(u.default(o).find(r)))[n.length-1]),i=u.default.Event("hide.bs.tab",{relatedTarget:this._element}),r=u.default.Event("show.bs.tab",{relatedTarget:n}),n&&u.default(n).trigger(i),u.default(this._element).trigger(r),r.isDefaultPrevented()||i.isDefaultPrevented()||(t&&(e=document.querySelector(t)),this._activate(this._element,o),o=function(){var e=u.default.Event("hidden.bs.tab",{relatedTarget:s._element}),t=u.default.Event("shown.bs.tab",{relatedTarget:n});u.default(n).trigger(e),u.default(s._element).trigger(t)},e?this._activate(e,e.parentNode,o):o()))},_e.dispose=function(){u.default.removeData(this._element,"bs.tab"),this._element=null},_e._activate=function(e,t,n){var i=this,r=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?u.default(t).children(".active"):u.default(t).find("> li > .active"))[0],o=n&&r&&u.default(r).hasClass("fade"),t=function(){return i._transitionComplete(e,r,n)};r&&o?(o=f.getTransitionDurationFromElement(r),u.default(r).removeClass("show").one(f.TRANSITION_END,t).emulateTransitionEnd(o)):t()},_e._transitionComplete=function(e,t,n){var i;t&&(u.default(t).removeClass("active"),(i=u.default(t.parentNode).find("> .dropdown-menu .active")[0])&&u.default(i).removeClass("active"),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1)),u.default(e).addClass("active"),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),f.reflow(e),e.classList.contains("fade")&&e.classList.add("show"),e.parentNode&&u.default(e.parentNode).hasClass("dropdown-menu")&&((t=u.default(e).closest(".dropdown")[0])&&(t=[].slice.call(t.querySelectorAll(".dropdown-toggle")),u.default(t).addClass("active")),e.setAttribute("aria-expanded",!0)),n&&n()},ut._jQueryInterface=function(n){return this.each(function(){var e=u.default(this),t=e.data("bs.tab");if(t||(t=new ut(this),e.data("bs.tab",t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},r(ut,null,[{key:"VERSION",get:function(){return"4.5.3"}}]),ut);function ut(e){this._element=e}u.default(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(e){e.preventDefault(),ct._jQueryInterface.call(u.default(this),"show")}),u.default.fn.tab=ct._jQueryInterface,u.default.fn.tab.Constructor=ct,u.default.fn.tab.noConflict=function(){return u.default.fn.tab=lt,ct._jQueryInterface};var ft=u.default.fn.toast,dt={animation:"boolean",autohide:"boolean",delay:"number"},ht={animation:!0,autohide:!0,delay:500},pt=((_e=gt.prototype).show=function(){var e,t=this,n=u.default.Event("show.bs.toast");u.default(this._element).trigger(n),n.isDefaultPrevented()||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),e=function(){t._element.classList.remove("showing"),t._element.classList.add("show"),u.default(t._element).trigger("shown.bs.toast"),t._config.autohide&&(t._timeout=setTimeout(function(){t.hide()},t._config.delay))},this._element.classList.remove("hide"),f.reflow(this._element),this._element.classList.add("showing"),this._config.animation?(n=f.getTransitionDurationFromElement(this._element),u.default(this._element).one(f.TRANSITION_END,e).emulateTransitionEnd(n)):e())},_e.hide=function(){var e;this._element.classList.contains("show")&&(e=u.default.Event("hide.bs.toast"),u.default(this._element).trigger(e),e.isDefaultPrevented()||this._close())},_e.dispose=function(){this._clearTimeout(),this._element.classList.contains("show")&&this._element.classList.remove("show"),u.default(this._element).off("click.dismiss.bs.toast"),u.default.removeData(this._element,"bs.toast"),this._element=null,this._config=null},_e._getConfig=function(e){return e=o({},ht,u.default(this._element).data(),"object"==typeof e&&e?e:{}),f.typeCheckConfig("toast",e,this.constructor.DefaultType),e},_e._setListeners=function(){var e=this;u.default(this._element).on("click.dismiss.bs.toast",'[data-dismiss="toast"]',function(){return e.hide()})},_e._close=function(){function e(){n._element.classList.add("hide"),u.default(n._element).trigger("hidden.bs.toast")}var t,n=this;this._element.classList.remove("show"),this._config.animation?(t=f.getTransitionDurationFromElement(this._element),u.default(this._element).one(f.TRANSITION_END,e).emulateTransitionEnd(t)):e()},_e._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},gt._jQueryInterface=function(n){return this.each(function(){var e=u.default(this),t=e.data("bs.toast");if(t||(t=new gt(this,"object"==typeof n&&n),e.data("bs.toast",t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n](this)}})},r(gt,null,[{key:"VERSION",get:function(){return"4.5.3"}},{key:"DefaultType",get:function(){return dt}},{key:"Default",get:function(){return ht}}]),gt);function gt(e,t){this._element=e,this._config=this._getConfig(t),this._timeout=null,this._setListeners()}u.default.fn.toast=pt._jQueryInterface,u.default.fn.toast.Constructor=pt,u.default.fn.toast.noConflict=function(){return u.default.fn.toast=ft,pt._jQueryInterface},e.Alert=l,e.Button=h,e.Carousel=_,e.Collapse=S,e.Dropdown=ke,e.Modal=Le,e.Popover=tt,e.Scrollspy=st,e.Tab=ct,e.Toast=pt,e.Tooltip=Ye,e.Util=f,Object.defineProperty(e,"__esModule",{value:!0})}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).SimpleBar=t()}(this,function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e,t){return e(t={exports:{}},t.exports),t.exports}function m(e){try{return!!e()}catch(e){return!0}}function v(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}function i(e){return k.call(e).slice(8,-1)}function s(e){if(null==e)throw TypeError("Can't call method on "+e);return e}function l(e){return N(s(e))}function y(e){return"object"==typeof e?null!==e:"function"==typeof e}function r(e,t){if(!y(e))return e;var n,i;if(t&&"function"==typeof(n=e.toString)&&!y(i=n.call(e)))return i;if("function"==typeof(n=e.valueOf)&&!y(i=n.call(e)))return i;if(!t&&"function"==typeof(n=e.toString)&&!y(i=n.call(e)))return i;throw TypeError("Can't convert object to primitive value")}function c(e,t){return D.call(e,t)}function n(e){return L?j.createElement(e):{}}function E(e){if(!y(e))throw TypeError(String(e)+" is not an object");return e}function u(t,n){try{H(x,t,n)}catch(e){x[t]=n}return n}function a(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++z+$).toString(36)}var o,f,d,h,p,g,b,_,w="object",x=(le=function(e){return e&&e.Math==Math&&e})(typeof globalThis==w&&globalThis)||le(typeof window==w&&window)||le(typeof self==w&&self)||le(typeof e==w&&e)||Function("return this")(),T=!m(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}),C={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,A={f:S&&!C.call({1:2},1)?function(e){return!!(e=S(this,e))&&e.enumerable}:C},k={}.toString,O="".split,N=m(function(){return!Object("z").propertyIsEnumerable(0)})?function(e){return"String"==i(e)?O.call(e,""):Object(e)}:Object,D={}.hasOwnProperty,j=x.document,L=y(j)&&y(j.createElement),I=!T&&!m(function(){return 7!=Object.defineProperty(n("div"),"a",{get:function(){return 7}}).a}),M=Object.getOwnPropertyDescriptor,R={f:T?M:function(e,t){if(e=l(e),t=r(t,!0),I)try{return M(e,t)}catch(e){}if(c(e,t))return v(!A.f.call(e,t),e[t])}},P=Object.defineProperty,q={f:T?P:function(e,t,n){if(E(e),t=r(t,!0),E(n),I)try{return P(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},H=T?function(e,t,n){return q.f(e,t,v(1,n))}:function(e,t,n){return e[t]=n,e},W=t(function(e){var t="__core-js_shared__",n=x[t]||u(t,{});(e.exports=function(e,t){return n[e]||(n[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.2.1",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})}),F=W("native-function-to-string",Function.toString),B="function"==typeof(Ze=x.WeakMap)&&/native code/.test(F.call(Ze)),z=0,$=Math.random(),V=W("keys"),U=function(e){return V[e]||(V[e]=a(e))},Y={},Q=x.WeakMap;function X(e){return"function"==typeof e?e:void 0}function G(e){return isNaN(e=+e)?0:(0<e?ue:ce)(e)}function K(e){return 0<e?fe(G(e),9007199254740991):0}function J(e,t){var n,i=l(e),r=0,o=[];for(n in i)!c(Y,n)&&c(i,n)&&o.push(n);for(;t.length>r;)c(i,n=t[r++])&&(~pe(o,n)||o.push(n));return o}function Z(e,t){var n,i,r,o=e.target,s=e.global,a=e.stat,l=s?x:a?x[o]||u(o,{}):(x[o]||{}).prototype;if(l)for(n in t){if(i=t[n],r=e.noTargetGet?(r=Se(l,n))&&r.value:l[n],!Ce(s?n:o+(a?".":"#")+n,e.forced)&&void 0!==r){if(typeof i==typeof r)continue;!function(e,t){for(var n=be(t),i=q.f,r=R.f,o=0;o<n.length;o++){var s=n[o];c(e,s)||i(e,s,r(t,s))}}(i,r)}(e.sham||r&&r.sham)&&H(i,"sham",!0),se(l,n,i,e)}}function ee(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e}function te(i,r,e){if(ee(i),void 0===r)return i;switch(e){case 0:return function(){return i.call(r)};case 1:return function(e){return i.call(r,e)};case 2:return function(e,t){return i.call(r,e,t)};case 3:return function(e,t,n){return i.call(r,e,t,n)}}return function(){return i.apply(r,arguments)}}function ne(e){return Object(s(e))}function ie(e){return Ne[e]||(Ne[e]=ke&&Oe[e]||(ke?Oe:a)("Symbol."+e))}function re(e,t){var n;return Ae(e)&&("function"==typeof(n=e.constructor)&&(n===Array||Ae(n.prototype))||y(n)&&null===(n=n[De]))&&(n=void 0),new(void 0===n?Array:n)(0===t?0:t)}b=B?(o=new Q,f=o.get,d=o.has,h=o.set,p=function(e,t){return h.call(o,e,t),t},g=function(e){return f.call(o,e)||{}},function(e){return d.call(o,e)}):(_=U("state"),Y[_]=!0,p=function(e,t){return H(e,_,t),t},g=function(e){return c(e,_)?e[_]:{}},function(e){return c(e,_)});var oe={set:p,get:g,has:b,enforce:function(e){return b(e)?g(e):p(e,{})},getterFor:function(n){return function(e){var t;if(!y(e)||(t=g(e)).type!==n)throw TypeError("Incompatible receiver, "+n+" required");return t}}},se=t(function(e){var t=oe.get,s=oe.enforce,a=String(F).split("toString");W("inspectSource",function(e){return F.call(e)}),(e.exports=function(e,t,n,i){var r=!!i&&!!i.unsafe,o=!!i&&!!i.enumerable,i=!!i&&!!i.noTargetGet;"function"==typeof n&&("string"!=typeof t||c(n,"name")||H(n,"name",t),s(n).source=a.join("string"==typeof t?t:"")),e!==x?(r?!i&&e[t]&&(o=!0):delete e[t],o?e[t]=n:H(e,t,n)):o?e[t]=n:u(t,n)})(Function.prototype,"toString",function(){return"function"==typeof this&&t(this).source||F.call(this)})}),ae=x,le=function(e,t){return arguments.length<2?X(ae[e])||X(x[e]):ae[e]&&ae[e][t]||x[e]&&x[e][t]},ce=Math.ceil,ue=Math.floor,fe=Math.min,de=Math.max,he=Math.min,pe=((w=function(a){return function(e,t,n){var i,r=l(e),o=K(r.length),s=(e=o,(n=G(n=n))<0?de(n+e,0):he(n,e));if(a&&t!=t){for(;s<o;)if((i=r[s++])!=i)return!0}else for(;s<o;s++)if((a||s in r)&&r[s]===t)return a||s||0;return!a&&-1}})(!0),w(!1)),ge=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],me=ge.concat("length","prototype"),ve={f:Object.getOwnPropertyNames||function(e){return J(e,me)}},ye={f:Object.getOwnPropertySymbols},be=le("Reflect","ownKeys")||function(e){var t=ve.f(E(e)),n=ye.f;return n?t.concat(n(e)):t},_e=/#|\.prototype\./,we=(C=function(e,t){return(e=Ee[we(e)])==Te||e!=xe&&("function"==typeof t?m(t):!!t)}).normalize=function(e){return String(e).replace(_e,".").toLowerCase()},Ee=C.data={},xe=C.NATIVE="N",Te=C.POLYFILL="P",Ce=C,Se=R.f,Ae=Array.isArray||function(e){return"Array"==i(e)},ke=!!Object.getOwnPropertySymbols&&!m(function(){return!String(Symbol())}),Oe=x.Symbol,Ne=W("wks"),De=ie("species"),je=[].push,w=function(e,t){var n=[][e];return!n||!m(function(){n.call(null,t||function(){throw 1},1)})},Le=(Q={forEach:(Ze=function(d){var h=1==d,p=2==d,g=3==d,m=4==d,v=6==d,y=5==d||v;return function(e,t,n,i){for(var r,o,s=ne(e),a=N(s),l=te(t,n,3),c=K(a.length),u=0,i=i||re,f=h?i(e,c):p?i(e,0):void 0;u<c;u++)if((y||u in a)&&(o=l(r=a[u],u,s),d))if(h)f[u]=o;else if(o)switch(d){case 3:return!0;case 5:return r;case 6:return u;case 2:je.call(f,r)}else if(m)return!1;return v?-1:g||m?m:f}})(0),map:Ze(1),filter:Ze(2),some:Ze(3),every:Ze(4),find:Ze(5),findIndex:Ze(6)}).forEach,Ie=w("forEach")?function(e,t){return Le(this,e,1<arguments.length?t:void 0)}:[].forEach;Z({target:"Array",proto:!0,forced:[].forEach!=Ie},{forEach:Ie});var Me,Re={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};for(Me in Re){var Pe=x[Me],qe=Pe&&Pe.prototype;if(qe&&qe.forEach!==Ie)try{H(qe,"forEach",Ie)}catch(e){qe.forEach=Ie}}var He=!("undefined"==typeof window||!window.document||!window.document.createElement),We=ie("species"),Fe=Q.filter;function Be(){}Z({target:"Array",proto:!0,forced:!!m(function(){var e=[];return(e.constructor={})[We]=function(){return{foo:1}},1!==e.filter(Boolean).foo})},{filter:function(e,t){return Fe(this,e,1<arguments.length?t:void 0)}});var ze=Object.keys||function(e){return J(e,ge)},$e=T?Object.defineProperties:function(e,t){E(e);for(var n,i=ze(t),r=i.length,o=0;o<r;)q.f(e,n=i[o++],t[n]);return e},Ve=le("document","documentElement"),Ue=U("IE_PROTO"),Ye="prototype",Qe=function(){var e=n("iframe"),t=ge.length;for(e.style.display="none",Ve.appendChild(e),e.src=String("javascript:"),(e=e.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),Qe=e.F;t--;)delete Qe[Ye][ge[t]];return Qe()},Xe=Object.create||function(e,t){var n;return null!==e?(Be[Ye]=E(e),n=new Be,Be[Ye]=null,n[Ue]=e):n=Qe(),void 0===t?n:$e(n,t)};Y[Ue]=!0;var Ge=ie("unscopables"),Ke=Array.prototype;null==Ke[Ge]&&H(Ke,Ge,Xe(null));var C=function(e){Ke[Ge][e]=!0},Je={},Ze=!m(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}),et=U("IE_PROTO"),tt=Object.prototype,nt=Ze?Object.getPrototypeOf:function(e){return c(e=ne(e),et)?e[et]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?tt:null},le=ie("iterator"),U=!1;function it(e,t,n){e&&!c(e=n?e:e.prototype,at)&&st(e,at,{configurable:!0,value:t})}function rt(){return this}function ot(){return this}[].keys&&("next"in(Ze=[].keys())?(Ze=nt(nt(Ze)))!==Object.prototype&&(gt=Ze):U=!0),null==gt&&(gt={}),c(gt,le)||H(gt,le,function(){return this});var le={IteratorPrototype:gt,BUGGY_SAFARI_ITERATORS:U},st=q.f,at=ie("toStringTag"),lt=le.IteratorPrototype,ct=Object.setPrototypeOf||("__proto__"in{}?function(){var n,i=!1,e={};try{(n=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(e,[]),i=e instanceof Array}catch(e){}return function(e,t){return E(e),function(){if(!y(t)&&null!==t)throw TypeError("Can't set "+String(t)+" as a prototype")}(),i?n.call(e,t):e.__proto__=t,e}}():void 0),ut=le.IteratorPrototype,ft=le.BUGGY_SAFARI_ITERATORS,dt=ie("iterator"),ht="values",pt="entries",gt=function(e,t,n,i,r,o,s){var a;function l(e){if(e===r&&g)return g;if(!ft&&e in h)return h[e];switch(e){case"keys":case ht:case pt:return function(){return new n(this,e)}}return function(){return new n(this)}}f=t,f+=" Iterator",(a=n).prototype=Xe(lt,{next:v(1,i)}),it(a,f,!1),Je[f]=rt;var c,u,f=t+" Iterator",d=!1,h=e.prototype,p=h[dt]||h["@@iterator"]||r&&h[r],g=!ft&&p||l(r);if((i="Array"==t&&h.entries||p)&&(e=nt(i.call(new e)),ut!==Object.prototype&&e.next&&(nt(e)!==ut&&(ct?ct(e,ut):"function"!=typeof e[dt]&&H(e,dt,ot)),it(e,f,!0))),r==ht&&p&&p.name!==ht&&(d=!0,g=function(){return p.call(this)}),h[dt]!==g&&H(h,dt,g),Je[t]=g,r)if(c={values:l(ht),keys:o?g:l("keys"),entries:l(pt)},s)for(u in c)!ft&&!d&&u in h||se(h,u,c[u]);else Z({target:t,proto:!0,forced:ft||d},c);return c},mt="Array Iterator",vt=oe.set,yt=oe.getterFor(mt),bt=gt(Array,"Array",function(e,t){vt(this,{type:mt,target:l(e),index:0,kind:t})},function(){var e=yt(this),t=e.target,n=e.kind,i=e.index++;return!t||i>=t.length?{value:e.target=void 0,done:!0}:"keys"==n?{value:i,done:!1}:"values"==n?{value:t[i],done:!1}:{value:[i,t[i]],done:!1}},"values");Je.Arguments=Je.Array,C("keys"),C("values"),C("entries");var _t=Object.assign,U=!_t||m(function(){var e={},t={},n=Symbol(),i="abcdefghijklmnopqrst";return e[n]=7,i.split("").forEach(function(e){t[e]=e}),7!=_t({},e)[n]||ze(_t({},t)).join("")!=i})?function(e){for(var t=ne(e),n=arguments.length,i=1,r=ye.f,o=A.f;i<n;)for(var s,a=N(arguments[i++]),l=r?ze(a).concat(r(a)):ze(a),c=l.length,u=0;u<c;)s=l[u++],T&&!o.call(a,s)||(t[s]=a[s]);return t}:_t;function wt(e){var t;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(e=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),Et))?e:xt?i(t):"Object"==(e=i(t))&&"function"==typeof t.callee?"Arguments":e}Z({target:"Object",stat:!0,forced:Object.assign!==U},{assign:U});var Et=ie("toStringTag"),xt="Arguments"==i(function(){return arguments}());(le={})[ie("toStringTag")]="z",(C="[object z]"!==String(le)?function(){return"[object "+wt(this)+"]"}:le.toString)!==(U=Object.prototype).toString&&se(U,"toString",C,{unsafe:!0});var U="["+(le="\t\n\v\f\r                　\u2028\u2029\ufeff")+"]",Tt=RegExp("^"+U+U+"*"),Ct=RegExp(U+U+"*$"),St=((C=function(t){return function(e){return e=String(s(e)),1&t&&(e=e.replace(Tt,"")),2&t&&(e=e.replace(Ct,"")),e}})(1),C(2),C(3)),At=x.parseInt,kt=/^[+-]?0[Xx]/,U=8!==At(le+"08")||22!==At(le+"0x16")?function(e,t){return e=St(String(e)),At(e,t>>>0||(kt.test(e)?16:10))}:At;Z({global:!0,forced:parseInt!=U},{parseInt:U});var Ot=(le={codeAt:(C=function(o){return function(e,t){var n,i=String(s(e)),r=G(t),e=i.length;return r<0||e<=r?o?"":void 0:(t=i.charCodeAt(r))<55296||56319<t||r+1===e||(n=i.charCodeAt(r+1))<56320||57343<n?o?i.charAt(r):t:o?i.slice(r,r+2):n-56320+(t-55296<<10)+65536}})(!1),charAt:C(!0)}).charAt,Nt="String Iterator",Dt=oe.set,jt=oe.getterFor(Nt);function Lt(e,t,n){for(var i in t)se(e,i,t[i],n);return e}function It(e,t,n){if(!(e instanceof t))throw TypeError("Incorrect "+(n?n+" ":"")+"invocation");return e}gt(String,"String",function(e){Dt(this,{type:Nt,string:String(e),index:0})},function(){var e=jt(this),t=e.string,n=e.index;return n>=t.length?{value:void 0,done:!0}:(n=Ot(t,n),e.index+=n.length,{value:n,done:!1})});var Mt=!m(function(){return Object.isExtensible(Object.preventExtensions({}))}),Rt=t(function(e){function n(e){t(e,i,{value:{objectID:"O"+ ++r,weakData:{}}})}var t=q.f,i=a("meta"),r=0,o=Object.isExtensible||function(){return!0},s=e.exports={REQUIRED:!1,fastKey:function(e,t){if(!y(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!c(e,i)){if(!o(e))return"F";if(!t)return"E";n(e)}return e[i].objectID},getWeakData:function(e,t){if(!c(e,i)){if(!o(e))return!0;if(!t)return!1;n(e)}return e[i].weakData},onFreeze:function(e){return Mt&&s.REQUIRED&&o(e)&&!c(e,i)&&n(e),e}};Y[i]=!0}),Pt=(Rt.REQUIRED,Rt.fastKey,Rt.getWeakData,Rt.onFreeze,ie("iterator")),qt=Array.prototype,Ht=ie("iterator"),Wt=t(function(e){function f(e,t){this.stopped=e,this.result=t}(e.exports=function(e,t,n,i,r){var o,s,a,l,c,u=te(t,n,i?2:1);if(r)o=e;else{if("function"!=typeof(n=function(){if(null!=e)return e[Ht]||e["@@iterator"]||Je[wt(e)]}()))throw TypeError("Target is not iterable");if(void 0!==(r=n)&&(Je.Array===r||qt[Pt]===r)){for(s=0,a=K(e.length);s<a;s++)if((l=i?u(E(c=e[s])[0],c[1]):u(e[s]))&&l instanceof f)return l;return new f(!1)}o=n.call(e)}for(;!(c=o.next()).done;)if((l=function(e,t,n,i){try{return i?t(E(n)[0],n[1]):t(n)}catch(t){var r=e.return;throw void 0!==r&&E(r.call(e)),t}}(o,u,c.value,i))&&l instanceof f)return l;return new f(!1)}).stop=function(e){return new f(!0,e)}}),Ft=ie("iterator"),Bt=!1;try{var zt=0,$t={next:function(){return{done:!!zt++}},return:function(){Bt=!0}};$t[Ft]=function(){return this},Array.from($t,function(){throw 2})}catch(e){}function Vt(s,e,t,a,i){function n(e){var n=d[e];se(d,e,"add"==e?function(e){return n.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(i&&!y(e))&&n.call(this,0===e?0:e)}:"get"==e?function(e){return i&&!y(e)?void 0:n.call(this,0===e?0:e)}:"has"==e?function(e){return!(i&&!y(e))&&n.call(this,0===e?0:e)}:function(e,t){return n.call(this,0===e?0:e,t),this})}var r,o,l,c,u,f=x[s],d=f&&f.prototype,h=f,p=a?"set":"add",g={};return Ce(s,"function"!=typeof f||!(i||d.forEach&&!m(function(){(new f).entries().next()})))?(h=t.getConstructor(e,s,a,p),Rt.REQUIRED=!0):Ce(s,!0)&&(o=(r=new h)[p](i?{}:-0,1)!=r,l=m(function(){r.has(1)}),c=function(){if(!Bt)return!1;var e=!1;try{var t={};t[Ft]=function(){return{next:function(){return{done:e=!0}}}},new f(t)}catch(e){}return e}(),u=!i&&m(function(){for(var e=new f,t=5;t--;)e[p](t,t);return!e.has(-0)}),c||(((h=e(function(e,t){It(e,h,s);var n,i,r,o=(o=new f,n=e,e=h,ct&&"function"==typeof(i=n.constructor)&&i!==e&&y(r=i.prototype)&&r!==e.prototype&&ct(o,r),o);return null!=t&&Wt(t,o[p],o,a),o})).prototype=d).constructor=h),(l||u)&&(n("delete"),n("has"),a&&n("get")),(u||o)&&n(p),i&&d.clear&&delete d.clear),Z({global:!0,forced:(g[s]=h)!=f},g),it(h,s),i||t.setStrong(h,s,a),h}function Ut(e){return e.frozen||(e.frozen=new en)}function Yt(e,t){return Kt(e.entries,function(e){return e[0]===t})}var Qt=Rt.getWeakData,Xt=oe.set,Gt=oe.getterFor,Kt=Q.find,Jt=Q.findIndex,Zt=0,en=function(){this.entries=[]};en.prototype={get:function(e){if(e=Yt(this,e))return e[1]},has:function(e){return!!Yt(this,e)},set:function(e,t){var n=Yt(this,e);n?n[1]=t:this.entries.push([e,t])},delete:function(t){var e=Jt(this.entries,function(e){return e[0]===t});return~e&&this.entries.splice(e,1),!!~e}};var tn,nn={getConstructor:function(e,n,i,r){function o(e,t,n){var i=a(e),r=Qt(E(t),!0);return!0===r?Ut(i).set(t,n):r[i.id]=n,e}var s=e(function(e,t){It(e,s,n),Xt(e,{type:n,id:Zt++,frozen:void 0}),null!=t&&Wt(t,e[r],e,i)}),a=Gt(n);return Lt(s.prototype,{delete:function(e){var t=a(this);if(!y(e))return!1;var n=Qt(e);return!0===n?Ut(t).delete(e):n&&c(n,t.id)&&delete n[t.id]},has:function(e){var t=a(this);if(!y(e))return!1;var n=Qt(e);return!0===n?Ut(t).has(e):n&&c(n,t.id)}}),Lt(s.prototype,i?{get:function(e){var t=a(this);if(y(e)){var n=Qt(e);return!0===n?Ut(t).get(e):n?n[t.id]:void 0}},set:function(e,t){return o(this,e,t)}}:{add:function(e){return o(this,e,!0)}}),s}},rn=(t(function(e){function t(t){return function(e){return t(this,arguments.length?e:void 0)}}var i,n,r,o,s,a=oe.enforce,l=!x.ActiveXObject&&"ActiveXObject"in x,c=Object.isExtensible,e=e.exports=Vt("WeakMap",t,nn,!0,!0);B&&l&&(i=nn.getConstructor(t,"WeakMap",!0),Rt.REQUIRED=!0,e=e.prototype,n=e.delete,r=e.has,o=e.get,s=e.set,Lt(e,{delete:function(e){if(!y(e)||c(e))return n.call(this,e);var t=a(this);return t.frozen||(t.frozen=new i),n.call(this,e)||t.frozen.delete(e)},has:function(e){if(!y(e)||c(e))return r.call(this,e);var t=a(this);return t.frozen||(t.frozen=new i),r.call(this,e)||t.frozen.has(e)},get:function(e){if(!y(e)||c(e))return o.call(this,e);var t=a(this);return t.frozen||(t.frozen=new i),r.call(this,e)?o.call(this,e):t.frozen.get(e)},set:function(e,t){var n;return y(e)&&!c(e)?((n=a(this)).frozen||(n.frozen=new i),r.call(this,e)?s.call(this,e,t):n.frozen.set(e,t)):s.call(this,e,t),this}}))}),ie("iterator")),on=ie("toStringTag"),sn=bt.values;for(tn in Re){var an=x[tn],ln=an&&an.prototype;if(ln){if(ln[rn]!==sn)try{H(ln,rn,sn)}catch(e){ln[rn]=sn}if(ln[on]||H(ln,on,tn),Re[tn])for(var cn in bt)if(ln[cn]!==bt[cn])try{H(ln,cn,bt[cn])}catch(e){ln[cn]=bt[cn]}}}function un(){return bn.Date.now()}var fn="Expected a function",dn=NaN,hn="[object Symbol]",pn=/^\s+|\s+$/g,gn=/^[-+]0x[0-9a-f]+$/i,mn=/^0b[01]+$/i,vn=/^0o[0-7]+$/i,yn=parseInt,U="object"==typeof e&&e&&e.Object===Object&&e,C="object"==typeof self&&self&&self.Object===Object&&self,bn=U||C||Function("return this")(),_n=Object.prototype.toString,wn=Math.max,En=Math.min;function xn(e){var t=typeof e;return e&&("object"==t||"function"==t)}function Tn(e){if("number"==typeof e)return e;if("symbol"==typeof e||e&&"object"==typeof e&&_n.call(e)==hn)return dn;if(xn(e)&&(e=xn(t="function"==typeof e.valueOf?e.valueOf():e)?t+"":t),"string"!=typeof e)return 0===e?e:+e;e=e.replace(pn,"");var t=mn.test(e);return t||vn.test(e)?yn(e.slice(2),t?2:8):gn.test(e)?dn:+e}function Cn(){return jn.Date.now()}var Sn=function(e,t,n){var i=!0,r=!0;if("function"!=typeof e)throw new TypeError(fn);return xn(n)&&(i="leading"in n?!!n.leading:i,r="trailing"in n?!!n.trailing:r),function(i,n,e){var r,o,s,a,l,c,u=0,f=!1,d=!1,t=!0;if("function"!=typeof i)throw new TypeError(fn);function h(e){var t=r,n=o;return r=o=void 0,u=e,a=i.apply(n,t)}function p(e){var t=e-c;return void 0===c||n<=t||t<0||d&&s<=e-u}function g(){var e,t=un();if(p(t))return m(t);l=setTimeout(g,(t=n-((e=t)-c),d?En(t,s-(e-u)):t))}function m(e){return l=void 0,t&&r?h(e):(r=o=void 0,a)}function v(){var e=un(),t=p(e);if(r=arguments,o=this,c=e,t){if(void 0===l)return u=t=c,l=setTimeout(g,n),f?h(t):a;if(d)return l=setTimeout(g,n),h(c)}return void 0===l&&(l=setTimeout(g,n)),a}return n=Tn(n)||0,xn(e)&&(f=!!e.leading,d="maxWait"in e,s=d?wn(Tn(e.maxWait)||0,n):s,t="trailing"in e?!!e.trailing:t),v.cancel=function(){void 0!==l&&clearTimeout(l),r=c=o=l=void(u=0)},v.flush=function(){return void 0===l?a:m(un())},v}(e,t,{leading:i,maxWait:t,trailing:r})},An=/^\s+|\s+$/g,kn=/^[-+]0x[0-9a-f]+$/i,On=/^0b[01]+$/i,Nn=/^0o[0-7]+$/i,Dn=parseInt,gt="object"==typeof e&&e&&e.Object===Object&&e,Q="object"==typeof self&&self&&self.Object===Object&&self,jn=gt||Q||Function("return this")(),Ln=Object.prototype.toString,In=Math.max,Mn=Math.min;function Rn(e){var t=typeof e;return e&&("object"==t||"function"==t)}function Pn(e){if("number"==typeof e)return e;if("symbol"==typeof e||e&&"object"==typeof e&&"[object Symbol]"==Ln.call(e))return NaN;if(Rn(e)&&(e=Rn(t="function"==typeof e.valueOf?e.valueOf():e)?t+"":t),"string"!=typeof e)return 0===e?e:+e;e=e.replace(An,"");var t=On.test(e);return t||Nn.test(e)?Dn(e.slice(2),t?2:8):kn.test(e)?NaN:+e}var qn=function(i,n,e){var r,o,s,a,l,c,u=0,f=!1,d=!1,t=!0;if("function"!=typeof i)throw new TypeError("Expected a function");function h(e){var t=r,n=o;return r=o=void 0,u=e,a=i.apply(n,t)}function p(e){var t=e-c;return void 0===c||n<=t||t<0||d&&s<=e-u}function g(){var e,t=Cn();if(p(t))return m(t);l=setTimeout(g,(t=n-((e=t)-c),d?Mn(t,s-(e-u)):t))}function m(e){return l=void 0,t&&r?h(e):(r=o=void 0,a)}function v(){var e=Cn(),t=p(e);if(r=arguments,o=this,c=e,t){if(void 0===l)return u=t=c,l=setTimeout(g,n),f?h(t):a;if(d)return l=setTimeout(g,n),h(c)}return void 0===l&&(l=setTimeout(g,n)),a}return n=Pn(n)||0,Rn(e)&&(f=!!e.leading,d="maxWait"in e,s=d?In(Pn(e.maxWait)||0,n):s,t="trailing"in e?!!e.trailing:t),v.cancel=function(){void 0!==l&&clearTimeout(l),r=c=o=l=void(u=0)},v.flush=function(){return void 0===l?a:m(Cn())},v},Hn="Expected a function",Wn="__lodash_hash_undefined__",Fn="[object Function]",Bn="[object GeneratorFunction]",zn=/^\[object .+?Constructor\]$/,U="object"==typeof e&&e&&e.Object===Object&&e,C="object"==typeof self&&self&&self.Object===Object&&self,gt=U||C||Function("return this")(),Q=Array.prototype,e=Function.prototype,U=Object.prototype,C=gt["__core-js_shared__"],$n=(C=/[^.]+$/.exec(C&&C.keys&&C.keys.IE_PROTO||""))?"Symbol(src)_1."+C:"",Vn=e.toString,Un=U.hasOwnProperty,Yn=U.toString,Qn=RegExp("^"+Vn.call(Un).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Xn=Q.splice,Gn=ii(gt,"Map"),Kn=ii(Object,"create");function Jn(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function Zn(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function ei(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var i=e[t];this.set(i[0],i[1])}}function ti(e,t){for(var n,i=e.length;i--;)if((n=e[i][0])===t||n!=n&&t!=t)return i;return-1}function ni(e,t){var n=e.__data__;return("string"==(e=typeof t)||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t)?n["string"==typeof t?"string":"hash"]:n.map}function ii(e,t){return t=null==e?void 0:e[t],!oi(n=t)||$n&&$n in n||!((e=oi(e=n)?Yn.call(e):"")==Fn||e==Bn||function(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}(n)?Qn:zn).test(function(e){if(null!=e){try{return Vn.call(e)}catch(e){}try{return e+""}catch(e){}}return""}(n))?void 0:t;var n}function ri(i,r){if("function"!=typeof i||r&&"function"!=typeof r)throw new TypeError(Hn);var o=function(){var e=arguments,t=r?r.apply(this,e):e[0],n=o.cache;return n.has(t)?n.get(t):(e=i.apply(this,e),o.cache=n.set(t,e),e)};return o.cache=new(ri.Cache||ei),o}function oi(e){var t=typeof e;return e&&("object"==t||"function"==t)}Jn.prototype.clear=function(){this.__data__=Kn?Kn(null):{}},Jn.prototype.delete=function(e){return this.has(e)&&delete this.__data__[e]},Jn.prototype.get=function(e){var t=this.__data__;if(Kn){var n=t[e];return n===Wn?void 0:n}return Un.call(t,e)?t[e]:void 0},Jn.prototype.has=function(e){var t=this.__data__;return Kn?void 0!==t[e]:Un.call(t,e)},Jn.prototype.set=function(e,t){return this.__data__[e]=Kn&&void 0===t?Wn:t,this},Zn.prototype.clear=function(){this.__data__=[]},Zn.prototype.delete=function(e){var t=this.__data__;return!((e=ti(t,e))<0||(e==t.length-1?t.pop():Xn.call(t,e,1),0))},Zn.prototype.get=function(e){var t=this.__data__;return(e=ti(t,e))<0?void 0:t[e][1]},Zn.prototype.has=function(e){return-1<ti(this.__data__,e)},Zn.prototype.set=function(e,t){var n=this.__data__,i=ti(n,e);return i<0?n.push([e,t]):n[i][1]=t,this},ei.prototype.clear=function(){this.__data__={hash:new Jn,map:new(Gn||Zn),string:new Jn}},ei.prototype.delete=function(e){return ni(this,e).delete(e)},ei.prototype.get=function(e){return ni(this,e).get(e)},ei.prototype.has=function(e){return ni(this,e).has(e)},ei.prototype.set=function(e,t){return ni(this,e).set(e,t),this},ri.Cache=ei;var si=ri,ai="undefined"!=typeof Map?Map:(Object.defineProperty(ci.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),ci.prototype.get=function(e){return e=li(this.__entries__,e),(e=this.__entries__[e])&&e[1]},ci.prototype.set=function(e,t){var n=li(this.__entries__,e);~n?this.__entries__[n][1]=t:this.__entries__.push([e,t])},ci.prototype.delete=function(e){var t=this.__entries__;~(e=li(t,e))&&t.splice(e,1)},ci.prototype.has=function(e){return!!~li(this.__entries__,e)},ci.prototype.clear=function(){this.__entries__.splice(0)},ci.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,i=this.__entries__;n<i.length;n++){var r=i[n];e.call(t,r[1],r[0])}},ci);function li(e,n){var i=-1;return e.some(function(e,t){return e[0]===n&&(i=t,!0)}),i}function ci(){this.__entries__=[]}var ui="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,fi="undefined"!=typeof global&&global.Math===Math?global:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),di="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(fi):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)},hi=["top","right","bottom","left","width","height","size","weight"],pi="undefined"!=typeof MutationObserver,gi=(mi.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},mi.prototype.removeObserver=function(e){var t=this.observers_;~(e=t.indexOf(e))&&t.splice(e,1),!t.length&&this.connected_&&this.disconnect_()},mi.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},mi.prototype.updateObservers_=function(){var e=this.observers_.filter(function(e){return e.gatherActive(),e.hasActive()});return e.forEach(function(e){return e.broadcastActive()}),0<e.length},mi.prototype.connect_=function(){ui&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),pi?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},mi.prototype.disconnect_=function(){ui&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},mi.prototype.onTransitionEnd_=function(e){var t=void 0===(e=e.propertyName)?"":e;hi.some(function(e){return!!~t.indexOf(e)})&&this.refresh()},mi.getInstance=function(){return this.instance_||(this.instance_=new mi),this.instance_},mi.instance_=null,mi);function mi(){function e(){o&&(o=!1,i()),s&&n()}function t(){di(e)}function n(){var e=Date.now();if(o){if(e-a<2)return;s=!0}else s=!(o=!0),setTimeout(t,r);a=e}var i,r,o,s,a;this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=(i=this.refresh.bind(this),s=o=!(r=20),a=0,n)}function vi(e,t){for(var n=0,i=Object.keys(t);n<i.length;n++){var r=i[n];Object.defineProperty(e,r,{value:t[r],enumerable:!1,writable:!1,configurable:!0})}return e}var yi=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||fi},bi=Ti(0,0,0,0);function _i(e){return parseFloat(e)||0}function wi(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return e.reduce(function(e,t){return e+_i(n["border-"+t+"-width"])},0)}var Ei="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof yi(e).SVGGraphicsElement}:function(e){return e instanceof yi(e).SVGElement&&"function"==typeof e.getBBox};function xi(e){return ui?Ei(e)?Ti(0,0,(t=(t=e).getBBox()).width,t.height):function(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return bi;var i=yi(e).getComputedStyle(e),r=function(e){for(var t={},n=0,i=["top","right","bottom","left"];n<i.length;n++){var r=i[n],o=e["padding-"+r];t[r]=_i(o)}return t}(i),o=r.left+r.right,s=r.top+r.bottom,a=_i(i.width),l=_i(i.height);return"border-box"===i.boxSizing&&(Math.round(a+o)!==t&&(a-=wi(i,"left","right")+o),Math.round(l+s)!==n&&(l-=wi(i,"top","bottom")+s)),e!==yi(e).document.documentElement&&(t=Math.round(a+o)-t,n=Math.round(l+s)-n,1!==Math.abs(t)&&(a-=t),1!==Math.abs(n)&&(l-=n)),Ti(r.left,r.top,a,l)}(e):bi;var t}function Ti(e,t,n,i){return{x:e,y:t,width:n,height:i}}var Ci=(Si.prototype.isActive=function(){var e=xi(this.target);return(this.contentRect_=e).width!==this.broadcastWidth||e.height!==this.broadcastHeight},Si.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},Si);function Si(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Ti(0,0,0,0),this.target=e}function Ai(e,t){var n,i,r,o=(n=(o=t).x,i=o.y,r=o.width,t=o.height,o="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,o=Object.create(o.prototype),vi(o,{x:n,y:i,width:r,height:t,top:i,right:n+r,bottom:t+i,left:n}),o);vi(this,{target:e,contentRect:o})}var ki=(Oi.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof yi(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new Ci(e)),this.controller_.addObserver(this),this.controller_.refresh())}},Oi.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof yi(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},Oi.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},Oi.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(e){e.isActive()&&t.activeObservations_.push(e)})},Oi.prototype.broadcastActive=function(){var e,t;this.hasActive()&&(e=this.callbackCtx_,t=this.activeObservations_.map(function(e){return new Ai(e.target,e.broadcastRect())}),this.callback_.call(e,t,e),this.clearActive())},Oi.prototype.clearActive=function(){this.activeObservations_.splice(0)},Oi.prototype.hasActive=function(){return 0<this.activeObservations_.length},Oi);function Oi(e,t,n){if(this.activeObservations_=[],this.observations_=new ai,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}function Ni(e){if(!(this instanceof Ni))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var t=gi.getInstance(),t=new ki(e,t,this);Di.set(this,t)}var Di=new("undefined"!=typeof WeakMap?WeakMap:ai);["observe","unobserve","disconnect"].forEach(function(t){Ni.prototype[t]=function(){var e;return(e=Di.get(this))[t].apply(e,arguments)}});var ji=void 0!==fi.ResizeObserver?fi.ResizeObserver:Ni,Li=null,Ii=null;function Mi(){if(null===Li){if("undefined"==typeof document)return Li=0;var e=document.body,t=document.createElement("div");t.classList.add("simplebar-hide-scrollbar"),e.appendChild(t);var n=t.getBoundingClientRect().right;e.removeChild(t),Li=n}return Li}He&&window.addEventListener("resize",function(){Ii!==window.devicePixelRatio&&(Ii=window.devicePixelRatio,Li=null)});var Ri=[(Q=function(c){return function(e,t,n,i){ee(t);var r=ne(e),o=N(r),s=K(r.length),a=c?s-1:0,l=c?-1:1;if(n<2)for(;;){if(a in o){i=o[a],a+=l;break}if(a+=l,c?a<0:s<=a)throw TypeError("Reduce of empty array with no initial value")}for(;c?0<=a:a<s;a+=l)a in o&&(i=t(i,o[a],a,r));return i}})(!1),Q(!0)][0];Z({target:"Array",proto:!0,forced:w("reduce")},{reduce:function(e,t){return Ri(this,e,arguments.length,1<arguments.length?t:void 0)}});var gt=q.f,Pi=(Q=Function.prototype).toString,qi=/^\s*function ([^ (]*)/;!T||"name"in Q||gt(Q,"name",{configurable:!0,get:function(){try{return Pi.call(this).match(qi)[1]}catch(e){return""}}});var Hi=RegExp.prototype.exec,Wi=String.prototype.replace,w=Hi,Fi=(gt=/a/,Q=/b*/g,Hi.call(gt,"a"),Hi.call(Q,"a"),0!==gt.lastIndex||0!==Q.lastIndex),Bi=void 0!==/()??/.exec("")[1];(Fi||Bi)&&(w=function(e){var t,n,i,r;return Bi&&(n=new RegExp("^"+this.source+"$(?!\\s)",function(){var e=E(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}.call(this))),Fi&&(t=this.lastIndex),i=Hi.call(this,e),Fi&&i&&(this.lastIndex=this.global?i.index+i[0].length:t),Bi&&i&&1<i.length&&Wi.call(i[0],n,function(){for(r=1;r<arguments.length-2;r++)void 0===arguments[r]&&(i[r]=void 0)}),i});var zi=w;function $i(e,t,n){return t+(n?Xi(e,t).length:1)}function Vi(e,t){var n=e.exec;if("function"==typeof n){if("object"!=typeof(n=n.call(e,t)))throw TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==i(e))throw TypeError("RegExp#exec called on incompatible receiver");return zi.call(e,t)}Z({target:"RegExp",proto:!0,forced:/./.exec!==zi},{exec:zi});var Ui=ie("species"),Yi=!m(function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}),Qi=!m(function(){var e=/(?:)/,t=e.exec;return e.exec=function(){return t.apply(this,arguments)},2!==(e="ab".split(e)).length||"a"!==e[0]||"b"!==e[1]}),w=function(n,e,t,i){var o,r,s=ie(n),a=!m(function(){var e={};return e[s]=function(){return 7},7!=""[n](e)}),l=a&&!m(function(){var e=!1,t=/a/;return t.exec=function(){return e=!0,null},"split"===n&&(t.constructor={},t.constructor[Ui]=function(){return t}),t[s](""),!e});a&&l&&("replace"!==n||Yi)&&("split"!==n||Qi)||(o=/./[s],t=(l=t(s,""[n],function(e,t,n,i,r){return t.exec===zi?a&&!r?{done:!0,value:o.call(t,n,i)}:{done:!0,value:e.call(n,t,i)}:{done:!1}}))[0],r=l[1],se(String.prototype,n,t),se(RegExp.prototype,s,2==e?function(e,t){return r.call(e,this,t)}:function(e){return r.call(e,this)}),i&&H(RegExp.prototype[s],"sham",!0))},Xi=le.charAt;w("match",1,function(i,l,c){return[function(e){var t=s(this),n=null==e?void 0:e[i];return void 0!==n?n.call(e,t):new RegExp(e)[i](String(t))},function(e){var t=c(l,e,this);if(t.done)return t.value;var n=E(e),i=String(this);if(!n.global)return Vi(n,i);for(var r=n.unicode,o=[],s=n.lastIndex=0;null!==(a=Vi(n,i));){var a=String(a[0]);""===(o[s]=a)&&(n.lastIndex=$i(i,K(n.lastIndex),r)),s++}return 0===s?null:o}]});var Gi=Math.max,Ki=Math.min,Ji=Math.floor,Zi=/\$([$&'`]|\d\d?|<[^>]*>)/g,er=/\$([$&'`]|\d\d?)/g;function tr(e){return Array.prototype.reduce.call(e,function(e,t){var n=t.name.match(/data-simplebar-(.+)/);if(n)switch(n=n[1].replace(/\W+(.)/g,function(e,t){return t.toUpperCase()}),t.value){case"true":e[n]=!0;break;case"false":e[n]=!1;break;case void 0:e[n]=!0;break;default:e[n]=t.value}return e},{})}function nr(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window}function ir(e){return e&&e.ownerDocument?e.ownerDocument:document}w("replace",2,function(r,_,w){return[function(e,t){var n=s(this),i=null==e?void 0:e[r];return void 0!==i?i.call(e,n,t):_.call(String(n),e,t)},function(e,t){var n=w(_,e,this,t);if(n.done)return n.value;var i=E(e),r=String(this),o="function"==typeof t;o||(t=String(t));var s,a=i.global;a&&(s=i.unicode,i.lastIndex=0);for(var l=[];;){var c=Vi(i,r);if(null===c)break;if(l.push(c),!a)break;""===String(c[0])&&(i.lastIndex=$i(r,K(i.lastIndex),s))}for(var u,f="",d=0,h=0;h<l.length;h++){c=l[h];for(var p=String(c[0]),g=Gi(Ki(G(c.index),r.length),0),m=[],v=1;v<c.length;v++)m.push(void 0===(u=c[v])?u:String(u));var y,b=c.groups,b=o?(y=[p].concat(m,g,r),void 0!==b&&y.push(b),String(t.apply(void 0,y))):function(o,s,a,l,c,e){var u=a+o.length,f=l.length,t=er;return void 0!==c&&(c=ne(c),t=Zi),_.call(e,t,function(e,t){var n;switch(t.charAt(0)){case"$":return"$";case"&":return o;case"`":return s.slice(0,a);case"'":return s.slice(u);case"<":n=c[t.slice(1,-1)];break;default:var i=+t;if(0==i)return e;if(f<i){var r=Ji(i/10);return 0!==r&&r<=f?void 0===l[r-1]?t.charAt(1):l[r-1]+t.charAt(1):e}n=l[i-1]}return void 0===n?"":n})}(p,r,g,m,b,t);d<=g&&(f+=r.slice(d,g)+b,d=g+p.length)}return f+r.slice(d)}]});var rr=function(){function a(e,t){var s=this;this.onScroll=function(){var e=nr(s.el);s.scrollXTicking||(e.requestAnimationFrame(s.scrollX),s.scrollXTicking=!0),s.scrollYTicking||(e.requestAnimationFrame(s.scrollY),s.scrollYTicking=!0)},this.scrollX=function(){s.axis.x.isOverflowing&&(s.showScrollbar("x"),s.positionScrollbar("x")),s.scrollXTicking=!1},this.scrollY=function(){s.axis.y.isOverflowing&&(s.showScrollbar("y"),s.positionScrollbar("y")),s.scrollYTicking=!1},this.onMouseEnter=function(){s.showScrollbar("x"),s.showScrollbar("y")},this.onMouseMove=function(e){s.mouseX=e.clientX,s.mouseY=e.clientY,(s.axis.x.isOverflowing||s.axis.x.forceVisible)&&s.onMouseMoveForAxis("x"),(s.axis.y.isOverflowing||s.axis.y.forceVisible)&&s.onMouseMoveForAxis("y")},this.onMouseLeave=function(){s.onMouseMove.cancel(),(s.axis.x.isOverflowing||s.axis.x.forceVisible)&&s.onMouseLeaveForAxis("x"),(s.axis.y.isOverflowing||s.axis.y.forceVisible)&&s.onMouseLeaveForAxis("y"),s.mouseX=-1,s.mouseY=-1},this.onWindowResize=function(){s.scrollbarWidth=s.getScrollbarWidth(),s.hideNativeScrollbar()},this.hideScrollbars=function(){s.axis.x.track.rect=s.axis.x.track.el.getBoundingClientRect(),s.axis.y.track.rect=s.axis.y.track.el.getBoundingClientRect(),s.isWithinBounds(s.axis.y.track.rect)||(s.axis.y.scrollbar.el.classList.remove(s.classNames.visible),s.axis.y.isVisible=!1),s.isWithinBounds(s.axis.x.track.rect)||(s.axis.x.scrollbar.el.classList.remove(s.classNames.visible),s.axis.x.isVisible=!1)},this.onPointerEvent=function(e){var t,n;s.axis.x.track.rect=s.axis.x.track.el.getBoundingClientRect(),s.axis.y.track.rect=s.axis.y.track.el.getBoundingClientRect(),(s.axis.x.isOverflowing||s.axis.x.forceVisible)&&(t=s.isWithinBounds(s.axis.x.track.rect)),(s.axis.y.isOverflowing||s.axis.y.forceVisible)&&(n=s.isWithinBounds(s.axis.y.track.rect)),(t||n)&&(e.preventDefault(),e.stopPropagation(),"mousedown"===e.type&&(t&&(s.axis.x.scrollbar.rect=s.axis.x.scrollbar.el.getBoundingClientRect(),s.isWithinBounds(s.axis.x.scrollbar.rect)?s.onDragStart(e,"x"):s.onTrackClick(e,"x")),n&&(s.axis.y.scrollbar.rect=s.axis.y.scrollbar.el.getBoundingClientRect(),s.isWithinBounds(s.axis.y.scrollbar.rect)?s.onDragStart(e,"y"):s.onTrackClick(e,"y"))))},this.drag=function(e){var t=s.axis[s.draggedAxis].track,n=t.rect[s.axis[s.draggedAxis].sizeAttr],i=s.axis[s.draggedAxis].scrollbar,r=s.contentWrapperEl[s.axis[s.draggedAxis].scrollSizeAttr],o=parseInt(s.elStyles[s.axis[s.draggedAxis].sizeAttr],10);e.preventDefault(),e.stopPropagation(),o=(("y"===s.draggedAxis?e.pageY:e.pageX)-t.rect[s.axis[s.draggedAxis].offsetAttr]-s.axis[s.draggedAxis].dragOffset)/(n-i.size)*(r-o),"x"===s.draggedAxis&&(o=s.isRtl&&a.getRtlHelpers().isRtlScrollbarInverted?o-(n+i.size):o,o=s.isRtl&&a.getRtlHelpers().isRtlScrollingInverted?-o:o),s.contentWrapperEl[s.axis[s.draggedAxis].scrollOffsetAttr]=o},this.onEndDrag=function(e){var t=ir(s.el),n=nr(s.el);e.preventDefault(),e.stopPropagation(),s.el.classList.remove(s.classNames.dragging),t.removeEventListener("mousemove",s.drag,!0),t.removeEventListener("mouseup",s.onEndDrag,!0),s.removePreventClickId=n.setTimeout(function(){t.removeEventListener("click",s.preventClick,!0),t.removeEventListener("dblclick",s.preventClick,!0),s.removePreventClickId=null})},this.preventClick=function(e){e.preventDefault(),e.stopPropagation()},this.el=e,this.minScrollbarWidth=20,this.options=Object.assign({},a.defaultOptions,{},t),this.classNames=Object.assign({},a.defaultOptions.classNames,{},this.options.classNames),this.axis={x:{scrollOffsetAttr:"scrollLeft",sizeAttr:"width",scrollSizeAttr:"scrollWidth",offsetSizeAttr:"offsetWidth",offsetAttr:"left",overflowAttr:"overflowX",dragOffset:0,isOverflowing:!0,isVisible:!1,forceVisible:!1,track:{},scrollbar:{}},y:{scrollOffsetAttr:"scrollTop",sizeAttr:"height",scrollSizeAttr:"scrollHeight",offsetSizeAttr:"offsetHeight",offsetAttr:"top",overflowAttr:"overflowY",dragOffset:0,isOverflowing:!0,isVisible:!1,forceVisible:!1,track:{},scrollbar:{}}},this.removePreventClickId=null,a.instances.has(this.el)||(this.recalculate=Sn(this.recalculate.bind(this),64),this.onMouseMove=Sn(this.onMouseMove.bind(this),64),this.hideScrollbars=qn(this.hideScrollbars.bind(this),this.options.timeout),this.onWindowResize=qn(this.onWindowResize.bind(this),64,{leading:!0}),a.getRtlHelpers=si(a.getRtlHelpers),this.init())}a.getRtlHelpers=function(){(i=document.createElement("div")).innerHTML='<div class="hs-dummy-scrollbar-size"><div style="height: 200%; width: 200%; margin: 10px 0;"></div></div>';var e=i.firstElementChild;document.body.appendChild(e);var t=e.firstElementChild;e.scrollLeft=0;var n=a.getOffset(e),i=a.getOffset(t);return e.scrollLeft=999,t=a.getOffset(t),{isRtlScrollingInverted:n.left!==i.left&&i.left-t.left!=0,isRtlScrollbarInverted:n.left!==i.left}},a.getOffset=function(e){var t=e.getBoundingClientRect(),n=ir(e),e=nr(e);return{top:t.top+(e.pageYOffset||n.documentElement.scrollTop),left:t.left+(e.pageXOffset||n.documentElement.scrollLeft)}};var e=a.prototype;return e.init=function(){a.instances.set(this.el,this),He&&(this.initDOM(),this.scrollbarWidth=this.getScrollbarWidth(),this.recalculate(),this.initListeners())},e.initDOM=function(){var e,t,n=this;if(Array.prototype.filter.call(this.el.children,function(e){return e.classList.contains(n.classNames.wrapper)}).length)this.wrapperEl=this.el.querySelector("."+this.classNames.wrapper),this.contentWrapperEl=this.options.scrollableNode||this.el.querySelector("."+this.classNames.contentWrapper),this.contentEl=this.options.contentNode||this.el.querySelector("."+this.classNames.contentEl),this.offsetEl=this.el.querySelector("."+this.classNames.offset),this.maskEl=this.el.querySelector("."+this.classNames.mask),this.placeholderEl=this.findChild(this.wrapperEl,"."+this.classNames.placeholder),this.heightAutoObserverWrapperEl=this.el.querySelector("."+this.classNames.heightAutoObserverWrapperEl),this.heightAutoObserverEl=this.el.querySelector("."+this.classNames.heightAutoObserverEl),this.axis.x.track.el=this.findChild(this.el,"."+this.classNames.track+"."+this.classNames.horizontal),this.axis.y.track.el=this.findChild(this.el,"."+this.classNames.track+"."+this.classNames.vertical);else{for(this.wrapperEl=document.createElement("div"),this.contentWrapperEl=document.createElement("div"),this.offsetEl=document.createElement("div"),this.maskEl=document.createElement("div"),this.contentEl=document.createElement("div"),this.placeholderEl=document.createElement("div"),this.heightAutoObserverWrapperEl=document.createElement("div"),this.heightAutoObserverEl=document.createElement("div"),this.wrapperEl.classList.add(this.classNames.wrapper),this.contentWrapperEl.classList.add(this.classNames.contentWrapper),this.offsetEl.classList.add(this.classNames.offset),this.maskEl.classList.add(this.classNames.mask),this.contentEl.classList.add(this.classNames.contentEl),this.placeholderEl.classList.add(this.classNames.placeholder),this.heightAutoObserverWrapperEl.classList.add(this.classNames.heightAutoObserverWrapperEl),this.heightAutoObserverEl.classList.add(this.classNames.heightAutoObserverEl);this.el.firstChild;)this.contentEl.appendChild(this.el.firstChild);this.contentWrapperEl.appendChild(this.contentEl),this.offsetEl.appendChild(this.contentWrapperEl),this.maskEl.appendChild(this.offsetEl),this.heightAutoObserverWrapperEl.appendChild(this.heightAutoObserverEl),this.wrapperEl.appendChild(this.heightAutoObserverWrapperEl),this.wrapperEl.appendChild(this.maskEl),this.wrapperEl.appendChild(this.placeholderEl),this.el.appendChild(this.wrapperEl)}this.axis.x.track.el&&this.axis.y.track.el||(e=document.createElement("div"),t=document.createElement("div"),e.classList.add(this.classNames.track),t.classList.add(this.classNames.scrollbar),e.appendChild(t),this.axis.x.track.el=e.cloneNode(!0),this.axis.x.track.el.classList.add(this.classNames.horizontal),this.axis.y.track.el=e.cloneNode(!0),this.axis.y.track.el.classList.add(this.classNames.vertical),this.el.appendChild(this.axis.x.track.el),this.el.appendChild(this.axis.y.track.el)),this.axis.x.scrollbar.el=this.axis.x.track.el.querySelector("."+this.classNames.scrollbar),this.axis.y.scrollbar.el=this.axis.y.track.el.querySelector("."+this.classNames.scrollbar),this.options.autoHide||(this.axis.x.scrollbar.el.classList.add(this.classNames.visible),this.axis.y.scrollbar.el.classList.add(this.classNames.visible)),this.el.setAttribute("data-simplebar","init")},e.initListeners=function(){var t=this,e=nr(this.el);this.options.autoHide&&this.el.addEventListener("mouseenter",this.onMouseEnter),["mousedown","click","dblclick"].forEach(function(e){t.el.addEventListener(e,t.onPointerEvent,!0)}),["touchstart","touchend","touchmove"].forEach(function(e){t.el.addEventListener(e,t.onPointerEvent,{capture:!0,passive:!0})}),this.el.addEventListener("mousemove",this.onMouseMove),this.el.addEventListener("mouseleave",this.onMouseLeave),this.contentWrapperEl.addEventListener("scroll",this.onScroll),e.addEventListener("resize",this.onWindowResize);var n=!1,i=e.ResizeObserver||ji;this.resizeObserver=new i(function(){n&&t.recalculate()}),this.resizeObserver.observe(this.el),this.resizeObserver.observe(this.contentEl),e.requestAnimationFrame(function(){n=!0}),this.mutationObserver=new e.MutationObserver(this.recalculate),this.mutationObserver.observe(this.contentEl,{childList:!0,subtree:!0,characterData:!0})},e.recalculate=function(){var e=nr(this.el);this.elStyles=e.getComputedStyle(this.el),this.isRtl="rtl"===this.elStyles.direction;var t=this.heightAutoObserverEl.offsetHeight<=1,n=this.heightAutoObserverEl.offsetWidth<=1,i=this.contentEl.offsetWidth,r=this.contentWrapperEl.offsetWidth,o=this.elStyles.overflowX,s=this.elStyles.overflowY;this.contentEl.style.padding=this.elStyles.paddingTop+" "+this.elStyles.paddingRight+" "+this.elStyles.paddingBottom+" "+this.elStyles.paddingLeft,this.wrapperEl.style.margin="-"+this.elStyles.paddingTop+" -"+this.elStyles.paddingRight+" -"+this.elStyles.paddingBottom+" -"+this.elStyles.paddingLeft;var a=this.contentEl.scrollHeight,e=this.contentEl.scrollWidth;this.contentWrapperEl.style.height=t?"auto":"100%",this.placeholderEl.style.width=n?i+"px":"auto",this.placeholderEl.style.height=a+"px",n=this.contentWrapperEl.offsetHeight,this.axis.x.isOverflowing=i<e,this.axis.y.isOverflowing=n<a,this.axis.x.isOverflowing="hidden"!==o&&this.axis.x.isOverflowing,this.axis.y.isOverflowing="hidden"!==s&&this.axis.y.isOverflowing,this.axis.x.forceVisible="x"===this.options.forceVisible||!0===this.options.forceVisible,this.axis.y.forceVisible="y"===this.options.forceVisible||!0===this.options.forceVisible,this.hideNativeScrollbar(),o=this.axis.x.isOverflowing?this.scrollbarWidth:0,s=this.axis.y.isOverflowing?this.scrollbarWidth:0,this.axis.x.isOverflowing=this.axis.x.isOverflowing&&r-s<e,this.axis.y.isOverflowing=this.axis.y.isOverflowing&&n-o<a,this.axis.x.scrollbar.size=this.getScrollbarSize("x"),this.axis.y.scrollbar.size=this.getScrollbarSize("y"),this.axis.x.scrollbar.el.style.width=this.axis.x.scrollbar.size+"px",this.axis.y.scrollbar.el.style.height=this.axis.y.scrollbar.size+"px",this.positionScrollbar("x"),this.positionScrollbar("y"),this.toggleTrackVisibility("x"),this.toggleTrackVisibility("y")},e.getScrollbarSize=function(e){if(void 0===e&&(e="y"),!this.axis[e].isOverflowing)return 0;var t=this.contentEl[this.axis[e].scrollSizeAttr],t=(e=this.axis[e].track.el[this.axis[e].offsetSizeAttr])/t,e=Math.max(~~(t*e),this.options.scrollbarMinSize);return this.options.scrollbarMaxSize&&(e=Math.min(e,this.options.scrollbarMaxSize)),e},e.positionScrollbar=function(e){var t,n,i,r,o;void 0===e&&(e="y"),this.axis[e].isOverflowing&&(t=this.contentWrapperEl[this.axis[e].scrollSizeAttr],n=this.axis[e].track.el[this.axis[e].offsetSizeAttr],o=parseInt(this.elStyles[this.axis[e].sizeAttr],10),i=this.axis[e].scrollbar,r=this.contentWrapperEl[this.axis[e].scrollOffsetAttr],o=(r="x"===e&&this.isRtl&&a.getRtlHelpers().isRtlScrollingInverted?-r:r)/(t-o),o=~~((n-i.size)*o),o="x"===e&&this.isRtl&&a.getRtlHelpers().isRtlScrollbarInverted?o+(n-i.size):o,i.el.style.transform="x"===e?"translate3d("+o+"px, 0, 0)":"translate3d(0, "+o+"px, 0)")},e.toggleTrackVisibility=function(e){void 0===e&&(e="y");var t=this.axis[e].track.el,n=this.axis[e].scrollbar.el;this.axis[e].isOverflowing||this.axis[e].forceVisible?(t.style.visibility="visible",this.contentWrapperEl.style[this.axis[e].overflowAttr]="scroll"):(t.style.visibility="hidden",this.contentWrapperEl.style[this.axis[e].overflowAttr]="hidden"),this.axis[e].isOverflowing?n.style.display="block":n.style.display="none"},e.hideNativeScrollbar=function(){this.offsetEl.style[this.isRtl?"left":"right"]=this.axis.y.isOverflowing||this.axis.y.forceVisible?"-"+this.scrollbarWidth+"px":0,this.offsetEl.style.bottom=this.axis.x.isOverflowing||this.axis.x.forceVisible?"-"+this.scrollbarWidth+"px":0},e.onMouseMoveForAxis=function(e){void 0===e&&(e="y"),this.axis[e].track.rect=this.axis[e].track.el.getBoundingClientRect(),this.axis[e].scrollbar.rect=this.axis[e].scrollbar.el.getBoundingClientRect(),this.isWithinBounds(this.axis[e].scrollbar.rect)?this.axis[e].scrollbar.el.classList.add(this.classNames.hover):this.axis[e].scrollbar.el.classList.remove(this.classNames.hover),this.isWithinBounds(this.axis[e].track.rect)?(this.showScrollbar(e),this.axis[e].track.el.classList.add(this.classNames.hover)):this.axis[e].track.el.classList.remove(this.classNames.hover)},e.onMouseLeaveForAxis=function(e){void 0===e&&(e="y"),this.axis[e].track.el.classList.remove(this.classNames.hover),this.axis[e].scrollbar.el.classList.remove(this.classNames.hover)},e.showScrollbar=function(e){void 0===e&&(e="y");var t=this.axis[e].scrollbar.el;this.axis[e].isVisible||(t.classList.add(this.classNames.visible),this.axis[e].isVisible=!0),this.options.autoHide&&this.hideScrollbars()},e.onDragStart=function(e,t){void 0===t&&(t="y");var n=ir(this.el),i=nr(this.el),r=this.axis[t].scrollbar,e="y"===t?e.pageY:e.pageX;this.axis[t].dragOffset=e-r.rect[this.axis[t].offsetAttr],this.draggedAxis=t,this.el.classList.add(this.classNames.dragging),n.addEventListener("mousemove",this.drag,!0),n.addEventListener("mouseup",this.onEndDrag,!0),null===this.removePreventClickId?(n.addEventListener("click",this.preventClick,!0),n.addEventListener("dblclick",this.preventClick,!0)):(i.clearTimeout(this.removePreventClickId),this.removePreventClickId=null)},e.onTrackClick=function(e,n){var i,t,r,o,s,a,l=this;void 0===n&&(n="y"),this.options.clickOnTrack&&(i=nr(this.el),this.axis[n].scrollbar.rect=this.axis[n].scrollbar.el.getBoundingClientRect(),t=this.axis[n].scrollbar.rect[this.axis[n].offsetAttr],r=parseInt(this.elStyles[this.axis[n].sizeAttr],10),o=this.contentWrapperEl[this.axis[n].scrollOffsetAttr],s=("y"===n?this.mouseY-t:this.mouseX-t)<0?-1:1,a=-1==s?o-r:o+r,function e(){var t;-1==s?a<o&&(o-=l.options.clickOnTrackSpeed,l.contentWrapperEl.scrollTo(((t={})[l.axis[n].offsetAttr]=o,t)),i.requestAnimationFrame(e)):o<a&&(o+=l.options.clickOnTrackSpeed,l.contentWrapperEl.scrollTo(((t={})[l.axis[n].offsetAttr]=o,t)),i.requestAnimationFrame(e))}())},e.getContentElement=function(){return this.contentEl},e.getScrollElement=function(){return this.contentWrapperEl},e.getScrollbarWidth=function(){try{return"none"===getComputedStyle(this.contentWrapperEl,"::-webkit-scrollbar").display||"scrollbarWidth"in document.documentElement.style||"-ms-overflow-style"in document.documentElement.style?0:Mi()}catch(e){return Mi()}},e.removeListeners=function(){var t=this,e=nr(this.el);this.options.autoHide&&this.el.removeEventListener("mouseenter",this.onMouseEnter),["mousedown","click","dblclick"].forEach(function(e){t.el.removeEventListener(e,t.onPointerEvent,!0)}),["touchstart","touchend","touchmove"].forEach(function(e){t.el.removeEventListener(e,t.onPointerEvent,{capture:!0,passive:!0})}),this.el.removeEventListener("mousemove",this.onMouseMove),this.el.removeEventListener("mouseleave",this.onMouseLeave),this.contentWrapperEl&&this.contentWrapperEl.removeEventListener("scroll",this.onScroll),e.removeEventListener("resize",this.onWindowResize),this.mutationObserver&&this.mutationObserver.disconnect(),this.resizeObserver&&this.resizeObserver.disconnect(),this.recalculate.cancel(),this.onMouseMove.cancel(),this.hideScrollbars.cancel(),this.onWindowResize.cancel()},e.unMount=function(){this.removeListeners(),a.instances.delete(this.el)},e.isWithinBounds=function(e){return this.mouseX>=e.left&&this.mouseX<=e.left+e.width&&this.mouseY>=e.top&&this.mouseY<=e.top+e.height},e.findChild=function(e,t){var n=e.matches||e.webkitMatchesSelector||e.mozMatchesSelector||e.msMatchesSelector;return Array.prototype.filter.call(e.children,function(e){return n.call(e,t)})[0]},a}();return rr.defaultOptions={autoHide:!0,forceVisible:!1,clickOnTrack:!0,clickOnTrackSpeed:40,classNames:{contentEl:"simplebar-content",contentWrapper:"simplebar-content-wrapper",offset:"simplebar-offset",mask:"simplebar-mask",wrapper:"simplebar-wrapper",placeholder:"simplebar-placeholder",scrollbar:"simplebar-scrollbar",track:"simplebar-track",heightAutoObserverWrapperEl:"simplebar-height-auto-observer-wrapper",heightAutoObserverEl:"simplebar-height-auto-observer",visible:"simplebar-visible",horizontal:"simplebar-horizontal",vertical:"simplebar-vertical",hover:"simplebar-hover",dragging:"simplebar-dragging"},scrollbarMinSize:25,scrollbarMaxSize:0,timeout:1e3},rr.instances=new WeakMap,rr.initDOMLoadedElements=function(){document.removeEventListener("DOMContentLoaded",this.initDOMLoadedElements),window.removeEventListener("load",this.initDOMLoadedElements),Array.prototype.forEach.call(document.querySelectorAll("[data-simplebar]"),function(e){"init"===e.getAttribute("data-simplebar")||rr.instances.has(e)||new rr(e,tr(e.attributes))})},rr.removeObserver=function(){this.globalObserver.disconnect()},rr.initHtmlApi=function(){this.initDOMLoadedElements=this.initDOMLoadedElements.bind(this),"undefined"!=typeof MutationObserver&&(this.globalObserver=new MutationObserver(rr.handleMutations),this.globalObserver.observe(document,{childList:!0,subtree:!0})),"complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll?window.setTimeout(this.initDOMLoadedElements):(document.addEventListener("DOMContentLoaded",this.initDOMLoadedElements),window.addEventListener("load",this.initDOMLoadedElements))},rr.handleMutations=function(e){e.forEach(function(e){Array.prototype.forEach.call(e.addedNodes,function(e){1===e.nodeType&&(e.hasAttribute("data-simplebar")?rr.instances.has(e)||new rr(e,tr(e.attributes)):Array.prototype.forEach.call(e.querySelectorAll("[data-simplebar]"),function(e){"init"===e.getAttribute("data-simplebar")||rr.instances.has(e)||new rr(e,tr(e.attributes))}))}),Array.prototype.forEach.call(e.removedNodes,function(e){1===e.nodeType&&(e.hasAttribute('[data-simplebar="init"]')?rr.instances.has(e)&&rr.instances.get(e).unMount():Array.prototype.forEach.call(e.querySelectorAll('[data-simplebar="init"]'),function(e){rr.instances.has(e)&&rr.instances.get(e).unMount()}))})})},rr.getOptions=tr,He&&rr.initHtmlApi(),rr}),function(e){"function"==typeof define&&define.amd?define(["jquery"],e):e(jQuery)}(function(a){"use strict";function i(e,t){var n=t.scrollTop(),i=t.prop("scrollHeight"),r=t.prop("clientHeight"),o=e.originalEvent.wheelDelta||-1*e.originalEvent.detail||-1*e.originalEvent.deltaY,s=0;return"wheel"===e.type?(t=t.height()/a(window).height(),s=e.originalEvent.deltaY*t):this.options.touch&&"touchmove"===e.type&&(o=e.originalEvent.changedTouches[0].clientY-this.startClientY),{prevent:(e=0<o&&n+s<=0)||o<0&&i-r<=n+s,top:e,scrollTop:n,deltaY:s}}function r(e,t){var n,i=t.scrollTop(),r={top:!1,bottom:!1};return r.top=0===i&&(e.keyCode===s||e.keyCode===u||e.keyCode===f),r.top||(n=t.prop("scrollHeight"),t=t.prop("clientHeight"),r.bottom=n===i+t&&(e.keyCode===o||e.keyCode===l||e.keyCode===c||e.keyCode===d)),r}var e,o=32,s=33,l=34,c=35,u=36,f=38,d=40,h=function(e,t){this.$element=e,this.options=a.extend({},h.DEFAULTS,this.$element.data(),t),this.enabled=!0,this.startClientY=0,this.options.unblock&&this.$element.on(h.CORE.wheelEventName+h.NAMESPACE,this.options.unblock,a.proxy(h.CORE.unblockHandler,this)),this.$element.on(h.CORE.wheelEventName+h.NAMESPACE,this.options.selector,a.proxy(h.CORE.handler,this)),this.options.touch&&(this.$element.on("touchstart"+h.NAMESPACE,this.options.selector,a.proxy(h.CORE.touchHandler,this)),this.$element.on("touchmove"+h.NAMESPACE,this.options.selector,a.proxy(h.CORE.handler,this)),this.options.unblock&&this.$element.on("touchmove"+h.NAMESPACE,this.options.unblock,a.proxy(h.CORE.unblockHandler,this))),this.options.keyboard&&(this.$element.attr("tabindex",this.options.keyboard.tabindex||0),this.$element.on("keydown"+h.NAMESPACE,this.options.selector,a.proxy(h.CORE.keyboardHandler,this)),this.options.unblock&&this.$element.on("keydown"+h.NAMESPACE,this.options.unblock,a.proxy(h.CORE.unblockHandler,this)))};h.NAME="ScrollLock",h.VERSION="3.1.2",h.NAMESPACE=".scrollLock",h.ANIMATION_NAMESPACE=h.NAMESPACE+".effect",h.DEFAULTS={strict:!1,strictFn:function(e){return e.prop("scrollHeight")>e.prop("clientHeight")},selector:!1,animation:!1,touch:"ontouchstart"in window,keyboard:!1,unblock:!1},h.CORE={wheelEventName:"onwheel"in document.createElement("div")?"wheel":void 0!==document.onmousewheel?"mousewheel":"DOMMouseScroll",animationEventName:["webkitAnimationEnd","mozAnimationEnd","MSAnimationEnd","oanimationend","animationend"].join(h.ANIMATION_NAMESPACE+" ")+h.ANIMATION_NAMESPACE,unblockHandler:function(e){e.__currentTarget=e.currentTarget},handler:function(e){var t,n;this.enabled&&!e.ctrlKey&&(t=a(e.currentTarget),!0===this.options.strict&&!this.options.strictFn(t)||(e.stopPropagation(),n=a.proxy(i,this)(e,t),e.__currentTarget&&(n.prevent&=a.proxy(i,this)(e,a(e.__currentTarget)).prevent),n.prevent&&(e.preventDefault(),n.deltaY&&t.scrollTop(n.scrollTop+n.deltaY),n=n.top?"top":"bottom",this.options.animation&&setTimeout(h.CORE.animationHandler.bind(this,t,n),0),t.trigger(a.Event(n+h.NAMESPACE)))))},touchHandler:function(e){this.startClientY=e.originalEvent.touches[0].clientY},animationHandler:function(e,t){var n=this.options.animation[t],t=this.options.animation.top+" "+this.options.animation.bottom;e.off(h.ANIMATION_NAMESPACE).removeClass(t).addClass(n).one(h.CORE.animationEventName,function(){e.removeClass(n)})},keyboardHandler:function(e){var t=a(e.currentTarget),n=(t.scrollTop(),r(e,t));return e.__currentTarget&&(e=r(e,a(e.__currentTarget)),n.top&=e.top,n.bottom&=e.bottom),n.top?(t.trigger(a.Event("top"+h.NAMESPACE)),this.options.animation&&setTimeout(h.CORE.animationHandler.bind(this,t,"top"),0),!1):n.bottom?(t.trigger(a.Event("bottom"+h.NAMESPACE)),this.options.animation&&setTimeout(h.CORE.animationHandler.bind(this,t,"bottom"),0),!1):void 0}},h.prototype.toggleStrict=function(){this.options.strict=!this.options.strict},h.prototype.enable=function(){this.enabled=!0},h.prototype.disable=function(){this.enabled=!1},h.prototype.destroy=function(){this.disable(),this.$element.off(h.NAMESPACE),this.$element=null,this.options=null},e=a.fn.scrollLock,a.fn.scrollLock=function(i){return this.each(function(){var e=a(this),t="object"==typeof i&&i,n=e.data(h.NAME);!n&&"destroy"===i||(n||e.data(h.NAME,n=new h(e,t)),"string"==typeof i&&n[i]())})},a.fn.scrollLock.defaults=h.DEFAULTS,a.fn.scrollLock.noConflict=function(){return a.fn.scrollLock=e,this}}),function(e,t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?t(require("jquery")):t(e.jQuery)}(this,function(i){i.fn.appear=function(n,e){var d=i.extend({data:void 0,one:!0,accX:0,accY:0},e);return this.each(function(){var u,t,e,f=i(this);f.appeared=!1,n?(u=i(window),t=function(){var e,t,n,i,r,o,s,a,l,c;f.is(":visible")?(e=u.scrollLeft(),t=u.scrollTop(),n=(c=f.offset()).left,i=c.top,r=d.accX,o=d.accY,s=f.height(),a=u.height(),l=f.width(),c=u.width(),t<=i+s+o&&i<=t+a+o&&e<=n+l+r&&n<=e+c+r?f.appeared||f.trigger("appear",d.data):f.appeared=!1):f.appeared=!1},e=function(){var e;f.appeared=!0,d.one&&(u.unbind("scroll",t),0<=(e=i.inArray(t,i.fn.appear.checks))&&i.fn.appear.checks.splice(e,1)),n.apply(this,arguments)},d.one?f.one("appear",d.data,e):f.bind("appear",d.data,e),u.scroll(t),i.fn.appear.checks.push(t),t()):f.trigger("appear",d.data)})},i.extend(i.fn.appear,{checks:[],timeout:null,checkAll:function(){var e=i.fn.appear.checks.length;if(0<e)for(;e--;)i.fn.appear.checks[e]()},run:function(){i.fn.appear.timeout&&clearTimeout(i.fn.appear.timeout),i.fn.appear.timeout=setTimeout(i.fn.appear.checkAll,20)}}),i.each(["append","prepend","after","before","attr","removeAttr","addClass","removeClass","toggleClass","remove","css","show","hide"],function(e,t){var n=i.fn[t];n&&(i.fn[t]=function(){var e=n.apply(this,arguments);return i.fn.appear.run(),e})})}),function(e){var t,n,i;"function"==typeof define&&define.amd&&(define(e),t=!0),"object"==typeof exports&&(module.exports=e(),t=!0),t||(n=window.Cookies,(i=window.Cookies=e()).noConflict=function(){return window.Cookies=n,i})}(function(){function a(){for(var e=0,t={};e<arguments.length;e++){var n,i=arguments[e];for(n in i)t[n]=i[n]}return t}function c(e){return e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent)}return function e(l){function s(){}function n(e,t,n){if("undefined"!=typeof document){"number"==typeof(n=a({path:"/"},s.defaults,n)).expires&&(n.expires=new Date(+new Date+864e5*n.expires)),n.expires=n.expires?n.expires.toUTCString():"";try{var i=JSON.stringify(t);/^[\{\[]/.test(i)&&(t=i)}catch(e){}t=l.write?l.write(t,e):encodeURIComponent(String(t)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent),e=encodeURIComponent(String(e)).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/[\(\)]/g,escape);var r,o="";for(r in n)n[r]&&(o+="; "+r,!0!==n[r]&&(o+="="+n[r].split(";")[0]));return document.cookie=e+"="+t+o}}function t(e,t){if("undefined"!=typeof document){for(var n={},i=document.cookie?document.cookie.split("; "):[],r=0;r<i.length;r++){var o=i[r].split("="),s=o.slice(1).join("=");t||'"'!==s.charAt(0)||(s=s.slice(1,-1));try{var a=c(o[0]),s=(l.read||l)(s,a)||c(s);if(t)try{s=JSON.parse(s)}catch(e){}if(n[a]=s,e===a)break}catch(e){}}return e?n[e]:n}}return s.set=n,s.get=function(e){return t(e,!1)},s.getJSON=function(e){return t(e,!0)},s.remove=function(e,t){n(e,"",a(t,{expires:-1}))},s.defaults={},s.withConverter=e,s}(function(){})});
$(function (){
    /**
     * notyf.min.js
     */
    var Notyf=function(){"use strict";var e,o=function(){return(o=Object.assign||function(t){for(var i,e=1,n=arguments.length;e<n;e++)for(var o in i=arguments[e])Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o]);return t}).apply(this,arguments)},n=(t.prototype.on=function(t,i){var e=this.listeners[t]||[];this.listeners[t]=e.concat([i])},t.prototype.triggerEvent=function(t,i){var e=this;(this.listeners[t]||[]).forEach(function(t){return t({target:e,event:i})})},t);function t(t){this.options=t,this.listeners={}}(i=e=e||{})[i.Add=0]="Add",i[i.Remove=1]="Remove";var f,i,s=(a.prototype.push=function(t){this.notifications.push(t),this.updateFn(t,e.Add,this.notifications)},a.prototype.splice=function(t,i){i=this.notifications.splice(t,i)[0];return this.updateFn(i,e.Remove,this.notifications),i},a.prototype.indexOf=function(t){return this.notifications.indexOf(t)},a.prototype.onUpdate=function(t){this.updateFn=t},a);function a(){this.notifications=[]}(i=f=f||{}).Dismiss="dismiss";var r={types:[{type:"success",className:"notyf__toast--success",backgroundColor:"#3dc763",icon:{className:"notyf__icon--success",tagName:"i"}},{type:"error",className:"notyf__toast--error",backgroundColor:"#ed3d3d",icon:{className:"notyf__icon--error",tagName:"i"}}],duration:2e3,ripple:!0,position:{x:"right",y:"bottom"},dismissible:!(i.Click="click")},c=(p.prototype.on=function(t,i){var e;this.events=o(o({},this.events),((e={})[t]=i,e))},p.prototype.update=function(t,i){i===e.Add?this.addNotification(t):i===e.Remove&&this.removeNotification(t)},p.prototype.removeNotification=function(t){var i,e,n=this,t=this._popRenderedNotification(t);t&&((e=t.node).classList.add("notyf__toast--disappear"),e.addEventListener(this.animationEndEventName,i=function(t){t.target===e&&(e.removeEventListener(n.animationEndEventName,i),n.container.removeChild(e))}))},p.prototype.addNotification=function(t){var i=this._renderNotification(t);this.notifications.push({notification:t,node:i}),this._announce(t.options.message||"Notification")},p.prototype._renderNotification=function(t){var i=this._buildNotificationCard(t),e=t.options.className;return e&&(t=i.classList).add.apply(t,e.split(" ")),this.container.appendChild(i),i},p.prototype._popRenderedNotification=function(t){for(var i=-1,e=0;e<this.notifications.length&&i<0;e++)this.notifications[e].notification===t&&(i=e);if(-1!==i)return this.notifications.splice(i,1)[0]},p.prototype.getXPosition=function(t){return(null===(t=null==t?void 0:t.position)||void 0===t?void 0:t.x)||"right"},p.prototype.getYPosition=function(t){return(null===(t=null==t?void 0:t.position)||void 0===t?void 0:t.y)||"bottom"},p.prototype.adjustContainerAlignment=function(t){var i=this.X_POSITION_FLEX_MAP[this.getXPosition(t)],e=this.Y_POSITION_FLEX_MAP[this.getYPosition(t)],t=this.container.style;t.setProperty("justify-content",e),t.setProperty("align-items",i)},p.prototype._buildNotificationCard=function(n){var o=this,t=n.options,i=t.icon;this.adjustContainerAlignment(t);var e=this._createHTMLElement({tagName:"div",className:"notyf__toast"}),s=this._createHTMLElement({tagName:"div",className:"notyf__ripple"}),a=this._createHTMLElement({tagName:"div",className:"notyf__wrapper"}),r=this._createHTMLElement({tagName:"div",className:"notyf__message"});r.innerHTML=t.message||"";var c,p,d,l,u=t.background||t.backgroundColor;i&&(c=this._createHTMLElement({tagName:"div",className:"notyf__icon"}),("string"==typeof i||i instanceof String)&&(c.innerHTML=new String(i).valueOf()),"object"==typeof i&&(p=i.tagName,d=i.className,l=i.text,i=void 0===(i=i.color)?u:i,l=this._createHTMLElement({tagName:void 0===p?"i":p,className:d,text:l}),i&&(l.style.color=i),c.appendChild(l)),a.appendChild(c)),a.appendChild(r),e.appendChild(a),u&&(t.ripple?(s.style.background=u,e.appendChild(s)):e.style.background=u),t.dismissible&&(s=this._createHTMLElement({tagName:"div",className:"notyf__dismiss"}),u=this._createHTMLElement({tagName:"button",className:"notyf__dismiss-btn"}),s.appendChild(u),a.appendChild(s),e.classList.add("notyf__toast--dismissible"),u.addEventListener("click",function(t){var i,e;null!==(e=(i=o.events)[f.Dismiss])&&void 0!==e&&e.call(i,{target:n,event:t}),t.stopPropagation()})),e.addEventListener("click",function(t){var i,e;return null===(e=(i=o.events)[f.Click])||void 0===e?void 0:e.call(i,{target:n,event:t})});t="top"===this.getYPosition(t)?"upper":"lower";return e.classList.add("notyf__toast--"+t),e},p.prototype._createHTMLElement=function(t){var i=t.tagName,e=t.className,t=t.text,i=document.createElement(i);return e&&(i.className=e),i.textContent=t||null,i},p.prototype._createA11yContainer=function(){var t=this._createHTMLElement({tagName:"div",className:"notyf-announcer"});t.setAttribute("aria-atomic","true"),t.setAttribute("aria-live","polite"),t.style.border="0",t.style.clip="rect(0 0 0 0)",t.style.height="1px",t.style.margin="-1px",t.style.overflow="hidden",t.style.padding="0",t.style.position="absolute",t.style.width="1px",t.style.outline="0",document.body.appendChild(t),this.a11yContainer=t},p.prototype._announce=function(t){var i=this;this.a11yContainer.textContent="",setTimeout(function(){i.a11yContainer.textContent=t},100)},p.prototype._getAnimationEndEventName=function(){var t,i=document.createElement("_fake"),e={MozTransition:"animationend",OTransition:"oAnimationEnd",WebkitTransition:"webkitAnimationEnd",transition:"animationend"};for(t in e)if(void 0!==i.style[t])return e[t];return"animationend"},p);function p(){this.notifications=[],this.events={},this.X_POSITION_FLEX_MAP={left:"flex-start",center:"center",right:"flex-end"},this.Y_POSITION_FLEX_MAP={top:"flex-start",center:"center",bottom:"flex-end"};var t=document.createDocumentFragment(),i=this._createHTMLElement({tagName:"div",className:"notyf"});t.appendChild(i),document.body.appendChild(t),this.container=i,this.animationEndEventName=this._getAnimationEndEventName(),this._createA11yContainer()}function d(t){var e=this;this.dismiss=this._removeNotification,this.notifications=new s,this.view=new c;var i=this.registerTypes(t);this.options=o(o({},r),t),this.options.types=i,this.notifications.onUpdate(function(t,i){return e.view.update(t,i)}),this.view.on(f.Dismiss,function(t){var i=t.target,t=t.event;e._removeNotification(i),i.triggerEvent(f.Dismiss,t)}),this.view.on(f.Click,function(t){var i=t.target,t=t.event;return i.triggerEvent(f.Click,t)})}return d.prototype.error=function(t){t=this.normalizeOptions("error",t);return this.open(t)},d.prototype.success=function(t){t=this.normalizeOptions("success",t);return this.open(t)},d.prototype.open=function(i){var t=this.options.types.find(function(t){return t.type===i.type})||{},t=o(o({},t),i);this.assignProps(["ripple","position","dismissible"],t);t=new n(t);return this._pushNotification(t),t},d.prototype.dismissAll=function(){for(;this.notifications.splice(0,1););},d.prototype.assignProps=function(t,i){var e=this;t.forEach(function(t){i[t]=(null==i[t]?e.options:i)[t]})},d.prototype._pushNotification=function(t){var i=this;this.notifications.push(t);var e=(void 0!==t.options.duration?t:this).options.duration;e&&setTimeout(function(){return i._removeNotification(t)},e)},d.prototype._removeNotification=function(t){t=this.notifications.indexOf(t);-1!==t&&this.notifications.splice(t,1)},d.prototype.normalizeOptions=function(t,i){t={type:t};return"string"==typeof i?t.message=i:"object"==typeof i&&(t=o(o({},t),i)),t},d.prototype.registerTypes=function(t){var i=(t&&t.types||[]).slice();return r.types.map(function(e){var n=-1;i.forEach(function(t,i){t.type===e.type&&(n=i)});var t=-1!==n?i.splice(n,1)[0]:{};return o(o({},e),t)}).concat(i)},d}();
    window.notyf = new Notyf({
        duration: 2000,
        position: {
            x: 'center',
            y: 'top',
        }});
});
$.fn.serializeObject = function () {
    var o = {};
    var a = this.serializeArray();
    $.each(a, function () {
        if (o[this.name]) {
            if (!o[this.name].push) {
                o[this.name] = [o[this.name]];
            }
            o[this.name].push(this.value || '');
        } else {
            o[this.name] = this.value || '';
        }
    });
    return o;
};
// 显示加载效果
$.fn.loading = function(loadingText = 'Loading...') {
    return this.each(function() {
        const $button = $(this);

        // 保存原始状态
        $button.data('original-text', $button.html());
        $button.data('original-class', $button.attr('class'));

        // 标记为正在加载
        $button.attr('data-loading', 'true');

        // 设置加载状态
        $button.html(`
      <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
      ${loadingText}
    `);
        $button.addClass('disabled');
        $button.attr('disabled', true);
    });
};

// 移除加载效果
$.fn.removeLoading = function(newText) {
    return this.each(function() {
        const $button = $(this);

        // 恢复原始状态
        const originalText = $button.data('original-text');
        const originalClass = $button.data('original-class');

        if (originalText) {
            $button.html(newText || originalText);
            $button.attr('class', originalClass);
            $button.removeAttr('disabled');

            // 移除加载标记
            $button.removeAttr('data-loading');
        }
    });
};
$(document).on("click", ".btn-loading", function(event) {
    const $btn = $(this);
    const loadingText = $btn.data("loading-text") || "Loading...";
    if ($btn.hasClass("data-loading")) return;
    if ($btn.attr("type") === "submit") {
        const $form = $btn.closest("form");
        console.log($form);
        if ($form[0].checkValidity() === false) {
            event.preventDefault();
            event.stopPropagation();
            $form[0].reportValidity();
        }else{
            // event.preventDefault();
            $btn.loading(loadingText);
            $btn.closest("form").submit();
        }
    }else{
        $btn.loading(loadingText);
    }
});
// 浏览器返回时自动取消所有加载效果
if (document.addEventListener) {
    window.addEventListener('pageshow', function (event) {
        if (event.persisted || window.performance &&
            window.performance.navigation.type == 2) {
            $('[data-loading="true"]').removeLoading();
        }
    },false);
}

function setCookie(name,value)
{
    document.cookie = name + "="+ escape (value) + ";expires=0;path=/";
}

function getCookie(name)
{
    var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
    if(arr=document.cookie.match(reg))
        return unescape(arr[2]);
    else
        return null;
}

function setVisitUrls(url,text)
{
    if(typeof(url)==='undefined' || url.length==0 || typeof(text)==='undefined' || text.length==0){
        return;
    }
    // console.log('currentMenu');
    // console.log(url);
    localStorage.setItem('currentMenu',url);
    // setCookie('currentMenu',url);
}

function showError(message, url, seconds) {
    seconds = parseInt(seconds);
    if (seconds == 0 || isNaN(seconds)) seconds = 3000;
    else seconds *= 1000;
    var notification = notyf.error(message);
    setTimeout(function () {
        notyf.dismiss(notification);
        if (typeof (url) != "undefined") {
            pageJump(url);
        }
    }, seconds);
}

function showSuccess(message, url, seconds) {
    seconds = parseInt(seconds);
    if (seconds == 0 || isNaN(seconds)) seconds = 2000;
    else seconds *= 1000;
    var notification = notyf.success(message);
    setTimeout(function () {
        notyf.dismiss(notification);
        if (typeof (url) != "undefined") {
            pageJump(url);
        }
    }, seconds);
}

function pageJump(url) {
    window.location.href = url;
}

function getTimeState(){
    // 获取当前时间
    var timeNow = new Date();
    // 获取当前小时
    var hours = timeNow.getHours();
    // 设置默认文字
    var text = '';
    // 判断当前时间段
    if (hours >= 0 && hours <= 11) {
        text = `早上好，`;
    } else if (hours > 11 && hours <= 14) {
        text = `中午好，`;
    } else if (hours > 14 && hours <= 18) {
        text = `下午好，`;
    } else if (hours > 18 && hours <= 24) {
        text = `晚上好，`;
    }
    // 返回当前时间段对应的状态
    return text;
}

function getQueryVariable(variable)
{
    var href = window.location.href;
    var vars = href.split("/");
    for (var i=0;i<vars.length;i++) {
        var pair = vars[i];
        if(pair == variable){return vars[i+1];}
    }
    return(false);
}

function showNotify()
{
    var url = window.location.href;
    if(url.indexOf('/_add/yes')!=-1){
        url = url.replace('/_add/yes','');
        history.replaceState('', '', url);
        showSuccess('添加成功！');
    }
    if(url.indexOf('/_edit/yes')!=-1){
        url = url.replace('/_edit/yes','');
        history.replaceState('', '', url);
        showSuccess('修改成功！');
    }
    if(url.indexOf('/_save/')!=-1){
        var result = getQueryVariable('_save');
        var msg = getQueryVariable('_msg');
        if(msg!==false){
            msg = decodeURI(msg);
        }
        url = url.replace(/\/_save\/.*/,'');
        history.replaceState('', '', url);
        if(result=='yes'){
            showSuccess(msg ? msg : '保存成功！');
        }else{
            showError(msg ? msg : '保存失败！');
        }
    }
    if(url.indexOf('/_delete/yes')!=-1){
        url = url.replace('/_delete/yes','');
        history.replaceState('', '', url);
        showSuccess('删除成功！');
    }
    if(url.indexOf('/_login/yes')!=-1){
        url = url.replace('/_login/yes','');
        history.replaceState('', '', url);
        showSuccess(getTimeState()+'欢迎登录！');
    }
    if(url.indexOf('/_logout/yes')!=-1){
        url = url.replace('/_logout/yes','');
        history.replaceState('', '', url);
        showSuccess('你已成功退出系统！');
    }
}
//菜单是否异步生成
var menu_async=true;

function changeMenu(id)
{
    if(id==0)
    {
        // setCookie('navigation_id',0);
        localStorage.setItem('navigation_id',0);
        loading();
        window.location.reload(true);
    }
    var navigation_id = id;
    if(!navigation_id) return false;

    // setCookie('navigation_id',navigation_id);
    localStorage.setItem('navigation_id',navigation_id);

    if(menu_async)
    {
        $('.navbar-menu .nav li[class="light-orange"]').removeClass('light-orange').addClass('light-blue');
        $('.navbar-menu .nav li a[id="nav'+navigation_id+'"]').parent().addClass('light-orange').removeClass('light-blue');
        var url = baseurl+'menu/getMenustr';
        ajaxHtml(url, {'id': navigation_id}, $('.nav-main'), {
            beforeSend: function () {
                $('.nav-main').html('<div class="text-center padding-10 text-info"><i class="fa fa-spinner fa-spin"></i>载入中...</div>');
            }, success: function (html) {
                $('.nav-main').html(html);
                // highlightMenu();
                // autoMenu();
            }
        });
    }
    else
    {
        var url = baseurl+'menu/getChangemenu/nid/'+navigation_id;
        window.top.location.href = url;
    }
    return false;
}


function scrollToLocation() {
    var mainContainer = $('.simplebar-content-wrapper');
    if(mainContainer.length==1){
        var scrollToContainer = mainContainer.find('.nav-main-link.active');
        //非动画效果
        if(scrollToContainer.length>0){
            mainContainer.scrollTop(
                scrollToContainer.offset().top - mainContainer.offset().top + mainContainer.scrollTop()-300
            );
        }
        //动画效果
        // mainContainer.animate({
        //     scrollTop: scrollToContainer.offset().top - mainContainer.offset().top + mainContainer.scrollTop()
        // }, 2000);//2秒滑动到指定位置
    }
}

window.onload = function () {
    setTimeout(scrollToLocation, 100 );
    setTableAutoWidth();
};

function setTableAutoWidth() {
    if($("table.table-scrollx").width()>$(".content .row").width()){
        var width = $("table.table-scrollx").width()+100;
        $(".content .row").css('width',width+'px');
    }
}

function parseURL(url) {
    var a = document.createElement('a');
    a.href = url;
    return {
        source: url,
        protocol: a.protocol.replace(':',''),
        host: a.hostname,
        port: a.port,
        query: a.search,
        params: (function(){
            var ret = {},
                seg = a.search.replace(/^\?/,'').split('&'),
                len = seg.length, i = 0, s;
            for (;i<len;i++) {
                if (!seg[i]) { continue; }
                s = seg[i].split('=');
                ret[s[0]] = s[1];
            }
            return ret;
        })(),
        file: (a.pathname.match(/\/([^\/?#]+)$/i) || [,''])[1],
        hash: a.hash.replace('#',''),
        path: a.pathname.replace(/^([^\/])/,'/$1'),
        relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [,''])[1],
        segments: a.pathname.replace(/^\//,'').split('/')
    };
}

/**
 * 调整顶部菜单栏按钮的个数
 */
function resizeNav()
{
    var wdithArr = [1930,1750,1650,1500,1400,1300,1200,1100,800];
    var countObj = {
        800:0,
        1100:3,
        1200:4,
        1300:5,
        1400:6,
        1500:7,
        1650:8,
        1750:10,
        1930:11,
    }

    var btnCount = $(".header-nav-buttons>button").length;
    var bodyWidth = $("body").width();
    console.log(bodyWidth);
    var maxBtnCount = 8;
    // console.log(bodyWidth);
    for (var w in wdithArr){
        if(bodyWidth<wdithArr[w]) {
            var k = wdithArr[w];
            maxBtnCount = countObj[k];
        }
    }

    // console.log(btnCount,maxBtnCount);
    if(btnCount>maxBtnCount){
        var i =0;
        var html = '';
        $(".header-nav-buttons button").each(function (){
            i++;
            if(i>maxBtnCount){
                var text = $(this).html();
                var onclick = $(this).attr('onclick');
                var id = $(this).attr('id');
                html+='<a class="dropdown-item" href="javascript:;" onclick="'+onclick+'" id="'+id+'">'+text+'</a>'
                $(this).remove();
            }
        });
        html+'';
        if(maxBtnCount==0) $(".header-nav-dropdown button").html('<i class="far fa-fw fa-list-alt"></i>');
        else $(".header-nav-dropdown button").html('<span>更多</span> <i class="fa fa-fw fa-angle-down ml-1"></i>');
        $(".header-nav-dropdown .p-2").prepend(html);
        $(".header-nav-dropdown").addClass('d-inline-block');
    }
    // if(bodyWidth>1600){
    //     var html ='';
    //     $(".header-nav-dropdown .p-2 a").each(function (){
    //         var text = $(this).html();
    //         var onclick = $(this).attr('onclick');
    //         var id = $(this).attr('id');
    //         html+='<button type="button" class="btn btn-dual mr-1" onclick="'+onclick+'" id="'+id+'">'+text+'</button>';
    //
    //     });
    //     $(".header-nav-dropdown .p-2").html('');
    //     $(".header-nav-dropdown").removeClass('d-inline-block');
    //     $(".header-nav-buttons").append(html);
    // }
}

function validateSelect()
{
    var formId = 'validateForm';
    var fields = new Array();
    $("select").each(function(){
        formId = $(this).parents('form').attr('id');
        return false;
    });

    var selectObjs = {};
    $("select").each(function(){
        var selectname = $(this).attr('name');
        fields.push(selectname);
        selectObjs[selectname] = new Array();
        $(this).find('option').each(function (){
            if($(this).val().length==0) return;
            selectObjs[selectname].push($(this).val())
        });
    });
    $("input[type='checkbox']").each(function(){
        var selectname = $(this).attr('name');
        fields.push(selectname.replace('[]',''));
        selectObjs[selectname] = new Array();
        $('input[name="'+selectname+'"]').each(function(){
            if($(this).val().length==0) return;
            selectObjs[selectname].push($(this).val())
        });
    });
    //去重
    fields = Array.from(new Set(fields));
    if(fields.length>0){
        $("#"+formId).append($("<input type='hidden' name='__fields' value='"+fields.join('|')+"'></input>"));
        $.each(selectObjs,function(k,v){
            var k = k.replace('[]','');
            var v = v.join('|');
            $("#"+formId).append($("<input type='hidden' name='__"+k+"' value='"+v+"'></input>"));
        });
    }
}

function highlightMenu()
{
    var currentMenu = localStorage.getItem('currentMenu');
    if(typeof(currentMenu)!=='undefined'){
        var currentMenuObj;
        $(".nav-main .nav-main-item .nav-main-link").each(function (){
            var href = $(this).attr('href');
            var isLevel1 = $(this).parent().hasClass('level-1');
            var isLevel2 = $(this).parent().hasClass('level-2');
            // console.log(href,isLevel1,isLevel2);
            if(typeof(href)!=='undefined' && href==currentMenu){
                $(this).addClass('active');
                if(isLevel1===false){
                    $(this).prepend('<i class="nav-main-link-icon fa fa-long-arrow-alt-right" style="margin-left: -30px;"></i>')
                }
                $(this).parents('.level-1').addClass('open')
                $(this).parents('.level-2').addClass('open')
                $(this).parents('.nav-main-submenu').css('overflow','visible')
                return false;
            }
        });
    }
}

function stopAutofill()
{
    //generate a random string to append to the names
    this.autocompleteString = btoa(Math.random().toString()).substr(10, 5);

    this.add_submit_handlers = () => {
        document.querySelectorAll("form").forEach(value => {
            value.addEventListener("submit", (e) => {
                this.form_submit_override(e)
            })
        })
    }

    //add random characters to input names
    this.changeInputNames = () => {
        for (var i = 0; i < this.input_elements_arr.length; i++) {
            this.input_elements_arr[i].setAttribute("name", this.input_elements_arr[i].getAttribute("name") + this.autocompleteString);
        }
    }

    //remove the random characters from input names
    this.changeInputNamesBack = () => {
        for (var i = 0; i < this.input_elements_arr.length; i++) {
            this.input_elements_arr[i].setAttribute("name", this.input_elements_arr[i].getAttribute("name").replace(this.autocompleteString, ''));
        }
    }

    this.form_submit_override = (e) => {
        e.preventDefault()
        this.changeInputNamesBack()
        e.currentTarget.submit()
        return true
    }

    this.setup_form = () => {
        //get all the inputs in the form
        this.input_elements_arr = document.querySelectorAll("input");
        this.changeInputNames();
        this.add_submit_handlers();
    }

    //make sure script calls function after page load
    this.init = () => {
        if (document.readyState === "complete") {
            this.setup_form()
        } else {
            let setup_form = this.setup_form
            document.addEventListener("DOMContentLoaded", function (e) {
                setup_form()
            })
        }
    }
}
$(function (){
    showNotify();
    highlightMenu();
    if($('.navbar-list').length>0 && menu_async)
    {
        // var navigation_id = getCookie('navigation_id');
        var navigation_id = localStorage.getItem('navigation_id');
        if(navigation_id>0)
            changeMenu(navigation_id);
    }

    $("#servicesDropdown").click(function (){
        $(".mynav").toggle();
        $(".mynav").css('min-width','49'+Math.random()+'px');
        if(isFirefox=navigator.userAgent.indexOf("Firefox")>0){
            $(".mynav").css('min-width','88'+Math.random()+'px');
        }

        $('#page-container').toggleClass('side-overlay-o');
        // var currentMenuArrs = JSON.parse(localStorage.getItem('currentMenus'));
        var currentMenuArrs = JSON.parse(getCookie('currentMenus'));
        var menuUrls = new Array();
        var menuSubjects = new Array();
        for (var key in currentMenuArrs){
            menuUrls.push(key);
            menuSubjects.push(currentMenuArrs[key]);
        }
        menuUrls = menuUrls.reverse();
        menuSubjects = menuSubjects.reverse();
        var i = 0;
        $(".visits").html('');
        for (var key in menuUrls){
            if(i>5){
                break;
            }
            $(".visits").append('<div class="mynav-inside"><div class="mynav-transition"><a href="'+menuUrls[key]+'" class="mynav-item mynav-link mynav-link-padding">'+menuSubjects[key]+'</a></div></div>')
            i++;
        }
    });

    $(".mynav-transition a").click(function (){
        var href = $(this).attr('href');
        setVisitUrls(href,$(this).text());
    });
    $(".todolist a").click(function (){
        var href = $(this).attr('href');
        setVisitUrls(href,$(this).text());
    });
    $(".row-deck a").click(function (){
        var href = $(this).attr('href');
        setVisitUrls(href,$(this).text());
    });

    $(document).on("click",".nav-main .nav-main-item .nav-main-link",function () {
        if($(this).parent().find(".nav-main-submenu").length==0){
            var href = $(this).attr('href');
            //在frame中打开
            // var hrefInfo = parseURL(href);
            // var frameUrl = href;
            // if(hrefInfo.query==''){
            //     frameUrl+='?open_model=frame';
            // }
            // if(hrefInfo.query && hrefInfo.query.indexOf('open_model')==-1) frameUrl+='&open_model=frame';
            // $(this).attr('href',frameUrl);
            // $(this).attr('target','main');
            setVisitUrls(href,$(this).text());
        }
    });

    $("#nav-search").keyup(function (){
        var title = $(this).val();
        $(".mynav-h3").removeClass("mynav-button-active");
        if(title!=''){
            $('.nav-group').hide();
            $('mynav-gap a').removeClass('active');
            $(".mynav-h3:contains('" + title + "')").map(function () {
                $(this).parent().show();
            });
            $(".mynav-gap a:contains('" + title + "')").map(function () {
                $(this).parents('.nav-group').show();
                if ($(this).text() == title) {
                    $(this).addClass('active');
                }else{
                    $(this).removeClass('active');
                }
            });
        }else{
            $('.nav-group').show();
        }

    });

    $(".mynav-buttons button").click(function () {
        var title = $(this).text();
        $(".mynav-h3").removeClass("mynav-button-active");
        $(".mynav-h3:contains('" + title + "')").map(function () {
            if ($(this).text() == title) {
                $(this).addClass("mynav-button-active");
                var sTop = $(this).offset().top - 200;
                var nowScrollTop = $('.mynav-left-content').scrollTop();//当前已经滚动了多少
                $(".mynav-left-content").stop(true).animate({
                    scrollTop: sTop + nowScrollTop
                }, 500);
            }
        });
    });

    $(".block-content .table tbody").each(function (){
        if($(this).find("tr").length==0){
            var colspan = $(this).parent().find("th").length;
            if(colspan==0){
                colspan = $(this).parent().find("td").length;
            }
            $(this).append(`
        <tr class="ant-table-placeholder">
            <td colspan="`+colspan+`" class="ant-table-cell">
                <div class="ant-empty ant-empty-normal">
                    <div class="ant-empty-image">
                        <svg class="ant-empty-img-simple" width="64" height="41" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg">
                            <g transform="translate(0 1)" fill="none" fill-rule="evenodd">
                                <ellipse class="ant-empty-img-simple-ellipse" cx="32" cy="33" rx="32" ry="7"></ellipse>
                                <g class="ant-empty-img-simple-g" fill-rule="nonzero">
                                    <path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path>
                                    <path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" class="ant-empty-img-simple-path"></path>
                                </g>
                            </g>
                        </svg>
                    </div>
                    <div class="ant-empty-description">暂无数据</div>
                </div>
            </td>
        </tr>`);
        }
    });


    //数据验证
    // validateSelect();
});

